# Resolve react_native_pods.rb with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip

platform :ios, min_ios_version_supported
prepare_react_native_project!
linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

production = ENV["PRODUCTION"] == "1"

use_modular_headers!
target 'Tinyview' do
  config = use_native_modules!
  
  use_frameworks! :linkage => :static
  $RNFirebaseAsStaticFramework = true
  use_react_native!(
    :path => config[:reactNativePath],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )
  
  $RNFirebaseAnalyticsWithoutAdIdSupport=true

  use_react_native!(
    :path => config[:reactNativePath]
    )
  
  pod 'RNGestureHandler', :path => '../node_modules/react-native-gesture-handler'

  pod 'RNCClipboard', :path => '../node_modules/@react-native-clipboard/clipboard'

  target 'TinyviewTests' do
    inherit! :complete
    # Pods for testing
  end

  post_install do |installer|
    system("chmod -R u+w Pods/RCT-Folly")
    Dir.glob("Pods/RCT-Folly/folly/Portability.h").each do |file|
    text = File.read(file)
    new_contents = text.gsub('#define FOLLY_HAS_COROUTINES 1', '#define FOLLY_HAS_COROUTINES 0')
    File.open(file, "w") { |file| file.puts new_contents }
   end
    bitcode_strip_path = `xcrun --find bitcode_strip`.chop!
   def strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
     framework_path = File.join(Dir.pwd, framework_relative_path)
     command = "#{bitcode_strip_path} #{framework_path} -r -o #{framework_path}"
     puts "Stripping bitcode: #{command}"
     system(command)
   end

   framework_paths = [
     "Pods/LogRocket/LogRocket.xcframework/ios-arm64/LogRocket.framework/LogRocket",
     "Pods/hermes-engine/destroot/Library/Frameworks/macosx/hermes.framework/hermes",
     "Pods/hermes-engine/destroot/Library/Frameworks/macosx/hermes.framework/Versions/Current/hermes",
     "Pods/hermes-engine/destroot/Library/Frameworks/universal/hermes.xcframework/ios-arm64/hermes.framework/hermes",
     "Pods/hermes-engine/destroot/Library/Frameworks/universal/hermes.xcframework/ios-arm64_x86_64-maccatalyst/hermes.framework/hermes"
   ]

   framework_paths.each do |framework_relative_path|
     strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
   end
   
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      # :ccache_enabled => true
    )
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['APPLICATION_EXTENSION_API_ONLY'] = 'NO'
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.0'
        config.build_settings['LIBRARY_SEARCH_PATHS'] = ['$(inherited)', '$(SDKROOT)/usr/lib/swift']
      end
    end
  end


  target 'ImageNotification' do
    pod 'Firebase/Messaging', '~> 11.10.0' # eg 6.31.0
  end
end
