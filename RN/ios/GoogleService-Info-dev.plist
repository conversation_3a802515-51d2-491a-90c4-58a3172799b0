<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>535922886764-dgdpmcoqppfh8gui5me96dmddqc2t4cn.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.535922886764-dgdpmcoqppfh8gui5me96dmddqc2t4cn</string>
	<key>API_KEY</key>
	<string>AIzaSyA_YbtxkX1oExNTap3LV2lbftnR3TyBA5g</string>
	<key>GCM_SENDER_ID</key>
	<string>535922886764</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.newput.tinyview</string>
	<key>PROJECT_ID</key>
	<string>tinyview-dev</string>
	<key>STORAGE_BUCKET</key>
	<string>tinyview-dev.appspot.com</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:535922886764:ios:8cd5fe84f9e360e7037049</string>
	<key>DATABASE_URL</key>
	<string>https://tinyview-dev.firebaseio.com</string>
	<key>WEB_CLIENT_ID</key>
	<string>535922886764-eht00lb24gvfu5q100rutsnkhumbr0fg.apps.googleusercontent.com</string>
</dict>
</plist>