<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>156156699881-uh2rm20gk1jrap8gksmh1qgguqcljr64.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.156156699881-uh2rm20gk1jrap8gksmh1qgguqcljr64</string>
	<key>API_KEY</key>
	<string>AIzaSyD_L5_W_7gQXOYQ_VX1FhDrYZpAYaHIrdw</string>
	<key>GCM_SENDER_ID</key>
	<string>156156699881</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.newput.tinyview</string>
	<key>PROJECT_ID</key>
	<string>tinyview-d78fb</string>
	<key>STORAGE_BUCKET</key>
	<string>tinyview-d78fb.appspot.com</string>
	<key>IS_ADS_ENABLED</key>
	<false/>
	<key>IS_ANALYTICS_ENABLED</key>
	<true/>
	<key>IS_APPINVITE_ENABLED</key>
	<true/>
	<key>IS_GCM_ENABLED</key>
	<true/>
	<key>IS_SIGNIN_ENABLED</key>
	<true/>
	<key>GOOGLE_APP_ID</key>
	<string>1:156156699881:ios:87b7d7ea0acc6cb4e53c78</string>
	<key>DATABASE_URL</key>
	<string>https://tinyview-d78fb.firebaseio.com</string>
	<key>WEB_CLIENT_ID</key>
	<string>156156699881-8dcesl5mnllp3omuj2m88rcs56ckq9dc.apps.googleusercontent.com</string>
</dict>
</plist>
