#import "AppDelegate.h"

#import <React/RCTBundleURLProvider.h>

#import <UserNotifications/UserNotifications.h>
#import <RNCPushNotificationIOS.h>
#import <RNBranch/RNBranch.h>
#import <FBSDKCoreKit/FBSDKCoreKit-Swift.h>
#import <React/RCTLog.h>
#import <React/RCTLinkingManager.h>
#import "Orientation.h"

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{
  
  self.moduleName = @"Tinyview";
  // You can add your custom initial props in the dictionary below.
  // They will be passed down to the ViewController used by React Native.
  self.initialProps = @{};
  
  [[UNUserNotificationCenter currentNotificationCenter] setDelegate:self];
  //[RNBranch setDebug];
  //[[Branch getInstance] enableLogging];
  //[[Branch getInstance] validateSDKIntegration];
  
  //  NSString *filePath = [[NSBundle mainBundle] pathForResource:(isDev ? @"GoogleService-Info-dev" : @"GoogleService-Info-prod") ofType:@"plist"];
  //  FIROptions *options = [[FIROptions alloc] initWithContentsOfFile:filePath];
  //  [FIRApp configureWithOptions:options];
  [FIRApp configure];
  [FIRMessaging messaging].delegate = self;
  [application registerForRemoteNotifications];
    //RCTSetLogThreshold(RCTLogLevelInfo - 1); // Open this line if you want logs in the Release build
  [RNBranch initSessionWithLaunchOptions:launchOptions isReferrable:YES];

  self.window = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
  UIViewController *rootViewController = [UIViewController new];
  self.window.rootViewController = rootViewController;
  [self.window makeKeyAndVisible];
  
  [[FBSDKApplicationDelegate sharedInstance] application:application
  didFinishLaunchingWithOptions:launchOptions];
  
  [super application:application didFinishLaunchingWithOptions:launchOptions];
  
  return YES;
}

- (NSURL *)sourceURLForBridge:(RCTBridge *)bridge
{
  return [self bundleURL];
}
 
- (NSURL *)bundleURL
{
#if DEBUG
  return [[RCTBundleURLProvider sharedSettings] jsBundleURLForBundleRoot:@"index"];
#else
  return [[NSBundle mainBundle] URLForResource:@"main" withExtension:@"jsbundle"];
#endif
}

- (void)application:(UIApplication *)application didReceiveRemoteNotification:(nonnull NSDictionary *)userInfo fetchCompletionHandler:(nonnull void (^)(UIBackgroundFetchResult))completionHandler{
  [[FIRMessaging messaging] appDidReceiveMessage:userInfo];

}

- (void)applicationDidBecomeActive:(UIApplication *)application {
  
}

- (BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options {
    if (![RNBranch application:app openURL:url options:options]) {
        // do other deep link routing for the Facebook SDK, Pinterest SDK, etc
      if ([[FBSDKApplicationDelegate sharedInstance] application:app openURL:url options:options]) {
          return YES;
      } else if ([RCTLinkingManager application:app openURL:url options:options]) {
        return YES;
      } else {
        return NO;
      }
    }
    return YES;
}


- (BOOL)application:(UIApplication *)application continueUserActivity:(NSUserActivity *)userActivity restorationHandler:(void (^)(NSArray *restorableObjects))restorationHandler {
  if ([RNBranch continueUserActivity:userActivity]) {
    return YES;
  } else {
    return [RCTLinkingManager application:application
                      continueUserActivity:userActivity
                        restorationHandler:restorationHandler];
  }
  
}

- (void)messaging:(FIRMessaging *)messaging didReceiveRegistrationToken:(NSString *)fcmToken {
  NSLog(@"didReceiveRegistrationToken %@", fcmToken);
}

- (void)application:(UIApplication *)application didRegisterForRemoteNotificationsWithDeviceToken:(NSData *)deviceToken {
  NSLog(@"didRegisterForRemoteNotificationsWithDeviceToken %@", deviceToken);
  [[FIRMessaging messaging] setAPNSToken:deviceToken];
}

- (void)application:(UIApplication *)application didFailToRegisterForRemoteNotificationsWithError:(NSError *)error {
  NSLog(@"didFailToRegisterForRemoteNotificationsWithError %@", error);
}

- (void)userNotificationCenter:(UNUserNotificationCenter *)center willPresentNotification:(UNNotification *)notification withCompletionHandler:(void (^)(UNNotificationPresentationOptions))completionHandler {
  //[[RNFirebaseNotifications instance]];
  completionHandler(UNNotificationPresentationOptionSound | UNNotificationPresentationOptionAlert | UNNotificationPresentationOptionBadge);
}

- (void)userNotificationCenter:(UNUserNotificationCenter *)center didReceiveNotificationResponse:(UNNotificationResponse *)response withCompletionHandler:(void (^)(void))completionHandler {
  NSLog(@"did receive notification response");
  //[[RNFirebaseNotifications instance] userNotificationCenter:center didReceiveNotificationResponse:response];
  [RNCPushNotificationIOS didReceiveNotificationResponse:response];
}

- (UIInterfaceOrientationMask)application:(UIApplication *)application supportedInterfaceOrientationsForWindow:(UIWindow *)window {
  return [Orientation getOrientation];
}

@end
