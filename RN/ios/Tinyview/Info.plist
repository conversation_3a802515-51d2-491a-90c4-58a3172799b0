<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>APP_ENVIRONMENT</key>
	<string>$(APP_ENVIRONMENT)</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>$(APP_DISPLAY_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>$(CUSTOM_URL_SCHEMA)</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>$(FACEBOOK_BUNDLE_URL_SCHEMES)</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>$(REVERSED_CLIENT_ID)</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>$(APP_BUNDLE_ID)</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>$(OAUTH_CLIENT_ID)</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>FacebookAppID</key>
	<string>$(FACEBOOK_APP_ID)</string>
	<key>FacebookClientToken</key>
	<string>$(FACEBOOK_CLIENT_TOKEN)</string>
	<key>FacebookDisplayName</key>
	<string>$(APP_DISPLAY_NAME)</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>fbapi</string>
		<string>fb-messenger-share-api</string>
		<string>fbauth2</string>
		<string>fbshareextension</string>
		<string>instagram</string>
		<string>whatsapp</string>
		<string>fb</string>
	</array>
	<key>LSMinimumSystemVersion</key>
	<string>13.0</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>Access is needed if you want to take a new picture for your profile.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Access is needed if you want to save a panel image in your photos.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Access is needed if you want to upload an existing picture for your profile.</string>
	<key>UIAppFonts</key>
	<array>
		<string>SFUIDisplay-Regular.otf</string>
		<string>AntDesign.ttf</string>
		<string>Entypo.ttf</string>
		<string>EvilIcons.ttf</string>
		<string>Feather.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>FontAwesome5_Brands.ttf</string>
		<string>FontAwesome5_Regular.ttf</string>
		<string>FontAwesome5_Solid.ttf</string>
		<string>Foundation.ttf</string>
		<string>Ionicons.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>Octicons.ttf</string>
		<string>Roboto_medium.ttf</string>
		<string>Roboto.ttf</string>
		<string>rubicon-icon-font.ttf</string>
		<string>SimpleLineIcons.ttf</string>
		<string>Zocial.ttf</string>
		<string>Fontisto.ttf</string>
		<string>SpinnerRackBB-BoldItalic.otf</string>
		<string>SpinnerRackBB-Ital.otf</string>
		<string>SpinnerRackBB-Reg.otf</string>
		<string>Georgia.ttf</string>
		<string>Georgia-bold.ttf</string>
		<string>Georgia-italic.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>branch_app_domain</key>
	<string>$(BRANCH_DOMAIN)</string>
	<key>branch_key</key>
	<string>$(BRANCH_KEY)</string>
	<key>firebase_performance_collection_enabled</key>
	<true/>
</dict>
</plist>
