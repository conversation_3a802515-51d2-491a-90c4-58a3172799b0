{"name": "Tinyview", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "iosDevice": "react-native run-ios --device 'iPhone'", "start": "react-native start", "test": "jest", "pod-install": "cd ios && RCT_NEW_ARCH_ENABLED=0 pod install"}, "engines": {"node": ">=16", "npm": ">=8.19.4 <8.19.5"}, "rnpm": {"assets": ["./assets/fonts"]}, "dependencies": {"@fawazahmed/react-native-read-more": "^3.0.4", "@invertase/react-native-apple-authentication": "^2.4.0", "@likashefqet/react-native-image-zoom": "^4.3.0", "@react-native-async-storage/async-storage": "^2.1.0", "@react-native-clipboard/clipboard": "^1.15.0", "@react-native-community/cli-platform-android": "^10.0.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/push-notification-ios": "^1.11.0", "@react-native-firebase/analytics": "^21.13.0", "@react-native-firebase/app": "^21.13.0", "@react-native-firebase/auth": "^21.13.0", "@react-native-firebase/crashlytics": "^21.13.0", "@react-native-firebase/functions": "^21.13.0", "@react-native-firebase/messaging": "^21.13.0", "@react-native-firebase/perf": "^21.13.0", "@react-native-firebase/remote-config": "^21.13.0", "@react-native-google-signin/google-signin": "^19.1.0", "@react-native/gradle-plugin": "^0.77.0", "@react-navigation/drawer": "^6.6.11", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "axios": "^1.7.9", "deprecated-react-native-prop-types": "^2.2.0", "initials": "^3.1.1", "jetifier": "^1.6.6", "metro-config": "^0.81.0", "moment": "^2.29.3", "native-base": "^3.4.28", "prop-types": "^15.7.2", "react": "18.3.1", "react-native": "0.76.1", "react-native-branch": "^6.7.1", "react-native-build-config": "^0.3.2", "react-native-context-menu-view": "^1.16.0", "react-native-country-picker-module": "^1.0.1", "react-native-device-info": "^14.0.1", "react-native-element-dropdown": "^2.12.2", "react-native-fast-image": "^8.6.3", "react-native-fbsdk-next": "^13.3.0", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.21.2", "react-native-haptic-feedback": "^2.3.3", "react-native-iap": "^12.16.0", "react-native-image-crop-picker": "^0.42.0", "react-native-image-marker": "^1.2.6", "react-native-in-app-review": "^4.3.3", "react-native-inappbrowser-reborn": "^3.7.0", "react-native-linear-gradient": "^2.8.3", "react-native-orientation": "^3.1.3", "react-native-otp-inputs": "^7.4.0", "react-native-pager-view": "^6.6.1", "react-native-photo-view": "git+https://github.com/alwx/react-native-photo-view.git", "react-native-android-navbar-height": "git+https://github.com/ConnectyCube/react-native-android-navbar-height.git#main", "react-native-push-notification": "^8.1.1", "react-native-raw-bottom-sheet": "^3.0.0", "react-native-reanimated": "^3.16.6", "react-native-render-html": "^6.3.4", "react-native-safe-area": "^0.5.1", "react-native-safe-area-context": "^4.14.0", "react-native-screens": "^4.3.0", "react-native-send-intent-module": "^1.0.0", "react-native-share": "^12.0.3", "react-native-size-matters": "^0.4.2", "react-native-status-bar-height": "^2.6.0", "react-native-svg": "^15.10.0", "react-native-switch": "^1.5.1", "react-native-tab-view": "^4.0.4", "react-native-version-info": "^1.1.1", "react-redux": "8.0.2", "redux": "4.2.0", "redux-thunk": "2.4.1", "runes": "^0.4.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.0", "@react-native-community/cli-platform-android": "15.0.0", "@react-native-community/cli-platform-ios": "15.0.0", "@react-native/babel-preset": "0.76.1", "@react-native/eslint-config": "^0.76.1", "@react-native/metro-config": "0.76.1", "@react-native/typescript-config": "0.76.1", "babel-jest": "^26.6.3", "babel-plugin-transform-remove-console": "^6.9.4", "eslint": "^8.19.0", "jest": "^29.6.3", "react-test-renderer": "18.3.1"}, "packageManager": "yarn@3.6.4"}