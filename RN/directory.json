{"pedro-x-molina": {"title": "<PERSON>", "profile": "/pedro-x-molina/pedro-x-molina-profile.jpg", "cover": "/pedro-x-molina/cover.jpg", "action": "/pedro-x-molina/index.json", "description": "<PERSON> is an internationally acclaimed political cartoonist.", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#FCE298", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#FCF0CF"}, "roles": [{"admins": ["+16072795712"]}]}, "nick-anderson": {"title": "<PERSON>", "profile": "/nick-anderson/nick-anderson-profile.jpg", "cover": "/nick-anderson/cover.jpg", "action": "/nick-anderson/index.json", "description": "<PERSON> is a Pulitzer Prize-winning editorial cartoonist.", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#dff2ff", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#f0f9ff"}, "roles": [{"admins": ["+18324590370"]}]}, "adhdinos": {"title": "ADHDinos", "profile": "/adhdinos/adhdinos-profile.jpg", "cover": "/adhdinos/cover.jpg", "action": "/adhdinos/index.json", "description": "<PERSON><PERSON><PERSON><PERSON> is a webcomic following <PERSON> the Brontosaurus and his misadventures in mental health. By <PERSON>.", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#c0dfd7", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#e3f4ee"}, "roles": [{"admins": ["+12894428503"]}]}, "brief-histories": {"title": "Brief Histories", "profile": "/brief-histories/brief-histories-profile.jpg", "cover": "/brief-histories/cover.jpg", "action": "/brief-histories/index.json", "description": "Brief Histories explores the weird and funny stories that hide in the little things you take for granted. Every comic will make the world around you seem a little more interesting! By <PERSON>.", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#FCE298", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#FCF0CF"}, "roles": [{"admins": ["+18054507586"]}]}, "the-other-end": {"title": "The Other End", "profile": "/the-other-end/the-other-end-profile.jpg", "cover": "/the-other-end/cover.jpg", "action": "/the-other-end/index.json", "description": "Weird little webcomics sketches spinning wildly out of control. By <PERSON> and <PERSON>.", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#FFD459", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#FFF0C6"}, "roles": [{"admins": ["+15622340786"]}]}, "the-daily-show": {"title": "The Daily Show", "profile": "/the-daily-show/the-daily-show-profile.jpg", "cover": "/the-daily-show/preview.jpg", "action": "/the-daily-show/index.json", "description": "<PERSON> and The Daily Show news team cover the biggest headlines of the day.", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#CCF4FE", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#F2FCFF"}, "roles": [{"admins": ["+14086219305"]}]}, "this-modern-world": {"title": "This Modern World", "profile": "/this-modern-world/this-modern-world-profile.jpg", "cover": "/this-modern-world/cover.jpg", "action": "/this-modern-world/index.json", "description": "This Modern World is a weekly cartoon of political and social satire which has been a mainstay of the alternative press for more than two and a half decades.", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#FFE29A", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#FFF7DD"}, "roles": [{"admins": ["+12034301354"]}]}, "quotes": {"title": "Quotes", "profile": "/quotes/quotes-profile.jpg", "cover": "/quotes/cover.jpg", "action": "/quotes/index.json", "description": "Feast your eyes on stellar quotes paired with jaw-dropping art! And guess what? We've played detective, too. Each quote is fact-checked and comes with its own reference receipt. Inspiration with a side of truth!", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#bbc4eb", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#d3d8eb"}, "roles": [{"admins": ["+14086219305"]}]}, "caption-contest": {"title": "Caption Contest", "profile": "/caption-contest/caption-contest-profile.jpg", "cover": "/caption-contest/cover.jpg", "action": "/caption-contest/index.json", "description": "So you think you are witty? Each week we'll publish a cartoon and it's your opportunity to provide a caption. The caption with most likes wins.", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#E0E0E0", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#F2F2F2"}, "roles": [{"admins": ["+14086219305"]}]}, "eggs-n-ben": {"title": "Eggs n' Ben", "profile": "/eggs-n-ben/eggs-n-ben-profile.jpg", "cover": "/eggs-n-ben/cover.png", "action": "/eggs-n-ben/index.json", "description": "Eggs n’ Ben is a raunchy sketch-style comedy about the day-to-day lives of two weird yet lovable roommates.", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#FADF85", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#FAF4E5"}, "roles": [{"admins": ["+17153501318", "+13522816414"]}]}, "cyanide-and-happiness": {"title": "Cyanide & Happiness", "profile": "/cyanide-and-happiness/cyanide-and-happiness-profile.jpg", "cover": "/cyanide-and-happiness/cover.png", "action": "/cyanide-and-happiness/index.json", "description": "Cyanide & Happiness comics now have another home! Enjoy a mix of old and exclusive Tinyview only C&H comics from <PERSON>, <PERSON> and <PERSON>.", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#B7F1FF", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#EBF9FF"}, "roles": [{"admins": ["+12147947001"]}]}, "candy-hearts": {"title": "Candy Hearts", "profile": "/candy-hearts/candy-hearts-profile.jpg", "cover": "/candy-hearts/cover.jpg", "action": "/candy-hearts/index.json", "description": "I did a comic every day for 500 days against all good judgment. A comic series by <PERSON>.", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#E4CCFE", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#FBF8FF"}, "roles": [{"admins": ["+19032358817"]}]}, "they-can-talk": {"title": "They Can Talk", "profile": "/they-can-talk/they-can-talk-profile.jpg", "cover": "/they-can-talk/cover.jpg", "action": "/they-can-talk/index.json", "description": "Comics where animals and bugs talk, like humans, but not really. By <PERSON>.", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#E5E5E5", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#F2F2F2"}, "roles": [{"admins": ["+19789870141"]}]}, "bite-subscribe": {"title": "Bite+Subscribe!", "profile": "/bite-subscribe/bite-subscribe-profile.jpg", "cover": "/bite-subscribe/cover.jpg", "action": "/bite-subscribe/index.json", "description": "Welcome to the spooky online world of vampire vloggers <PERSON> and <PERSON>, and their pets, Spaghetti and...Gromlin?!", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#DFCBC2", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#FAF4F2"}, "roles": [{"admins": ["+447889128758"]}]}, "connie": {"title": "Creative Notes", "profile": "/connie/connie-profile.jpg", "cover": "/connie/cover.jpg", "action": "/connie/index.json", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#ACA1BF", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#F5F2F9"}, "description": "Comics about the highs and lows of the creative process and getting through it. By <PERSON> (aka <PERSON><PERSON><PERSON>)."}, "archie": {"title": "Bite Sized Archie", "profile": "/archie/archie-profile.jpg", "cover": "/archie/cover.jpg", "action": "/archie/index.json", "description": "We hope you're hungry... hungry for laughs! Archie Comics' first-ever webcomic series, <PERSON><PERSON>, is here!", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#FFCC00", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#FFFBF0"}, "roles": [{"admins": ["+19148448208"]}]}, "say-their-names": {"title": "Say Their Names", "profile": "/say-their-names/say-their-names-profile.jpg", "cover": "/say-their-names/say-their-names-cover.jpg", "action": "/say-their-names/index.json", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#EFC8CD", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#FFF8F8"}, "description": "Stories of victims of injustice. By <PERSON>."}, "itchy-feet": {"title": "<PERSON><PERSON>et", "profile": "/itchy-feet/itchy-feet-profile.jpg", "cover": "/itchy-feet/itchy-feet-cover.jpg", "action": "/itchy-feet/index.json", "description": "Itchy Feet is the weekly web comic about travel, life in foreign countries, and learning new languages.", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#FFE29A", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#FFF7DD"}, "roles": [{"admins": ["+491627209454"]}]}, "in-science-we-trust": {"title": "In Science We Trust", "profile": "/in-science-we-trust/in-science-we-trust-profile.jpg", "cover": "/in-science-we-trust/in-science-we-trust-cover.jpg", "action": "/in-science-we-trust/index.json", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#CCF4FE", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#F2FCFF"}, "description": "Inspire the Einstein in you—and your kids! Dive into IN SCIENCE WE TRUST, a series of true comic stories about brilliant scientists and the groundbreaking discoveries that shaped our world!"}, "frankie-fearless": {"title": "<PERSON>", "profile": "/frankie-fearless/frankie-fearless-profile.jpg", "cover": "/frankie-fearless/frankie-fearless-800x1000.jpg", "action": "/frankie-fearless/index.json", "description": "This is the story of a grade-schooler named <PERSON> who's anything but-and the paranormal adventures she reluctantly keeps stumbling into. By <PERSON>, the creator of Fowl Language.", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#D9D9FF", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#F5F5FF"}, "roles": [{"admins": ["+19135486470"]}]}, "biographic": {"title": "Biographic", "profile": "/biographic/biographic-profile.jpg", "cover": "/biographic/biographic-cover.jpg", "action": "/biographic/index.json", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#F5D053", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#FFFAE8"}, "description": "Illustrated life stories of pop culture's biggest icons! By <PERSON>."}, "fowl-language": {"title": "Fowl Language", "profile": "/fowl-language/fowl-language-profile.jpg", "cover": "/fowl-language/fowl-language-cover.jpg", "action": "/fowl-language/index.json", "description": "Imperfect parent and coffee enthusiast, <PERSON>, records his struggles to raise his children while trying to maintain his sanity.", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#AFE9D3", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#EAF3F0"}, "roles": [{"admins": ["+19135486470"]}]}, "gemma": {"title": "<PERSON>", "profile": "/gemma/gemma-profile.jpg", "cover": "/gemma/gemma-cover.jpg", "action": "/gemma/index.json", "description": "Comics for... her! Fun! Wellness. Real-life stories + anxiety. Fashun + beauty. Lifestyle. Pugs! For some reason? By <PERSON>.", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#FA4616", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#FFECE8"}, "roles": [{"admins": ["+15108330637"]}]}, "heart-and-brain": {"title": "Heart and Brain", "profile": "/heart-and-brain/heart-and-brain-profile.jpg", "cover": "/heart-and-brain/HeartandBrain-cover.jpg", "action": "/heart-and-brain/index.json", "description": "Heart and Brain is a New York Times Bestselling series by <PERSON>.", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#C0DEFA", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#EBF2F8"}, "roles": [{"admins": ["+12482315957"]}]}, "matt-bors": {"title": "<PERSON>", "profile": "/matt-bors/matt-bors-profile.jpg", "cover": "/matt-bors/bors-cover.jpg", "action": "/matt-bors/index.json", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#A1C387", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#F5FDEF"}, "description": "From mass protests to <PERSON> and the pandemic, two-time Pulitzer Finalist <PERSON>' cartoons satirize the powerful and chronicle America's march into a strange dystopian future."}, "olo": {"title": "O<PERSON>", "profile": "/olo/olo-profile.jpg", "cover": "/olo/olo-cover.jpg", "action": "/olo/index.json", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#30DBBB", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#E9FFFB"}, "description": "An alien kid and his pet <PERSON><PERSON> accidentally create human life. While observing human behavior, they must also deal with the existential pressure of being responsible for a planet of beings."}, "product-plug": {"title": "Product Plug", "profile": "/product-plug/product-plug-profile.jpg", "cover": "/product-plug/product-plug-cover.jpg", "action": "/product-plug/index.json", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#9FE2FF", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#EBF9FF"}, "description": "A Tinyview original series about life and tech.", "roles": [{"admins": ["+919074748243"]}]}, "rob-rogers": {"title": "<PERSON>", "profile": "/rob-rogers/rob-rogers-profile.jpg", "cover": "/rob-rogers/rob-rogers-cover.jpg", "action": "/rob-rogers/index.json", "description": "<PERSON> is an award-winning freelance editorial cartoonist. <PERSON> was a finalist for the Pulitzer Prize in both 1999 and 2019.", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#D7E4F5", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#ECEFF5"}, "roles": [{"admins": ["+14123277334"]}]}, "lunarbaboon": {"title": "Lunarbaboon", "profile": "/lunarbaboon/lunarbaboon-profile.jpg", "cover": "/lunarbaboon/cover.jpg", "action": "/lunarbaboon/index.json", "description": "A half man/half moon monkey trying to make sense of it all!", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#E0E0E0", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#F2F2F2"}, "roles": [{"admins": ["+14168862989"]}]}, "thenib": {"title": "The Nib", "profile": "/thenib/thenib-profile.jpg", "cover": "/thenib/cover.jpg", "action": "/thenib/index.json", "description": "The Nib is a publication devoted to political and non-fiction comics. They publish journalism, essays, memoir and satire about what is going down in the world, all in comics form, the best medium.", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#FDE071", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#FEF6D4"}, "roles": [{"admins": ["+13302807979"]}]}, "tinyview": {"title": "Tinyview", "roles": [{"admins": ["+14086219305"]}, {"analysts": ["+917974044520", "+917000197192", "+919074748243", "+919893086712", "+919424595392", "+919893000187", "+918602169251"]}], "profile": "/tinyview/tinyview-profile.jpg", "cover": "/tinyview/tinyview-cover.jpg", "action": "/tinyview/index.json", "showCommentsFeed": 1, "showCommentsComics": 2, "freeEpisodes": -1, "freeHours": -1, "style": {"backgroundColor": "#CCF4FE", "borderWidth": 0, "borderColor": "#E8E8E8", "notificationColor": "#F2FCFF"}, "description": "To see why we started Tinyview, read the story of, well, Tinyview :-)"}}