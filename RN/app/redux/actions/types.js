const Types = {
  //READ COMIC
  FETCH_TOC: "FETCH_TOC",
  ERROR_IN_FETCH: "ERROR_FETCH",
  SHOW_LOADING: "SHOW_LOADING",
  HIDE_LOADING: "HIDE_LOADING",
  <PERSON><PERSON><PERSON>_FETCH_TOC: "<PERSON><PERSON><PERSON>_FETCH_TOC",
  READ_COMIC: "READ_COMIC",
  CLEAR_COMIC: "CLEAR_COMIC",
  REMOVE_COMIC: "REMOVE_COMIC",
  MULTIPLE_PANEL_STATUS: "MULTIPLE_PANEL_STATUS",
  REFRESH_PAGE: "REFRESH_PAGE",
  SET_PATH_URL: 'SET_PATH_URL',
  SET_CURRENT_ALERT: 'SET_CURRENT_ALERT',
  RECORD_LIKE: 'RECORD_LIKE',
  REMOVE_LIKE: 'REMOVE_LIKE',
  ADD_GIFT: 'ADD_GIFT',
  REMOVE_GIFT: 'REMOVE_GIFT',
  ADD_LIKE: 'ADD_LIKE',
  LIKE_COMMENT: 'LIKE_COMMENT',
  DIS<PERSON><PERSON>KE_COMMENT: 'DISLIKE_COMMENT',
  RESET_STATE: 'RESET_STATE',
  SENT_POST: 'SENT_POST',
  ADD_COMMENT: 'ADD_COMMENT',
  REMOVE_COMMENT: 'REMOVE_COMMENT',
  REFRESH_COMIC_PAGE: 'REFRESH_COMIC_PAGE',
  NOTIFICATION_PERMISSION_GRANTED: 'NOTIFICATION_PERMISSION_GRANTED',
  NOTIFICATION_PERMISSION_REVOKED: 'NOTIFICATION_PERMISSION_REVOKED',

  SHOW_LOGIN_INDICATOR: 'SHOW_LOGIN_INDICATOR',
  HIDE_LOGIN_INDICATOR: 'HIDE_LOGIN_INDICATOR',
  SAVE_USER_ANONYMOUS_STATE: 'SAVE_USER_ANONYMOUS_STATE',


  //USER INFO
  UPDATE_ALERTS: 'UPDATE_ALERTS',
  //UPDATE_REFERRAL_CODE: 'UPDATE_REFERRAL_CODE',

  //PRODUCT ALREADY PURCHASED
  IS_PURCHASE_IN_PROGRESS: 'IS_PURCHASE_IN_PROGRESS',
  PRODUCT_ALREADY_PURCHASED: 'PRODUCT_ALREADY_PURCHASED',
  PRODUCT_PURCHASED_FROM_INFLUENCE: 'PRODUCT_PURCHASED_FROM_INFLUENCE',

  //BUY FROM INSLUENCE POINT
  BUY_FROM_INFLENCE_POINT: 'BUY_FROM_INFLENCE_POINT',

  //FREELY_ACCESSABLE_COMIC
  FREELY_ACCESSABLE_COMIC: 'FREELY_ACCESSABLE_COMIC',

  //SIGN_OUT USER
  SIGN_OUT_USER: 'SIGN_OUT_USER',

  SHOW_LOGIN_SCREEN_ERROR: 'SHOW_LOGIN_SCREEN_ERROR',
  CLEAR_LOGIN_SCREEN_ERROR: 'CLEAR_LOGIN_SCREEN_ERROR',

  SHOW_OTP_SCREEN_ERROR: 'SHOW_OTP_SCREEN_ERROR',
  CLEAR_OTP_SCREEN_ERROR: 'CLEAR_OTP_SCREEN_ERROR',

  UPDATE_USER_DETAILS: 'UPDATE_USER_DETAILS',

  SHOULD_SHOW_ALL_COMICS: 'SHOULD_SHOW_ALL_COMICS',

}

export default Types