import T from './types'
import { Ajax } from '../../api/ajax'
import FileCache from '../../config/FileCache'
import FirebaseManager from '../../config/FirebaseManager'
import IAPManager from '../../config/IAPManager'
import store from '../store'
import { Platform, Alert } from 'react-native'
import { Toast } from 'native-base'
import { Utils } from '../../config/Utils';
import { settings } from '../../config/settings';
import { SharedPreferences } from '../../config/SharedPreferences'
import RNInAppBrowser from 'react-native-inappbrowser-reborn'
import NetworkUtils from '../../config/NetworkUtils'
import SessionManager from '../../config/SessionManager'
import Share from "react-native-share";
import ImageMarker from "react-native-image-marker"
import { scale } from 'react-native-size-matters'
import { Color } from '../../config/Color'
import { Linking } from 'react-native'
import FriendsModal from '../../components/friendsPackage/modals/FriendsModal'
import FriendRequestModal from '../../components/friendsPackage/modals/FriendRequestModal'
import moment from 'moment'
import { Constants } from '../../config/Constants'
import UserSession from '../../config/UserSession'
import Orientation from 'react-native-orientation';
import { batch } from 'react-redux'
import DeepLinkManager from '../../config/DeepLinkManager'
import NavigationService from '../../config/NavigationService'

export const readComicAction = (url, ignoreJSONCache, isSharedByPremiumUser = false, callback = null) => (dispatch, getState) => {
  Utils.log("Performance Checking : readComicAction 1 " + new Date().getTime())
  Utils.log('Going to read comic for url ' + url + ' with force ' + ignoreJSONCache + " Time " + new Date().getTime());

  dispatch({
    type: T.SET_PATH_URL,
    payload: url
  })

  const endPointURL = Utils.getMeaningFullURL(url);

  Utils.log("Performance Checking : readComicAction 2 " + new Date().getTime())
  loadComicAndUpdate(url, ignoreJSONCache, isSharedByPremiumUser, callback)(dispatch, getState)

  FirebaseManager.instance.logEvent('TINYVIEW_PAGEVIEW', { 'url': endPointURL });
}

const fetchComic = async (url, isStale, ignoreJSONCache, getState, dispatch, isHeaderFooterURL = false) => {
  var comicURL = url
  if (isStale || ignoreJSONCache) {

    return Ajax.fetchAndSaveComic(comicURL, true, !isHeaderFooterURL)
      .catch(fetchError => {
        if (isHeaderFooterURL) {
          return;
        }

        let errorObj = { status: 500, title: "", message: "Something went wrong. Please try again later" }
        if (!NetworkUtils.instance.isAvailable()) {
          errorObj.status = 502
          errorObj.title = "No Internet Available"
          errorObj.message = Constants.INTERNET_ERROR
        } else if (fetchError.status == 404) {
          errorObj.status = 404
          errorObj.title = "Not Available"
          errorObj.message = "Oops! The page you are looking for does not exist"
        }

        onResponse(getState, url, dispatch, {}, {}, errorObj)
      })
  } else {
    return FileCache.default.getFileContentForURL(url)
      .then(
        result => {
          let responseJson = JSON.parse(result)
          return responseJson.comics;
        }
      )
      .catch(error => Utils.log(error))
  }
}

export const clearComics = () => async (dispatch, getState) => {
  dispatch({
    type: T.CLEAR_COMIC,
  })
}

export const loadComicAndUpdate = (url, ignoreJSONCache, isSharedByPremiumUser = false, callback) => async (dispatch, getState) => {
  try {
    Utils.log("Performance Checking : loadComicAndUpdate 1 " + new Date().getTime())
    const { comicRes, pageStatusRes, error } = await fetchComicData(url, true, ignoreJSONCache, isSharedByPremiumUser, getState, dispatch);
    Utils.log("Performance Checking : loadComicAndUpdate 2 " + new Date().getTime())
    if (error && !comicRes) {
      onResponse(getState, url, dispatch, {}, pageStatusRes, error, callback)
    } else {
      onResponse(getState, url, dispatch, comicRes, pageStatusRes, null, callback)
    }
  } catch (error) {
    onResponse(getState, url, dispatch, {}, {}, error, callback)
    Utils.log(error)
  }
}

const fetchComicData = async (url, isStale, ignoreJSONCache, isSharedByPremiumUser = false, getState, dispatch) => {
  try {
    return fetchComic(url, isStale, ignoreJSONCache, getState, dispatch).then(async (comicRes) => {
      if (comicRes) {
        if (Utils.isFutureDate(comicRes.datetime)) {
          let errorObj = {}
          errorObj.status = 404
          errorObj.title = "Not Available"
          errorObj.message = "Oops! The page you are looking for does not exist"
          return { comicRes: null, pageStatusRes: {}, error: errorObj }
        }

        comicRes.isSharedByPremiumUser = isSharedByPremiumUser
        for (const index in comicRes.panels) { //Removing the same image from the comic
          let validPanel = comicRes.panels[index]
          if (validPanel.image && validPanel.image == comicRes.ogImage) {
            comicRes.panels.splice(index, 1)
          }

          if (validPanel.action == "follow" && validPanel.actionType == "tinyview") {
            comicRes.autoRefreshOnFollow = true
          }

          if (validPanel.highlightPanel && validPanel.highlightPanel.length > 0) {
            const purchasedSubs = SessionManager.instance.getPurchasedItems()
            const isPurchasedEmpty = Utils.isEmptyObject(purchasedSubs)

            let addActiveBorder = isPurchasedEmpty
            for (const reqData of validPanel.highlightPanel) {
              const [subKey, subValue] = Object.entries(reqData)[0]

              if (subValue === true) {
                if (isPurchasedEmpty) {
                  addActiveBorder = false
                  break
                } else if (purchasedSubs.includes(subKey)) {
                  addActiveBorder = true
                  break
                }
              }
            }

            if (addActiveBorder) {
              comicRes.panels[index].showActiveBorder = true
            }
          }
        }

        let hasBonusPanel = false
        let indexOfBonusPanel = -1
        let hasAnySubscription = SessionManager.instance.hasAnySubscriptionPurchase()
        if (!hasAnySubscription && !isSharedByPremiumUser) {
          for (const iterator of comicRes.panels) {
            indexOfBonusPanel = indexOfBonusPanel + 1
            if (iterator.template == "bonus") {
              hasBonusPanel = true;
              comicRes.autoRefreshOnFollow = true
              break;
            }
          }
        }

        let comicUrl = Utils.getMeaningFullURL(url)
        let pageStatus = await getPageStatus(comicUrl)(dispatch, getState)
        const isComicReaded = pageStatus && pageStatus.isRead
        let isComicUnlocked = false
        if (!Utils.isTinyviewPage(url)) {
          isComicUnlocked = UserSession.instance.isComicUnlocked(comicUrl)

          if (isComicUnlocked) {
            comicRes.isComicUnlocked = true //Will use on the Comic Page UI to show UI
          }

          const previewLength = Utils.getPanelLength(comicRes)
          let diffActionsInfluencePoints = SessionManager.instance.getInfluencePointsValuesForActions()
          if (Utils.isPremiumComic(comicRes)) {
            if (!isComicUnlocked && !hasAnySubscription && !isSharedByPremiumUser) {
              comicRes.influencePoints = !Utils.isEmptyObject(diffActionsInfluencePoints) ? diffActionsInfluencePoints.READ_PREMIUM_COMIC : Constants.REQUIRED_POINTS_FOR_PREMIUM_COMIC //Setting by default influence point to 5
              comicRes.panels.splice(previewLength, comicRes.panels.length)
              comicRes.panels.push(Utils.getTinyviewSubscriptionPanel())
            } else {
              let readValue = 1
              if (isSharedByPremiumUser) {
                readValue = 0
              }
              if (!Utils.isEmptyObject(pageStatus)) {
                pageStatus.seriesReadCount = pageStatus.seriesReadCount + 1
                pageStatus.viewCount = pageStatus.viewCount + 1
                pageStatus.isRead = true
              }

              recordPageView(comicUrl, readValue)()
            }
          } else if (!isComicUnlocked) {  // monthly read condition
            const seriesName = Utils.getChannelName(url);
            const monthlyReadQuota = UserSession.instance.getMonthlyMaxComicReadQuota();
            const showingMonthlyReadQuota = monthlyReadQuota !== -1
            const maxReadQuota = showingMonthlyReadQuota ? monthlyReadQuota : SessionManager.instance.getSeriesFreeEpisodes(seriesName);

            let remainingSeriesFreeEpisodes = 0, isComicAccessWithinTime = false;
            if (!showingMonthlyReadQuota) {
              isComicAccessWithinTime = Utils.isComicAccessibleWithinTime(comicRes.datetime, seriesName)
              remainingSeriesFreeEpisodes = Utils.remainingSeriesQuota(pageStatus.seriesReadCount, seriesName)
            }

            if (!showingMonthlyReadQuota && hasAnySubscription) {
              recordPageView(comicUrl)()
            } else if (maxReadQuota < 0) {
              if (!isComicUnlocked && hasBonusPanel && !isSharedByPremiumUser) { //Also used same condition when maxReadQuota value is greater than or equals to 0
                comicRes.influencePoints = !Utils.isEmptyObject(diffActionsInfluencePoints) ? diffActionsInfluencePoints.READ_BONUS_PANEL : Constants.REQUIRED_POINTS_FOR_BONUS_PANEL //Setting by default influence point to 1
              }
              recordPageView(comicUrl)()
            } else if (maxReadQuota >= 0) {
              let readValue = 0
              if (showingMonthlyReadQuota) {
                readValue = await hasComicReadAccess({ pageUrl: comicUrl })
                comicRes.isUserAlreadyReadComic = readValue == 2 //Will use on the Comic Page UI to show UI
                if (readValue == 2) {
                  readValue = 1
                }
              } else {
                if ((isComicReaded || remainingSeriesFreeEpisodes > 0) && !isComicAccessWithinTime && !isSharedByPremiumUser) {
                  readValue = 1
                }
              }
              if (!Utils.isEmptyObject(pageStatus) && (readValue === 1 || isComicAccessWithinTime || isSharedByPremiumUser)) {
                pageStatus.seriesReadCount = pageStatus.seriesReadCount + 1
                pageStatus.viewCount = pageStatus.viewCount + 1
                pageStatus.isRead = true
              }
              recordPageView(comicUrl, readValue)()

              comicRes.canUserReadComic = true //Will use on the Comic Page UI to show UI
              if (!isSharedByPremiumUser && !isComicAccessWithinTime && ((showingMonthlyReadQuota && readValue !== 1) || (!showingMonthlyReadQuota && remainingSeriesFreeEpisodes === 0 && readValue !== 1))) {
                comicRes.canUserReadComic = false //Will use on the Comic Page UI to show UI
                comicRes.panels.splice(previewLength, comicRes.panels.length)
                if (showingMonthlyReadQuota) {
                  comicRes.panels.push(Utils.getTinyviewUnlimitedReadPanel())
                } else {
                  comicRes.influencePoints = !Utils.isEmptyObject(diffActionsInfluencePoints) ? diffActionsInfluencePoints.READ_PREMIUM_COMIC : Constants.REQUIRED_POINTS_FOR_PREMIUM_COMIC //Setting by default influence point to 1
                  comicRes.panels.push(Utils.getTinyviewSubscriptionPanel())
                }
              } else if (!isComicUnlocked && hasBonusPanel && !isSharedByPremiumUser) { //Also used same condition when maxReadQuota value is smaller than 0
                comicRes.influencePoints = !Utils.isEmptyObject(diffActionsInfluencePoints) ? diffActionsInfluencePoints.READ_BONUS_PANEL : Constants.REQUIRED_POINTS_FOR_BONUS_PANEL //Setting by default influence point to 1
              }
            }
          } else {
            recordPageView(comicUrl)()
          }
        } else { // Tinyview Comic pages
          if (Utils.isDirectoryPageURL(comicUrl) || Utils.isSubscriptionURL(comicUrl) || Utils.isUpdatePageURL(comicUrl)) {
            recordPageView(comicUrl)()
          } else {
            recordPageView(comicUrl, 0)()
          }
        }

        if (Utils.isHomeURL(url) && !SessionManager.instance.getShowAllComics()) {
          comicRes.ogImage = "/tinyview/following-banner.jpg"
        }

        const isComicPageURL = comicRes.panels && Utils.isComicURL(url)

        //#start - Finding the last comic panel (without header and footer)
        if (isComicPageURL) {
          comicRes.panels.push(Utils.getLastComicInfoPanel())
          //const validPanels = Utils.getValidPanels(comicRes.panels, null, true, url)
          //const lastValidPanel = validPanels[validPanels.length - 1]

          // for (const iterator of comicRes.panels) {
          //   if ((lastValidPanel.md5sum && iterator.md5sum == lastValidPanel.md5sum) || (lastValidPanel.image && iterator.image == lastValidPanel.image)) {
          //     iterator.lastComicPanel = true
          //     break
          //   }
          // }
        }
        //#end

        if (!Utils.isComicURL(url) || Utils.isSubscriptionURL(url)) {
          const series = Utils.getChannelName(url)
          const seriesStyle = Utils.getSeriesSpecificStyle(series)
          comicRes.panels.forEach((panel) => {
            panel.bgColor = seriesStyle.backgroundColor
          })
        }

        Utils.log("Performance Checking : Loading Header File " + new Date().getTime())
        const headerEndPointURL = Utils.getComicSeriesHeaderURL(url)
        const headerURL = Utils.resolvePath(url, headerEndPointURL);
        var headerPanels = await fetchComic(headerURL, isStale, ignoreJSONCache, getState, dispatch, true)
        if (headerPanels && headerPanels.panels && comicRes) {
          const series = Utils.getChannelName(url)
          const seriesStyle = Utils.getSeriesSpecificStyle(series)
          headerPanels.panels.forEach((panel) => {
            panel.bgColor = seriesStyle.backgroundColor
          })
          comicRes.panels = headerPanels.panels.concat(comicRes.panels)
        }

        if (Utils.checkObject(comicRes.previewPanels) && isComicPageURL) {
          if (headerPanels && headerPanels.panels && comicRes) {
            comicRes.previewPanels = headerPanels.panels.length + comicRes.previewPanels
          }
        }

        const alertName = Utils.getChannelName(url)
        const isFollowingSeries = getState().userInfo && getState().userInfo.alerts && getState().userInfo.alerts[alertName] == true ? true : false

        if (!comicRes.footer && !Utils.isComicURL(url)) {
          //#start - Getting valid panels for this user
          const validPanels = Utils.getValidPanels(comicRes.panels, null, isFollowingSeries, url, isSharedByPremiumUser)
          comicRes.panels = validPanels
          //#end

          return { comicRes, pageStatusRes: pageStatus, error: null }
        }

        Utils.log("Performance Checking : Fetched Header and Going to Load Footer File " + new Date().getTime())

        const footerEndPoint = comicRes.footer ? comicRes.footer : Utils.getComicSeriesFooterURL(url)

        const footerURL = Utils.resolvePath(url, footerEndPoint);
        return fetchComic(footerURL, isStale, ignoreJSONCache, getState, dispatch, true).then((footerPanels) => {
          if (footerPanels && comicRes && comicRes.panels) {
            const series = Utils.getChannelName(url)
            const seriesStyle = Utils.getSeriesSpecificStyle(series)
            footerPanels.panels.forEach((element, index) => {
              if (index == 0) {
                element.footerPanelsStart = true
              }
              element.bgColor = seriesStyle.backgroundColor
              comicRes.panels.push(element)
            });
          }
          Utils.log("Performance Checking : Fetched Footer File " + new Date().getTime())

          //#start - Getting valid panels for this user
          const validPanels = Utils.getValidPanels(comicRes.panels, null, isFollowingSeries, url, isSharedByPremiumUser)
          comicRes.panels = validPanels
          //#end

          return { comicRes, pageStatusRes: pageStatus, error: null }
        })
      }
    })

  } catch (error) {
    Utils.log("Inner catch " + error)
  }
}

export const onComicChange = (url, response) => async (dispatch, getState) => {

  dispatch({
    type: T.SET_PATH_URL,
    payload: url
  })

  checkComicPurchaseStatus(response, url)(dispatch, getState)
}

export const getPageStatus = (logURL, callback = null) => async (dispatch, getState) => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      let tinyviewPageData = null
      let pageURL = logURL
      if (Utils.isHomeURL(pageURL)) {
        const seriesURL = Utils.getComicSeriesURL(pageURL);
        tinyviewPageData = await FirebaseManager.instance.getPageInfo(seriesURL)
      }

      let data = await FirebaseManager.instance.getPageInfo(pageURL)

      if (Utils.isHomeURL(logURL)) {
        data.likeCount = tinyviewPageData.likeCount
      }

      if (data && callback) {
        callback(data)
      }
      return data
    } else {
      if (!Toast.isActive("internetError")) {
        Utils.showToast(Constants.INTERNET_ERROR, "bottom", "internetError")
      }
      if (callback) {
        callback()
      }
    }
  } catch (error) {
    Utils.log(error)
    if (callback) {
      callback()
    }
  }
}

const hasComicReadAccess = async (data) => {
  try {
    const result = await FirebaseManager.instance.hasComicReadAccess(data)
    if (result && result.monthlyReadCounter) {
      UserSession.instance.updateReadedFreeComicsCount(result.monthlyReadCounter.count)
    }
    return result.isRead
  } catch (error) {
    return 1
  }
}

export const getMultiplePanelInfo = (pages) => async (dispatch, getState) => {
  const data = await FirebaseManager.instance.getMultiplePanelInfo(pages)

  dispatch({
    type: T.MULTIPLE_PANEL_STATUS,
    payload: data
  })

}

export const recordPageView = (url, isRead = null, storyID = null) => async (dispatch, getState) => {
  let data = { pageUrl: url, storyID: storyID }
  if (isRead != null) {
    data.isRead = isRead
  }
  await FirebaseManager.instance.recordPageView(data)
}

export const clearComicPageInfo = () => async (dispatch, getState) => {
  dispatch({
    type: T.REFRESH_PAGE,
    urlToRefresh: getState().readComic.pathUrl,
    isRefreshed: false,
  })
}

function isProductPurchased(cachedPurchases, productID) {
  let hasBought = false;
  if (cachedPurchases && cachedPurchases.length > 0) {
    for (const key in cachedPurchases) {
      const purchase = cachedPurchases[key];
      if (purchase == productID) {
        hasBought = true;
        break;
      }
    }
  }
  return hasBought;
}

function checkIfBuyFromInfluence(state, productId, url) {
  let hasBuy = false;
  const puchasedItems = state.userInfo.purchases;
  if (puchasedItems && puchasedItems.length > 0 && productId != null) {
    for (const i in puchasedItems) {
      const purchase = puchasedItems[i];

      if (purchase.productId == productId && purchase.store && purchase.store.toLowerCase() == "influence") {
        hasBuy = true;
        break;
      }
    }
  }

  if (!hasBuy && url) {
    hasBuy = UserSession.instance.isComicUnlocked(url)
  }

  return hasBuy;
}

const getInAppProducts = (url, response) => {
  if (!response.inAppProducts || response.inAppProducts.length == 0) { // Needs to remove it after home page and series home page have isFree attribute.
    return null;
  }
  const channelName = Utils.getChannelName(url)
  let seriesData = SessionManager.instance.getSeriesData(channelName)

  Utils.log('response.inAppProducts' + response.inAppProducts)
  let productsToCheck = response.inAppProducts != null ? JSON.parse(JSON.stringify(response.inAppProducts)) : []

  // if (seriesData.retiredInAppProducts && seriesData.retiredInAppProducts.length > 0) {
  //   for (const key in productsToCheck) {
  //     let shouldRemove = false
  //     for (const index in seriesData.retiredInAppProducts) {
  //       let value = seriesData.retiredInAppProducts[index]
  //       if (productsToCheck[key] == value) {
  //         shouldRemove = true
  //         break
  //       }
  //     }
  //     if (shouldRemove) {
  //       productsToCheck.splice(key, 1)
  //     }
  //   }
  // }

  if (seriesData && seriesData.inAppProducts) {
    productsToCheck = productsToCheck.concat(seriesData.inAppProducts)
  }
  const allProducts = [...new Set(productsToCheck)]

  return (!allProducts || allProducts.length == 0) ? null : allProducts
}

export const checkComicPurchaseStatus = (response, url) => async (dispatch, getState) => {
  if (!response) {
    return;
  }

  let cachedPurchases = SessionManager.instance.getPurchasedItems();

  let hasPurchase = false
  let productsToCheck = response.inAppProducts
  if (productsToCheck == null) productsToCheck = []
  if (response && response.productId) {
    productsToCheck.push(response.productId)
  }
  if (productsToCheck && productsToCheck.length > 0) {
    for (const key in productsToCheck) {
      const value = productsToCheck[key];
      hasPurchase = isProductPurchased(cachedPurchases, value)
      if (hasPurchase) {
        break;
      }
    }

    if (!hasPurchase && url) {
      hasPurchase = UserSession.instance.isComicUnlocked(url)
    }

    if (hasPurchase) {
      let hasBuyFromInfluence = checkIfBuyFromInfluence(getState(), response.productId, url);
      batch(() => {
        if (hasBuyFromInfluence) {
          dispatch({
            type: T.PRODUCT_PURCHASED_FROM_INFLUENCE,
            payload: true
          })
        } else {
          dispatch({
            type: T.PRODUCT_ALREADY_PURCHASED,
            payload: true
          })
        }
      })
    } else {
      batch(() => {
        dispatch({
          type: T.PRODUCT_ALREADY_PURCHASED,
          payload: false
        })
        dispatch({
          type: T.PRODUCT_PURCHASED_FROM_INFLUENCE,
          payload: false
        })
      })
    }
  } else {
    let hasPurchase = response.productId != null && isProductPurchased(cachedPurchases, response.productId)

    batch(() => {
      if (Utils.checkPurchaseStatus(response.inAppProducts)) {
        dispatch({
          type: T.PRODUCT_ALREADY_PURCHASED,
          payload: true
        })
      } else {
        dispatch({
          type: T.PRODUCT_ALREADY_PURCHASED,
          payload: false
        })
      }
      if (hasPurchase) {
        dispatch({
          type: T.PRODUCT_PURCHASED_FROM_INFLUENCE,
          payload: true
        })
      } else {
        dispatch({
          type: T.PRODUCT_PURCHASED_FROM_INFLUENCE,
          payload: false
        })
      }
    })
  }
}

const onResponse = async (getState, url, dispatch, response = {}, pageStatusRes = {}, error = null, callback = null) => {
  let pageStatus = pageStatusRes
  if (Utils.isEmptyObject(pageStatus)) {
    const endPointURL = Utils.getMeaningFullURL(url);
    pageStatus = await getPageStatus(endPointURL)(dispatch, getState)
  }
  SessionManager.instance.setCurrectComicPageData({ storyID: pageStatus.storyID, pageURL: pageStatus.pageURL })

  batch(() => {
    dispatch({
      type: T.REFRESH_PAGE,
      pathUrl: getState().readComic.pathUrl,
      isRefreshed: true,
    })
    dispatch({
      type: T.RESET_STATE,
    })

    if (error) {
      dispatch({
        type: T.ERROR_IN_FETCH,
        urlToCheck: url,
        payload: error
      })
    } else {
      dispatch({
        type: T.ERROR_IN_FETCH,
        urlToCheck: null,
        payload: null
      })
    }
  })

  checkComicPurchaseStatus(response, url)(dispatch, getState)

  let newPanels = [];

  let isAnonymous = FirebaseManager.instance.isUserAnonymous()

  if (response.panels) {
    response.panels.forEach((element, index) => {
      let addPanel = false

      if (element.platforms) {
        for (const obj of element.platforms) {
          if (Platform.OS == 'ios' && obj.hasOwnProperty("iosApp")) {
            addPanel = true;
          } else if (Platform.OS == 'android' && obj.hasOwnProperty("androidApp")) {
            addPanel = true;
          }
        }
      }

      try {
        if (element.versions) {
          let currentVersion = parseFloat(settings.appVersion).toFixed(2)
          var isMinVersionSupported = true
          var isMaxVersionSupported = true
          for (const obj of element.versions) {
            if (obj.hasOwnProperty(Constants.MIN_VERSION) && Utils.checkData(obj[Constants.MIN_VERSION])) {
              const minVerValue = parseFloat(obj[Constants.MIN_VERSION]).toFixed(2)
              if (parseFloat(currentVersion) >= parseFloat(minVerValue)) {
                isMinVersionSupported = true
              } else {
                isMinVersionSupported = false
              }
            } else if (obj.hasOwnProperty(Constants.MAX_VERSION) && Utils.checkData(obj[Constants.MAX_VERSION])) {
              const maxVerValue = parseFloat(obj[Constants.MAX_VERSION]).toFixed(2)
              if (parseFloat(currentVersion) <= parseFloat(maxVerValue)) {
                isMaxVersionSupported = true
              } else {
                isMaxVersionSupported = false
              }
            }
          }

          addPanel = isMinVersionSupported && isMaxVersionSupported
        }
      } catch (error) {
        Utils.log("Error in element.versions parsing on action")
      }

      if (!element.platforms && !element.versions) {
        addPanel = true
      }

      if ((element.datetime && Utils.isFutureDate(element.datetime)) || (element.action == "signup" && !isAnonymous)) {
        addPanel = false
      }

      if (element.image) {
        element.image = decodeURIComponent(element.image)
      }

      if (element.lastComicPanel) {
        if (SessionManager.instance.getCurrectComicPageData() != null) {
          const payload = SessionManager.instance.getCurrectComicPageData()
          if (payload.storyID && payload.pageURL == Utils.getMeaningFullURL(url) && element.actionType != "website") {
            element.storyID = payload.storyID
          }
          const series = Utils.getChannelName(url)
          const seriesStyle = Utils.getSeriesSpecificStyle(series)
          element.bgColor = seriesStyle.backgroundColor
        }
      }

      //Adding the panel.
      if (addPanel) {
        newPanels.push(element)
      }
    });

    let sharePanel = null

    if (newPanels && newPanels.length > 0 && !response.hideShare) {
      if (response.sharePanel) {
        sharePanel = { "width": 1600, "height": 937, "image": Utils.resolvePath(url, response.sharePanel), "action": "staticPanel", "actionType": "staticPanel", "md5sum": "098765432111" }
        newPanels.push(sharePanel)
      } else {
        sharePanel = { "width": 1600, "height": 1200, "image": Utils.getPanelURL("share.jpg"), "action": "staticPanel", "actionType": "staticPanel", "md5sum": "098765432111" }
        newPanels.push(sharePanel)
      }
    }

    response.panels = newPanels;
    let inAppProducts = getInAppProducts(url, response)
    response.inAppProducts = inAppProducts
  }

  Utils.log("Performance Checking : On Response calling callback " + new Date().getTime())
  Utils.log('Comic has been read for url ' + url + " Time " + new Date().getTime());
  if (callback) {
    callback(response, pageStatus)
  }

  Utils.log("End Time " + new Date().getTime());
}

export const getUserDetails = (callback = null, userID = null) => async (dispatch, getState) => {
  try {
    SessionManager.instance.setIsGeneratingEmailLink(false)
    let data = null
    var currentUserID = FirebaseManager.instance.currentUser().uid
    if (userID == null) {
      data = { userID: currentUserID, friendShipInfo: 1 }
    } else {
      data = { userID: userID, friendShipInfo: 1 }
    }
    const userData = await FirebaseManager.instance.getUserDetails(data)
    Utils.log("getUserDetails" + JSON.stringify(userData))

    let userRes = {}
    if (userData) {
      const dobObj = Utils.checkData(userData.dob) ? moment(userData.dob).format("DD/MM/YYYY") : null
      userRes = { "displayName": userData.displayName, "gender": userData.gender, "dob": dobObj }
    }

    if (currentUserID === data.userID) {
      if (userData && userData.status && userData.status == "MERGED") {
        await store.dispatch(signOutUser())
        Alert.alert("", "You have been signed out because you signed in on another device.", [
          {
            text: "Close", onPress: () => {
              NavigationService.navigate('Home', { 'forceReload': true })
            }
          },
          {
            text: "Sign In Again", onPress: () => {
              Utils.navigateToDrawerLoginRoute(this.props, Constants.LOGIN_SCREEN, { isForLoginProcess: true })
            }
          }
        ])

        if (callback) {
          callback(userData.status)
        }
        return
      }

      UserSession.instance.updateUserDetails({ ...userData, ...userRes })
      if (userData.monthlyReadCounter) {
        UserSession.instance.updateReadedFreeComicsCount(userData.monthlyReadCounter.count)
      }
      if (userData.subscriptions && userData.subscriptions.length > 0) {
        var subscriptionIDs = []
        for (const key in userData.subscriptions) {
          const element = userData.subscriptions[key];
          subscriptionIDs.push(element.productID)
          if (Utils.isSubsProduct(element.productID)) {
            SessionManager.instance.updatePurchasedData(element)
          }
        }
        await SharedPreferences.storePurchasesProducts(subscriptionIDs);
      } else {
        SessionManager.instance.updatePurchasedData([])
        await SharedPreferences.storePurchasesProducts([]);
      }

      await SharedPreferences.setShowAllComicsValue(Utils.checkObject(userData.showAllComics) ? userData.showAllComics : true)
      SessionManager.instance.profileImageURL = userData.photoURL

      batch(() => {
        dispatch({
          type: T.UPDATE_USER_DETAILS,
          payload: userData
        })
        dispatch({
          type: T.UPDATE_ALERTS,
          payload: userData.alerts
        })
      })
    }

    if (callback) {
      callback(userData, userID)
    }

    return userData
  } catch (error) {
    Utils.log("getUserDetails failed " + error)
    SessionManager.instance.profileImageURL = null
    SessionManager.instance.showAllComics = true
    if (callback) {
      callback(null, null, error.message)
    }
  }
}

export const getUserFriendshipStatus = (requestedData, callback = null) => async (dispatch) => {
  Utils.log("Calling get friendship status")
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.getUserFriendshipStatus(requestedData)
      if (res && res.success) {
        callback(res.data)
      }
    } else {
      Utils.log("getUserFriendshipStatus" + Constants.INTERNET_ERROR)
      callback()
    }
  } catch (error) {
    Utils.log(error)
    callback()
  }
}

export const requestPurchase = (product, feedData = null, callback = null) => async (dispatch, getState) => {
  Utils.log('Going to Purchase : ' + JSON.stringify(product))
  const isSubsProd = product && Utils.isSubsProduct(product.productId)
  if (!isSubsProd) {
    dispatch({
      type: T.IS_PURCHASE_IN_PROGRESS,
      payload: true
    })
  }

  const purchasedSubs = SessionManager.instance.getPurchasedSubscription()

  let extraData = { product, feedData, callback }

  try {
    const result = await IAPManager.default.requestPurchase(product, purchasedSubs, extraData)
    Utils.log('Request Purchase Result : ' + result)
  } catch (error) {
    Utils.log('requestPurchase Error : ' + error.code + error)
    store.dispatch({
      type: T.IS_PURCHASE_IN_PROGRESS,
      payload: false
    })

    if (callback) {
      callback()
    }
    if (error.code == "E_ALREADY_OWNED" || error.code == "E_USER_CANCELLED") {
      return;
    }
    Utils.showError(error)
  }
}

export const restorePurchase = (checkCurrentPageIAPs = true) => (dispatch, getState) => {
  dispatch({
    type: T.IS_PURCHASE_IN_PROGRESS,
    payload: true
  })

  //const pathURL = getState().readComic.pathUrl;
  let iapProducts = []//getState().readComic.list[pathURL].inAppProducts;

  IAPManager.default.getAvailablePurchases()
    .then(result => {
      Utils.log('Available Purchases: ' + result)
      if (result && result.length > 0) {
        let foundPurchase = !checkCurrentPageIAPs;
        if (!foundPurchase) {
          result.forEach(element => {
            for (const productId of iapProducts) {
              if (element == productId) {
                foundPurchase = true;
                break;
              }
            }
          });
        }

        if (foundPurchase) {
          finishedPurchase()
        } else {
          store.dispatch({
            type: T.IS_PURCHASE_IN_PROGRESS,
            payload: false
          })
          Utils.showError('Purchase not found')
        }
      } else {
        store.dispatch({
          type: T.IS_PURCHASE_IN_PROGRESS,
          payload: false
        })
        Utils.showError('Purchase not found')
      }
    })
    .catch(error => {
      Utils.log('restorePurchase: ' + error)
      store.dispatch({
        type: T.IS_PURCHASE_IN_PROGRESS,
        payload: false
      })
      if (error.message) {
        Utils.showError(error)
      } else {
        Utils.showError('Can not connect to store')
      }
    })
}

const showImageSharePrompt = async (fileName, destPath, isCopied) => {
  let shareData = {};
  let base64Image = "";
  if (Platform.OS == 'ios' || !isCopied) {
    base64Image = await FileCache.default.getBase64Content(destPath);
    shareData.url = "data:image/jpg;base64," + base64Image;
  } else {
    shareData.url = destPath
  }

  if (Platform.OS == "ios") {
    shareData.activityItemSources = [{
      linkMetadata: {
        title: fileName,
      },
    }]
  }

  const result = await Share.open(shareData)
  Utils.log("result is " + JSON.stringify(result));
  if (result.app && result.app.toLowerCase().includes("SaveToCameraRoll".toLowerCase())) {
    Alert.alert("", "Image saved in Photos")
  }
}

export const shareImage = (item) => async (dispatch, getState) => {
  let destPath = null;
  let newPath = null;
  let fileName = "comic";
  try {
    if (item.image) {
      const comicpath = item.image.split("/");
      fileName = comicpath[comicpath.length - 1];
    }

    let isCopied = true;
    const pathURL = getState().readComic.pathUrl
    let imageLocalPath = item.imageLocalPath;

    if (!Utils.checkData(imageLocalPath) && Utils.checkData(item.image)) {
      let finalUrl = Utils.resolvePath(getState().readComic.pathUrl, item.image)
      imageLocalPath = "file://" + FileCache.default.getLocalPathForURL(finalUrl, item.md5sum)
    }

    destPath = imageLocalPath.replace("." + item.md5sum, "");

    if (imageLocalPath != destPath) {
      isCopied = await FileCache.default.copyFile(imageLocalPath, destPath);
    }

    let webURL = null;
    if (!item.action || item.actionType == "website" || item.actionType == "tinyview") {
      webURL = Utils.getWebChannelURL(pathURL);
    } else {
      const fullUrl = Utils.resolvePath(pathURL, item.action);
      webURL = Utils.getWebChannelURL(fullUrl);
    }

    newPath = await ImageMarker.markText({
      backgroundImage: {
        src: destPath,
        scale: 1,
      },
      watermarkTexts: [{
        text: webURL,
        positionOptions: {
          X: 30,
          Y: item.height - 35,
        },
        style: {
          color: '#FFF',
          fontName: Platform.OS == 'android' ? 'Roboto' : 'SFUIDisplay-Regular',
          fontSize: scale(20),
          textBackgroundStyle: {
            type: '',
            paddingX: 30,
            paddingY: 10,
            color: Color.RED_BACKGROUND_COLOR
          },
        },
      }],
      filename: fileName,
      quality: 100
    })

    await showImageSharePrompt(fileName, "file://" + newPath, isCopied);

  } catch (exc) {
    Utils.log("share Image Error " + exc);
    if (destPath) {
      if (!newPath) {
        await showImageSharePrompt(fileName, destPath, true);
      }
      FileCache.default.delete(destPath);
    }
    if (Platform.OS == 'ios' && exc.error && exc.error.domain && exc.error.domain.includes("ALAssetsLibraryErrorDomain")) {
      Alert.alert("", "You need to go to Settings and allow Photos permission.", [
        {
          text: "Cancel"
        },
        {
          text: "Settings", onPress: () => {
            Linking.openSettings()
          }
        }
      ])
    }
  }
}

export const clearLoginState = () => async (dispatch, getState) => {
  batch(() => {
    dispatch({
      type: T.CLEAR_LOGIN_SCREEN_ERROR
    })
    dispatch({
      type: T.CLEAR_OTP_SCREEN_ERROR,
    })
  })
}

export const showErrorAlert = (error) => async (dispatch, getState) => {
  let title = "Error"
  let message = "Something went wrong. Please try again later."
  if (error.code == "auth/invalid-phone-number") {
    message = ""
    dispatch({
      type: T.SHOW_LOGIN_SCREEN_ERROR, // We are dispatching here to show error on UI
      payload: "Please enter a valid phone number.",
    })
  } else if (error.code == "auth/invalid-email") {
    message = ""
    dispatch({
      type: T.SHOW_LOGIN_SCREEN_ERROR, // We are dispatching here to show error on UI
      payload: "Please enter a valid email address.",
    })
  } else if (error.code == 'auth/network-request-failed') {
    message = ""
    dispatch({
      type: T.SHOW_LOGIN_SCREEN_ERROR, // We are dispatching here to show error on UI
      payload: Constants.INTERNET_ERROR
    })
  } else if (error.code == "auth/captcha-check-failed") {
    message = "Captcha verification failed. Please try again."
  } else if (error.code == "auth/popup-closed-by-user") {
    message = "Sign in process is not completed"
  } else if (error.code == "auth/too-many-requests") {
    message = "Too many request from the same number. Please try again later"
  } else if (settings.isDevBuild && error.code == "auth/quota-exceeded") { // Must use for development purposes
    message = "SMS quota for the Firebase project has been exceeded."
  } else if (error.code == "auth/invalid-verification-code" || error.code == "auth/code-expired" || error.code == "auth/invalid-credential") {
    message = "";
    dispatch({
      type: T.SHOW_OTP_SCREEN_ERROR, // We are dispatching here to show error on UI
      payload: "Invalid verification code. ",
    })
  } else if (error.code == "auth/session-expired") {
    message = "The sms code has expired. Please re-send the verification code to try again.";
  } else if (error.code == "auth/provider-already-linked" || (Platform.OS == 'android' && error.code == "auth/unknown")) {
    message = "User already linked with same credentials";
  } else if (error.code == "invalid-argument") {
    message = error.message;
  } else if (error.code == "unknow") {
    message = ''
    dispatch({
      type: T.SHOW_OTP_SCREEN_ERROR,
      payload: Constants.INTERNET_ERROR
    })
  } else if (error.message) {
    message = error.message;
  }

  if (message.length > 0) {
    Alert.alert(title, message);
  }

}

export const getUserProfileByIdentity = (requestedData, callback = null) => async (dispatch, getState) => {
  try {
    store.dispatch(clearLoginState())
    if (!NetworkUtils.instance.isAvailable()) {
      batch(() => {
        dispatch({
          type: T.SHOW_LOGIN_SCREEN_ERROR,
          payload: Constants.INTERNET_ERROR,
        })
      })
      callback(false, null, true)
      return
    }

    const alreadyAUser = await FirebaseManager.instance.getUserProfile(requestedData)
    if (alreadyAUser && alreadyAUser.length > 0 && callback) {
      callback(true, alreadyAUser[0])
    } else {
      callback(false, null)
    }
  }
  catch (error) {
    showErrorAlert(error)(dispatch, getState)
    callback(false, null, true)
  }
}

export const signInWithMobile = (mobileNo, callback = null) => async (dispatch, getState) => {
  try {

    store.dispatch(clearLoginState())

    if (!NetworkUtils.instance.isAvailable()) {
      batch(() => {
        dispatch({
          type: T.SHOW_LOGIN_SCREEN_ERROR,
          payload: Constants.INTERNET_ERROR,
        })
      })
      if (callback) {
        callback(null, true)
      }
      return
    }

    const signInData = await FirebaseManager.instance.signInWithMobileNumber(mobileNo)
    if (signInData) {
      if (callback) {
        callback(signInData)
      }
    }
  }
  catch (error) {
    if (callback) {
      callback(null, true)
    }
    showErrorAlert(error)(dispatch, getState)
  }
}

export const signinWithSocialLink = (linkType, callback = null) => async (dispatch, getState) => {
  try {
    store.dispatch(clearLoginState())

    if (!NetworkUtils.instance.isAvailable()) {
      batch(() => {
        dispatch({
          type: T.SHOW_LOGIN_SCREEN_ERROR,
          payload: Constants.INTERNET_ERROR,
        })
      })
      return
    }

    var signedUser = null
    if (linkType == "google") {
      signedUser = await FirebaseManager.instance.signInWithGoogle()
    } else if (linkType == "apple") {
      signedUser = await FirebaseManager.instance.signInWithApple()
    }

    if (signedUser) {
      FirebaseManager.instance.onAuthSuccess()
      if (callback) {
        callback(signedUser)
      }
    } else {
      if (callback) {
        callback(null)
      }
    }
  }
  catch (error) {
    if (callback) {
      callback(null)
    }
    showErrorAlert(error)(dispatch, getState)
  }
}

export const resendOTP = (whichScreen, phoneNumber, callback = null) => async (dispatch, getState) => {

  let type = whichScreen == 'loginScreen' ? T.SHOW_LOGIN_SCREEN_ERROR : T.SHOW_OTP_SCREEN_ERROR
  try {
    dispatch({
      type: T.SHOW_LOGIN_INDICATOR
    })

    store.dispatch(clearLoginState())

    if (!NetworkUtils.instance.isAvailable()) {
      batch(() => {
        dispatch({
          type: type,
          payload: Constants.INTERNET_ERROR
        })
        dispatch({
          type: T.HIDE_LOGIN_INDICATOR
        })
      })
      return
    }

    const signInData = await FirebaseManager.instance.resendOTP(phoneNumber)
    if (signInData) {
      if (callback) {
        callback(signInData)
      }
    }
    dispatch({
      type: T.HIDE_LOGIN_INDICATOR
    })
  }
  catch (error) {
    dispatch({
      type: T.HIDE_LOGIN_INDICATOR
    })
    showErrorAlert(error)(dispatch, getState)
  }
}

export const verifyOTPAndLinkUser = (phoneNumber, verificationId, otp, callback = null) => async (dispatch, getState) => {
  try {
    dispatch({
      type: T.SHOW_LOGIN_INDICATOR
    })

    store.dispatch(clearLoginState())

    if (!NetworkUtils.instance.isAvailable()) {
      batch(() => {
        dispatch({
          type: T.SHOW_OTP_SCREEN_ERROR,
          payload: Constants.INTERNET_ERROR,
        })
        dispatch({
          type: T.HIDE_LOGIN_INDICATOR
        })
      })
      return
    }

    const verifiedUser = await FirebaseManager.instance.verifyOTPAndLinkUser(phoneNumber, verificationId, otp)
    Utils.log("verifiedUser " + verifiedUser)
    if (verifiedUser) {
      await store.dispatch(getUserDetails())
      FirebaseManager.instance.onAuthSuccess()
      if (callback) {
        callback()
      }
    }
    dispatch({
      type: T.HIDE_LOGIN_INDICATOR
    })
  }
  catch (error) {
    dispatch({
      type: T.HIDE_LOGIN_INDICATOR
    })
    showErrorAlert(error)(dispatch, getState)
  }
}

export const onUserAutoVerified = (callback = null) => async (dispatch, getState) => {
  const verifiedUser = FirebaseManager.instance.currentUser()
  Utils.log("verifiedUser " + verifiedUser)

  if (verifiedUser) {
    if (callback) {
      callback()
    }
    dispatch({
      type: T.SAVE_USER_ANONYMOUS_STATE,
      payload: FirebaseManager.instance.isUserAnonymous()
    })
  }
}

export const validateLastSubscription = (callback) => async (dispatch, getState) => {
  try {
    Utils.log("validateLastSubscription process starts")

    if (!NetworkUtils.instance.isAvailable()) {
      let title = "No Internet Available"
      let message = Constants.INTERNET_ERROR
      Alert.alert(title, message);
      return
    }

    const txnData = SessionManager.instance.getLastPurchasedData()
    let isValidTxnSubs = false
    if (txnData.store && txnData.store.toLowerCase() == Constants.PAYMENT_MODE_STRIPE && txnData.autoRenew) {
      callback(true, txnData.store)
      return
    }

    dispatch({
      type: T.SHOW_LOGIN_INDICATOR
    })

    if (txnData != null) {
      const validPurchases = await IAPManager.default.validateTransaction(txnData)
      isValidTxnSubs = validPurchases && validPurchases.length > 0
    } else {
      isValidTxnSubs = await IAPManager.default.IsSubscriptionPurchaseValid()
    }

    callback(isValidTxnSubs)

    dispatch({
      type: T.HIDE_LOGIN_INDICATOR
    })
  } catch (error) {
    dispatch({
      type: T.HIDE_LOGIN_INDICATOR
    })
  }
}

export const deleteAccount = (callback = null) => async (dispatch, getState) => {
  try {
    Utils.log("Delete user account process start")

    dispatch({
      type: T.SHOW_LOGIN_INDICATOR
    })

    if (!NetworkUtils.instance.isAvailable()) {
      let title = "No Internet Available"
      let message = Constants.INTERNET_ERROR
      Alert.alert(title, message);
      dispatch({
        type: T.HIDE_LOGIN_INDICATOR
      })
      if (callback) {
        callback()
      }
      return
    }

    await FirebaseManager.instance.deleteUserAccount();

    let keyToSave = [settings.INITIAL_INTRO_LOAD_KEY, settings.SHOW_NOTIFICATION_PERMISSION_ALERT_KEY]
    await SharedPreferences.clearSharedPrefrences(keyToSave);
    await FileCache.default.delete()

    batch(() => {
      dispatch({ type: T.SIGN_OUT_USER })
      dispatch({ type: T.HIDE_LOGIN_INDICATOR })
    })

    if (callback) {
      callback(true)
    }

  } catch (error) {
    Utils.log("deleteAccount failed" + error)
    if (callback) {
      callback()
    }
    dispatch({
      type: T.HIDE_LOGIN_INDICATOR
    })
  }
}

export const signOutUser = (pathUrl, callback = null, shouldCreateAnony = true) => async (dispatch, getState) => {
  try {
    Utils.log("Sign out process start")

    dispatch({
      type: T.SHOW_LOGIN_INDICATOR
    })

    if (!NetworkUtils.instance.isAvailable()) {
      let title = "No Internet Available"
      let message = Constants.INTERNET_ERROR
      Alert.alert(title, message);
      dispatch({
        type: T.HIDE_LOGIN_INDICATOR
      })
      if (callback) {
        callback()
      }
      return
    }

    await FirebaseManager.instance.signOutUser();
    if (shouldCreateAnony) {
      var data = await FirebaseManager.instance.signInAnonymously()
    }

    let keyToSave = [settings.INITIAL_INTRO_LOAD_KEY, settings.SHOW_NOTIFICATION_PERMISSION_ALERT_KEY]
    await SharedPreferences.clearSharedPrefrences(keyToSave);
    await SharedPreferences.storePurchasesProducts([]);
    await FileCache.default.delete()

    SessionManager.instance.profileImageURL = null
    SharedPreferences.setShowAllComicsValue(true)

    UserSession.instance.updateUserDetails(null)
    UserSession.instance.updateUnlockedComics(null)
    SessionManager.instance.shownAlertMessage = true

    if (shouldCreateAnony) {
      Utils.log("Calling purchased comic list")
      store.dispatch(getUnlockComicURLs())
      store.dispatch(getUserNotification({ countOnly: 1 }))
    } else {
      UserSession.instance.updateUnlockedComics([])
      UserSession.instance.updateUnreadNotifications(0)
    }

    batch(() => {
      if (shouldCreateAnony) {
        if (data) {
          dispatch({
            type: T.SAVE_USER_ANONYMOUS_STATE,
            payload: FirebaseManager.instance.isUserAnonymous()
          })
        }
      }
      dispatch({
        type: T.SIGN_OUT_USER,
      })
    })

    FirebaseManager.instance.hasNotificationPermission()

    dispatch({
      type: T.HIDE_LOGIN_INDICATOR
    })

    if (callback) {
      callback(true)
    }

  } catch (error) {
    Utils.log("signOutUser failed" + error)
    if (callback) {
      callback(false)
    }
    dispatch({
      type: T.HIDE_LOGIN_INDICATOR
    })
  }
}

export const flagUserComment = (data, callback = null) => async (dispatch, getState) => {
  try {
    await FirebaseManager.instance.flagUserComment(data.data);

    if (callback) {
      callback(true)
    }

  } catch (error) {
    Utils.log("flagUserComment failed" + error)
    Utils.showError(error)
    if (callback) {
      callback(false)
    }
  }
}

export const blockUserAction = (data, callback = null) => async () => {
  try {
    var result = await FirebaseManager.instance.callBlockUserAPI(data.data);

    if (result) {
      callback(result.message ? result.message : "Thank you for reporting this user")
    }

  } catch (error) {
    Utils.log("Block User API failed" + error)
    Utils.showError(error)
    if (callback) {
      callback(null)
    }
  }
}

export const getSubscribeProgressAction = (data, callback = null) => async () => {
  try {
    var result = await FirebaseManager.instance.getSubscribersProgreesAPI(data);

    if (result) {
      const totalSubsCount = result.newSubscription + result.subscriptionRenewCount
      SessionManager.instance.setSubsProgressData({ subsTotalAmount: result.totalAmount, subscriberCount: totalSubsCount })
    }

    if (callback) {
      callback(result)
    }

  } catch (error) {
    Utils.log("Get Subscribers Progress API failed" + error)
    if (callback) {
      callback(null)
    }
  }
}

export const reloadComic = (pathUrl) => async (dispatch, getState) => {
  try {
    store.dispatch(readComicAction(pathUrl, true));
    store.dispatch(getUserDetails())
  } catch (error) {
    Utils.log("reloadComic Error", error)
  }
}

export const mergeUserData = (anonymousUserIdToken, phoneNumber) => async (dispatch, getState) => {
  try {
    dispatch({
      type: T.SHOW_LOGIN_INDICATOR
    })

    if (!NetworkUtils.instance.isAvailable()) {
      let title = "No Internet Available"
      let message = Constants.INTERNET_ERROR
      Alert.alert(title, message);
      dispatch({
        type: T.HIDE_LOGIN_INDICATOR
      })
      return
    }

    const phoneData = { phoneNumber: phoneNumber }
    await FirebaseManager.instance.updateUserProfile(phoneData)
    await FirebaseManager.instance.mergeUserData(anonymousUserIdToken)

    dispatch({
      type: T.HIDE_LOGIN_INDICATOR
    })
  } catch (error) {
    dispatch({
      type: T.HIDE_LOGIN_INDICATOR
    })
    showErrorAlert(error)(dispatch, getState)
  }
}

export const shareAppIntroLink = () => (dispatch, getState) => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const currentUser = FirebaseManager.instance.currentUser()
      const pathURL = settings.getWhatsTinyviewURL();

      Ajax.readComic(pathURL).then(res => {
        const description = res.comics.description
        const ogImagePath = res.comics.ogImage
        const title = res.comics.title

        let data = { title, description, ogImagePath, pathURL, currentUser };

        shareData(data)

      }).catch((error) => {
        store.dispatch(shareApp());
      })
    }
    else {
      Utils.showError(Constants.INTERNET_ERROR)
    }
  } catch (error) {
    Utils.showError(error)
  }
}

export const shareData = async (data, callback = null) => {
  try {
    if (data.description) {
      data.description = Utils.excludeHTML(data.description);
    }

    // const { url, error } = await DeepLinkManager.instance.createLink(data)
    // Utils.log('branch url is ' + url);


    let url = null
    if (data.pathURL && (!data.actionType || data.actionType != "website")) {
      url = await DeepLinkManager.instance.getShareBranchURL(data)
    } else {
      url = data.pathURL
    }

    if (url) {
      FirebaseManager.instance.logEvent('TINYVIEW_SHARE_INITIATE', { 'sharedURL': url });
      let shareData = {};
      if (Platform.OS == 'ios') {
        if (data.actionType == settings.SET_REFERRAL) {
          shareData.title = "";
          shareData.message = data.description;
        } else {
          shareData.message = data.messageText
        }
        shareData.url = url; //Android doesn't support url param
      } else {
        shareData.title = data.title
        shareData.message = data.actionType == settings.SET_REFERRAL ? data.description + " " + url : data.messageText + " " + url
      }

      shareData.subject = data.title;

      await Share.open(shareData)
      if (callback) {
        callback()
      }
    }
  } catch (error) {
    if (callback) {
      callback()
    }
  }
}

export const shareInviteLink = async (shareData = null, callback = null) => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      await Share.open(shareData)
      if (callback) {
        callback()
      }
    }
    else {
      let error = Constants.INTERNET_ERROR
      Utils.showError(error)
    }
  }
  catch (error) {
    if (callback) {
      callback()
    }
  }
}

export const shareApp = (panelItems = null, actionType = null, callback = null) => (dispatch, getState) => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const currentUser = FirebaseManager.instance.currentUser()
      if (panelItems) {
        let { action, title, description, ogImagePath, storyID, isSharedByPremiumUser = false, messageText } = panelItems
        if (!ogImagePath) {
          ogImagePath = panelItems.image
        }

        if (actionType == null) {
          actionType = panelItems.actionType
        }

        if (!action) {
          action = getState().readComic.pathUrl
        }

        let pathURL = Utils.resolvePath(settings.webBaseURL, action)
        let data = { title, description, ogImagePath, pathURL, currentUser, actionType, storyID, isSharedByPremiumUser, messageText };
        return shareData(data, callback)
      }
    }
    else {
      let error = Constants.INTERNET_ERROR
      Utils.showError(error)
    }
  } catch (error) {
    Utils.showError(error)
  }
}

export const updateUserProfile = (data, callback = null, callGetUserDetails = false) => async (dispatch, getState) => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      let res = await FirebaseManager.instance.updateUserProfile(data)
      if (res) {
        if (callGetUserDetails) {
          getUserDetails(callback)(dispatch, getState)
        } else if (callback) {
          callback(data)
        }
      }
    } else {
      Utils.showError(Constants.INTERNET_ERROR)
      callback()
    }
  } catch (error) {
    Alert.alert(error.details.errorMsg, Constants.AGE_VALIDATION_ERROR)
    callback()
  }
}

export const updateProfilePicURL = (data, callback) => async () => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      let res = await FirebaseManager.instance.updateProfilePicURL(data)
      if (res && callback) {
        callback()
      }
      Utils.log("updateProfilePicURL", res)
    } else {
      Utils.showError(Constants.INTERNET_ERROR)
    }
  } catch (error) {
    Utils.log('updateProfilePicURL: actionError ', error)
  }
}

export const sendTextToContacts = (data, callback = null) => async () => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const sendTextResponse = await FirebaseManager.instance.sendTextToContacts(data)
      if (sendTextResponse && callback) {
        callback(true)
      }
    }
    else {
      Utils.showError(Constants.INTERNET_ERROR)
      if (callback) {
        callback()
      }
    }
  } catch (error) {
    Utils.showError(error)
    if (callback) {
      callback()
    }
  }
}

export const sendFriendRequest = (sendFriendRequestData, sendTextToContactsData = null, callback = null) => async (dispatch) => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      dispatch({
        type: T.SHOW_LOADING
      })

      try {
        if (Utils.checkObject(sendTextToContactsData)) {
          const sendTextResponse = await FirebaseManager.instance.sendTextToContacts(sendTextToContactsData)
          if (sendTextResponse && sendTextResponse.success) {
            Utils.log("sendTextToContacts API called successfully: ", JSON.stringify(sendTextToContactsData))
          }
        }
      } catch (error) {
        Utils.log("sendTextToContacts API failed: ", error)
      }

      const sendRequestResponse = await FirebaseManager.instance.sendFriendRequest(sendFriendRequestData)
      if (callback) {
        if (sendRequestResponse) {
          callback(true, Constants.SEND_FRIEND_REQUEST)
        } else {
          callback()
        }
      }

      dispatch({
        type: T.HIDE_LOADING
      })
    }
    else {
      Utils.showError(Constants.INTERNET_ERROR)
      if (callback) {
        callback()
      }
    }

  } catch (error) {
    Utils.showError(error)
    if (callback) {
      callback()
    }
    dispatch({
      type: T.HIDE_LOADING
    })
  }
}

export const resendFriendRequest = (data) => async () => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      let shareData = {};
      if (Platform.OS == 'ios') {
        shareData.title = data.title
        shareData.message = data.message;
        shareData.url = data.url; //Android doesn't support url param
      } else {
        shareData.title = data.title
        shareData.message = data.message + " " + data.url
      }

      shareData.subject = data.title;
      Share.open(shareData)

    } else {
      Utils.showError(Constants.INTERNET_ERROR)
    }
  }
  catch (error) {
    Utils.showError(error)
  }
}

export const unfriend = (data, callback = null) => async (dispatch, getState) => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      dispatch({
        type: T.SHOW_LOADING
      })
      const res = await FirebaseManager.instance.unfriend(data)
      if (res && callback) {
        callback(true, "unfriend")
      }

      dispatch(getUserDetails())
      dispatch({
        type: T.HIDE_LOADING
      })
    } else {
      Utils.showError(Constants.INTERNET_ERROR)
      if (callback) {
        callback()
      }
    }
  } catch (error) {
    Utils.showError(error)
    if (callback) {
      callback()
    }
    dispatch({
      type: T.HIDE_LOADING
    })
  }
}

export const updateFriendRequest = (data, callback = null) => async (dispatch, getState) => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      dispatch({
        type: T.SHOW_LOADING
      })
      const res = await FirebaseManager.instance.updateFriendRequest(data)
      if (res && callback) {
        callback(true, data.status)
        setTimeout(() => {
          if (data.status == "accepted") {
            batch(() => {
              dispatch(getUserDetails())
              dispatch(getFriendList(null, true)) // calling this to update latest friend list in file cache
            })
          }
        }, 2000);
      }
      dispatch({
        type: T.HIDE_LOADING
      })
    }
    else {
      Utils.showError(Constants.INTERNET_ERROR)
      if (callback) {
        callback()
      }
    }
  } catch (error) {
    Utils.showError(error)
    if (callback) {
      callback()
    }
    dispatch({
      type: T.HIDE_LOADING
    })
  }
}

export const getFriendRequest = (data, callback = null) => async () => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.getFriendRequest(data)
      if (res && callback) {
        const requests = []
        res.sort((a, b) => {
          return b.updatedTime - a.updatedTime
        })
        res.forEach(element => {
          requests.push(new FriendRequestModal(element, data.requestType))
        });

        callback(requests)
      }
    }
    else {
      Utils.showError(Constants.INTERNET_ERROR)
      if (callback) {
        callback([])
      }
    }

  } catch (error) {
    Utils.showError(error)
    if (callback) {
      callback([])
    }
  }
}

export const getFriendList = (callback = null, forceLoad = false, data = null) => async () => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      if (data) {
        var res = await FirebaseManager.instance.getFriendList(data)
      } else {
        let dataInJSON = await FileCache.default.readFile(Constants.ALL_FRIENDS)
        var res = Utils.getParsedData(dataInJSON)
        if (!res || res.length == 0 || forceLoad) {
          res = await FirebaseManager.instance.getFriendList()
          FileCache.default.writeFile(Constants.ALL_FRIENDS, JSON.stringify(res))
        }
      }

      if (callback) {
        const friends = []
        if (res) {
          res.forEach(element => {
            element.status = Constants.ACCEPTED
            friends.push(new FriendsModal(element))
          });
        }

        callback(friends, "allContacts")  //need to pass "friends" in future release
      }
    }
    else {
      Utils.showError(Constants.INTERNET_ERROR)
      if (callback) {
        callback([])
      }
    }
  } catch (error) {
    Utils.showError(error)
    if (callback) {
      callback([])
    }
  }
}

//fetching list of gift items
export const fetchAppConfigList = (callback = null) => async () => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.fetchAppConfigList()
      if (res) {
        const productIDs = res.giftItems.map((item) => {
          if (item.productId) {
            return item.productId
          } else {
            return Constants.Test_PRODUCT_ID
          }
        })
        const products = await IAPManager.default.getProducts(productIDs) // updating product price as per country
        if (products) {
          products.forEach(element => {
            for (const iterator of res.giftItems) {
              if (element.productId == iterator.productId) {
                iterator.localizedPrice = element.localizedPrice
                iterator.productId = element.productId
                break
              }
            }
          });
        }

        SessionManager.instance.updateGiftItemList(res.giftItems)
        SessionManager.instance.updateComicsConfigList(res.comicConfig)
        SessionManager.instance.setInfluencePointsValuesForActions(res.influencePoint)

        if (callback) {
          callback(res)
        }
      }
    }
    else {
      Utils.log('fetchAppConfigList: ' + Constants.INTERNET_ERROR)
      if (callback) {
        callback()
      }
    }
  } catch (error) {
    Utils.log(error)
    if (callback) {
      callback()
    }
  }
}

export const senitizeFeeds = (feeds, series = "tinyview", req) => {
  let newFeeds = []
  let isAnonymous = FirebaseManager.instance.isUserAnonymous()
  feeds.forEach((element, index) => {
    let addFeed = false
    if (element.platforms) {
      for (const obj of element.platforms) {
        if (Platform.OS == 'ios' && obj.hasOwnProperty("iosApp")) {
          addFeed = true;
        } else if (Platform.OS == 'android' && obj.hasOwnProperty("androidApp")) {
          addFeed = true;
        }
      }
    } else {
      addFeed = true
    }

    if ((element.datetime && Utils.isFutureDate(element.datetime)) || (element.action == "signup" && !isAnonymous)) {
      addFeed = false
    }

    const seriesStyle = Utils.getSeriesSpecificStyle(series)
    element.bgColor = seriesStyle.backgroundColor

    if (addFeed && newFeeds.length == 0 && !req.startAfter) {
      element.isFirstFeedPanel = true
    }

    if (addFeed) {
      if (element.image && series != "tinyview") { // PhotoView is causing issues on home page. So assigning only to series page
        let pathUrl = null
        if (store.getState() && store.getState().readComic && store.getState().readComic.pathUrl) {
          pathUrl = store.getState().readComic.pathUrl
        } else {
          pathUrl = Utils.getComicSeriesURLFromChannel(series)
        }

        element.image = Utils.resolvePath(pathUrl, element.image)
      }

      newFeeds.push(element)
    }
  });

  return newFeeds
}

// Getting all Latest Comics

export const getAllComics = (data, callback = null) => async (dispatch, getState) => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.getAllComics(data.data)
      if (res && callback) {
        const feeds = senitizeFeeds(res.data, "tinyview", data.data)
        callback(feeds, res.loadMore)
      }
    }
    else {
      let errorObj = { status: 500, title: "No Internet Available", message: Constants.INTERNET_ERROR }
      if (callback) {
        callback(errorObj)
      }
    }

  } catch (error) {
    Utils.log(error)
    if (callback) {
      callback([])
    }
  }
}

//Feed Action Method

export const getUserFeeds = (data, callback = null) => async (dispatch, getState) => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.getUserFeeds(data.data)
      if (res && callback) {
        const feeds = senitizeFeeds(res.data, "tinyview", data.data)
        callback(feeds, res.loadMore)
      }
    }
    else {
      let errorObj = { status: 500, title: "No Internet Available", message: Constants.INTERNET_ERROR }
      if (callback) {
        callback(errorObj)
      }
    }

  } catch (error) {
    Utils.showError(error)
    if (callback) {
      callback([])
    }
  }
}

export const getSeriesComics = (data, callback = null) => async (dispatch, getState) => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      if (!data.data.startAfter) {
        batch(() => {
          dispatch({
            type: T.FETCH_TOC
          })
        })
      }

      const res = await FirebaseManager.instance.getSeriesComics(data.data)
      if (res && callback) {
        const feeds = senitizeFeeds(res.data, data.data.series, data.data)
        callback(feeds, res.loadMore)
      }
    }
    else {
      Utils.showError(Constants.INTERNET_ERROR)
      if (callback) {
        callback([])
      }
    }

  } catch (error) {
    Utils.log(error)
    if (callback) {
      callback([])
    }

    batch(() => {
      dispatch({
        type: T.CANCEL_FETCH_TOC
      })
    })
  }
}

export const getFeedEpisodes = (data, callback) => async () => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.getFeedEpisodes(data)
      if (res && callback) {
        callback(res.data)
      }
    } else {
      Utils.showError(Constants.INTERNET_ERROR)
      if (callback) {
        callback()
      }
    }
  } catch (error) {
    Utils.log(error)
    if (callback) {
      callback()
    }
  }
}

export const getStoryDetails = (data, callback = null) => async (dispatch, getState) => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.getStoryDetails(data.data)
      if (res && callback) {
        callback(res, data)
      }
    }
    else {
      Utils.showError(Constants.INTERNET_ERROR)
      if (callback) {
        callback([])
      }
    }
  } catch (error) {
    Utils.log(error)
    if (callback) {
      callback(error)
    }
  }
}

export const updateAlertSubscription = (subscribe, seriesURL = null, showFollowMsg = true) => (dispatch, getState) => {
  Utils.log("updateAlertSubscription")
  const pathUrl = seriesURL ? seriesURL : getState().readComic.pathUrl
  const seriesPath = Utils.getComicSeriesURL(pathUrl);
  const alerts = (getState().userInfo && getState().userInfo.alerts != null) ? getState().userInfo.alerts : []
  const channelName = Utils.getChannelName(pathUrl)
  const newAlerts = JSON.parse(JSON.stringify(alerts))

  if (subscribe) {
    newAlerts[channelName] = true
  } else {
    delete newAlerts[channelName];
  }

  const multiplePanels = getState().comicPageStatus.multiplePanelStatus
  if (subscribe) {
    if (multiplePanels[seriesPath] != null) {
      multiplePanels[seriesPath].likeCount++
    }

    batch(() => {
      dispatch({
        type: T.ADD_LIKE,
        payload: seriesPath,
      })

      if (Utils.isTVSeriesURL(seriesPath)) {
        dispatch({
          type: T.ADD_LIKE,
          payload: settings.homePath,
        })
      }

      dispatch({
        type: T.UPDATE_ALERTS,
        payload: newAlerts
      })
    })

    if (showFollowMsg) {
      const seriesName = SessionManager.instance.getSeriesName(channelName)
      Utils.showToast(`Thanks for following ${seriesName}!`, "bottom", "seriesFollowed", 5000)
    }
  } else {
    if (multiplePanels[seriesPath] != null) {
      multiplePanels[seriesPath].likeCount--
    }

    batch(() => {
      dispatch({
        type: T.REMOVE_LIKE,
        payload: seriesPath,
      })

      if (Utils.isTVSeriesURL(seriesPath)) {
        dispatch({
          type: T.REMOVE_LIKE,
          payload: settings.homePath,
        })
      }

      dispatch({
        type: T.UPDATE_ALERTS,
        payload: newAlerts
      })
    })
  }

  FirebaseManager.instance.updateAlertSubscription(pathUrl, alerts, subscribe)
    .then(result => {
      dispatch({
        type: T.RESET_STATE,
      })
    })
    .catch(error => {
      Utils.log('UpdateAlertSubscription: ' + error)
      var alert = Utils.getChannelName(subscribe)
      if (subscribe) {
        if (alerts && alerts[alert]) {
          delete alerts[alert];
        }
      } else {
        alerts[alert] = true;
      }

      dispatch({
        type: T.UPDATE_ALERTS,
        payload: alerts
      })
    })
}

export const likeDislikeFeed = (data, isLike, callback = null) => async (dispatch, getState) => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      let res = null
      let actionURL = data.data.action ? data.data.action : data.data.pageURL
      if (!isLike) {
        if (data.data.refType == "STORY") {
          dispatch({
            type: T.ADD_LIKE,
            payload: actionURL,
            storyID: data.data.storyID,
          })
        } else {
          dispatch({
            type: T.ADD_LIKE,
            storyID: data.data.storyID
          })
        }

        res = await FirebaseManager.instance.likeFeed(data.data)
      } else {
        if (data.data.refType == "STORY") {
          dispatch({
            type: T.REMOVE_LIKE,
            payload: actionURL,
            storyID: data.data.storyID
          })

        } else {
          dispatch({
            type: T.REMOVE_LIKE,
            storyID: data.data.storyID
          })
        }
        res = await FirebaseManager.instance.disLikeFeed(data.data)
      }

      dispatch({
        type: T.RESET_STATE,
      })

      if (res && callback) {
        callback(data)
      }
    }
    else {
      Utils.showError(Constants.INTERNET_ERROR)
      if (callback) {
        callback()
      }
    }

  } catch (error) {
    Utils.log(error)
    if (callback) {
      callback()
    }
  }
}

export const fetchFeedLikedUserList = (requestedData, callback = null) => async () => {  // This API returns all user's list who liked the story.
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.fetchFeedLikes(requestedData.data)
      if (res && callback) {
        callback(res)
      }
    }
    else {
      Utils.showError(Constants.INTERNET_ERROR)
      if (callback) {
        callback()
      }
    }
  } catch (error) {
    Utils.log(error)
    if (callback) {
      callback()
    }
  }
}

export const fetchCommentLikedUserList = (requestedData, callback = null) => async () => {  // This API returns all user's list who liked a comment.
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.fetchCommentLikes(requestedData.data)
      if (res && callback) {
        callback(res)
      }
    }
    else {
      Utils.showError(Constants.INTERNET_ERROR)
      if (callback) {
        callback()
      }
    }
  } catch (error) {
    Utils.log(error)
    if (callback) {
      callback()
    }
  }
}

export const fetchRepostedUserList = (requestedData, callback = null) => async () => {  // This API returns all user's list who liked a comment.
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.fetchRepostedUsersList(requestedData.data)
      if (res && callback) {
        callback(res)
      }
    }
    else {
      Utils.showError(Constants.INTERNET_ERROR)
      if (callback) {
        callback()
      }
    }
  } catch (error) {
    Utils.showError(error)
    if (callback) {
      callback()
    }
  }
}

export const fetchWithWhomStorySharedUL = (requestedData, callback = null) => async () => {  // This API returns all user's list with whom story shared.
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.fetchWithWhomStorySharedUL(requestedData.data)
      if (res && callback) {
        callback(res)
      }
    }
    else {
      Utils.showError(Constants.INTERNET_ERROR)
      if (callback) {
        callback()
      }
    }
  } catch (error) {
    Utils.showError(error)
    if (callback) {
      callback()
    }
  }
}

export const getFeedLikes = (requestedData, callback = null) => async () => { // This Method returns, is user liked the story or not.
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.getFeedLikes(requestedData.data)
      if (res && callback) {
        callback(requestedData, res)
      }
    }
    else {
      Utils.showError(Constants.INTERNET_ERROR)
      if (callback) {
        callback(requestedData)
      }
    }
  } catch (error) {
    Utils.log(error)
    if (callback) {
      callback(requestedData)
    }
  }
}

export const getFeedComments = (requestedData, callback = null) => async () => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.getFeedComments(requestedData.data)
      if (res && callback) {
        callback(requestedData, res)
      }
    }
    else {
      Utils.showError(Constants.INTERNET_ERROR)
      if (callback) {
        callback()
      }
    }
  } catch (error) {
    Utils.log(error)
    if (callback) {
      callback()
    }
  }
}

export const postCommentOnFeed = (data, callback = null) => async (dispatch) => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {

      dispatch({
        type: T.ADD_COMMENT,
        storyID: data.data.storyID
      })

      const res = await FirebaseManager.instance.addComment(data.data)
      if (res && callback) {
        callback(data, res)
      }

      dispatch({
        type: T.RESET_STATE,
      })
    }
    else {
      Utils.showError(Constants.INTERNET_ERROR)
      if (callback) {
        callback()
      }
    }
  } catch (error) {
    Utils.showError(error)
    if (callback) {
      callback()
    }
  }
}

export const editComment = (data, callback = null) => async () => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.editFeedComment(data.data)
      if (res && callback) {
        callback(res)
      }
    }
    else {
      Utils.showError(Constants.INTERNET_ERROR)
      if (callback) {
        callback()
      }
    }
  } catch (error) {
    Utils.showError(error)
    if (callback) {
      callback()
    }
  }
}

export const deleteComment = (data, callback = null) => async (dispatch) => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {

      dispatch({
        type: T.REMOVE_COMMENT,
        storyID: data.data.storyID
      })

      Utils.showToast("Comment deleted")
      const res = await FirebaseManager.instance.deleteFeedComment(data.data)
      if (res && callback) {
        callback(res)
      }

      dispatch({
        type: T.RESET_STATE,
      })
    }
    else {
      Utils.showError(Constants.INTERNET_ERROR)
      if (callback) {
        callback()
      }
    }
  } catch (error) {
    Utils.showError(error)
    if (callback) {
      callback()
    }
  }
}

export const likeDislikeComment = (data, isLike, callback = null) => async (dispatch) => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      let res = null
      if (!isLike) {
        dispatch({
          type: T.LIKE_COMMENT,
          commentID: data.data.commentID,
          storyID: data.data.storyID,
          subCommentID: data.data.subCommentID
        })
        res = await FirebaseManager.instance.likeFeedComment(data.data)
      } else {
        dispatch({
          type: T.DISLIKE_COMMENT,
          commentID: data.data.commentID,
          storyID: data.data.storyID,
          subCommentID: data.data.subCommentID
        })
        res = await FirebaseManager.instance.disLikeFeedComment(data.data)
      }

      dispatch({
        type: T.RESET_STATE,
      })

      if (res && callback) {
        callback(data)
      }
    } else {
      Utils.showError(Constants.INTERNET_ERROR)
      if (callback) {
        callback()
      }
    }

  } catch (error) {
    Utils.log(error)
    if (callback) {
      callback()
    }
  }
}


export const getUserRepost = (requestedData, callback = null) => async () => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.getUserRepost(requestedData.data)
      if (res && callback) {
        callback(res)
      }
    }
    else {
      Utils.showError(Constants.INTERNET_ERROR)
      if (callback) {
        callback()
      }
    }
  } catch (error) {
    Utils.log(error)
    if (callback) {
      callback()
    }
  }
}

export const addInfluencePoint = (requestedData, callback = null) => async (dispatch, getState) => {
  Utils.log("influence point adding")
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.addInfluencePoint(requestedData)
      if (res && res.status == 200) {
        SharedPreferences.saveData(settings.INFLUENCE_POINTS_SP_KEY, "true")
        if (callback) {
          callback(true)
        }
      } else if (res && res.status == 400) {
        Utils.log(res.customMsg)
        if (callback) {
          callback(false)
        }

      }
    } else {
      Utils.showError(Constants.INTERNET_ERROR)
      if (callback) {
        callback(false)
      }
    }
  } catch (error) {
    Utils.log(error)
    if (callback) {
      callback(false)
    }
  }
}

export const getUserNotification = (requestedData, callback = null) => async (dispatch, getState) => {
  Utils.log("getting user notifications")
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.getUserNotification(requestedData)
      if (res && res.status == 200) {
        callback(res.data)
      } else if (res && res.success) {
        UserSession.instance.updateUnreadNotifications(res.unReadCount)
        callback(res.unReadCount)
      }
    } else {
      Utils.showError(Constants.INTERNET_ERROR)
      callback()
    }
  } catch (error) {
    Utils.log(error)
    callback()
  }
}

export const sendNotificationsLastSeen = (requestedData, callback = null) => async (dispatch, getState) => {
  Utils.log("Sending notifications last seen")
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.sendNotificationsLastSeen(requestedData)
      if (res && res.status == 200) {
        callback(res.data)
      }
    } else {
      Utils.showError(Constants.INTERNET_ERROR)
      callback()
    }
  } catch (error) {
    Utils.log(error)
    callback()
  }
}

export const getNavigationComics = (requestedData, callback = null) => async (dispatch, getState) => {
  Utils.log("getting comics data for navigation panels")
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.getNavigationComics(requestedData)
      if (res && res.status == 200) {
        callback(res.data)
      }
    } else {
      Utils.showError(Constants.INTERNET_ERROR)
      callback()
    }
  } catch (error) {
    Utils.log(error)
    callback()
  }
}

export const storeUserEmailData = (requestedData, callback = null) => async (dispatch, getState) => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.storeUserEmailData(requestedData)
      if (res && res.status == 200) {
        callback(res.data)
      }
    } else {
      Utils.showError(Constants.INTERNET_ERROR)
      callback()
    }
  } catch (error) {
    Utils.log(error)
    callback()
  }
}

export const getUserEmailData = (requestedData, callback = null) => async (dispatch, getState) => {
  Utils.log("Getting user email data")
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.getUserEmailData(requestedData)
      if (res && res.data == 200) {
        callback(res.data)
      }
    } else {
      Utils.showError(Constants.INTERNET_ERROR)
      callback()
    }
  } catch (error) {
    Utils.log(error)
    callback()
  }
}

export const setNotificationAlerts = (requestedData, callback = null) => async (dispatch, getState) => {
  Utils.log("Setting Notifications Alerts")
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.setNotificationAlerts(requestedData)
      if (res && res.success) {
        callback(res.data)
      }
    } else {
      Utils.showError(Constants.INTERNET_ERROR)
      callback()
    }
  } catch (error) {
    Utils.log(error)
    callback()
  }
}

export const getUserRecentComments = (requestedData, callback = null) => async (dispatch, getState) => {
  Utils.log("fetching user recent comments")
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.getUserRecentComments(requestedData)
      if (res && res.status == 200) {
        callback(res.data)
      }
    } else {
      Utils.showError(Constants.INTERNET_ERROR)
      callback()
    }
  } catch (error) {
    Utils.log(error)
    callback()
  }
}

export const getUserGiftsSent = (requestedData, callback = null) => async (dispatch, getState) => {
  Utils.log("fetching user gifts sent")
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.getUserGiftsSent(requestedData)
      if (res && res.status == 200) {
        callback(res.data)
      }
    } else {
      Utils.showError(Constants.INTERNET_ERROR)
      callback()
    }
  } catch (error) {
    Utils.log(error)
    callback()
  }
}

export const redeemInfluencePoint = (requestedData, callback = null) => async (dispatch, getState) => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      dispatch({
        type: T.IS_PURCHASE_IN_PROGRESS,
        payload: true
      })
      const res = await FirebaseManager.instance.redeemInfluencePoint(requestedData)
      if (res && res.data && res.status == 200) {
        callback(res)
      } else if (res && res.status == 400) {
        Utils.log(res.customMsg)
        callback()
      }
      dispatch({
        type: T.IS_PURCHASE_IN_PROGRESS,
        payload: false
      })
    } else {
      Utils.showError(Constants.INTERNET_ERROR)
      callback()
      dispatch({
        type: T.IS_PURCHASE_IN_PROGRESS,
        payload: false
      })
    }
  } catch (error) {
    Utils.showError(error)
    callback()
    dispatch({
      type: T.IS_PURCHASE_IN_PROGRESS,
      payload: false
    })
  }
}

export const getUnlockComicURLs = () => async () => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.getUnlockComicURLs()
      if (res && res.data && res.status == 200) {
        Utils.log("Added list of puchased comics")
        UserSession.instance.updateUnlockedComics(res.data.urls)
      } else if (res && res.status == 400) {
        Utils.log(res.customMsg)
      }
    } else {
      Utils.log('getUnlockComicURLs: ' + Constants.INTERNET_ERROR)
    }
  } catch (error) {
    Utils.log('getUnlockComicURLs: failed ' + error)
  }
}

export const removeProfileImage = (callback = null) => async () => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.removeProfileImage()
      if (res && res.success) {
        callback(res)
      } else if (res && res.status == 400) {
        Alert.alert(res.name, res.customMsg)
        callback()
      }
    } else {
      Utils.log('removeProfileImage: ' + Constants.INTERNET_ERROR)
      callback()
    }
  } catch (error) {
    Utils.showError(error)
    callback()
  }
}

export const getStripePortalLink = (callback = null, showLoader = true) => async (dispatch, getState) => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      if (showLoader) {
        dispatch({
          type: T.IS_PURCHASE_IN_PROGRESS,
          payload: true
        })
      }

      const res = await FirebaseManager.instance.callAPITogetPortalLinkForStripe()
      if (res && res.data && (res.data.status == 200 || res.status == 200)) {
        store.dispatch({
          type: T.IS_PURCHASE_IN_PROGRESS,
          payload: false
        })
        if (res.data.url) {
          return callback(res.data.url)
        } else {
          callback()
          Alert.alert("", res.data.message)
        }
      }
    }
    else {
      callback()
      Utils.showError(Constants.INTERNET_ERROR)
    }
  } catch (error) {
    Utils.log(error)
    callback()
    store.dispatch({
      type: T.IS_PURCHASE_IN_PROGRESS,
      payload: false
    })
    if (error && error.status == 400) {
      let title = error.name
      let message = error.customMsg
      Alert.alert(title, message)
    }
  }
}

export const repostComic = (data, callback = null) => async (dispatch, getState) => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      dispatch({
        type: T.SENT_POST,
        storyID: data.data.storyID
      })
      const res = await FirebaseManager.instance.addRepost(data.data)
      if (res && callback) {
        callback(res)
      }

      dispatch({
        type: T.RESET_STATE,
      })
    }
    else {
      Utils.showError(Constants.INTERNET_ERROR)
      if (callback) {
        callback()
      }
    }
  } catch (error) {
    Utils.showError(error)
    if (callback) {
      callback()
    }
    dispatch({
      type: T.HIDE_LOGIN_INDICATOR
    })
  }
}

export const sendPost = (data, callback = null) => async (dispatch, getState) => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {

      dispatch({
        type: T.SENT_POST,
        storyID: data.data.storyID
      })
      const res = await FirebaseManager.instance.sendPost(data.data)
      if (res && callback) {
        callback(res)
      }

      dispatch({
        type: T.RESET_STATE,
      })
    }
    else {
      Utils.showError(Constants.INTERNET_ERROR)
      if (callback) {
        callback()
      }
    }
  } catch (error) {
    Utils.showError(error)
    if (callback) {
      callback()
    }
    dispatch({
      type: T.HIDE_LOGIN_INDICATOR
    })
  }
}

export const editRepostedComic = (data, callback = null) => async () => {
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.editRepost(data.data)
      if (res && callback) {
        callback(res)
      }
    }
    else {
      Utils.showError(Constants.INTERNET_ERROR)
      if (callback) {
        callback()
      }
    }
  } catch (error) {
    Utils.showError(error)
    if (callback) {
      callback()
    }
  }
}

export const deleteRepostedComic = (data, callback = null) => async () => { //It is use to delete reposted (All Friend).
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.deleteRepost(data.data)
      if (res && callback) {
        callback(res)
      }
    }
    else {
      Utils.showError(Constants.INTERNET_ERROR)
      if (callback) {
        callback()
      }
    }
  } catch (error) {
    Utils.showError(error)
    if (callback) {
      callback()
    }
  }
}

export const getSeriesCarousel = (data, callback = null) => async () => {
  Utils.log("Getting series data for carousel panels")
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.getSeriesCarousel(data)
      if (res && callback) {
        callback(res.data)
      }
    }
    else {
      Utils.showError(Constants.INTERNET_ERROR)
      if (callback) {
        callback()
      }
    }
  } catch (error) {
    Utils.showError(error)
    if (callback) {
      callback()
    }
  }
}

export const deleteSentPost = (data, callback = null) => async () => { //It is use to delete Sent_To_Each and Sent_To_Group post (Selected Friend).
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      const res = await FirebaseManager.instance.deleteSentPost(data.data)
      if (res && callback) {
        callback(res)
      }
    }
    else {
      Utils.showError(Constants.INTERNET_ERROR)
      if (callback) {
        callback()
      }
    }
  } catch (error) {
    Utils.showError(error)
    if (callback) {
      callback()
    }
  }
}

export const finishedPurchase = async (result = null, extraData = null) => {
  if (extraData && result && Utils.isGiftProduct(result.productId)) { // Send Gift Data update
    Utils.log("Going to update Gift in the BE and UI")

    if (extraData.feedData && extraData.product) {
      store.dispatch({
        type: T.ADD_GIFT,
        payload: extraData.feedData.action,
        storyID: extraData.feedData.storyID,
        reactionName: extraData.product.reactionName
      })

      let data = { "data": { storyID: extraData.feedData.storyID, reactionType: extraData.product.reactionName, pageURL: extraData.feedData.action, refType: Constants.UPPERCASE_STORY } }
      let res = await FirebaseManager.instance.likeFeed(data.data)

      store.dispatch({
        type: T.RESET_STATE,
      })

      if (res && extraData.callback) {
        extraData.callback(extraData.product.reactionName, extraData.feedData)
      }
      Utils.log('Updating Gifted Item On BE: ' + res)

      setTimeout(() => {
        const reactionName = extraData.product?.title
        if (reactionName) {
          Utils.showToast("A notification has been sent to the creator.", "bottom", "internalPaymentSuccess", 5000, `Thank you for gifting ${reactionName}.`)
        }
      }, 2000)
    }
    IAPManager.default.extraPurchaseData = null

    store.dispatch({
      type: T.IS_PURCHASE_IN_PROGRESS,
      payload: false
    })

    return
  } else {
    store.dispatch({
      type: T.IS_PURCHASE_IN_PROGRESS,
      payload: false
    })
  }

  if (extraData && extraData.callback) {
    extraData.callback()
  }

  store.dispatch({
    type: T.REFRESH_COMIC_PAGE,
    urlToCheck: store.getState().readComic.pathUrl,
  })
}

export const storeCancelledPurchase = () => {
  store.dispatch({
    type: T.IS_PURCHASE_IN_PROGRESS,
    payload: false
  })
}

export const purchaseFailed = (error) => {
  store.dispatch({
    type: T.IS_PURCHASE_IN_PROGRESS,
    payload: false
  })

  if (error && error.code == "E_USER_CANCELLED") {
    return;
  }

  if (error) {
    Utils.log('purchaseFailed ' + error)
    Utils.showError(error)
  }
}

export const updateShowAllComicsSettings = (showAllComics) => async (dispatch, getState) => {
  try {
    dispatch({
      type: T.SHOULD_SHOW_ALL_COMICS,
      payload: showAllComics
    })
    await FirebaseManager.instance.updateUserSettings(showAllComics)
  } catch (error) {
    Utils.log("updateUserSettings failed" + error)
  }
}

export const openWebViewAction = (url) => async () => {
  if (!url || url.startsWith('/') || (!url.includes('www') && !url.includes('https') && !url.includes('http') && !url.includes('.com'))) {
    Utils.showToast("URL must be wrong")
    return
  }
  try {
    if (await RNInAppBrowser.isAvailable()) {
      if (Platform.OS == 'ios') {
        Orientation.unlockAllOrientations()
      }

      RNInAppBrowser.close()
      await RNInAppBrowser.open(url, {
        dismissButtonStyle: 'Done',
        readerMode: false,
        animated: true,
        modalEnabled: true,
        enableBarCollapsing: true,
        showTitle: true,
        preferredBarTintColor: 'white',
        toolbarColor: 'white',
        modalPresentationStyle: 'overFullScreen',
        enableUrlBarHiding: false,
        enableDefaultShare: true,
      }).then((result) => {
        if (Platform.OS == 'ios') {
          Orientation.lockToPortrait()
        }
      }).catch((exception) => {
        Utils.log("Open URL Exception" + exception)
        if (Platform.OS == 'ios') {
          Orientation.lockToPortrait()
        }
      })
    } else {
      Linking.openURL(url)
    }
  } catch (error) {
    Utils.log("Error in openWebViewAction " + error)
  }
}

export const askForNotificationPermission = () => async (dispatch, getState) => {
  const hasPermission = await FirebaseManager.instance.askForNotificationPermission();
  if (hasPermission) {
    dispatch({
      type: T.NOTIFICATION_PERMISSION_GRANTED,
    })
  } else {
    dispatch({
      type: T.NOTIFICATION_PERMISSION_REVOKED,
    })
  }
}

export const addNewTransaction = (requestedData, feedData = null, callback = null) => async (dispatch, getState) => {
  Utils.log("calling addNewTransaction API")
  const isConnected = NetworkUtils.instance.isAvailable()
  try {
    if (isConnected) {
      dispatch({
        type: T.IS_PURCHASE_IN_PROGRESS,
        payload: true
      })

      dispatch({
        type: T.ADD_GIFT,
        payload: feedData?.action,
        storyID: feedData?.storyID,
        reactionName: requestedData?.data?.productInfo?.reactionName
      })

      const res = await FirebaseManager.instance.addNewTransaction(requestedData)
      dispatch({
        type: T.RESET_STATE
      })

      if (res && callback) {
        await dispatch(getUserDetails())
        const userBalance = UserSession.instance.getUserReferralBalance()
        const referralBalance = parseFloat(userBalance).toFixed(2)

        Alert.alert(
          "Payment successful",
          `Your payment using Referral Bonus was successful. New balance: USD ${referralBalance}`,
          [
            {
              text: "OK", onPress: () => {
                const reactionName = requestedData?.data?.productInfo?.reactionName
                let giftDetails = SessionManager.instance.getGiftItemDetails(reactionName)
                if (giftDetails) {
                  const { title } = giftDetails
                  Utils.showToast("A notification has been sent to the creator.", "bottom", "internalPaymentSuccess", 5000, `Thank you for gifting ${title}.`)
                }
              }
            },
          ],
          { cancelable: false }
        )
        callback(res, feedData)
      }

      dispatch({
        type: T.IS_PURCHASE_IN_PROGRESS,
        payload: false
      })
    } else {
      Utils.showError(Constants.INTERNET_ERROR)
      callback()
    }
  } catch (error) {
    Utils.showError(error)
    callback()

    setTimeout(() => {
      dispatch({
        type: T.REMOVE_GIFT,
        payload: feedData?.action,
        storyID: feedData?.storyID,
        reactionName: requestedData?.data?.productInfo?.reactionName
      })

      if (Platform.OS == 'ios') {
        dispatch({
          type: T.RESET_STATE
        })
      } else {
        setTimeout(() => {
          dispatch({
            type: T.RESET_STATE
          }, 200)
        })
      }

      dispatch({
        type: T.IS_PURCHASE_IN_PROGRESS,
        payload: false
      })
    }, 500)
  }
}

export const hasNotificationPermission = () => async (dispatch, getState) => {
  const hasPermission = await FirebaseManager.instance.hasNotificationPermission();
}