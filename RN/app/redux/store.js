import appReducer from './reducers/reducers'
import { createStore, applyMiddleware } from 'redux'
import thunk from 'redux-thunk'

const iState = {
  "readComic": {
    "isLoading": false,    
    "errorRes": {},
    "pathUrl": null,   
    "refreshComicPage": {},
    "hasNotificationPermission": false,
    "showAllComics": null
  },
  "loginInfo": {
    "isLogInProcess": false,
    "isAnonymous": true,    
    "loginScreenError": null,
    "otpScreenError": null,
    "userDetails": {},
  },
  "userInfo": {
    "alerts": {},    
  },
  "purchaseIndicators": {
    "isPurchaseInProgress": false
  },  
  "comicPageStatus": { 
    "multiplePanelStatus": {
    }
  },
  "getLikesStatus": {    
    "performAction": {}
  },
  "friendActivityIndicator": {
    "friendsActivityInProgress": false
  }
}

storeFactory = (initialState = iState) => {
  return applyMiddleware(thunk)(createStore)(appReducer, initialState)
}
const store = storeFactory()
export default store