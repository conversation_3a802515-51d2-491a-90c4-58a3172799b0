import T from '../actions/types'
import { combineReducers } from 'redux'

export const fetching = (state = false, action) => {

  switch (action.type) {
    case T.SHOW_COMIC_LOADING:
      return true

    case T.HIDE_COMIC_LOADING:
      return false

    default:
      return state
  }
}

export const readComicError = (state = [], action) => {
  switch (action.type) {
    case T.ERROR_IN_FETCH: {
      return action
    }
    default: return state
  }
}

export const pathUrl = (state = '', action) => {
  switch (action.type) {
    case T.SET_PATH_URL: {
      return action.payload
    }
    default: return state
  }
}

export const showAllComics = (state = null, action) => {
  switch (action.type) {
    case T.SHOULD_SHOW_ALL_COMICS: {
      return action.payload
    }
    case T.SAVE_USER_ANONYMOUS_STATE: {
      return null
    }
    default: return state
  }
}

export const isLogInProcess = (state = false, action) => {
  switch (action.type) {
    case T.SHOW_LOGIN_INDICATOR:
      return true

    case T.HIDE_LOGIN_INDICATOR:
      return false

    default:
      return state
  }
}

export const friendsActivityInProgress = (state = false, action) => {
  switch (action.type) {
    case T.SHOW_LOADING:
      return true

    case T.HIDE_LOADING:
      return false

    default:
      return state
  }
}

export const isAnonymous = (state = true, action) => {
  switch (action.type) {

    case T.SAVE_USER_ANONYMOUS_STATE:
      return action.payload

    default:
      return state
  }
}

export const userDetails = (state = {}, action) => {
  switch (action.type) {
    case T.UPDATE_USER_DETAILS: {
      return action.payload
    }
    default:
      return state
  }
}

//UserInfo
export const updateAlerts = (state = {}, action) => {
  switch (action.type) {
    case T.UPDATE_ALERTS: {
      return action.payload
    }
    default:
      return state
  }
}

export const loginScreenError = (state = null, action) => {
  switch (action.type) {
    case T.SHOW_LOGIN_SCREEN_ERROR:
      return action.payload

    case T.CLEAR_LOGIN_SCREEN_ERROR:
      return null

    default:
      return state
  }
}

export const otpScreenError = (state = null, action) => {
  switch (action.type) {
    case T.SHOW_OTP_SCREEN_ERROR:
      return action.payload

    case T.CLEAR_OTP_SCREEN_ERROR:
      return null

    default:
      return state
  }
}

export const productAlreadyPurchased = (state = false, action) => {
  switch (action.type) {
    case T.PRODUCT_ALREADY_PURCHASED:
      return action.payload
    default:
      return state
  }
}
/**
 * CHECK PURCHASE FROM INFLUENCE
 * @param {*} state 
 * @param {*} action 
 */
export const productAlreadyPurchasedFromInfluence = (state = false, action) => {
  switch (action.type) {
    case T.PRODUCT_PURCHASED_FROM_INFLUENCE:
      return action.payload
    default:
      return state
  }
}

export const isPurchaseInProgress = (state = false, action) => {
  switch (action.type) {
    case T.IS_PURCHASE_IN_PROGRESS:
      return action.payload
    default:
      return state
  }
}

export const freelyAccessableComics = (state = false, action) => {
  switch (action.type) {
    case T.FREELY_ACCESSABLE_COMIC:
      return action.payload
    default:
      return state
  }
}

export const multiplePanelStatus = (state = [], action) => {
  switch (action.type) {
    case T.MULTIPLE_PANEL_STATUS:
      return Object.assign({}, state, action.payload)
    default:
      return state
  }
}

export const refreshPageInfo = (state = false, action) => {
  switch (action.type) {
    case T.REFRESH_PAGE:
      return action;
    default:
      return state
  }
}

export const performAction = (state = {}, action) => {
  switch (action.type) {
    case T.ADD_LIKE:
      return action
    case T.REMOVE_LIKE:
      return action
    case T.SENT_POST:
      return action
    case T.ADD_COMMENT:
      return action
    case T.ADD_GIFT:
      return action
    case T.REMOVE_GIFT:
      return action
    case T.REMOVE_COMMENT:
      return action
    case T.LIKE_COMMENT:
      return action
    case T.DISLIKE_COMMENT:
      return action
    case T.RESET_STATE:
      return {}
    default:
      return state
  }
}

export const refreshComicPage = (state = {}, action) => {
  switch (action.type) {
    case T.REFRESH_COMIC_PAGE:
      return action
    case T.RESET_STATE:
      return {}
    default:
      return state
  }
}

export const hasNotificationPermission = (state = false, action) => {
  switch (action.type) {
    case T.NOTIFICATION_PERMISSION_GRANTED:
      return true
    case T.NOTIFICATION_PERMISSION_REVOKED:
      return false
    default:
      return state
  }
}

const rootReducer = combineReducers({
  readComic: combineReducers({
    isLoading: fetching,
    errorRes: readComicError,
    pathUrl,
    showAllComics,
    refreshComicPage,
    hasNotificationPermission
  }),
  loginInfo: combineReducers({
    isLogInProcess,
    isAnonymous,
    loginScreenError,
    otpScreenError,
    userDetails,

  }),
  userInfo: combineReducers({
    alerts: updateAlerts,
  }),
  purchaseIndicators: combineReducers({
    productAlreadyPurchased,
    isPurchaseInProgress,
    productAlreadyPurchasedFromInfluence,
    freelyAccessableComics
  }),
  comicPageStatus: combineReducers({
    multiplePanelStatus,
    refreshPageInfo
  }),
  getLikesStatus: combineReducers({
    performAction
  }),
  friendActivityIndicator: combineReducers({
    friendsActivityInProgress
  })
})

export default (state, action) =>
  rootReducer(action.type === T.SIGN_OUT_USER ? undefined : state, action)