import axios from 'axios'
import { request<PERSON><PERSON><PERSON>, successHandler, errorHandler } from '../interceptors'
import config from '../../config'
import { Utils } from '../../config/Utils'
import { settings } from '../../config/settings'

const axiosClient = (baseUrl, config) => {
  Utils.log("Node axiosClient Config: BaseURL : " + baseUrl + " config " + JSON.stringify(config))
  return (
    axios.create({
      baseURL: baseUrl,
      ...config
    })
  )
}

const microServicesURLs = {
  NODE_API: settings.isDevBuild ? `${config.apiGateway.DEV_URL}` : `${config.apiGateway.PROD_URL}` ,
  // TIKIT_TEMPLATE: `${config.apiGateway.TEMPLATE}`,
  // TIKIT_CHARITY: `${config.apiGateway.CHARITY}`,
  // TIKIT_CHECKOUT: `${config.apiGateway.CHECKOUT}`
}

const clients = {}
const microServices = {}

for (const key in microServicesURLs) {
  const axiosInstance = axiosClient(microServicesURLs[key], {
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json'
    }
  })
  microServices[key] = key
  clients[key] = axiosInstance

  // Handle request process
  axiosInstance.interceptors.request.use(
    request => requestHandler(request),
    error => errorHandler(error)
  )
  // // Handle response process
  axiosInstance.interceptors.response.use(
    response => successHandler(response),
    error => errorHandler(error)
  )
}

export default (method, uri, data = {}, configs = {}) => {
  Utils.log("Node DATA: ", method, uri, data, configs)
  const {
    successMessage = null,
    server = microServices.TIKIT_TEMPLATE,
    headers = {},
    params = {},
    responseType = 'json',
    handlerEnabled = true,
    showLoader = true
  } = configs

  const axiosConfig = {
    headers: Object.entries(headers).reduce((a, [k, v]) => ((v == null || v === undefined || v === "null") ? a : (a[k] = v, a)), {}),
    params,
    handlerEnabled,
    showLoader
  }

  if (responseType) {
    axiosConfig.responseType = responseType
  }

  if (successMessage) {
    axiosConfig.successMessage = successMessage
  }

  return clients[server][method](uri, method === 'get' || method === 'delete' ? axiosConfig : data, axiosConfig)
}

export { microServices }
