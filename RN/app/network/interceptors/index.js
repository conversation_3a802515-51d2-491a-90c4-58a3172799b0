// import store from '../../store'
// import { loader } from '../../store/Loader/LoaderAction'
// import { dispatchSnackbarSuccess } from '../../utils/Shared'
import FirebaseManager from "../../config/FirebaseManager"
import { Utils } from "../../config/Utils"

export const isHandlerEnabled = (config = {}) => {
  return !(config.hasOwnProperty('handlerEnabled') && !config.handlerEnabled)
}

export const isShowLoader = (config = {}) => {
  return !(config.hasOwnProperty('showLoader') && !config.showLoader)
}

export const requestHandler = async request => {
  if (isHandlerEnabled(request)) {
    // Modify request here
    const authToken = await FirebaseManager.instance.currentUser().getIdToken()
    if (authToken) {
      request.headers.Authorization = `Bearer ${authToken}`
    }

    if (isShowLoader(request)) {
      // store.dispatch(loader(true))
    }
  }
  return request
}

export const successHandler = response => {
  if (isHandlerEnabled(response.config)) {
    // Handle Response
    const {
      successMessage = null,
      responseType = 'json'
    } = response.config || {}

    if (responseType === 'blob') {
      return { file: response.data, headers: response.headers }
    }

    // successMessage && dispatchSnackbarSuccess(successMessage)
    // store.dispatch(loader(false))
  }
  return response.data
}

export const errorHandler = error => {
  console.log('Interceptor error: ',error)
  if (isHandlerEnabled(error.config)) {
    // store.dispatch(loader(false))
    if (error.response && error.response.data) {
        return Promise.reject({ ...error.response.data, message: error.response.data.customMsg })
      }      
    }
    // You can decide what you need to do to handle errors.
    // here's example for unautherized user to log them out .
    // error.response.status === 401 && Auth.signOut();  
  return Promise.reject({ ...error })
}
