import FileCache from '../config/FileCache';
import { settings } from '../config/settings'
import { Utils } from '../config/Utils';

export class Ajax {

  static async readComic(url) {
    // eslint-disable-next-line no-useless-catch
    try {
      Utils.log("Performance Checking : readComic API call 1 " + new Date().getTime() + " url " + url)
      let response = await fetch(url)
      Utils.log("Performance Checking : readComic API call 2 " + new Date().getTime() + " url " + url)
      if (response && response.status == 404) {
        throw response;
      } else {
        let responseJson = await response.json()
        Utils.log("Performance Checking : readComic API call 3 " + new Date().getTime() + " url " + url)
        return responseJson
      }
    } catch (error) {
      throw error
    }
  }

  static async fetchAndSaveComic(url, isComic = true, configForFeed = true) {
    var fetchURL = url
    var comicURL = url + "?mockParam=" + Date.now()
    if (configForFeed) {
      if (Utils.isHomeURL(fetchURL)) {
        fetchURL = settings.getComicFeedHomeURL()
      } else if (!Utils.isComicURL(fetchURL)) {
        fetchURL = Utils.getComicSeriesFeedURL(fetchURL);
      }
      Utils.log("API Calling - fetchAndSaveComic", "configForFeed", configForFeed, ", API =>", fetchURL)
    }

    fetchURL = fetchURL + "?mockParam=" + Date.now()

    return Ajax.readComic(fetchURL)
      .then(
        response => {
          let res = response
          if (isComic) {
            res = response.comics
          }
          FileCache.default.downloadFile(comicURL)
            .then(
              result => { }
            )
            .catch(
              error => Utils.log(error)
            )
          return res
        }
      )
      .catch(async fetchError => {
        Utils.log('fetch call error ' + JSON.stringify(fetchError))
        try {
          const result = await FileCache.default.getFileContentForURL(url)
          let responseJson = JSON.parse(result)
          if (isComic) {
            return responseJson.comics
          } else {
            return responseJson
          }
        }
        catch (error) {
          throw fetchError
        }
      })
  }

  static async getJSONFile(url, isComic = false) {
    try {
      const isStale = await FileCache.default.isFileStaleForURL(url)
      return await this.fetchComic(url, isStale, isComic)
    } catch (error) {
      Utils.log(error)
      throw error
    }
  }

  static async fetchComic(url, isStale, isComic = false) {
    if (isStale) {
      return Ajax.fetchAndSaveComic(url, isComic, false)
    } else {
      return FileCache.default.getFileContentForURL(url)
        .then(
          result => {
            let responseJson = JSON.parse(result)
            let res = responseJson
            if (isComic) {
              res = responseJson.comics
            }
            return res
          }
        )
        .catch(error => Utils.log(error))
    }
  }
}
