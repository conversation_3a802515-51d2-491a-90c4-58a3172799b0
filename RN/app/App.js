import { Provider } from 'react-redux'
import store from './redux/store'
import React, { Component } from 'react';
import NavigationService from './config/NavigationService';
import DeepLinkManager from './config/DeepLinkManager';
import IAPManager from './config/IAPManager';
import { Utils } from './config/Utils';
import { NativeBaseProvider } from 'native-base';
import { DimenContext, ThemeContext } from './Contexts.js';
import { Color } from './config/Color';
import { ActivityIndicator, Dimensions, Platform } from 'react-native';
import { Ajax } from './api/ajax';
import { settings } from './config/settings';
const styling = require('../styles.json');
import { GestureHandlerRootView } from 'react-native-gesture-handler'
import { NavigationContainer } from '@react-navigation/native';
import MainNavigator from './config/routes';
import {
  GoogleOneTapSignIn,  
} from '@react-native-google-signin/google-signin';
import { getNavigationBarHeight } from "react-native-android-navbar-height";  


import {
  DefaultTheme
} from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';

const navTheme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    background: 'white',
  },
};

class App extends Component {

  constructor(props) {
    super(props)

    this.state = {
      themeValue: null,
      dimen: { },
    }

    this.parseStyle = this.parseStyle.bind(this)
  }

  parseStyle(style) {
    if (Platform.OS == "android" && style.app.comments && style.app.comments.fontFamily) {
      if (style.app.comments.fontFamily.includes("italic")) {
        delete style.app.comments["fontStyle"]
      }
    }

    return style
  }

  componentDidMount() {
    GoogleOneTapSignIn.configure({
      webClientId: 'autoDetect',      
      scopes: ['profile', 'email']
    });    
  
    if (Platform.OS === 'android') {
      const screenScale = Dimensions.get('screen').scale;
      getNavigationBarHeight().then(height => {
        const navBarActualHeight = Platform.Version >= 35 ? height / screenScale : 0
        this.setState({ dimen: { navbarHeight: navBarActualHeight } })
        Utils.log("navbarHeight: ", navBarActualHeight)
      }
      ).catch(error => {
        console.error("Error getting navigation bar height: ", error);  
      }); 
    }    

    Ajax.getJSONFile(Utils.resolvePath("", settings.stylingJSONEndPoint), false).then((response) => {
      const parsedStyle = this.parseStyle(response ? response.styles : styling.styles)
      this.setState({ themeValue: { ...parsedStyle.app, colors: parsedStyle.colors } })
    }).catch((error) => {
      const parsedStyle = this.parseStyle(styling.styles)
      this.setState({ themeValue: { ...parsedStyle.app, colors: parsedStyle.colors } })
    })
  }

  render() {
    return (
      <>
        {this.state.themeValue != null ?
          <Provider store={store}>            
              <ThemeContext.Provider value={this.state.themeValue}>
                <DimenContext.Provider value={this.state.dimen}>
                <NativeBaseProvider>
                  <GestureHandlerRootView style={{ flex: 1 }}>
                    <NavigationContainer theme={navTheme} ref={navigatorRef => {
                      NavigationService.setTopLevelNavigator(navigatorRef);
                    }}>
                      <MainNavigator />
                    </NavigationContainer>
                  </GestureHandlerRootView>
                </NativeBaseProvider>
                </DimenContext.Provider>
              </ThemeContext.Provider>            
          </Provider>
          :
          <SafeAreaView style={{ flex: 1, justifyContent: 'center', }} >
            <ActivityIndicator size="large" color={Color.BLACK_COLOR} />
          </SafeAreaView>
        }
    </>
    )
  }

  componentWillUnmount() {
    try {
      DeepLinkManager.instance.unsubscribeFromBranch();
      IAPManager.default.purchaseListeners.updateListner.remove()
      IAPManager.default.purchaseListeners.failureListner.remove()
    } catch (error) {
      Utils.log("Error in removing IAP listener " + error)
    }
  }
}

export default App;
