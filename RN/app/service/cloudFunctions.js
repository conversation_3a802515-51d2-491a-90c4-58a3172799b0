export const cloudFunctions = {

    MERGE_AUTHENTICATED_USER: 'mergeAuthenticatedUser',
    SEND_TEXT_MESSAGE: 'sendTextMessage',
    UPDATE_USER_PROFILE: 'updateUserProfile',
    UPDATE_PROFILE_PIC_URL: 'uploadImage',
    GET_UNLOCK_COMIC_URLS: 'getUnlockComicURLs',
    HAS_COMIC_READ_ACCESS: 'hasComicReadAccess',

    UNFRIEND: 'removeFriend',
    UPDATE_FRIEND_REQUEST: 'updateFriendRequest',
    GET_MY_FRIENDS: 'getUserFriends',
    GET_FRIEND_REQUEST: 'getFriendRequests',
    SEND_FRIEND_REQUEST: 'sendFriendRequest',
    GET_USER_PROFILE: 'getUserProfile',
    GET_FRIENDSHIP_STATUS: 'getFriendshipStatus',

    GET_APP_CONFIG: 'getAppConfig',
    ADD_INFLUENCE_POINT: 'addInfluencePoint',
    REDEEM_INFLUENCE_POINT: 'redeemInfluencePoint',
    GET_ALL_COMICS: 'getAllComics',
    GET_NOTIFICATIONS: 'getNotifications',

    GET_USER_FEED: 'getUserFeed',
    GET_SERIES_COMICS: 'getComics',

    FETCH_COMMENTS: 'fetchComments',
    ADD_COMMENT: 'addComment',
    LIKE_COMMENT: 'likeComment',
    DISLIKE_COMMENT: 'disLikeComment',
    EDIT_COMMENT: 'editComment',
    DELETE_COMMENT: 'deleteComment',

    GET_MY_REPOST: 'getMyReposts',
    ADD_REPOST: 'addRepost',
    SEND_POST: 'sendPost',
    EDIT_REPOST: 'editRepost',
    DELETE_REPOST: 'deleteRepost',
    DELETE_SENT_POST: 'deleteSentPost',

    GET_STORY_LIKES: 'getStoryLikes',
    FETCH_STORY_LIKES: 'fetchStoryLikes',
    LIKE_STORY: 'likeStory',
    DISLIKE_STORY: 'dislikeStory',

    GET_STORY_DETAILS: 'getStory',

    FETCH_COMMENT_LIKES: 'fetchCommentLikes',

    FETCH_REPOSTED_USERS_LIST: 'fetchReposts',

    FETCH_WITH_WHOM_STORY_SHARED: 'getSharedByList',

    ADD_USER_DEVICE_TOKEN: 'addUserDeviceToken',

    SUBSCRIBE_TO_ALERT: 'subscribeToAlert',

    ADD_NEW_TRANSACTION: 'addNewTransaction',
    VALIDATE_TRANSACTION: 'validateTransaction',

    SET_REFERRER: 'setReferrer',
    RECORD_PAGEVIEW: 'recordPageview',

    GET_PAGEVIEW_INFO: 'getPageInfo',
    GET_MULTIPLE_PAGEVIEW_INFO: 'getMultiplePageInfo',

    GET_USER_DETAILS: 'getUserDetailsv2',

    UPDATE_USER_SETTINGS: 'updateUserSettings',
    SIGN_OUT_USER: 'signOutUser',

    FLAG_COMMENT: 'flagComment',
    BLOCK_USER: 'blockUser',
    PORTAL_LINK: 'createPortalLink',
    DELETE_USER_ACCOUNT: 'deleteUser',

    GET_SUBSCRIBERS_PROGRESS: 'getSubscriptionMeterAggregates',

    GET_NAVIGATION: 'getNavigation',

    REMOVE_PROFILE_IMAGE: 'removeProfileImage',

    GET_EPISODES: 'getEpisodes',

    UPDATE_LAST_SEEN_NOTIFICATION_AT: 'updateLastSeenNotificationAt',
    ADD_EMAIL_FLOW_DATA: 'addEmailFlowData',
    GET_EMAIL_FLOW_DATA: 'getEmailFlowData',
    SET_NOTIFICATION_SETTINGS: 'setNotificationSettings',
    GET_SERIES_CAROUSEL: 'getSeriesCarousel'
  }