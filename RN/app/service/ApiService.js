import { getApp } from '@react-native-firebase/app'
import '@react-native-firebase/functions';
import axiosInstance, { microServices } from '../../app/network/apis/index';
import { apiMap } from './apiUrlMapper';
import { Utils } from '../config/Utils';
import { cloudFunctions } from './cloudFunctions';

export default class ApiService {
    static instance = new ApiService();

    callFunction(functionName, initialData = {}, initialConfig = {}) {
        // Return a function that when called, executes the REST API request
        return async (data = initialData, config = initialConfig) => {
            return this.callRestApi(functionName, data, config);
        };
    }

    callFirebaseFunction(functionName, data) {
        // Call the Firebase function
        Utils.log("Firebase Function DATA: ", functionName, data)
        return getApp().functions().httpsCallable(cloudFunctions[functionName])(data);
    }

    async callRestApi(functionName, data, config = {}) {
        const apiConfig = apiMap[functionName];
        if (!apiConfig) {
            throw new Error(`API configuration for function ${functionName} is not defined.`);
        }

        const { url: apiUrl, method } = apiConfig;
        let finalUrl = apiUrl;
        if (Utils.containsCurlyBrackets(finalUrl)) {
            finalUrl = finalUrl.replace(/{(\w+)}/g, (_, key) => {
                if (data[key]) {
                    return encodeURIComponent(data[key]);
                }
            });
        }

        // Separate query params from the data if needed
        let axiosConfig = {
            server: microServices.NODE_API,
            ...config
        };

        if (method === 'get' || method === 'delete') {
            // For GET or DELETE, treat `data` as query params or query string
            const queryParams = new URLSearchParams(data).toString();
            finalUrl = `${finalUrl}?${queryParams}`;
            data = {}; // Clear the data for GET/DELETE requests
        }

        // PUT requests can have both body and query params
        if (method === 'put' && data.queryParams) {
            const queryParams = new URLSearchParams(data.queryParams).toString();
            finalUrl = `${finalUrl}?${queryParams}`;
            delete data.queryParams; // Remove queryParams from data
        }

        const response = await axiosInstance(
            method,
            finalUrl,
            method === 'get' || method === 'delete' ? axiosConfig : data,
            axiosConfig
        );

        return response.result;
    }
}
