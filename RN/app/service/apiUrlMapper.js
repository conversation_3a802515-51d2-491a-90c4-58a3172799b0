export const apiMap = {
    // Add other function names and their corresponding API endpoints here

    'CREATE_USER': { url: '/user', method: 'post' },
    'MERGE_AUTHENTICATED_USER': { url: '/user/merge', method: 'post' },
    'SEND_TEXT_MESSAGE': { url: '/user/send-message', method: 'post' },
    'UPDATE_USER_PROFILE': { url: '/user/profile', method: 'put' },
    'UPDATE_PROFILE_PIC_URL': { url: '/user/profile/photo', method: 'post' },
    'GET_UNLOCK_COMIC_URLS': { url: '/story/unlock-urls', method: 'get' },
    'UPDATE_FRIEND_REQUEST': { url: '/user/friend-req', method: 'put' },
    'GET_FRIEND_REQUEST': { url: '/user/get-friend-req', method: 'post' },
    'SEND_FRIEND_REQUEST': { url: '/user/friend-req', method: 'post' },
    'GET_USER_PROFILE': { url: '/user/profile', method: 'get' },
    'GET_ALL_GIFT_ITEM': { url: '/rewards/gift', method: 'get' },
    'ADD_INFLUENCE_POINT': { url: '/rewards/redeem-points', method: 'post' },
    'REDEEM_INFLUENCE_POINT': { url: '/rewards/redeem-points', method: 'put' },
    'GET_INFLUENCE_POINTS_FOR_ACTIONS': { url: '/rewards', method: 'get' },
    'GET_ALL_COMICS': { url: '/story/comics', method: 'post' },
    'GET_NOTIFICATIONS': { url: '/user/get-notifications', method: 'post' },
    'GET_USER_FEED': { url: '/user/feed', method: 'post' },
    'GET_SERIES_COMICS': { url: '/story/comics-list', method: 'post' },
    'RECORD_STORY_VIEW': { url: '/stats/view', method: 'put' },
    'FETCH_COMMENTS': { url: '/story/comment', method: 'get' },
    'ADD_COMMENT': { url: '/story/comment', method: 'post' },
    'LIKE_COMMENT': { url: '/story/comment/like', method: 'post' },
    'DISLIKE_COMMENT': { url: '/story/comment/dislike', method: 'post' },
    'EDIT_COMMENT': { url: '/story/comment', method: 'put' },
    'DELETE_COMMENT': { url: '/story/comment', method: 'delete' },
    'GET_MY_REPOST': { url: '/story/get-repost', method: 'post' },
    'ADD_REPOST': { url: '/story/repost', method: 'post' },
    'SEND_POST': { url: '/story/post', method: 'post' },
    'EDIT_REPOST': { url: '/story/repost', method: 'put' },
    'DELETE_REPOST': { url: '/story/repost', method: 'delete' },
    'DELETE_SENT_POST': { url: '/story/post', method: 'delete' },
    'GET_STORY_LIKES': { url: '/story/all-like', method: 'post' },
    'FETCH_STORY_LIKES': { url: '/story/like/all-users', method: 'get' },
    'LIKE_STORY': { url: '/story/like', method: 'post' },
    'DISLIKE_STORY': { url: '/story/dislike', method: 'post' },
    'GET_STORY_DETAILS': { url: '/story', method: 'get' },
    'FETCH_COMMENT_LIKES': { url: '/story/fetch-comment-like', method: 'post' },
    'FETCH_REPOSTED_USERS_LIST': { url: '/story/repost/fetch', method: 'post' },
    'FETCH_WITH_WHOM_STORY_SHARED': { url: '/user/shared-with', method: 'get' },
    'ADD_USER_DEVICE_TOKEN': { url: '/user/add-token', method: 'post' },
    'SUBSCRIBE_TO_ALERT': { url: '/story/follow', method: 'post' },
    'ADD_NEW_TRANSACTION': { url: '/user/transaction', method: 'post' },
    'VALIDATE_TRANSACTION': { url: '/user/validate-transaction', method: 'post' },
    'SET_REFERRER': { url: '/user/referrer', method: 'post' },
    'RECORD_PAGEVIEW': { url: '/stats/page-view', method: 'post' },
    'GET_PAGEVIEW_INFO': { url: '/story/info', method: 'get' },
    'GET_MULTIPLE_PAGEVIEW_INFO': { url: '/story/pages', method: 'post' },
    'GET_USER_DETAILS': { url: '/user', method: 'get' },
    'UPDATE_USER_SETTINGS': { url: '/user/settings', method: 'put' },
    'SIGN_OUT_USER': { url: '/user/sign-out', method: 'post' },
    'FLAG_COMMENT': { url: '/user/comment/flag', method: 'post' },
    'BLOCK_USER': { url: '/user/block-comment', method: 'post' },
    'PORTAL_LINK': { url: '/payment', method: 'post' },
    'DELETE_USER_ACCOUNT': { url: '/user', method: 'delete' },
    'GET_SUBSCRIBERS_PROGRESS': { url: '/stats/subscriptions', method: 'get' },
    'GET_NAVIGATION': { url: '/story/navigate', method: 'get' },
    'REMOVE_PROFILE_IMAGE': { url: '/user/profile/photo', method: 'delete' },
    'UPDATE_LAST_SEEN_NOTIFICATION_AT': { url: '/user/notifications', method: 'put' },
    'ADD_EMAIL_FLOW_DATA': { url: '/user/add-email', method: 'post' },
    'SET_NOTIFICATION_SETTINGS': { url: '/user/notifications', method: 'post' },
    'HAS_COMIC_READ_ACCESS': { url: '/user/access/{pageUrl}', method: 'get' },
    'UNFRIEND': { url: '/user/friends/{friendUID}', method: 'delete' },
    'GET_MY_FRIENDS': { url: '/user/friends/{userID}', method: 'get' },
    'GET_FRIENDSHIP_STATUS': { url: '/user/friend/{userID}', method: 'get' },
    'GET_EPISODES': { url: '/story/series-episode', method: 'post' },
    'GET_EMAIL_FLOW_DATA': { url: '/user/data/{flowDataID}', method: 'get' },
    'GET_APP_CONFIG': { url: '/user/app-config', method: 'get' },
    'GET_SERIES_CAROUSEL': { url: '/story/carousel', method: 'get' },
    'VISITED_SHARED_COMIC': { url: '/story/visit-shared-comic', method: 'post' },
    'USER_RECENT_COMMENTS': { url: '/user/comment', method: 'get' },
    'USER_GIFTS_SENT': { url: 'user/gift', method: 'get' }
};