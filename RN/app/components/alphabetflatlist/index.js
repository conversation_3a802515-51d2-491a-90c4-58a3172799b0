import React, { Component } from 'react'
import {
  StyleSheet,
  View,
  Text,
  Dimensions,
  FlatList,
  TouchableOpacity,
  Platform,
  InteractionManager,
} from 'react-native'
import PropTypes from 'prop-types'
import { SystemFont } from '../../config/Typography'
import { scale } from 'react-native-size-matters'
import { Color } from '../../config/Color'
import { ThemeContext } from '../../Contexts'

const { height } = Dimensions.get('window')

const styleType = PropTypes.oneOfType([
  PropTypes.object,
  PropTypes.number,
  PropTypes.array,
])

export default class AlphabetFlatList extends Component {
  static propTypes = {
    data: PropTypes.array.isRequired,
    renderItem: PropTypes.func.isRequired,
    keyExtractor: PropTypes.func,
    viewabilityConfig: PropTypes.object,
    getItemLayout: PropTypes.func.isRequired,
    mainFlatListContainerStyle: styleType,
    alphabetListProps: PropTypes.shape({
      onPressLetter: PropTypes.func,
      alphabetListContainerStyle: styleType,
      alphabetButtonStyle: styleType,
      selectedAlphabetButtonStyle: styleType,
      alphabetTextStyle: styleType,
      selectedAlphabetTextStyle: styleType,
    }),
    matchFieldName: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  }

  static defaultProps = {
    viewabilityConfig: {
      itemVisiblePercentThreshold: 100,
    },
    keyExtractor: (item, index) => index.toString(),
    mainFlatListContainerStyle: {},
    alphabetListProps: {
      alphabetListContainerStyle: {},
      alphabetButtonStyle: {},
      selectedAlphabetButtonStyle: {},
      alphabetTextStyle: {},
      selectedAlphabetTextStyle: {},
    },
    matchFieldName: false,
  }

  constructor(props) {
    super(props)
    let letters = 'abcdefghijklmnopqrstuvwxyz'.toUpperCase().split('')
    this.state = {
      alphabetList: letters,
      selectedLetter: letters[0],
    }
  }

  onPressLetter = selectedItem => {
    let { matchFieldName } = this.props

    let matchedIndex = this.props.data.findIndex(item => {
      if (matchFieldName && !item[matchFieldName]) {
        return console.warn(
          `matchFieldName ${matchFieldName} is not present in data`,
        )
      }

      let letterToMatch = this.getLetterToMatch(item);

      if (!letterToMatch) {
        return false
      }

      return letterToMatch.toUpperCase() === selectedItem
    })
    if (matchedIndex === -1) return
    this.props.getMainListRef().scrollToIndex({
      animated: true,
      index: matchedIndex,
      viewPosition: 0,
    })

    InteractionManager.runAfterInteractions(() => {
      this.setState({ selectedLetter: selectedItem })
    })
    this.props.onPressLetter && this.props.onPressLetter(selectedItem)
  }

  getLetterToMatch = (item) => {
    let { matchFieldName } = this.props
    return matchFieldName ? item.firstName[matchFieldName][0] : item.firstName ? item.firstName[0] : item.lastName ? item.lastName[0] : null;
  }

  setAlphabetTextStyle = letter => {
    const { showSmallText } = this.props
    return this.state.selectedLetter === letter
      ? [
        styles.selectedAlphabetTextStyle(showSmallText),
        this.props.alphabetListProps.selectedAlphabetTextStyle,
      ]
      : [
        styles.alphabetTextStyle(showSmallText),
        this.props.alphabetListProps.alphabetTextStyle,
      ]
  }

  setAlphabetButtonStyle = letter =>
    this.state.selectedLetter === letter
      ? [
        styles.alphabetButtonStyle,
        this.props.alphabetListProps.selectedAlphabetButtonStyle,
      ]
      : [
        styles.alphabetButtonStyle,
        this.props.alphabetListProps.alphabetButtonStyle,
      ]

  renderAlphabetItem = ({ item }) => {
    return (
      <TouchableOpacity
        onPress={this.onPressLetter.bind(this, item)}
        style={styles.alphabetButtonContainerStyle}
        hitSlop={{ top: 5, bottom: 5, left: 10, right: 20 }}
      >
        <View style={this.setAlphabetButtonStyle(item)}>
          <Text adjustsFontSizeToFit={true} style={this.setAlphabetTextStyle(item)}>{item}</Text>
        </View>
      </TouchableOpacity>
    )
  }

  onViewableItemsChanged = ({ viewableItems, changed }) => {
    let topItem = viewableItems[0]
    let { matchFieldName } = this.props
    if (!topItem) return

    let { item } = topItem

    if (matchFieldName && !item[matchFieldName]) {
      return console.warn(
        `matchFieldName ${matchFieldName} is not present in data`,
      )
    }

    let letterToMatch = this.getLetterToMatch(item);

    if (!letterToMatch) {
      return false
    }

    let letter = letterToMatch.toUpperCase()
    let matchedIndex = this.state.alphabetList.findIndex(
      item => item === letter,
    )
    if (matchedIndex > -1 && letter !== this.state.selectedLetter) {
      InteractionManager.runAfterInteractions(() => {
        this.setState({
          selectedLetter: letter,
        })
      })
    }
  }

  renderSeparator = () => {
    return (
      <View style={[styles.separatorStyle, { backgroundColor: this.context.colors.separators }]} />
    )
  }

  alphabetKeyExtractor = (item, index) => index.toString()

  render() {
    return (
      <View style={styles.container}>
        <View
          style={[
            styles.mainFlatListContainerStyle,
            this.props.mainFlatListContainerStyle,
          ]}
        >
          <FlatList
            ref={this.props.inputRef}
            scrollEventThrottle={16}
            initialNumToRender={20}
            maxToRenderPerBatch={20}
            windowSize={9}
            removeClippedSubviews={true}
            onViewableItemsChanged={this.onViewableItemsChanged}
            extraData={this.props.extraData}
            ItemSeparatorComponent={this.renderSeparator}
            getItemLayout={this.props.getItemLayout}
            keyboardShouldPersistTaps={"always"}
            {...this.props}
          />
        </View>

        {/** Right Side Alphabet FlatList */}
        {(this.props.data && this.props.data.length > 0) &&
          <View
            style={[
              styles.alphabetListContainerStyle,
              this.props.alphabetListProps.alphabetListContainerStyle,
            ]}
          >
            <FlatList
              contentContainerStyle={{ flexGrow: 1, justifyContent: 'space-between', }}
              ref={ref => (this._alphaList = ref)}
              data={this.state.alphabetList}
              bounces={false}
              renderItem={this.renderAlphabetItem}
              keyExtractor={this.alphabetKeyExtractor}
              extraData={this.state}
              showsVerticalScrollIndicator={false}
              {...this.props.alphabetListProps}
            />
          </View>
        }
      </View>
    )
  }
}

AlphabetFlatList.contextType = ThemeContext;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    flexDirection: 'row',
  },
  mainFlatListContainerStyle: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  alphabetListContainerStyle: {
    flex: 0.08,
    backgroundColor: 'transparent',
    alignItems: 'flex-end',
    marginBottom: scale(5)
  },
  alphabetButtonStyle: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  alphabetButtonContainerStyle: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  alphabetTextStyle: (showSmallText = true) => {
    return {
      fontFamily: SystemFont.SELECTED_FONT,
      color: Color.RED_TEXT_COLOR,
      fontSize: height * (showSmallText ? 0.018 : 0.022),
    }
  },
  selectedAlphabetTextStyle: (showSmallText = true) => {
    return {
      fontFamily: SystemFont.SELECTED_FONT,
      fontWeight: 'bold',
      fontSize: height * (showSmallText ? 0.024 : 0.026),
      color: Color.RED_TEXT_COLOR,
    }
  },
  separatorStyle: {
    height: scale(1),
    width: "100%"
  },
})
