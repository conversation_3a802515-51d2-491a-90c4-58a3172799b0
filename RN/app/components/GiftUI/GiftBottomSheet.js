import React, { Component } from 'react';
import { Constants } from '../../config/Constants';
import GiftActionSheetItem from './GiftActionSheetItem'
import { StyleSheet } from 'react-native'
import { View, Text } from 'native-base'
import { Color } from '../../config/Color'
import { scale } from 'react-native-size-matters'
import SessionManager from '../../config/SessionManager';
import IAPManager from '../../config/IAPManager';
import FileCache from '../../config/FileCache';
import { LoadingIndicator } from '../LoadingIndicator';
import { Utils } from '../../config/Utils';
import RBSheet from "react-native-raw-bottom-sheet";
import { ThemeContext } from '../../Contexts';
import { FlatList } from 'react-native';
import UserSession from '../../config/UserSession';
export default class GiftBottomSheet extends Component {

  constructor(props) {
    super(props)

    const giftItems = SessionManager.instance.getGiftItemList()
    const visibleGifts = this.showEnabledGifts(giftItems)
    let giftSheetHeight = 380
    if (visibleGifts && visibleGifts.length > 6) {
      if (visibleGifts.length % 3 != 0) {
        giftSheetHeight = 380 + ((Math.floor(visibleGifts.length / 3) - 1) * 120)
      } else {
        giftSheetHeight = 380 + ((Math.floor(visibleGifts.length / 3) - 2) * 120)
      }
    }

    this.state = {
      gifts: visibleGifts,
      showLoadingIndicator: false,
    }

    this.isExternalComic = this.props.tappedComicData?.panelItems?.actionType == 'website' ? true : false

    this.selItem = null
    this.selFeedData = null

    this.referralBalance = UserSession.instance.getUserReferralBalance()
    this.sheetHeight = giftSheetHeight
    this.sheetTitle = 'Send love or more to the artist…'
    this.bottomSheetModalRef = null
    this.showBottomSheet = true
    this.presentBottomSheet = this.presentBottomSheet.bind(this)
    this.closeBottomSheet = this.closeBottomSheet.bind(this)
    this.fetchIAPProducts = this.fetchIAPProducts.bind(this)
    this.likeDislikeFeed = this.likeDislikeFeed.bind(this)
    this.sendGift = this.sendGift.bind(this)
    this.updateGiftIfEmpty = this.updateGiftIfEmpty.bind(this)
    this.setLoaderVisibility = this.setLoaderVisibility.bind(this)
    this.renderItem = this.renderItem.bind(this)
    this.showEnabledGifts = this.showEnabledGifts.bind(this)
  }

  componentDidMount() {
    this.updateGiftIfEmpty()
    this.presentBottomSheet()
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.showBottomSheet != nextProps.showBottomSheet) {
      this.showBottomSheet = nextProps.showBottomSheet
      if (nextProps.showBottomSheet) {
        this.presentBottomSheet()
      } else {
        this.closeBottomSheet()
      }
    }
  }

  componentWillUnmount() {
    this.selItem = null
    this.selFeedData = null
    Utils.log("Gift Bottom sheet is unmounted")
  }

  setLoaderVisibility(value) {
    this.setState({ showLoadingIndicator: value })
  }

  async fetchIAPProducts(finalItems) {
    try {
      const products = await IAPManager.default.getProducts(finalItems)
      if (products) this.setLoaderVisibility(false)
      return products;
    } catch (error) {
      Utils.log('Getting Product info error ' + error)
      this.setLoaderVisibility(false)
      return null;
    }
  }

  async updateGiftIfEmpty() {
    if (!this.state.gifts || this.state.gifts.length == 0) {
      let dataInJSON = await FileCache.default.readFile(Constants.ALL_GIFTS)
      let giftRes = Utils.getParsedData(dataInJSON)
      let selectedGifts = this.showEnabledGifts(giftRes)
      if (selectedGifts.length == 0 || selectedGifts == undefined) {
        this.closeBottomSheet()
        return Utils.showToast("Something went wrong, Please try again.")
      }
      this.setState({ gifts: selectedGifts })
    }
  }

  showEnabledGifts(giftRes = null) {
    const gifts = giftRes ? giftRes : this.state.gifts
    let visibleGiftsList = []
    if (gifts && gifts.length != 0) {
      for (const key in gifts) {
        const { hide } = gifts[key]
        if (!hide) {
          if (gifts[key].productId) {
            gifts[key].isDisable = true
          }
          visibleGiftsList.push(gifts[key])
        }
      }
    }
    return visibleGiftsList
  }

  async presentBottomSheet() {
    this.bottomSheetModalRef.open()

    const productIDs = this.state.gifts.map((item) => {
      if (item.productId) {
        return item.productId
      }
      else {
        return Constants.Test_PRODUCT_ID
      }
    })

    if (productIDs.length == 0 || (productIDs.length == 1 && productIDs.includes(Constants.Test_PRODUCT_ID))) {
      return
    }

    this.setLoaderVisibility(true)
    const products = await this.fetchIAPProducts(productIDs)
    Utils.log("Gift Item Products Info " + JSON.stringify(products))

    if (products) {
      products.forEach(element => {
        for (const iterator of this.state.gifts) {
          if (element.productId == iterator.productId) {
            iterator.localizedPrice = element.localizedPrice
            iterator.productId = element.productId
            iterator.isDisable = false
            break
          }
        }
      });
    }

    this.setState({ gifts: JSON.parse(JSON.stringify(this.state.gifts)) })
  }

  closeBottomSheet() {
    this.showBottomSheet = false
    if (this.props.closeBottomSheet) {
      const isFromGiftTap = this.selItem != null && this.selFeedData != null
      this.props.closeBottomSheet(isFromGiftTap, this.selItem, this.selFeedData)
    }
  }

  likeDislikeFeed(isLike, item, index) {
    this.props.likeDislikeFeed(isLike, item, index)
    this.bottomSheetModalRef.close()
  }

  sendGift(item, feedData) {
    this.selItem = item
    this.selFeedData = feedData
    this.bottomSheetModalRef.close() //First closing the sheet and then opening the Gift bottom sheet on onClose.    
  }

  renderItem(item) {
    return (
      <GiftActionSheetItem tappedComicData={this.props.tappedComicData} item={item.item} requestPurchase={this.props.requestPurchase} likeDislikeFeed={this.likeDislikeFeed} sendGift={this.sendGift} />
    )
  }

  renderBottomSheet() {
    const convertedReferralBalance = parseFloat(this.referralBalance).toFixed(2)

    return (
      <RBSheet
        draggable={true}
        customStyles={{
          container: {
            borderTopLeftRadius: 18,
            borderTopRightRadius: 18
          },
          draggableIcon: {
            width: 80,
          },
        }}
        onClose={this.closeBottomSheet}
        ref={(ref) => this.bottomSheetModalRef = ref}
        height={this.sheetHeight}
        minClosingHeight={100}
        closeOnPressBack={true}
        closeOnDragDown={true}
        closeOnPressMask={true}
        dragFromTopOnly={false}
        dragOnContent={true}>
        <View style={{ flex: 1 }}>
          <Text style={[this.context.p, styles.textStyle]}>
            {this.referralBalance > 0 && !this.isExternalComic ? (
              <>
                You have a balance of <Text style={this.context.pBold}>USD {convertedReferralBalance}</Text> from referral bonus. You can use it to send gifts.
              </>
            ) : (
              this.sheetTitle
            )}
          </Text>
          <FlatList
            numColumns={3}
            showsVerticalScrollIndicator={false}
            showsHorizontalScrollIndicator={false}
            data={this.state.gifts}
            renderItem={(item) => this.renderItem(item)}
            keyExtractor={(item, index) => item.localizedPrice + item.productId + index}
          />
        </View>
        {this.state.showLoadingIndicator && <LoadingIndicator />}
      </RBSheet>
    )
  }

  render() {
    return (
      this.renderBottomSheet()
    )
  }
}

GiftBottomSheet.contextType = ThemeContext

const styles = StyleSheet.create({
  whiteButtonStyle: {
    alignSelf: 'flex-start',
    backgroundColor: 'transparent',
    height: 40,
    borderColor: Color.LIGHTER_GREY,
    elevation: 0,
    borderRadius: 6,
    marginTop: scale(10),
    paddingTop: 0,
    paddingBottom: 0,
  },
  textStyle: {
    textAlign: 'center',
    marginTop: 10,
    marginBottom: 10,
    marginLeft: 12,
    marginRight: 12
  }
});