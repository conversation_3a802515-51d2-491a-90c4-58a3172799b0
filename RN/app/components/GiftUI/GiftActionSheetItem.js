import React, { Component } from 'react';
import { StyleSheet, Image, TouchableOpacity, Dimensions } from 'react-native'
import { scale } from 'react-native-size-matters';
import { Color } from '../../config/Color';
import { Text, View } from 'native-base';
import { Constants } from '../../config/Constants';
import { Utils } from '../../config/Utils';
import { ThemeContext } from '../../Contexts';

let dimensions = Dimensions.get('window')
export default class GiftActionSheetItem extends Component {

  constructor(props) {
    super(props)

    this.isRecentlyTapped = false
    this.isGiftButtomDisable = this.isGiftButtomDisable.bind(this)
  }

  isGiftButtomDisable(panelItems, reactionName) {
    let isDisable = false
    const { actionType, refType = Constants.UPPERCASE_STORY } = panelItems

    if (Constants.LIKE.toLowerCase() != reactionName.toLowerCase()) {
      isDisable = refType != Constants.UPPERCASE_STORY ? true : false
      if (!isDisable) {
        isDisable = actionType == 'website' ? true : false
      }

      if (!isDisable) {
        if (!Utils.isEmptyObject(panelItems.giftedItems)) {
          for (const key in panelItems.giftedItems) {
            if (reactionName.toLowerCase() == key.toLowerCase()) {
              isDisable = true
              break
            }
          }
        }
      }
    }
    return isDisable
  }

  render() {
    const { item } = this.props
    const { image, title, price, reactionName, unLikeTitle = null, isDisable = false } = item
    const { tappedComicData = null } = this.props
    if (!tappedComicData) {
      return
    }
    let panelItems = (Utils.checkData(tappedComicData) && Utils.checkData(tappedComicData.panelItems)) && tappedComicData.panelItems
    let isLiked = Utils.checkData(panelItems.isLiked) ? panelItems.isLiked : Utils.checkData(tappedComicData.isLiked) && tappedComicData.isLiked
    const amount = price == 0 ? Constants.FREE : price
    let index = tappedComicData.index != undefined ? tappedComicData.index : null

    let isGiftDisable = this.isGiftButtomDisable(panelItems, reactionName) || isDisable

    return (
      <View style={styles.mainContainer}>
        <TouchableOpacity
          style={styles.giftIcon(isGiftDisable)}
          disabled={isGiftDisable}
          hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
          onPress={() => {
            setTimeout(() => {
              this.isRecentlyTapped = false
            }, 2000);

            if (this.isRecentlyTapped) {
              return
            }

            this.isRecentlyTapped = true
            if (Utils.checkData(item.productId)) {
              let feedData = { storyID: panelItems.storyID, action: panelItems.action ? panelItems.action : panelItems.pageURL }
              this.props.sendGift(item, feedData)
            } else {
              this.props.likeDislikeFeed(isLiked, panelItems, index)
            }
          }}>
          <Image source={reactionName.toLowerCase() == Constants.LIKE.toLowerCase() ? (isLiked ? require('../../../assets/like_fill.png') : require('../../../assets/like_unfilled.png')) : { uri: image }} style={styles.icon} />
          <Text style={[this.context.pBold, { marginTop: 4 }]}>{reactionName.toLowerCase() == Constants.LIKE.toLowerCase() ? (isLiked ? unLikeTitle : title) : title}</Text>
          <Text style={this.context.p}>{amount}</Text>
        </TouchableOpacity>

      </View>
    )
  }
}

GiftActionSheetItem.contextType = ThemeContext

const styles = StyleSheet.create({
  mainContainer: {
    width: dimensions.width / 3,
    marginTop: scale(20)
  },
  giftIcon: (isGiftDisable) => {
    return {
      alignItems: 'center',
      tintColor: Color.BOTTOM_ICON_TINT_COLOR,
      opacity: isGiftDisable ? 0.2 : 1
    }
  },
  icon: {
    width: 50,
    height: 50
  }
})
