import React, { Component } from 'react';
import { TouchableOpacity, View, Image, StyleSheet, Text } from 'react-native';
import FloatingHearts from './FloatingHearts';
import { scale } from 'react-native-size-matters';
import { Color } from '../config/Color';
import { Utils } from '../config/Utils';
import { ThemeContext } from '../Contexts';

const options = {
  enableVibrateFallback: true,
  ignoreAndroidSystemSettings: false
};

class CommentLikeButtonView extends Component {

  constructor(props) {
    super(props)

    this.state = {
      showHearts: false,
    }
    this.renderHeartShape = this.renderHeartShape.bind(this)
  }

  renderHeartShape() {
    return (
      <Image source={require("../../assets/white_filled_heart.png")} style={{ tintColor: Color.RED_BACKGROUND_COLOR }} />
    )
  }


  render() {
    let { pageLikeCount, showCountSuffix = true } = this.props

    pageLikeCount = (!pageLikeCount || pageLikeCount < 0) ? 0 : pageLikeCount;
    let formatPageLikeCount = Utils.formatViewCount(pageLikeCount)
    return (
      <TouchableOpacity style={[{ flex: 1, flexDirection: 'row', justifyContent: 'space-between' }, this.props.parentStyle]}
        onPress={() => {
          this.props.onPress()
        }}
        disabled={pageLikeCount > 0 ? false : true}>
        <Text style={[this.context.bodyMini, styles.followerCountText]}>{showCountSuffix ? formatPageLikeCount : ""} </Text>
        {this.state.showHearts &&
          <View style={{ width: 200 }}>
            <FloatingHearts {...this.props} count={pageLikeCount ? pageLikeCount > 30 ? 20 : pageLikeCount : 5} renderCustomShape={this.renderHeartShape} />
          </View>
        }
        <Image
          style={[styles.navigationIcons, this.props.iconStyle, this.props.containerStyles]}
          source={this.props.isFollowing ? require('../../assets/like_fill.png') : require('../../assets/like.png')}
        />
      </TouchableOpacity >
    );
  }
}

CommentLikeButtonView.contextType = ThemeContext

const styles = StyleSheet.create({
  navigationIcons: {
    width: scale(25),
    height: scale(25)
  },
  followerCountText: {
    paddingLeft: scale(15)
  },
  followerText: {
  },
})

export default CommentLikeButtonView;