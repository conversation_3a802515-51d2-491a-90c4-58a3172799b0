import React, { useContext, useEffect, useRef, useState } from 'react'
import {
  StyleSheet,
  View,
  Animated,
  Image,
  Text,
  TouchableOpacity,
  Platform
} from 'react-native'
import { settings } from '../config/settings'
import { Color } from '../config/Color'
import { Utils } from '../config/Utils'
import { navigationStyle } from '../config/styles'
import SessionManager from '../config/SessionManager'
import { Constants } from '../config/Constants'
import NavigationService from '../config/NavigationService'
import { DimenContext, ThemeContext } from '../Contexts'
import FirebaseManager from '../config/FirebaseManager'
import UserSession from '../config/UserSession'
import BadgesView from './BadgesView'
import ImagePlaceHolder from './ImagePlaceHolder'

export default BottomTabBar = (props) => {

  const [errorInImageLoading, setErrorInImageLoading] = useState(false)

  const context = useContext(ThemeContext)
  const dimenContext = useContext(DimenContext)
  const openChapter = props.openChapter
  const onShowAllComicSwitchTap = props.onShowAllComicSwitchTap
  const pathUrl = props.pathUrl
  const scrollToTopOrReload = props.scrollToTopOrReload
  const hasAnySubscription = SessionManager.instance.hasAnySubscriptionPurchase()
  const isAtHomePage = Utils.isHomeURL(pathUrl)
  const isHomeTabSelected = isAtHomePage && SessionManager.instance.getShowAllComics()
  const isFollowingTabSelected = Utils.isHomeURL(pathUrl) && !SessionManager.instance.getShowAllComics()
  const isDirectoryTabSelected = Utils.isDirectoryPageURL(pathUrl)
  const currentRoute = NavigationService.getCurrentRoute()
  const isUserLoggedIn = UserSession.instance.isLoggedInUser()
  const badges = props.userDetails && props.userDetails.badges
  const profileImageUrl = props.userDetails?.photoURL
  const profileDisplayName = props.userDetails?.displayName
  let isLoggedInUserDetailsEmpty = isUserLoggedIn && UserSession.instance.isUserDetailsEmpty()
  let userUnreadNotifications = UserSession.instance.getUnreadNotifications()
  let unreadNotifCount = userUnreadNotifications > 99 ? "99+" : userUnreadNotifications
  var isNotificationsTabSelected = false
  if (currentRoute) {
    isNotificationsTabSelected = currentRoute == Constants.NOTIFICATIONS_SCREEN
  } else {
    FirebaseManager.instance.recordError(null, new Error("currentRoute value is null in Bottom Tab bar"))
  }
  const isAndroidDevice = Platform.OS == "android" ? true : false
  const isCurrHomePage = pathUrl && Utils.isHomeURL(pathUrl)

  const animatedTranslateY = useRef(new Animated.Value(0)).current;
  useEffect(() => {
    Animated.timing(animatedTranslateY, {
      toValue: props.hideBottomBar ? 200 : 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  }, [props.hideBottomBar]);

  const onImageError = () => {
    setErrorInImageLoading(true)
  }

  return (
    <View style={styles.containerView}>
      <Animated.View
        style={{
          transform: [{ translateY: animatedTranslateY }],
          backgroundColor: context.colors.textBold,
          zIndex: 1
        }}>
        {isAtHomePage &&
          <View>
            <View style={styles.allFollowingFreeContainer}>
              <View style={styles.flexContainer}>
                <TouchableOpacity
                  style={[styles.homeTextsView, styles.allView]}
                  onPress={() => {
                    if (onShowAllComicSwitchTap) {
                      if (isHomeTabSelected && scrollToTopOrReload) {
                        scrollToTopOrReload()
                      } else {
                        onShowAllComicSwitchTap(true)
                      }
                    }
                  }}>
                  <Text style={[isHomeTabSelected ? context.pBold : context.p, { marginBottom: isHomeTabSelected ? 10 : 13, color: context.colors.textInverse }]}>{Constants.ALL}</Text>
                  {isHomeTabSelected && <View style={[styles.separatorStyle, { width: 60, backgroundColor: context.colors.textInverse }]} />}
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.homeTextsView, styles.followingView]}
                  onPress={() => {
                    if (onShowAllComicSwitchTap) {
                      if (isFollowingTabSelected && scrollToTopOrReload) {
                        scrollToTopOrReload()
                      } else {
                        onShowAllComicSwitchTap(false)
                      }
                    }
                  }}>
                  <Text style={[isFollowingTabSelected ? context.pBold : context.p, { marginBottom: isFollowingTabSelected ? 10 : 13, color: context.colors.textInverse }]}>{Constants.FOLLOWING}</Text>
                  {isFollowingTabSelected && <View style={[styles.separatorStyle, { width: 100, backgroundColor: context.colors.textInverse }]} />}
                </TouchableOpacity>
              </View>
              {!hasAnySubscription &&
                <View style={styles.flexContainer}>
                  <TouchableOpacity
                    style={[styles.homeTextsView, styles.freePremiumPlanView, { flexDirection: 'row', alignItems: 'flex-start' }]}
                    onPress={() => { props.navigateToSubscribePage() }}>
                    <Text style={[context.bodyMini, { color: context.colors.separators }]}>{isUserLoggedIn ? Constants.FREE_PLAN : Constants.NON_SIGNED_IN_USER}</Text>
                    {isUserLoggedIn && <Text style={[context.bodyMini, styles.signinTextView, { marginLeft: 10, color: context.colors.separators }]}>{Constants.UPGRADE}</Text>}
                  </TouchableOpacity>
                  {!isUserLoggedIn &&
                    <TouchableOpacity
                      style={[styles.homeTextsView, styles.freePremiumPlanView, { marginLeft: 10 }]}
                      onPress={() => { props.navigateToSignIn() }}>
                      <Text style={[context.bodyMini, styles.signinTextView, { color: context.colors.separators }]}>{Constants.SIGN_IN}</Text>
                    </TouchableOpacity>}
                </View>
              }
              {hasAnySubscription &&
                <TouchableOpacity
                  style={[styles.homeTextsView, styles.freePremiumPlanView, styles.flexContainer, { alignItems: 'flex-start', paddingTop: 11 }]}
                  onPress={() => { props.navigateToSubscribePage() }}>
                  <BadgesView badgesStyle={styles.badgeView} badges={badges} badgeType={"subscriber"} />
                  <Text style={[context.bodyMini, { color: context.colors.separators, marginLeft: 5 }]}>{Constants.PREMIUM_PLAN}</Text>
                </TouchableOpacity>
              }
            </View>
            <View style={[{ height: 0.25, backgroundColor: context.colors.textInverse }]} />
          </View>
        }
        <View style={styles.footerContainer(isAndroidDevice, dimenContext.navbarHeight)}>
          <View style={styles.mainHeaderContainer}>
            {!isCurrHomePage &&

              <TouchableOpacity
                hitSlop={styles.iconArea}
                onPress={() => { props.onBackPress ? props.onBackPress() : null }}>
                <View>
                  <Image style={styles.backIconView} source={require('../../assets/back_arrow_icon.png')} />
                </View>
              </TouchableOpacity>}

            <TouchableOpacity
              hitSlop={styles.iconArea}
              onPress={() => {
                if (onShowAllComicSwitchTap) {
                  if ((isHomeTabSelected || isFollowingTabSelected) && scrollToTopOrReload) {
                    scrollToTopOrReload()
                  } else {
                    if (props.navigateToStackTop) {
                      props.navigateToStackTop()
                    }
                  }
                }
              }}>
              <View>
                <Image style={styles.navigationIcons} source={(isHomeTabSelected || isFollowingTabSelected) ? require('../../assets/home_filled_icon.png') : require('../../assets/home_icon.png')} />
                <Text style={[context.menuLabel, (isHomeTabSelected || isFollowingTabSelected) && { color: context.colors.textInverse }]}>{Constants.HOME}</Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              hitSlop={styles.iconArea}
              onPress={() => {
                props.openWebView(settings.tinyviewShopURL)
              }}>
              <View>
                <Image style={styles.navigationIcons} source={require('../../assets/shop_icon.png')} />
                <Text style={context.menuLabel}>{Constants.SHOP}</Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              hitSlop={styles.iconArea}
              onPress={() => {
                if (openChapter) {
                  if (isDirectoryTabSelected && scrollToTopOrReload) {
                    scrollToTopOrReload()
                  } else {
                    openChapter(settings.DIRECTORY_COMIC_URL)
                  }
                }
              }}>
              <View>
                <Image style={styles.navigationIcons} source={isDirectoryTabSelected ? require('../../assets/directory_filled_icon.png') : require('../../assets/directory_icon.png')} />
                <Text style={[context.menuLabel, isDirectoryTabSelected && { color: context.colors.textInverse }]}>{Constants.ALL_SERIES}</Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              hitSlop={styles.iconArea}
              onPress={() => {
                if (openChapter) {
                  if (isNotificationsTabSelected && scrollToTopOrReload) {
                    scrollToTopOrReload()
                  } else {
                    props.navigation.navigate(Constants.NOTIFICATIONS_SCREEN)
                  }
                }
              }}>
              <View>
                <View style={styles.notifAlignView}>
                  <Image style={styles.navigationIcons} source={isNotificationsTabSelected ? require('../../assets/notifications_filled_icon.png') : require('../../assets/notifications_icon_white.png')} />
                  {userUnreadNotifications > 0 &&
                    <View style={[styles.notifCountView(unreadNotifCount), { borderColor: context.colors.logoRed, backgroundColor: context.colors.logoRed }]}>
                      <Text style={[styles.unreadNotifText, { color: context.colors.textInverse }]}>{unreadNotifCount}</Text>
                    </View>
                  }
                </View>
                <Text style={[context.menuLabel, isNotificationsTabSelected && { color: context.colors.textInverse }]}>{Constants.NOTIFICATIONS}</Text>
              </View>
            </TouchableOpacity>
            <TouchableOpacity
              hitSlop={styles.iconArea}
              onPress={() => props.navigation.toggleDrawer()}>
              <View>
                <View style={styles.notifAlignView}>
                  {Utils.checkData(profileImageUrl) && !errorInImageLoading
                    ?
                    <Image style={[styles.navigationIcons, styles.userImageView, { borderColor: context.colors.textInverse }]} resizeMode="cover" source={{ uri: profileImageUrl }} onError={() => { onImageError() }} />
                    :
                    Utils.checkData(profileDisplayName)
                      ?
                      <ImagePlaceHolder
                        backgroundColor={Color.PROFILE_PLACE_HOLDER_BG}
                        showCircularBorder={true}
                        textColor={Color.COMMENT_TEXT_COLOR}
                        size={22}
                        style={[styles.navigationIcons, styles.userImageView, { borderColor: context.colors.textInverse }]}
                        type={'circle'}>{profileDisplayName}</ImagePlaceHolder>
                      :
                      <Image style={[styles.navigationIcons]} source={require('../../assets/account_icon.png')} />
                  }
                  {isLoggedInUserDetailsEmpty && <Image style={[styles.emptyProfileIcon]} source={require('../../assets/filled_exclamation_inside_circle_icon.png')} />}
                </View>
                <Text style={context.menuLabel}>{Constants.ACCOUNT}</Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>
      </Animated.View>
    </View>
  )
}

const styles = StyleSheet.create({
  navigationIcons: {
    width: 22,
    height: 22,
    alignSelf: 'center',
    marginBottom: 3
  },
  backIconView: {
    width: 20,
    height: 20,
    alignSelf: 'center',
    marginTop: 3
  },
  footerContainer: (isAndroidDev, navbarHeight = 0) => {
    return {
      height: navigationStyle.navHeight + navbarHeight,
      justifyContent: 'center',
      borderRadius: 0,
      borderWidth: 0,
      borderTopWidth: 0,
      paddingBottom: isAndroidDev ? navbarHeight : 10,
      elevation: 0
    }
  },
  allFollowingFreeContainer: {
    marginBottom: settings.isAndroidDevice ? -3 : -6.5,
    marginLeft: 15,
    marginRight: 15,
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  footerSeprator: {
    height: 1,
    backgroundColor: Color.BOTTOM_NAVBAR_BORDER_COLOR
  },
  mainHeaderContainer: {
    flexDirection: 'row',
    marginLeft: 15,
    marginRight: 15,
    justifyContent: 'space-between'
  },
  emptyProfileIcon: {
    height: 18,
    width: 18,
    marginLeft: -9,
    marginTop: -4
  },
  notifCountView: (count) => {
    return {
      height: count == "99+" ? 17 : 20,
      width: count == "99+" ? 30 : 20,
      borderWidth: 1,
      borderRadius: 12.5,
      justifyContent: 'center',
      alignItems: 'center',
      position: 'absolute',
      right: count == "99+" ? -20 : -16,
      top: -6,
      paddingLeft: count == "99+" ? 2 : 0
    }
  },
  unreadNotifText: {
    fontStyle: 'normal',
    fontWeight: '400',
    fontSize: 11,
    lineHeight: 12
  },
  notifAlignView: {
    flexDirection: 'row',
    alignSelf: 'center'
  },
  iconArea: {
    left: 12,
    right: 12
  },
  separatorStyle: {
    height: 3,
    width: '120%',
    borderRadius: 10
  },
  homeTextsView: {
    paddingTop: 10,
    height: navigationStyle.secondaryBottomNavHeight,
    alignItems: 'center'
  },
  allView: {
    minWidth: 60,
    marginRight: 20
  },
  followingView: {
    minWidth: 100
  },
  flexContainer: {
    flexDirection: 'row',
  },
  freePremiumPlanView: {
    paddingTop: 12
  },
  signinTextView: {
    textDecorationLine: 'underline'
  },
  badgeView: {
    marginTop: 1
  },
  containerView: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0
  },
  userImageView: {
    borderWidth: 1,
    borderRadius: 20,
    overflow: 'hidden'
  }
});
