import React, { useEffect, useRef, useContext } from 'react';
import { View, Animated, StyleSheet } from 'react-native';
import { ThemeContext } from '../Contexts';

const ProgressBar = (props) => {

    const context = useContext(ThemeContext)
    const progress = useRef(new Animated.Value(0)).current;
    const progressValue = props.value

    useEffect(() => {
        Animated.timing(progress, {
            toValue: progressValue,
            duration: 500,
            useNativeDriver: false,
        }).start();
    }, [progressValue]);

    const fillWidth = progress.interpolate({
        inputRange: [0, 1],
        outputRange: ['0%', '100%'],
    });

    return (
        <View style={styles.container}>
            <View style={[styles.background, { backgroundColor: context.colors.chatBubbles }]}>
                <Animated.View
                    style={[styles.fill, { width: fillWidth, backgroundColor: context.colors.logoRed }]}
                />
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        height: 10,
        marginTop: 8,
        marginBottom: 8
    },
    background: {
        height: '100%',
        borderRadius: 10,
        overflow: 'hidden',
    },
    fill: {
        height: '100%',
        borderRadius: 10,
    },
});

export default ProgressBar;