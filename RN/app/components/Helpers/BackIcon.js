import React from 'react'
import { StyleSheet, TouchableOpacity, Animated } from "react-native";
import FastImage from "react-native-fast-image";
import { backButtonView, navigationStyle } from "../../config/styles";
import { Color } from "../../config/Color";

export default BackIcon = (props) => {

  const mainStyle = props.mainContainerStyle ? props.mainContainerStyle : styles.mainContainer 
    return (
        <Animated.View
          style={mainStyle}>
          <TouchableOpacity
            style={styles.innerContainer}
            onPress={() => { props.onIconPress ? props.onIconPress() : null }}>
            <FastImage
              style={backButtonView}
              source={require('../../../assets/floating_cross_icon.png')}
            />
          </TouchableOpacity>
        </Animated.View>
      )
}

const styles = StyleSheet.create({
    mainContainer: {
        position: 'absolute',
        left: navigationStyle.panelLeftRightMargin,
        bottom: navigationStyle.navHeight + navigationStyle.panelsTopMargin + 5,
        backgroundColor: Color.FLOATING_BACK_BUTTON_COLOR,        
    },
    innerContainer: {
        backgroundColor: 'white', 
        width: 40,
        height: 40,
        borderRadius: 20,
        justifyContent: "center",
        alignItems: "center",
    }    
})

