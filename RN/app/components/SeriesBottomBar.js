import React, { useEffect, useRef, useContext } from 'react'
import {
    StyleSheet,
    View,
    Animated,
    Image,
    Text,
    TouchableOpacity,
    Platform
} from 'react-native'
import { navigationStyle } from '../config/styles';
import { DimenContext, ThemeContext } from '../Contexts';
import { Constants } from '../config/Constants';
import FastImage from 'react-native-fast-image';
import { settings } from '../config/settings';
import { Utils } from '../config/Utils';

const SeriesBottomBar = (props) => {

    const { hideBottomBar, pathUrl, isSubscribed, continueReadingComic, comicData } = props;

    const context = useContext(ThemeContext);
    const animatedTranslateY = useRef(new Animated.Value(0)).current;

    useEffect(() => {
        if (hideBottomBar !== undefined) {
            handleTabBarAnimation();
        }
    }, [hideBottomBar]);

    const handleTabBarAnimation = () => {
        Animated.timing(animatedTranslateY, {
            toValue: hideBottomBar ? 200 : 0,
            duration: 200,
            useNativeDriver: true
        }).start();
    };

    const renderFollowButton = () => {
        return (
            <TouchableOpacity
                style={[styles.ButtonMainView, { backgroundColor: context.colors.logoRed, borderColor: context.colors.logoRed }]}
                onPress={() => {
                    props.onFollowPress(pathUrl, isSubscribed, false, false)
                }}>
                <FastImage
                    style={styles.followButtonIcon}
                    source={require('../../assets/plus_icon.png')} />
                <Text style={[context.bodyMini, { color: context.colors.textInverse }]}>{Constants.FOLLOW}</Text>
            </TouchableOpacity>
        );
    };

    const renderContinueReadButton = () => {
        const hasContinueReading = !Utils.isEmptyObject(continueReadingComic);
        const unReadCount = hasContinueReading && (continueReadingComic.unReadCount > 99) ? "99+" : continueReadingComic.unReadCount;
        const seriesFirstEpisode = hasContinueReading && continueReadingComic.isFirstEpisode;

        return (
            <TouchableOpacity
                style={[styles.ButtonMainView, { borderColor: context.colors.separators, backgroundColor: hasContinueReading ? context.colors.textBold : context.colors.textBold, opacity: hasContinueReading ? 1 : 0.3 }]}
                onPress={() => { onNavButtonTap(continueReadingComic) }}
                disabled={!hasContinueReading}>
                <Text style={[context.bodyMini, { color: hasContinueReading ? context.colors.textInverse : context.colors.textInverse }]}>{seriesFirstEpisode ? Constants.FIRST_EPISODE : Constants.NEXT_EPISODE}</Text>
                {hasContinueReading && !seriesFirstEpisode &&
                    <View style={[styles.contiReadCountView(unReadCount), { borderColor: context.colors.logoRed, backgroundColor: context.colors.logoRed }]}>
                        <Text style={[styles.contiReadText, { color: context.colors.textInverse }]}>{unReadCount}</Text>
                    </View>
                }
            </TouchableOpacity>
        );
    };

    const onNavButtonTap = (item) => {
        if (item.actionType == "website") {
            const storyID = item.storyID ? item.storyID : null;
            const seriesName = item.series ? item.series : null;
            onCommentPress(storyID, item.action, seriesName);
        } else if (item.action || item.userId) {
            props.openChapter(item.action, item);
        };
    };

    const onCommentPress = (storyID = null, comicAction = null, seriesName = null) => {
        let storyData = { storyID: storyID, refType: Constants.STORY, action: comicAction, series: seriesName };
        props.onShowCommentPress(Constants.COMMENT, null, null, null, storyData);
    };

    const isAndroidDevice = Platform.OS == "android" ? true : false;

    return (
        <View style={styles.containerView}>
            <Animated.View
                style={{
                    transform: [{ translateY: animatedTranslateY }],
                    backgroundColor: context.colors.textBold,
                    zIndex: 1
                }}>
                <DimenContext.Consumer>
                    {dimen => (
                        <View style={styles.mainContainer(isAndroidDevice, dimen.navbarHeight)}>
                            <View style={[styles.mainIconView, { flex: 0.55 }]}>
                                <TouchableOpacity
                                    hitSlop={styles.iconArea}
                                    onPress={() => { props.onBackPress() }}>
                                    <Image style={styles.iconsView} source={require('../../assets/back_arrow_icon.png')} />
                                </TouchableOpacity>
                                <View style={styles.buttonHomeView}>
                                    {!isSubscribed && renderFollowButton()}
                                    {isSubscribed && renderContinueReadButton()}
                                </View>
                            </View>

                            <View style={[styles.mainIconView, styles.navIconsView]}>
                                <TouchableOpacity
                                    hitSlop={styles.iconArea}
                                    onPress={() => { props.navigateToStackTop() }}>
                                    <Image style={styles.iconsView} source={require('../../assets/home_icon.png')} />
                                </TouchableOpacity>
                                <TouchableOpacity
                                    hitSlop={styles.iconArea}
                                    onPress={() => { props.onIconPress(Constants.REPOST, comicData) }}>
                                    <Image style={styles.iconsView} source={require('../../assets/share_icon_white.png')} />
                                </TouchableOpacity>
                                <TouchableOpacity
                                    hitSlop={styles.iconArea}
                                    onPress={() => { props.openChapter(settings.DIRECTORY_COMIC_URL) }}>
                                    <Image style={styles.iconsView} source={require('../../assets/directory_icon.png')} />
                                </TouchableOpacity>
                                <TouchableOpacity
                                    hitSlop={styles.iconArea}
                                    onPress={() => { props.navigation.toggleDrawer() }}>
                                    <Image style={styles.iconsView} source={require('../../assets/account_icon.png')} />
                                </TouchableOpacity>
                            </View>
                        </View>
                    )}
                </DimenContext.Consumer>
            </Animated.View>
        </View>
    );
};

const styles = StyleSheet.create({
    mainContainer: (isAndroidDev, navbarHeight = 0) => {
        return {
            flex: 1,
            height: navigationStyle.navHeight + navbarHeight,
            marginLeft: 15,
            marginRight: 15,
            paddingBottom: isAndroidDev ? navbarHeight : 10,
            flexDirection: 'row',
            alignItems: 'center'
        }
    },
    iconsView: {
        height: 20,
        width: 20
    },
    mainIconView: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    separatorStyle: {
        height: 1,
        width: '100%'
    },
    ButtonMainView: {
        height: 32,
        paddingLeft: 8,
        paddingRight: 8,
        borderRadius: 6,
        borderWidth: 1,
        flexDirection: 'row',
        alignItems: 'center'
    },
    followButtonIcon: {
        height: 12,
        width: 12,
        marginRight: 4
    },
    navIconsView: {
        flex: 0.45,
        justifyContent: 'space-between'
    },
    buttonHomeView: {
        marginLeft: 20
    },
    viewComicButton: {
        borderWidth: 1,
        borderRadius: 6,
        flexDirection: 'row',
        alignItems: 'center'
    },
    contiReadCountView: (count) => {
        return {
            height: count == "99+" ? 17 : 20,
            width: count == "99+" ? 30 : 20,
            borderWidth: 1,
            borderRadius: 12,
            justifyContent: 'center',
            alignItems: 'center',
            position: 'absolute',
            right: count == "99+" ? -20 : -12,
            top: -8
        }
    },
    contiReadText: {
        fontStyle: 'normal',
        fontWeight: '400',
        fontSize: 11,
        lineHeight: 12
    },
    iconArea: {
        left: 12,
        right: 12
    },
    containerView: {
        position: 'absolute',
        left: 0,
        right: 0,
        bottom: 0
    }
});

export default SeriesBottomBar;
