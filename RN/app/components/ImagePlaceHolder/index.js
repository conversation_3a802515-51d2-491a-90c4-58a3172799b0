import React, { Component } from 'react'
import { View, Text, Platform } from 'react-native'
import initials from 'initials'
import { Color } from '../../config/Color'
const runes = require('runes')

function capitalizeFirstLetter(string) {
  var format = /[^a-zA-Z ]/g
  return string.replace(format, "").toUpperCase()
}

class ImagePlaceHolder extends Component {
  constructor(props) {
    super(props)
  }

  render() {
    let {
      children,
      size = 60,
      backgroundColor = '#333',
      textColor = '#fff',
      insidetextStyle = {},
      type,
      style,
      showCircularBorder = false,
    } = this.props

    if (typeof children !== 'string') throw new Error('Children must be only a string \n Ex: Technology')

    let childs = runes(children) ? runes(children) : []
    if (childs.length == 0) return null

    let abbr = initials(capitalizeFirstLetter(childs[0]))
    let secondLtr = ''
    for (const key in childs) {
      let letter = childs[key]
      if (letter == " " && childs[parseInt(key) + 1]) {
        secondLtr = initials(capitalizeFirstLetter(childs[parseInt(key) + 1]))
        break
      }
    }

    abbr = abbr + secondLtr

    if (typeof size !== 'number') throw new Error('Size must be an integer')

    let containerStyle = {
      width: size,
      height: size,
      borderColor: showCircularBorder ? Color.LIGHT_GREY : 'transparent',
      borderWidth: showCircularBorder ? 0.5 : 0,
      backgroundColor: backgroundColor,
      alignItems: 'center',
      justifyContent: 'center',
    }
    let textStyle = {
      color: textColor,
      fontSize: size / 3,
      fontWeight: 'bold',
      letterSpacing: 0.75,
      marginLeft: Platform.OS == 'ios' ? 2 : 0,
    }

    // Hexagon style inspired from https://codedaily.io/tutorials/22/The-Shapes-of-React-Native
    let hexagon = {
      height: size / 2
    }

    let hexagonAfter = {
      position: 'absolute',
      bottom: - size / 3,
      left: 0,
      width: 0,
      height: 0,
      borderStyle: 'solid',
      borderLeftWidth: size / 2,
      borderLeftColor: 'transparent',
      borderRightWidth: size / 2,
      borderRightColor: 'transparent',
      borderTopWidth: size / 3,
      borderTopColor: backgroundColor
    }
    let hexagonBefore = {
      position: 'absolute',
      top: -size / 3,
      left: 0,
      width: 0,
      height: 0,
      borderStyle: 'solid',
      borderLeftWidth: size / 2,
      borderLeftColor: 'transparent',
      borderRightWidth: size / 2,
      borderRightColor: 'transparent',
      borderBottomWidth: size / 3,
      borderBottomColor: backgroundColor
    }
    if (type == 'circle') {
      containerStyle.borderRadius = size / 2
      return (
        <View style={[style, containerStyle]}>
          <Text style={[textStyle, insidetextStyle]}
            adjustsFontSizeToFit={true}>{abbr}</Text>
        </View>
      );
    }
    else if (type == 'hexagon') {
      return (
        <View style={[style, containerStyle, hexagon]}>
          <View style={hexagonBefore} />
          <View style={hexagonAfter} />
          <Text style={textStyle}>{abbr}</Text>
        </View>
      );
    }
    else {
      return (
        <View style={[style, containerStyle]}>
          <Text style={textStyle}
            adjustsFontSizeToFit={true}>{abbr}</Text>
        </View>
      );
    }
  }
}

export default ImagePlaceHolder;