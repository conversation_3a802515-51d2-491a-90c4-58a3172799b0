import React, { Component } from 'react'
import PropTypes from 'prop-types'
import {
  StyleSheet,
  View,
  FlatList,
  Image,
  Dimensions,
  Platform,
  ActionSheetIOS,
  LogBox,
  Animated,
  RefreshControl,
} from 'react-native'
import { Color } from '../config/Color'
import { navigationStyle, loadingViewStyle } from '../config/styles'
import SessionManager from '../config/SessionManager'
import { Utils } from '../config/Utils';
import ComicHeaderView from './ComicHeaderView'
import ErrorView from './ErrorView'
import ComicPanel from './ComicPanel/ComicPanel'
import DirectoryPanel from './ComicPanel/DirectoryPanel'
import StoryPanel from './ComicPanel/StoryPanel'
import { Constants } from '../config/Constants'
import { settings } from '../config/settings'
import CardPanel from './ComicPanel/CardPanel'
import EpisodePanel from './friendsPackage/panels/EpisodePanel'
import SubscribersPanel from './ComicPanel/SubscribersPanel'
import ListPanel from './ComicPanel/ListPanel'
import NavigationPanel from './ComicPanel/NavigationPanel'
import UserSession from '../config/UserSession'
import { ThemeContext } from '../Contexts'

let dimensions = Dimensions.get('window')

export default class ReaderView extends Component {

  constructor(props) {
    super(props)

    this.state = {
      showRefresh: true,
    }

    this.restorePurchase = {
      title: 'ALREADY BOUGHT ON ANOTHER DEVICE?',
      description: 'CLICK BELOW TO RESTORE YOUR PURCHASES.',
      localizedPrice: 'RESTORE PURCHASES'
    }

    // this.getIAPProduts = this.getIAPProduts.bind(this);
    // this.fetchIAPProducts = this.fetchIAPProducts.bind(this);
    this.updateState = this.updateState.bind(this)
    this.editPress = this.editPress.bind(this)
    this.reloadChapter = this.reloadChapter.bind(this)
    this._keyExtractor = this._keyExtractor.bind(this)
    this.onLikePress = this.onLikePress.bind(this)
    this.onFollowPress = this.onFollowPress.bind(this)
    this.onSubscribePanelTap = this.onSubscribePanelTap.bind(this)
    this.shareImage = this.shareImage.bind(this)
    this.updateItemHeight = this.updateItemHeight.bind(this)
    this.renderItem = this.renderItem.bind(this)
    this.renderHeader = this.renderHeader.bind(this)
    this._listEmptyComponent = this._listEmptyComponent.bind(this)
    this.onViewableItemsChanged = this.onViewableItemsChanged.bind(this)
    this.onRefresh = this.onRefresh.bind(this)
    this._footerPlaceHolderComponent = this._footerPlaceHolderComponent.bind(this)
    this._onEndReached = this._onEndReached.bind(this)

    this.isRefreshingInProgress = false
    this.isProductsLoadingInProgress = false

    this.isComicURL = null
    this.isHomeURL = null
    this._isMounted = true
    LogBox.ignoreAllLogs(true)

    this.flatlist = null
    this.fetchedPages = []

    this.aspectRatio = dimensions.width / Constants.LOADING_GIF_DIMEN.width
    this.currentBGColor = null
    this.lastScrollOffset = 0;
    this.scrollY = new Animated.Value(0)
  }

  componentDidMount() {
    this._isMounted = true;
    this.isAnyUserAlertsEnabled = UserSession.instance.isAnyUserAlertsEnabled()
  }

  componentWillUnmount() {
    this._isMounted = false;
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    this.isComicURL = Utils.isComicURL(this.props.pathUrl)
    this.isHomeURL = Utils.isHomeURL(this.props.pathUrl)

    if (this.props.errorRes && this.props.error.urlToCheck == this.props.pathUrl) {
      this.updateState({ showRefresh: false })
    } else if (this.state.showRefresh && nextProps.comicData && nextProps.comicData.panels) {
      this.updateState({ showRefresh: false })
    } else if (nextProps.comicData == null && !this.state.showRefresh) {
      this.updateState({ showRefresh: true })
    }

    if (!nextProps.comicData || !nextProps.comicData.panels || nextProps.comicData.panels.length == 0) {
      this.fetchedPages = []
    }

    if (this.currentBGColor == null && this.props.pathUrl) {
      const series = Utils.getChannelName(this.props.pathUrl)
      const seriesStyle = Utils.getSeriesSpecificStyle(series)
      this.currentBGColor = seriesStyle.backgroundColor
    }

    this.isAnyUserAlertsEnabled = UserSession.instance.isAnyUserAlertsEnabled()
  }

  updateState(newState, callback = null) {
    if (this._isMounted) {
      this.setState({ ...newState }, () => {
        if (callback) {
          callback()
        }
      })
    } else {
      Utils.log("Componet destroyed")
    }
  }

  reloadChapter() {
    this.props.reloadChapter()
  }

  onLikePress(clickedPanelDetails) {
    this.props.onLikePress(clickedPanelDetails)
  }

  onFollowPress(seriesURL = this.props.pathUrl, isFollowing = this.state.isLiked, forceFollowSeries = false, forSync = false) {
    this.props.onFollowPress(seriesURL, isFollowing, forceFollowSeries, forSync)
  }

  editPress(panelItem, commentItem, selectedIndex) {
    this.props.onShowCommentPress(Constants.EDIT, panelItem, selectedIndex, commentItem)
  }

  onSubscribePanelTap() {
    this.props.navigateToSubscribePage()
  }

  shareImage(item) {
    if (Platform.OS === 'ios' && parseInt(Platform.Version, 10) <= 12) {
      ActionSheetIOS.showActionSheetWithOptions(
        {
          options: ["Cancel", "Share"],
          cancelButtonIndex: 0
        },
        buttonIndex => {
          if (buttonIndex === 0) {

          } else if (buttonIndex === 1) {
            this.props.shareImage(item)
          }
        }
      )
    } else {
      this.props.shareImage(item)
    }
  }

  updateItemHeight(index, height) {
    // const flatListData = this.props.comicData ? this.props.comicData.panels : []
    // if (flatListData.length < 0) { return }
    // const panelData = flatListData[index];
    // if (panelData != null) {
    //   panelData.viewHeight = height;
    // }
  }

  renderItem(item) {
    const valueProps = {
      getStoryDetails: this.props.getStoryDetails, getMultiplePanelInfo: this.props.getMultiplePanelInfo, isHomeURL: this.isHomeURL,
      onFlagCommentTap: this.props.onFlagCommentTap, onEditPress: this.editPress, alerts: this.props.alerts,
      navigateToSubscribePage: this.props.navigateToSubscribePage, shareImage: this.props.shareImage,
      deleteComment: this.props.deleteComment, navigateToUserProfile: this.props.navigateToUserProfile, isHideReactionComponentIcons: this.props.isHideReactionComponentIcons, requestPurchase: this.props.requestPurchase,
      restorePurchase: this.props.restorePurchase, isFollowing: this.state.isLiked, likeDislikeFeed: this.props.likeDislikeFeed, onCommentLikeCountPress: this.props.onCommentLikeCountPress, onSeriesHomeClicked: this.props.onSeriesHomeClicked,
      onLikePress: this.props.onLikePress, onIconPress: this.props.onIconPress, seriesHomeName: this.props.seriesHomeName, seriesHomeProfile: this.props.seriesHomeProfile, onShowCommentPress: this.props.onShowCommentPress,
      likeDislikeComment: this.props.likeDislikeComment, onCountPress: this.props.onCountPress, comicData: this.props.comicData, getStoryComments: this.props.getStoryComments, currentPageStatus: this.props.currentPageStatus,
      item: item, isComicURL: this.isComicURL, openHomePage: this.props.openHomePage, openSeriesHomePage: this.props.openSeriesHomePage, onFollowPress: this.props.onFollowPress, openChapter: this.props.openChapter, recordPageView: this.props.recordPageView,
      openWebView: this.props.openWebView, pathUrl: this.props.pathUrl, multiplePanelStatus: this.props.multiplePanelStatus, isSubscribed: this.props.isSubscribed, onReplyPress: this.props.onReplyPress,
      getSubscribersProgress: this.props.getSubscribersProgress, updateItemHeight: this.updateItemHeight, userDetails: this.props.userDetails, editRepost: this.props.editRepost,
      deleteRepost: this.props.deleteRepost, share: this.props.share, navigateToInfluenceScreen: this.props.navigateToInfluenceScreen, redeemInfluencePoint: this.props.redeemInfluencePoint, onRefresh: this.onRefresh,
      isAlreadyPurchasedFromInfluence: this.props.isAlreadyPurchasedFromInfluence, getNavigationComics: this.props.getNavigationComics, navigateToSignUp: this.props.navigateToSignUp, navigateToInviteBottomSheet: this.props.navigateToInviteBottomSheet,
      onShowAllComicSwitchTap: this.props.onShowAllComicSwitchTap, getFeedEpisodes: this.props.getFeedEpisodes, onChangeSeriesTab: this.props.onChangeSeriesTab, getUnlimitedComicAccessSheet: this.props.getUnlimitedComicAccessSheet,
      getLockedComicActionSheet: this.props.getLockedComicActionSheet, getContinueReadingComic: this.props.getContinueReadingComic, continueReadingComic: this.props.continueReadingComic, continueReadPanelEpisodes: this.props.continueReadPanelEpisodes, onBlockUserTap: this.props.onBlockUserTap,
      hasMoreData: this.props.hasMoreData, onEndReached: this._onEndReached, showFooterPlaceHolder: this.props.showFooterPlaceHolder, loadingProductId: this.props.loadingProductId
    }
    if (!item.item) {
      return null;
    }

    if (item.item) {
      if (item.item.template && item.item.template == "directory") {
        return (
          <DirectoryPanel {...valueProps} showComments={false} />
        )
      } else if (item.item.template && (item.item.template == "list")) {
        return (
          <ListPanel {...valueProps} />
        )
      } else if (item.item.template && (item.item.template == "toc" || item.item.template == "story")) {
        return (
          <StoryPanel {...valueProps} />
        )
      } else if (item.item.template && (item.item.template == "card")) {
        return (
          <CardPanel {...valueProps} />
        )
      } else if (item.item.template && (item.item.template == "carousel" || item.item.template == "episodes")) {
        return (
          <EpisodePanel {...valueProps} comicFeedEpisodes={this.props.comicFeedEpisodes} getSeriesCarousel={this.props.getSeriesCarousel} />
        )
      } else if (item.item.template && item.item.template == Constants.SUBSCRIBERS_TEMPLATE) {
        return (
          <SubscribersPanel {...valueProps} />
        )
      } else if (item.item.template && item.item.template == 'navigation') {
        return (
          <NavigationPanel {...valueProps} comicFeedEpisodes={this.props.comicFeedEpisodes} isChannelSubscribed={this.props.isSubscribed()} isAnyAlertsEnabled={this.isAnyUserAlertsEnabled} />
        )
      } else if ((item.item.template && (item.item.template == Constants.BONUS || item.item.template == "newsfeed" || item.item.template == Constants.TEMPLATE_TYPE_STORIES)) || !item.item.template) {
        return (
          <ComicPanel {...valueProps} isZoomPanEnable={true} showComments={this.isComicURL} />
        )
      } else {
        return null
      }
    }
  }

  renderHeader() {
    const valueProps = {
      alerts: this.props.alerts, seriesHomeName: this.props.seriesHomeName, seriesHomeProfile: this.props.seriesHomeProfile, onShowCommentPress: this.props.onShowCommentPress, currentPageStatus: this.props.currentPageStatus, pathUrl: this.props.pathUrl, isLoading: this.props.isLoading, onSeriesHomeClicked: this.props.onSeriesHomeClicked,
      onCountPress: this.props.onCountPress, onLikePress: this.props.onLikePress, onIconPress: this.props.onIconPress, comicData: this.props.comicData, errorObj: this.props.errorRes, openChapter: this.props.openChapter, restorePurchase: this.props.restorePurchase, navigateToSignUp: this.props.navigateToSignUp,
      navigateToSubscribePage: this.props.navigateToSubscribePage, openWebView: this.props.openWebView, recordPageView: this.props.recordPageView, share: this.props.share, isSubscribed: this.props.isSubscribed(), onFollowPress: this.onFollowPress, isFollowing: this.state.isLiked, isComicURL: this.isComicURL, isHomeURL: this.isHomeURL,
      isHideReactionComponentIcons: this.props.isHideReactionComponentIcons, onRefresh: this.onRefresh, getUnlimitedComicAccessSheet: this.props.getUnlimitedComicAccessSheet
    }
    return (
      <ComicHeaderView {...valueProps} />
    )
  }

  _listEmptyComponent() {
    if (this.props.errorRes) {
      return (
        <ErrorView errorObj={this.props.errorRes} refresh={this.props.reloadChapter} goToHome={this.props.openHomePage} />
      )
    }
  }

  onViewableItemsChanged({ viewableItems, changed }) {
    if (Utils.isChannelURL(this.props.pathUrl)) {
      return
    }

    let comicPanels = []
    let storyPanels = []
    if (viewableItems.length > 0) {
      for (const index in viewableItems) {
        const viewableData = viewableItems[index].item
        if (viewableData.template && !viewableData.storyID) {
          const pageUrl = viewableData.action

          let shouldAdd = true
          for (const key in this.fetchedPages) {
            if (this.fetchedPages[key].includes(pageUrl)) {
              shouldAdd = false
              break
            }
          }
          if (shouldAdd && pageUrl && viewableData.template == "directory") {
            comicPanels.push(pageUrl)
          }
        } else if (viewableData.template && viewableData.storyID) {
          const storyID = viewableData.storyID
          let shouldAdd = true
          for (const key in this.fetchedPages) {
            if (this.fetchedPages[key].includes(storyID)) {
              shouldAdd = false
              break
            }
          }
          if (shouldAdd) {
            storyPanels.push(storyID)
          }
        }
      }
    }

    if (comicPanels.length > 0) {
      var fetchedPageArray = this.fetchedPages.concat(comicPanels)
      this.fetchedPages = Array.from(new Set(fetchedPageArray))
      this.props.getMultiplePanelInfo && this.props.getMultiplePanelInfo(comicPanels)
    }

    if (storyPanels.length > 0) {
      var fetchedStoryPageArray = this.fetchedPages.concat(storyPanels)
      this.fetchedPages = Array.from(new Set(fetchedStoryPageArray))
      //this.props.getIsStoryLiked && this.props.getIsStoryLiked(storyPanels)
      if (Platform.OS == "ios" && (!Utils.checkObject(this.props.comicData.showComments) || this.props.comicData.showComments > 0)) {
        let data = { data: { storyIDs: storyPanels } }
        this.props.getStoryComments(data)
      }
    }
  }

  onRefresh() {
    this.reloadChapter()
  }

  _footerPlaceHolderComponent() {
    let isSeriesPage = Utils.isChannelURL(this.props.pathUrl)

    return (
      <View >
        <Image style={loadingViewStyle(this.aspectRatio)} source={isSeriesPage ? require('./../../assets/listView_loading_view.gif') : require('./../../assets/story_loading_view.gif')} />
      </View>
    )
  }

  _onEndReached() {
    let allComicsSwitchValue = SessionManager.instance.getShowAllComics()
    this.props.onEndReached(allComicsSwitchValue)
  }

  _keyExtractor(item, index) {
    const key = (item) ? (item.storyID + " " + item.action + " " + index) : index.toString()
    key
    Utils.log(" index " + index + " key " + key)
  }

  // getOffsetAndHeightForPosition(data, index) {
  //   let height = 0;
  //   let newOffset = 0;
  //   for (const key in data) {
  //     const item = data[key];
  //     let currentHeight = 0
  //     if (item.viewHeight) {
  //       currentHeight = item.viewHeight
  //     } else {
  //       currentHeight = 500
  //     }

  //     if (key == index) {
  //       height = currentHeight
  //       break;
  //     }
  //     if (key == 0) {
  //       newOffset = 300
  //     }
  //     newOffset = newOffset + navigationStyle.panelsTopMargin + currentHeight;
  //   }
  //   if (index == 0 && newOffset == 0) {
  //     newOffset = 300 + navigationStyle.panelsTopMargin;
  //   }
  //   return { newOffset, height };
  // }

  // getItemLayout(data, index) {
  //   if (index >= 0 && data.length > 0 && index < data.length) {
  //     const { newOffset, height } = this.getOffsetAndHeightForPosition(data, index);      

  //     return {
  //       length: height,
  //       offset: newOffset,
  //       index
  //     }
  //   }
  //   return { index, length: 0, offset: 0 };
  // }

  render() {
    const { containerInsets, showFooterPlaceHolder, hideBottomBar } = this.props
    const showRefresh = this.state.showRefresh
    const flatListData = this.props.comicData ? this.props.comicData.panels : []
    const secBottomViewHeight = (this.isComicURL || this.isHomeURL) ? navigationStyle.secondaryBottomNavHeight - containerInsets.bottom - 5 : 0 // Added 5px more to remove the white bg on comic page

    return (
      <View style={styles.mainContainer(containerInsets)}>
        <View style={{ flex: 1 }}>
          {(showRefresh)
            ?
            <Image style={loadingViewStyle(this.aspectRatio)} source={require('./../../assets/top_loading_view.gif')} />
            :
            <FlatList
              ref={ref => {
                if (ref) {
                  this.flatlist = ref
                  this.props.updateFlatListProps && this.props.updateFlatListProps(null, this.flatlist)
                }
              }}
              onViewableItemsChanged={this.onViewableItemsChanged}
              contentContainerStyle={{
                flexGrow: this.props.errorRes ? 1 : 0,
                paddingTop: 0,
                paddingBottom: navigationStyle.navHeight + secBottomViewHeight,
                backgroundColor: (this.currentBGColor && !this.isComicURL) ? this.currentBGColor : 'transparent'
              }}
              initialNumToRender={5}
              scrollEventThrottle={16}
              maxToRenderPerBatch={5}
              windowSize={21}
              onScrollBeginDrag={Animated.event([
                { nativeEvent: { contentOffset: { y: this.props.initialScrollY } } }
              ], { useNativeDriver: false })}
              contentInset={{
              }}
              contentOffset={{
                y: -navigationStyle.navHeight + secBottomViewHeight
              }}
              onScroll={Animated.event([{ nativeEvent: { contentOffset: { y: this.scrollY } } }],
                {
                  useNativeDriver: false,
                  listener: (event) => {
                    if (this.props.updateFlatListProps) {
                      this.props.updateFlatListProps(false, this.flatlist)
                      if (event.nativeEvent.contentOffset.y === - navigationStyle.navHeight || event.nativeEvent.contentOffset.y === 0) {
                        this.props.updateFlatListProps(true, this.flatlist)
                      } else {
                        this.props.updateFlatListProps(false, this.flatlist)
                      }
                    }
                    var scrollDif = event.nativeEvent.contentOffset.y - this.props.initialScrollY._value
                    var scrolledPosition = Math.floor(event.nativeEvent.contentOffset.y + event.nativeEvent.layoutMeasurement.height)
                    var contentHeight = Math.floor(event.nativeEvent.contentSize.height) - navigationStyle.navHeight
                    if (event.nativeEvent.contentOffset.y > navigationStyle.navHeight) {
                      if (scrolledPosition >= contentHeight) {
                        this.props.scrollY.setValue(-1)
                      } else {
                        this.props.scrollY.setValue(scrollDif)
                      }
                      //toggleNavigation()
                    } else if (scrollDif < 0) {
                      this.props.scrollY.setValue(scrollDif)
                      //toggleNavigation()
                    }

                    const { contentOffset: { y: currentOffset }, layoutMeasurement, contentSize } = event.nativeEvent
                    const isNearBottom = currentOffset + layoutMeasurement.height >= contentSize.height - 20

                    // Should not trigger on overscroll at the top or bottom of the page.
                    if (currentOffset < 0 || isNearBottom) {
                      if (isNearBottom) {
                        // Update the last scroll position
                        this.lastScrollOffset = currentOffset
                      }
                      this.props.updateHidingBottomBar(false)
                      return
                    }

                    // Initialize offset tracking on first scroll event
                    if (this.lastScrollOffset == null) {
                      this.lastScrollOffset = currentOffset;
                    } else {
                      if (Math.abs(currentOffset - this.lastScrollOffset) < Constants.SCROLL_THRESHOLD) {
                        return;
                      }

                      const scrollDirection = currentOffset > this.lastScrollOffset ? Constants.DOWN : Constants.UP

                      // Hide tab bar immediately when scrolling down
                      if (scrollDirection === Constants.DOWN && !hideBottomBar) {
                        this.props.updateHidingBottomBar(true)
                      }

                      // Show tab bar immediately when scrolling up
                      if (scrollDirection === Constants.UP && hideBottomBar) {
                        this.props.updateHidingBottomBar(false)
                      }

                      // Update the last scroll position
                      this.lastScrollOffset = currentOffset
                    }
                  }
                })}
              refreshControl={
                <RefreshControl
                  refreshing={showRefresh}
                  onRefresh={this.onRefresh}
                  progressViewOffset={settings.PROGRESS_VIEW_OFFSET}
                  tintColor={Color.CIRCLE_PROGRESS_BAR_COLOR}
                />
              }
              data={flatListData}
              ListHeaderComponent={this.renderHeader && this.renderHeader()}
              ListFooterComponent={showFooterPlaceHolder ? this._footerPlaceHolderComponent() : null}
              ListEmptyComponent={this._listEmptyComponent && this._listEmptyComponent()}
              keyExtractor={(item, id) => 'ReaderView' + id}
              renderItem={this.renderItem}
              //getItemLayout={this.getItemLayout}
              maintainVisibleContentPosition={{
                minIndexForVisible: 0,
              }}
              extraData={this.state}
            />
          }
        </View>
      </View>
    )
  }
}

ReaderView.contextType = ThemeContext

const styles = StyleSheet.create({
  mainContainer: (containerInsets) => {
    return {
      flex: 1,
      left: containerInsets.left,
      right: containerInsets.right,
    }
  },
  image: (w, h, margin = 2, bottomMargin = 0) => {
    let dimensions = Dimensions.get('window')
    let aspectRatio = (dimensions.width - margin * 2) / w

    return {
      width: w * aspectRatio,
      height: h * aspectRatio,
      marginLeft: margin,
      marginRight: margin,
      marginTop: margin,
      marginBottom: bottomMargin,
      resizeMode: 'contain'
    }
  },
});

ReaderView.propTypes = {
  props: PropTypes.shape({
    isLoading: PropTypes.bool,
    comicData: PropTypes.shape({
      title: PropTypes.string,
      image: PropTypes.string,
    }),
    openChapter: PropTypes.func,
    previousChapter: PropTypes.func,
    nextChapter: PropTypes.func,
    openWebView: PropTypes.func,
    influeancePurchase: PropTypes.func
  })
}