import React, { Component } from 'react';
import { Constants } from '../../config/Constants'
import { View } from 'native-base'
import { StyleSheet, Image, TouchableOpacity, Text } from 'react-native'
import { bottomTextView } from '../../config/styles'
import { Utils } from '../../config/Utils';
import SessionManager from '../../config/SessionManager';
import FileCache from '../../config/FileCache';
import GiftIcons from './GiftIcons';
import FloatingHearts from '../FloatingHearts';
import { ThemeContext } from '../../Contexts';
import UserSession from '../../config/UserSession';

export default class ReactionComponent extends Component {

  constructor(props) {
    super(props)
    this.state = {
      gifts: SessionManager.instance.getGiftItemList(),
    }

    this.onCommentPress = this.onCommentPress.bind(this)
    this.onIconPress = this.onIconPress.bind(this)
    this.updateGiftIfEmpty = this.updateGiftIfEmpty.bind(this)

    this.isTVComicPage = Utils.isTinyviewComicsPage(props.pathUrl)
  }

  onCommentPress(panelItems) {
    this.props.onShowCommentPress(Constants.COMMENT, panelItems)
  }

  onIconPress(panelItems) {
    this.props.onIconPress(Constants.REPOST, panelItems)
  }

  componentDidMount() {
    this.updateGiftIfEmpty()
  }

  async updateGiftIfEmpty() {
    if (this.state.gifts.length == 0) {
      let dataInJSON = await FileCache.default.readFile(Constants.ALL_GIFTS)
      let giftRes = Utils.getParsedData(dataInJSON)
      this.setState({ gifts: giftRes })
    }
  }

  render() {
    const { panelItems, isLiked, index, currentPageStatus, disableComments, enabledWhiteTheme = false, pathUrl = null } = this.props
    let likeCount = panelItems.likeCount
    let convertedCommentCount = Utils.formatViewCount(panelItems.commentCount)
    let pageViewCount = Utils.formatViewCount(panelItems.views)
    let storyID = panelItems.storyID
    let items = panelItems
    let isLikeChanged = Utils.checkData(panelItems.isLikeChanged) ? panelItems.isLikeChanged : false
    let showGiftShareIcon = Utils.isSubscriberAndNotSeriesAdmin() && !this.isTVComicPage && panelItems.actionType != "website" && !Utils.isInfluencePageURL(pathUrl)

    if (currentPageStatus && !storyID) {
      convertedCommentCount = Utils.formatViewCount(currentPageStatus.commentCount)
      storyID = currentPageStatus.storyID
      pageViewCount = Utils.formatViewCount(currentPageStatus.viewCount)
      items = { ...panelItems, ...currentPageStatus }
      isLikeChanged = Utils.checkData(currentPageStatus.isLikeChanged) ? currentPageStatus.isLikeChanged : false
    }

    let isEnable = Utils.checkData(storyID)

    return (
      <View>
        <View style={styles.mainContainer}>
          <GiftIcons enabledWhiteTheme={enabledWhiteTheme} panelItems={items} isEnable={isEnable} isLiked={isLiked} index={index} onLikePress={this.props.onLikePress} />
          <TouchableOpacity
            style={styles.reactionsView}
            disabled={!isEnable || disableComments}
            onPress={() => { this.props.onCommentPress ? this.props.onCommentPress(storyID) : this.onCommentPress(panelItems) }}>
            <Image style={[styles.navigationIcons, { marginRight: 4 }]} source={enabledWhiteTheme ? require('../../../assets/comments_icon_white.png') : require('../../../assets/comments_icon.png')} />
            <Text style={[this.context.bodyMini, { ...bottomTextView, color: enabledWhiteTheme ? this.context.colors.textInverse : this.context.colors.text }]} >{convertedCommentCount}</Text>
          </TouchableOpacity>
          <View
            style={styles.reactionsView}>
            <Image style={[styles.navigationIcons, { marginRight: 4 }]} source={enabledWhiteTheme ? require('../../../assets/eye_icon_white.png') : require('../../../assets/eye_icon_one.png')} />
            <Text style={[this.context.bodyMini, { ...bottomTextView, color: enabledWhiteTheme ? this.context.colors.textInverse : this.context.colors.text }]} >{pageViewCount}</Text>
          </View>
          <TouchableOpacity
            disabled={!isEnable}
            onPress={() => { this.props.onIconPress ? this.props.onIconPress(Constants.REPOST, items) : this.onIconPress(items) }} >
            <Image style={styles.navigationIcons} source={enabledWhiteTheme ? showGiftShareIcon ? require('../../../assets/white_gift_icon.png') : require('../../../assets/share_icon_white.png') : require('../../../assets/share_icon.png')} />
          </TouchableOpacity>
        </View>
        {isLikeChanged &&
          <View style={{ width: 200 }}>
            <FloatingHearts count={likeCount ? likeCount > 30 ? 20 : likeCount : 5} renderCustomShape={() => { return <Image source={require("../../../assets/white_filled_heart.png")} style={{ tintColor: this.context.colors.logoRed }} /> }} />
          </View>
        }
      </View>
    )
  }
}

ReactionComponent.contextType = ThemeContext;

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
    marginBottom: 8
  },
  navigationIcons: {
    width: 20,
    height: 20,
    alignSelf: 'center'
  },
  reactionsView: {
    flexDirection: 'row'
  }
})