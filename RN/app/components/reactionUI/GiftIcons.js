import React, { Component } from 'react';
import { Constants } from '../../config/Constants'
import { View, Text } from 'native-base'
import { StyleSheet, Image, TouchableOpacity } from 'react-native'
import { Utils } from '../../config/Utils';
import SessionManager from '../../config/SessionManager';
import FileCache from '../../config/FileCache';
import { connect } from 'react-redux';
import { fetchAppConfigList } from '../../redux/actions/actions'
import { ThemeContext } from '../../Contexts';

class GiftIcons extends Component {

  constructor(props) {
    super(props)
    this.state = {
      gifts: SessionManager.instance.getGiftItemList()
    }

    this.onIconPress = this.onIconPress.bind(this)
    this.updateGiftState = this.updateGiftState.bind(this)
    this.renderGiftIcons = this.renderGiftIcons.bind(this)
    this.updateGiftIfEmpty = this.updateGiftIfEmpty.bind(this)
  }

  onIconPress(panelItems) {
    this.props.onIconPress(Constants.REPOST, panelItems)
  }

  componentDidMount() {
    this.updateGiftIfEmpty()
  }

  async updateGiftIfEmpty() {
    if (this.state.gifts.length == 0) {
      let dataInJSON = await FileCache.default.readFile(Constants.ALL_GIFTS)
      let giftRes = Utils.getParsedData(dataInJSON)

      if (!giftRes || giftRes.length == 0) {
        return await this.props.fetchAppConfigList(this.updateGiftState)
      }

      this.updateGiftState(giftRes)
    }
  }

  updateGiftState(response) {
    let listOfGifts = response.giftItems
    if (listOfGifts) {
      this.setState({ gifts: listOfGifts })
    }
  }

  renderGiftIcons(giftsCount, likeCount) {
    if (!Utils.checkObject(giftsCount)) {
      return
    }

    if (!this.state.gifts || this.state.gifts.length <= 0) {
      return this.renderItem({ reactionName: Constants.LIKE }, giftsCount, likeCount)
    } else {
      return this.state.gifts && this.state.gifts.length > 0 && this.state.gifts.map((item, index) => {
        return (
          this.renderItem(item, giftsCount, likeCount)
        )
      })
    }
  }

  renderItem(item, giftsCount) {
    const { image, reactionName } = item

    if ((Utils.checkData(giftsCount) && !giftsCount.hasOwnProperty(reactionName) || giftsCount[reactionName] == 0) && Constants.LIKE.toLowerCase() != reactionName.toLowerCase()) {
      return
    }

    if ((Constants.LIKE.toLowerCase() === reactionName.toLowerCase()) || SessionManager.instance.getStoryHighestGift(giftsCount, reactionName) != reactionName && SessionManager.instance.getStorySecondHighestGift(giftsCount, reactionName) != reactionName) {
      return
    }

    return (
      <View key={item.reactionName}>
        <Image source={{ uri: image }} style={[styles.smallIcon, { marginLeft: 1 }]} />
      </View>
    )
  }

  render() {
    const { panelItems = {}, isLiked, index, enabledWhiteTheme } = this.props
    const { giftsCount = {}, likeCount } = panelItems

    let convertedLikeCount = Utils.formatViewCount(likeCount)
    let isEnable = this.props.isEnable

    let panelDetails = { isLiked: isLiked, panelItems, index: index }

    return (
      <View style={styles.mainContainer}>
        <TouchableOpacity
          disabled={!isEnable}
          style={styles.giftsView}
          onPress={() => { this.props.onLikePress && this.props.onLikePress(panelDetails) }}>
          <Image source={isLiked ? require('../../../assets/like_fill.png') : enabledWhiteTheme ? require('../../../assets/heart_icon_white.png') : require('../../../assets/like.png')} style={styles.smallIcon} />
          {this.renderGiftIcons(giftsCount, likeCount)}
          {(Utils.checkData(convertedLikeCount)) &&
            <Text style={[this.context.bodyMini, styles.giftsCountView, { color: enabledWhiteTheme ? this.context.colors.textInverse : this.context.colors.text }]}>
              {convertedLikeCount}
            </Text>
          }
        </TouchableOpacity>
      </View>
    )
  }
}

GiftIcons.contextType = ThemeContext;

const styles = StyleSheet.create({
  mainContainer: {
    justifyContent: 'center',
    flexDirection: 'row'
  },
  smallIcon: {
    width: 20,
    height: 20
  },
  giftsView: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  giftsCountView: {
    marginStart: 2,
    justifyContent: 'flex-start'
  }
})

const mapStateToProps = (state) => {
  return {
  }
}

const mapDispatchToProps = (dispatch) => {
  return {
    fetchAppConfigList(callback) {
      dispatch(
        fetchAppConfigList(callback)
      )
    },
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(GiftIcons)