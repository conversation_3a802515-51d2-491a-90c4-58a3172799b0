import React, { Component } from "react"
import { StyleSheet, Image, TouchableOpacity } from "react-native"
import { scale } from "react-native-size-matters"
import { Color } from "../config/Color"
import { View } from "native-base"
import TextInputComponent from "../config/TextInputComponent"

export default class SearchBar extends Component {
  constructor(props) {
    super(props)
    this.state = {
      searchText: "",
    }

    this.onChange = this.onChange.bind(this)
    this.clearSearchKeyword = this.clearSearchKeyword.bind(this)
  }

  onChange(key, value) {
    this.setState({ [key]: value }, () => {
      this.props.searchUpdated(value)
    })
  }

  clearSearchKeyword() {
    this.setState({ searchText: '' }, () => {
      this.props.searchUpdated(this.state.searchText)
    })
  }

  render() {
    const { showCancelIcon } = this.props
    return (
      <View style={styles.mainContainer}>
        <Image style={styles.navigationIcons} source={require("../../assets/search_icon.png")} />
        <TextInputComponent
          refState="searchText"
          autoFocus={false}
          selectionColor={Color.RED_TEXT_COLOR}
          onChange={(refState, text) => {
            this.onChange(refState, text)
          }}
          onBlur={(refState) => {
          }}
          fontSize={18}
          value={this.state.searchText}
          placeHolder={"Search"}
          placeholderTextColor={Color.DARK_GREY}
          textColor={Color.DARK_GREY}
          textInputStyle={styles.searchInput}
        />

        {showCancelIcon &&
          <TouchableOpacity
            style={styles.profileButtonView}
            onPress={() => this.clearSearchKeyword()}
            disabled={this.state.searchText != "" ? false : true}
            transparent>
            <Image style={[styles.navigationIcons, { marginRight: scale(10), width: scale(18), height: scale(18) }]} source={require("../../assets/close_window.png")} />
          </TouchableOpacity>
        }
      </View>
    )
  }
}

const styles = StyleSheet.create({
  mainContainer: {
    marginLeft: scale(10),
    height: scale(40),
    marginTop: scale(10),
    flexDirection: "row",
    backgroundColor: Color.LIGHTER_GREY,
    borderRadius: 12,
    borderWidth: 0.5,
    borderColor: "transparent",
    justifyContent: "center",
    alignItems: "center",
  },
  navigationIcons: {
    marginLeft: 10,
    width: scale(25),
    height: scale(25),
    opacity: 1,
    tintColor: Color.DARK_GREY,
    alignSelf: "center",
  },
  searchInput: {
    width: "100%",
    height: scale(45),
    paddingBottom: 0,
  },
})
