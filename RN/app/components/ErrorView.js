import React, { Component } from 'react'
import {
	StyleSheet,
	Text,
	View
} from 'react-native'
import { Button } from 'native-base';
import { Color } from '../config/Color';
import { scale } from 'react-native-size-matters';
import { ThemeContext } from '../Contexts';

export default class ErrorView extends Component {
	constructor(props) {
		super(props)

		this.onButtonTap = this.onButtonTap.bind(this)
	}

	onButtonTap() {
		if (this.props.errorObj.status == 404) {
			this.props.goToHome && this.props.goToHome()
		} else {
			this.props.refresh && this.props.refresh()
		}
	}

	render() {
		const { errorObj } = this.props;
		if (!errorObj) { return null }
		const buttonTitle = errorObj.status == 404 ? "Go to our home page" : "Try again";
		return (
			<View style={{ flex: 1, justifyContent: 'center' }}>				
				<Text style={[this.context.p, styles.messageFont]}>{errorObj.message}</Text>

				{(errorObj.status != null && errorObj.status != 500) &&
					<Button
						variant='unstyled'
						style={{ alignSelf: 'center' }}
						transparent
						onPress={this.onButtonTap && this.onButtonTap}>
						<Text style={[this.context.p, styles.buttonFont]}>{buttonTitle}</Text>
					</Button>
				}
			</View>
		)
	}
}

ErrorView.contextType = ThemeContext

const styles = StyleSheet.create({	
	messageFont: {
		textAlign: 'center',
		marginTop: scale(2),
	},
	buttonFont: {
		marginTop: scale(2),
		color: Color.DEFAULT_BLUE
	}
})