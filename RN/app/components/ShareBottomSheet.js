import React, { Component } from 'react';
import { Constants } from '../config/Constants';
import { StyleSheet, TouchableOpacity, Linking, Platform, FlatList } from 'react-native'
import { View, Text } from 'native-base'
import { Color } from '../config/Color'
import { scale } from 'react-native-size-matters'
import { navigationStyle } from '../config/styles'
import RBSheet from "react-native-raw-bottom-sheet";
import { LoadingIndicator } from './LoadingIndicator';
import { Utils } from '../config/Utils';
import SessionManager from '../config/SessionManager';
import DeepLinkManager from '../config/DeepLinkManager';
import UserSession from '../config/UserSession';
import FastImage from 'react-native-fast-image'
import { ThemeContext } from '../Contexts';
import { settings } from '../config/settings';

var SendIntentAndroid = require("react-native-send-intent-module");

export default class ShareBottomSheet extends Component {

  constructor(props) {
    super(props)

    this.state = {
      showLoadingIndicator: false,
    }

    const panelURL = props.clickedPanelItems && props.clickedPanelItems.action ? Utils.resolvePath(props.pathUrl, props.clickedPanelItems.action) : props.pathUrl
    const isExternalLink = props.clickedPanelItems && props.clickedPanelItems.actionType ? props.clickedPanelItems.actionType == "website" : false
    this.isEligibleForComicGift = !isExternalLink && Utils.isSubscriberAndNotSeriesAdmin() && Utils.isComicPage(panelURL) && !Utils.isInfluencePageURL(panelURL) && !Utils.isUpdatePageURL(panelURL)
    const extraHeight = (!settings.isAndroidDevice || settings.isAndroid14OrBelowDevice ? 0 : 40) // 40 is for the Android 35 edge to edge.
    let bottomSheetHeight = (this.isEligibleForComicGift ? 380 : 350) + extraHeight
    let diffActionsInfluencePoints = SessionManager.instance.getInfluencePointsValuesForActions()
    let isInviteSheet = (props.configShareSheetFor == Constants.INVITE_FRIENDS)
    let sheetDescription = SessionManager.instance.hasAnySubscriptionPurchase() ? this.isEligibleForComicGift ? Constants.COMIC_SHARE_SHEET_MESSAGE_FOR_ONLY_SUBSCRIBERS : Constants.REFERRAL_SHARE_SHEET_MESSAGE : Constants.REFERRAL_SHARE_SHEET_MESSAGE
    if (isInviteSheet) {
      if (SessionManager.instance.hasAnySubscriptionPurchase()) {
        bottomSheetHeight = 350 + extraHeight
        sheetDescription = Constants.INVITE_FRIENDS_SHARE_SHEET_COMMENT_FOR_SUBSCRIBERS
      } else {
        bottomSheetHeight = 380 + extraHeight
        sheetDescription = `Know when a friend likes or comments on a comic. Earn ${diffActionsInfluencePoints.FRIEND_REQUEST_SENDER} influence points for every invite accepted by friends.`
      }
    }

    this.sheetHeight = bottomSheetHeight
    this.isRecentlyTapped = false
    this.isSheetForInvite = isInviteSheet
    this.sheetTitle = this.isSheetForInvite ? Constants.INVITE_FRIENDS : this.isEligibleForComicGift ? 'Gift this comic' : 'Share and earn referral bonus'
    this.sheetSubTitle = sheetDescription
    this.bottomSheetModalRef = null
    this.showShareBottomSheet = true
    this.presentBottomSheet = this.presentBottomSheet.bind(this)
    this.closeBottomSheet = this.closeBottomSheet.bind(this)
    this.onSharedCompleted = this.onSharedCompleted.bind(this)
    this.shareItemsList = [
      // { id: 1, imageUrl: require('../../assets/tinyview-logo-only-mark-square-rounded-100.png'), shareText: 'Tinyview' },
      { id: 2, imageUrl: Platform.OS == 'ios' ? require('../../assets/apple-messages-100.png') : require('../../assets/android_messages_icon.png'), shareText: 'Messages' },
      { id: 3, imageUrl: require('../../assets/wa-whatsapp-icon-100.png'), shareText: 'WhatsApp' },
      { id: 4, imageUrl: require('../../assets/copy_icon.png'), shareText: 'Copy link' },
      { id: 5, imageUrl: require('../../assets/ig-instagram-icon-100.png'), shareText: 'Instagram' },
      { id: 6, imageUrl: require('../../assets/facebook-app-icon-100.png'), shareText: 'Facebook' },
      { id: 7, imageUrl: require('../../assets/twitter_large_icon.png'), shareText: 'X' },
      { id: 8, imageUrl: require('../../assets/More.png'), shareText: 'More' },
      { id: 9, imageUrl: '', shareText: '' }
    ]

    this.fullMessageText = ''
    if (this.isSheetForInvite) {
      let displayName = this.props.userDetails.displayName ? this.props.userDetails.displayName : Constants.TINYVIEW_USER
      this.fullMessageText = `${Constants.inviteFriendTemplateStart} ${displayName} ${Constants.inviteFriendTemplateEnd}`
    } else if (this.isEligibleForComicGift) {
      this.fullMessageText = "This is a gift link "
    }
  }

  componentDidMount() {
    this.presentBottomSheet()
  }

  async presentBottomSheet() {
    this.bottomSheetModalRef.open()
  }

  closeBottomSheet() {
    this.showShareBottomSheet = false
    this.props.closeBottomSheet && this.props.closeBottomSheet()
  }

  async sharingLink() {
    let clickedPanelItems = { ...this.props.clickedPanelItems, isSharedByPremiumUser: this.isEligibleForComicGift }
    const shareURL = await DeepLinkManager.instance.getShareBranchURL(clickedPanelItems)
    return shareURL
  }

  onSharedCompleted() {
    if (this.state.showLoadingIndicator) {
      this.setState({ showLoadingIndicator: false })
    }

    if (this.bottomSheetModalRef) {
      this.bottomSheetModalRef.close()
    }
  }

  async renderOnClick(tappedButton) {
    let panelItems = this.props.clickedPanelItems
    let isExternalLink = panelItems && panelItems.actionType && panelItems.actionType == 'website'

    if ((tappedButton != Constants.MORE || this.isSheetForInvite || (tappedButton == Constants.MORE && isExternalLink)) && tappedButton.toLowerCase() != Constants.TINYVIEW) { // For more creating link in the action
      this.setState({ showLoadingIndicator: true })
      var shareBranchURL = ''
      if (this.isSheetForInvite) {
        shareBranchURL = await DeepLinkManager.instance.getBranchLinkURL(Constants.INVITE_FRIEND_ACTION_TYPE, this.props.userDetails, null)
      } else {
        shareBranchURL = await this.sharingLink()
      }
    }

    if ((tappedButton != Constants.MORE) && !shareBranchURL) {
      this.setState({ showLoadingIndicator: false }, () => {
        this.bottomSheetModalRef.close()
        return
      })
    } else {
      let openingURL = null
      if (tappedButton == Constants.MORE) {
        this.bottomSheetModalRef.open()
        if (this.isSheetForInvite) {
          var clickedPanelItems = {}
          if (Platform.OS == 'ios') {
            clickedPanelItems = { message: this.fullMessageText, url: shareBranchURL }
          } else {
            clickedPanelItems = { message: this.fullMessageText + shareBranchURL }
          }
          this.props.shareInviteLink(clickedPanelItems, this.onSharedCompleted)
        } else {
          if (isExternalLink) {
            clickedPanelItems = { ...this.props.clickedPanelItems, action: shareBranchURL }
          } else {
            clickedPanelItems = { ...this.props.clickedPanelItems, messageText: this.fullMessageText, isSharedByPremiumUser: this.isEligibleForComicGift }
          }
          this.props.share(clickedPanelItems, null, this.onSharedCompleted)
        }
        return
      } else if (tappedButton.toLowerCase() == Constants.COPYLINK) {
        Utils.copyToClipboard(this.fullMessageText + shareBranchURL, "Link Copied")

      } else if (tappedButton.toLowerCase() == Constants.TINYVIEW) {
        if (!UserSession.instance.isLoggedInUser()) {
          this.props.signInSheet()
          return
        }
        const params = {
          sendToAllFriend: false,
          clickedPanelItems: this.props.clickedPanelItems,
          clickedStoryID: this.props.clickedStoryID,
          onStoryUpdated: this.props.onStoryUpdated,
          respotType: Constants.SELECT_FRIENDS
        }
        Utils.navigateToSubRouteWithParams(Constants.FEED_SCREEN, Constants.SEND_POST_SCREEN, this.props, params)

      } else if (tappedButton == Constants.WHATSAPP) {
        openingURL = `whatsapp://send?text=${this.fullMessageText + shareBranchURL}&phone=`

      } else if (tappedButton == Constants.MESSAGES) {
        if (Platform.OS == "android") {
          openingURL = `sms:${""}?body=${this.fullMessageText + shareBranchURL}`
        } else {
          openingURL = `sms:${""}&body=${this.fullMessageText + shareBranchURL}`
        }

      } else if (tappedButton.toLowerCase() == Constants.FACEBOOK_LINK) {
        if (Platform.OS == "android") {
          openingURL = `https://www.facebook.com/sharer/sharer.php?u=` + encodeURI(shareBranchURL)
        } else {
          openingURL = 'fb://share?link=' + this.fullMessageText + encodeURI(shareBranchURL)
        }

      } else if (tappedButton.toLowerCase() == Constants.X_LINK) {
        openingURL = `https://twitter.com/intent/tweet?url=${this.fullMessageText}` + encodeURI(shareBranchURL)

      } else if (tappedButton.toLowerCase() == Constants.INSTAGRAM_LINK) {
        if (Platform.OS == "android") {
          this.bottomSheetModalRef.close()
          SendIntentAndroid.shareOnInstagramDirect(this.fullMessageText + encodeURI(shareBranchURL))
          return
        } else {
          openingURL = `instagram://sharesheet?text=${this.fullMessageText}` + shareBranchURL
        }
      }

      this.setState({ showLoadingIndicator: false }, () => {
        if (tappedButton.toLowerCase() == Constants.COPYLINK || tappedButton.toLowerCase() == Constants.TINYVIEW) {
          this.bottomSheetModalRef.close()
          return
        }
        Linking.canOpenURL(openingURL).then((canHandle) => {
          if (Platform.OS == "android" || canHandle) {
            Linking.openURL(openingURL)
          } else {
            Utils.showToast("App is not available")
          }
          this.bottomSheetModalRef.close()
        }).catch((err) => {
          Utils.showToast("App is not supported")
          Utils.log("Error in sharing " + err)
          this.bottomSheetModalRef.close()
        })
      })
    }
  }

  renderItem(item) {
    let image = item.item.imageUrl
    let isButtonEnabled = (item.item.shareText.toLowerCase() != Constants.TINYVIEW || (this.props.clickedStoryID))
    let isAppInstalled = SessionManager.instance.isAppInstalled(item.item.shareText.toLowerCase())

    return (
      <View style={styles.mainContainer}>
        <TouchableOpacity
          disabled={!isButtonEnabled || !isAppInstalled}
          style={(!isButtonEnabled || !isAppInstalled) ? [styles.shareIcons, styles.iconOpacity] : [styles.shareIcons]}
          onPress={() => {
            setTimeout(() => {
              this.isRecentlyTapped = false
            }, 2000);

            if (this.isRecentlyTapped) {
              return
            }

            this.isRecentlyTapped = true
            this.renderOnClick(item.item.shareText)
          }}>
          <FastImage source={image} style={styles.icon} />
          <Text style={[this.context.bodyMini, styles.textStyle]}>{item.item.shareText}</Text>
        </TouchableOpacity>
      </View>
    )
  }


  renderBottomSheet() {
    return (
      <RBSheet
        draggable={true}
        dragFromTopOnly={false}
        dragOnContent={true}
        customStyles={{
          container: {
            borderRadius: 18
          },
          draggableIcon: {
            width: 80,
          },
        }}
        onClose={this.closeBottomSheet}
        ref={(ref) => this.bottomSheetModalRef = ref}
        height={this.sheetHeight}
        closeOnPressBack={true}
        closeOnDragDown={true}
        closeOnPressMask={true}>
        <View style={{ flex: 1 }}>
          <View style={{ justifyContent: 'center', flexDirection: 'row' }}>
            {this.isEligibleForComicGift && <FastImage source={require('../../assets/gift_badge.png')} style={styles.giftIcon} />}
            <Text style={[this.context.h1, styles.titleStyle]}>{this.sheetTitle}</Text>
          </View>
          <Text style={[this.context.p, styles.titleStyle, styles.subTitleView]}>{this.sheetSubTitle}</Text>
          <FlatList
            style={styles.flatListStyle}
            numColumns={4}
            showsVerticalScrollIndicator={false}
            showsHorizontalScrollIndicator={false}
            data={this.shareItemsList}
            renderItem={(item) => this.renderItem(item)}
            keyExtractor={(item, id) => '' + id}
          />
        </View>
        {this.state.showLoadingIndicator && <LoadingIndicator />}
      </RBSheet>
    )
  }

  render() {
    return (
      this.renderBottomSheet()
    )
  }
}

ShareBottomSheet.contextType = ThemeContext

const styles = StyleSheet.create({
  textStyle: {
    marginTop: scale(5),
    alignSelf: 'center',
  },
  titleStyle: {
    marginTop: 10,
    alignSelf: 'center'
  },
  icon: {
    width: 50,
    height: 50,
    alignSelf: 'center',
  },
  shareIcons: {
    alignSelf: 'center',
    tintColor: Color.BOTTOM_ICON_TINT_COLOR,
    flex: 0.25,
  },
  mainContainer: {
    marginTop: scale(20),
    justifyContent: 'center',
    flex: 1
  },
  flatListStyle: {
    marginLeft: navigationStyle.panelLeftRightMargin,
    marginRight: navigationStyle.panelLeftRightMargin
  },
  iconOpacity: {
    opacity: 0.5
  },
  subTitleView: {
    marginLeft: 12,
    marginRight: 12,
    textAlign: 'center'
  },
  giftIcon: {
    height: 30,
    width: 30,
    marginRight: 5,
    marginTop: 5,
    alignSelf: 'center',
  }
});