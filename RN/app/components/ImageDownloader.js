import FileCache from '../config/FileCache'
import { Utils } from '../config/Utils';

export default class ImageDownloader {

	constructor(downloadData) {
		if (downloadData) {
			this.data = downloadData.data
		}
		this.retryCount = 0
		this.pathUrl = downloadData.pathUrl;
		this.getImage(this.data, 0, downloadData.onImageDownloaded)
	}

	getImage = async (data, index, onImageDownloaded) => {
		if (!data || index >= data.length) {
			return;
		}

		let element = data[index]
		let imageData = element

		if (element.imageData) {
			imageData = element.imageData;
		}
		var { image, md5sum, user } = imageData;

		if (!image && (!user || user.image)) {
			if (onImageDownloaded) {
				onImageDownloaded({ imageData: { imageName: image, ...imageData } }, index)
			}
			this.getImage(this.data, index + 1, onImageDownloaded)
			return
		}

		let userLocalURL = null;
		if (user && user.image) {
			userLocalURL = await this.downloadPanelImage(user.image)
		}

		const panelLocalURL = await this.downloadPanelImage(image, md5sum)

		let comicsImages = null;
		if (userLocalURL) {
			comicsImages = { imageData: { ...imageData, imageName: image, image: panelLocalURL, user: { ...imageData.user, image: userLocalURL } } }
		} else {
			comicsImages = { imageData: { ...imageData, imageName: image, image: panelLocalURL } }
		}

		if (onImageDownloaded) {
			onImageDownloaded(comicsImages, index)
		}

		this.getImage(this.data, index + 1, onImageDownloaded)
	}

	static downloadPanelImage = async (imageUrl, md5sum = null) => {
		let finalUrl = imageUrl && imageUrl.replace(/ /g, '%20')
		try {
			return await ImageDownloader.downloadImage(finalUrl, md5sum);
		} catch (error) {
			if (this.retryCount < 3) {
				this.retryCount = this.retryCount + 1
				ImageDownloader.downloadImage(finalUrl, md5sum);
			} else {
				Utils.log("Downloading failed finalUrl " + finalUrl + " error " + error)
				return finalUrl;
			}
		}
	}

	static async downloadImage(finalUrl, md5sum = null) {
		try {
			const result = await FileCache.default.isFileExistsForUrl(finalUrl, md5sum);

			const imageLocalPath = FileCache.default.getLocalPathForURL(finalUrl, md5sum)
			let isDownloaded = result;
			if (!result) {
				finalUrl = finalUrl + "?mockParam=" + md5sum;
				isDownloaded = await FileCache.default.downloadFile(finalUrl, md5sum)
			}

			return isDownloaded ? "file://" + imageLocalPath : finalUrl;

		} catch (error) {
			Utils.log("Downloading failed finalUrl " + finalUrl + " error " + error)
			throw error;
		}

	}
}