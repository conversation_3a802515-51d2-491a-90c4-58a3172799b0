import React, { Component } from "react";
import { StyleSheet, View, Text, TouchableOpacity } from "react-native";
import { ThemeContext } from "../Contexts";
import { Constants } from "../config/Constants";

export default class CommentTextView extends Component {
    constructor(props) {
        super(props)

        this.state = {
            viewFullLongComment: false
        }

        const { textValue } = this.props
        this.commentTextLength = textValue.length

        this.onOpenCloseCommentTap = this.onOpenCloseCommentTap.bind(this)
    }

    onOpenCloseCommentTap() {
        const { viewFullLongComment } = this.state

        if (viewFullLongComment) {
            this.setState({ viewFullLongComment: false })
        } else {
            this.setState({ viewFullLongComment: true })
        }
    }

    render() {
        const { textValue } = this.props
        const { viewFullLongComment } = this.state

        let isLongComment = this.commentTextLength > Constants.LONG_COMMENT_LIMIT
        let visibleText = textValue
        if (!viewFullLongComment && isLongComment) {
            visibleText = textValue.slice(0, Constants.LONG_COMMENT_LIMIT)
        }

        return (
            <View style={styles.mainContainer}>
                <Text style={[this.context.p, { color: this.context.colors.textBold }]}>{visibleText}
                    {isLongComment &&
                        <TouchableOpacity
                            onPress={() => { this.onOpenCloseCommentTap() }}
                            style={styles.moreLessTextView}>
                            <Text style={this.context.bodyMiniBold}>{viewFullLongComment ? "...view less" : "...view more"}</Text>
                        </TouchableOpacity>
                    }
                </Text>
            </View >
        )
    }
}

CommentTextView.contextType = ThemeContext

const styles = StyleSheet.create({
    mainContainer: {
        flex: 1
    },
    moreLessTextView: {
        marginBottom: -2
    }
})

