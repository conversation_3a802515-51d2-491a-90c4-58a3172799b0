import React, { PureComponent } from 'react'
import {
	View,
	StyleSheet,
	Dimensions,
	Image,
	TouchableWithoutFeedback,
	Linking
} from 'react-native'
import { Text, Button } from 'native-base';
import { navigationStyle, buyButton, buyButtonContainer } from '../config/styles'
import { Utils } from '../config/Utils';
import { Color } from '../config/Color';
import { scale } from 'react-native-size-matters';
import SessionManager from '../config/SessionManager'
import { Constants } from '../config/Constants';
import { settings } from "../config/settings"
import { connect } from 'react-redux';
import { getStripePortalLink } from '../redux/actions/actions';
import moment from 'moment';
import { ThemeContext } from '../Contexts';
import { ThreeDotLoader } from './ThreeDotLoader';

class PurchasePanel extends PureComponent {

	constructor(props) {
		super(props)
		const { item } = this.props;

		this.state = {
			stripeProductID: null
		}

		const cachedPurchases = SessionManager.instance.getPurchasedItems();
		this.suffix = Utils.isSubsProduct(item.productId) ? Utils.isMonthlySubsProd(item) ? "per month" : Utils.isYearlySubsProd(item) ? "per year" : "" : "";
		this.hasSubscribed = cachedPurchases.includes(item.productId)

		if (this.hasSubscribed) {
			this.purchaseProduct = SessionManager.instance.getPurchasedItemsElement(item.productId);
			if (this.purchaseProduct && this.purchaseProduct.expiringAt) {
				this.expireDate = moment(this.purchaseProduct.expiringAt).format('MMMM DD, YYYY')
			}
		}
		this.imagePath = this.getImagePath(item.productId)
		this.openManageSubscriptionInBrowser = this.openManageSubscriptionInBrowser.bind(this)
		this.getRestartSubsLink = this.getRestartSubsLink.bind(this)
	}

	getNonPurchasedButtonName(proId) {
		if (proId == Constants.COOKIE_PRODUCT_ID) {
			return "Subscribe at Cookie Level"
		} else if (proId == Constants.COFFEE_PRODUCT_ID) {
			return "Subscribe at Coffee Level"
		} else if (proId == Constants.ART_SUPPLIES_PRODUCT_ID) {
			return "Subscribe at Art Supplies"
		} else if (proId == Constants.BAGEL_PRODUCT_ID) {
			return "Subscribe at Bagel Level"
		} else if (proId == Constants.PIZZA_PRODUCT_ID) {
			return "Subscribe at Pizza Level"
		}
	}

	render() {
		const { item, loadingProductId } = this.props;
		const { stripeProductID } = this.state
		const isActiveSubscription = this.hasSubscribed == true && this.purchaseProduct
		const showCancelButton = isActiveSubscription && this.purchaseProduct.autoRenew

		return (
			<View key={item.productId + item.index} style={[styles.iapImage(1600, navigationStyle.panelLeftRightMargin), this.props.panelStyle, { borderWidth: isActiveSubscription ? 3 : 0, borderColor: isActiveSubscription ? this.context.colors.highlightBorders : this.context.colors.chatBubbles, backgroundColor: this.context.colors.textInverse }]}>
				<TouchableWithoutFeedback
					onPress={() => {
						if (this.hasSubscribed) {
							return
						}
						this.props.requestPurchase(item)
					}}>
					<View style={styles.productView}>
						{this.imagePath &&
							<View style={styles.iconView}>
								<Image source={this.imagePath} style={{ alignSelf: 'flex-start', width: scale(30), height: scale(30) }} />
							</View>
						}

						<View style={{ flex: 0.85 }}>
							<Text style={this.context.pBold}>
								{item.title}
							</Text>
							<Text style={[this.context.comments, styles.prodcutDescription]}>
								${item.price} {this.suffix}
							</Text>
							<Text style={[this.context.p, styles.productRenewal]}>
								{item.renewal}
							</Text>

							<View style={[buyButtonContainer, { marginBottom: 5 }]}>
								<Button
									variant='solid'
									style={this.purchaseProduct && this.purchaseProduct.autoRenew ? [styles.buyButtonSubsCancelled, { borderColor: this.context.colors.separators, backgroundColor: this.context.colors.textInverse }] : [buyButton, styles.buyButton, { backgroundColor: this.context.colors.logoRed, borderColor: this.context.colors.logoRed }]}
									onPress={() => {
										this.onButtonTap(item)
									}}>
									{((item.productId == loadingProductId) || (item.productId == stripeProductID)) ?
										<ThreeDotLoader loaderColor={showCancelButton ? '#000' : ''} /> : (
											<>
												{showCancelButton &&
													<Text style={[this.context.p, { color: this.context.colors.text }]}>
														Cancel Subscription
													</Text>
												}
												{this.hasSubscribed == false &&
													<Text style={[this.context.p, { color: this.context.colors.textInverse }]}>
														{this.getNonPurchasedButtonName(item.productId)}
													</Text>
												}
												{(isActiveSubscription && !this.purchaseProduct.autoRenew) &&
													<Text style={[this.context.p, { color: this.context.colors.textInverse }]}>
														Restart Subscription
													</Text>
												}
											</>
										)}
								</Button>
							</View>
							{((this.hasSubscribed) && this.purchaseProduct) && (!this.purchaseProduct.autoRenew) &&
								<View>
									<Text style={[this.context.bodyMini, styles.prodcutDescription, styles.expiryStyle]}>Expiring {this.expireDate}</Text>
								</View>
							}
						</View>
					</View>
				</TouchableWithoutFeedback>
			</View>
		)
	}

	openManageSubscriptionInBrowser() {
		Linking.openURL(settings.manageSubscriptionsUrl).catch((err) => Utils.error("Couldn't load page", err))
	}

	getImagePath(proId) {
		if (proId == Constants.COOKIE_PRODUCT_ID) {
			return require("../../assets/cookie_icon.png")
		} else if (proId == Constants.COFFEE_PRODUCT_ID) {
			return require("../../assets/coffee_icon.png")
		} else if (proId == Constants.ART_SUPPLIES_PRODUCT_ID) {
			return require("../../assets/art_supplies_icon.png")
		} else if (proId == Constants.BAGEL_PRODUCT_ID) {
			return require("../../assets/bagel_icon.png")
		} else if (proId == Constants.PIZZA_PRODUCT_ID) {
			return require("../../assets/pizza_icon.png")
		}
	}

	async onButtonTap(item) {
		if (this.hasSubscribed) {
			if (this.purchaseProduct && this.purchaseProduct.store && this.purchaseProduct.store.toLowerCase() == Constants.PAYMENT_MODE_STRIPE) {
				this.setState({ stripeProductID: item.productId }, () => {
					this.props.getStripePortalLink(this.getRestartSubsLink, false)
				})
			} else {
				return this.openManageSubscriptionInBrowser()
			}
		}
		else {
			this.props.requestPurchase(item)
		}
	}

	getRestartSubsLink(pathUrl) {
		this.setState({ stripeProductID: null })
		if (pathUrl) {
			Linking.openURL(pathUrl)
		}
	}
}

const mapStateToProps = (state) => {
	return {
	}
}

const mapDispatchToProps = (dispatch) => {
	return {
		getStripePortalLink(callback, showLoader) {
			dispatch(
				getStripePortalLink(callback, showLoader)
			)
		},
	}
}

export default connect(mapStateToProps, mapDispatchToProps)(PurchasePanel)

PurchasePanel.contextType = ThemeContext

const styles = StyleSheet.create({
	iapImage: (w, margin = 2) => {
		let dimensions = Dimensions.get('window')
		let aspectRatio = (dimensions.width - margin * 2) / w

		return {
			width: w * aspectRatio,
			marginRight: margin,
			resizeMode: 'contain',
			borderWidth: 1,
			borderRadius: 12,
			shadowColor: Color.BLACK_COLOR,
			shadowOpacity: 0.1,
			shadowRadius: 12,
			elevation: 12
		}
	},
	productView: {
		flexDirection: 'row',
		alignItems: 'center',
		alignContent: 'center',
		margin: navigationStyle.panelsMargin
	},
	expiryStyle: {
		marginTop: 10,
		marginLeft: 1,
		color: Color.RED_BACKGROUND_COLOR,
	},
	prodcutDescription: {
		marginTop: 2,
	},
	productRenewal: {
		marginTop: 8,
	},
	buyButton: {
		height: 40,
		width: '100%',
		paddingLeft: 0,
		paddingRight: 0,
	},
	buyButtonSubsCancelled: {
		borderWidth: 1,
		height: 40,
		borderRadius: 6,
		position: 'absolute',
		width: '100%',
		elevation: 0,
		justifyContent: 'center',
		paddingTop: 0,
		paddingBottom: 0
	},
	iconView: {
		alignSelf: 'flex-start',
		flex: 0.15,
	}
})