import React, { Component } from "react";
import { StyleSheet, FlatList, TouchableOpacity, Platform, Linking } from "react-native";
import { View } from "native-base";
import FastImage from "react-native-fast-image";
import { ThemeContext } from "../Contexts";
import { Constants } from "../config/Constants";
import DeepLinkManager from "../config/DeepLinkManager";
import { Utils } from "../config/Utils";
import SessionManager from "../config/SessionManager";

var SendIntentAndroid = require("react-native-send-intent-module");

export default class ShareButtonsView extends Component {

    constructor(props) {
        super(props)

        this.isRecentlyTapped = false
        this.shareList = [
            { id: 1, imageUrl: Platform.OS == 'ios' ? require('../../assets/apple-messages-100.png') : require('../../assets/android_messages_icon.png'), shareIcon: Constants.MESSAGES_LINK },
            { id: 2, imageUrl: require('../../assets/wa-whatsapp-icon-100.png'), shareIcon: Constants.WHATSAPP_LINK },
            { id: 3, imageUrl: require('../../assets/ig-instagram-icon-100.png'), shareIcon: Constants.INSTAGRAM_LINK },
            { id: 4, imageUrl: require('../../assets/facebook-app-icon-100.png'), shareIcon: Constants.FACEBOOK_LINK },
            { id: 5, imageUrl: require('../../assets/twitter_large_icon.png'), shareIcon: Constants.X_LINK },
            { id: 6, imageUrl: require('../../assets/copy_icon.png'), shareIcon: Constants.COPYLINK },
        ]
        this.renderItem = this.renderItem.bind(this)
        this.onIconPress = this.onIconPress.bind(this)
        this.sharingLink = this.sharingLink.bind(this)
    }

    async sharingLink() {
        const { panelData } = this.props
        const shareURL = await DeepLinkManager.instance.getShareBranchURL(panelData)
        return shareURL
    }

    async onIconPress(shareIcon) {
        let sharePanelURL = await this.sharingLink()
        let openingURL = null

        if (shareIcon == Constants.COPYLINK) {
            Utils.copyToClipboard(sharePanelURL, "Link Copied")
            return
        } else if (shareIcon == Constants.WHATSAPP_LINK) {
            openingURL = `whatsapp://send?text=${sharePanelURL}&phone=`

        } else if (shareIcon == Constants.MESSAGES_LINK) {
            if (Platform.OS == "android") {
                openingURL = `sms:${""}?body= ${sharePanelURL}`
            } else {
                openingURL = `sms:${""}&body= ${sharePanelURL}`
            }

        } else if (shareIcon == Constants.FACEBOOK_LINK) {
            if (Platform.OS == "android") {
                openingURL = `https://www.facebook.com/sharer/sharer.php?u=` + encodeURI(sharePanelURL)
            } else {
                openingURL = 'fb://share?link=' + encodeURI(sharePanelURL)
            }

        } else if (shareIcon == Constants.X_LINK) {
            openingURL = `https://twitter.com/intent/tweet?url=` + encodeURI(sharePanelURL)

        } else if (shareIcon == Constants.INSTAGRAM_LINK) {
            if (Platform.OS == "android") {
                SendIntentAndroid.shareOnInstagramDirect(encodeURI(sharePanelURL))
                return
            } else {
                openingURL = `instagram://sharesheet?text=` + sharePanelURL
            }
        }

        Linking.canOpenURL(openingURL).then((canHandle) => {
            if (Platform.OS == "android" || canHandle) {
                Linking.openURL(openingURL)
            } else {
                Utils.showToast("App is not available")
            }
        }).catch((err) => {
            Utils.showToast("App is not supported")
            Utils.log("Error in sharing " + err)
        })
    }

    renderItem(item) {
        let isAppInstalled = SessionManager.instance.isAppInstalled(item.item.shareIcon)

        return (
            <View>
                <TouchableOpacity
                    disabled={!isAppInstalled}
                    style={!isAppInstalled ? [styles.iconOpacity] : []}
                    onPress={() => {
                        setTimeout(() => {
                            this.isRecentlyTapped = false
                        }, 2000);

                        if (this.isRecentlyTapped) {
                            return
                        }

                        this.isRecentlyTapped = true
                        this.onIconPress(item.item.shareIcon)
                    }}>
                    <FastImage source={item.item.imageUrl} style={[styles.shareIconsStyle, { marginRight: item.item.shareIcon == Constants.COPYLINK ? 0 : 12 }]} />
                </TouchableOpacity>
            </View>
        )
    }

    render() {
        return (
            <View style={styles.mainContainer}>                
                <FlatList
                    style={styles.flatListStyle}
                    data={this.shareList}
                    renderItem={(item) => this.renderItem(item)}
                    horizontal={true}
                    keyExtractor={(item, id) => '' + id}
                />                
            </View>
        )
    }
}

ShareButtonsView.contextType = ThemeContext

const styles = StyleSheet.create({
    mainContainer: {
        flex: 1
    },
    flatListStyle: {
        marginTop: 10,              
        alignSelf: 'center'
    },
    separatorStyle: {
        height: 0.5,
        width: '100%'
    },
    shareIconsStyle: {
        height: 36,
        width: 36
    },
    iconOpacity: {
        opacity: 0.5
    }
})