import React, { Component } from 'react';
import { View, Animated, StyleSheet } from 'react-native';

export class <PERSON><PERSON>ot<PERSON>oader extends Component {
    constructor(props) {
        super(props);
        this.dotOpacities = [
            new Animated.Value(1),   // Dot 1
            new Animated.Value(0.33), // Dot 2
            new Animated.Value(1), // Dot 3
        ];
        this.step = 0;

        this.startAnimationLoop = this.startAnimationLoop.bind(this)
    }

    componentDidMount() {
        this.startAnimationLoop();
    }

    componentWillUnmount() {
        clearInterval(this.interval);
    }

    startAnimationLoop() {
        // Pattern: [dot1, dot2, dot3] opacities
        const sequence = [
            [1, 0.33, 0.33],   // Step 1: Dot 1
            [1, 1, 0.33],      // Step 2: Dot 1 + Dot 2
            [0.33, 0.33, 1],   // Step 3: Dot 3
            [0.33, 1, 1]       // Step 4: Dot 2 + Dot 3
        ];

        setInterval(() => {
            const current = sequence[this.step % sequence.length];

            this.dotOpacities.forEach((dot, index) => {
                Animated.timing(dot, {
                    toValue: current[index],
                    duration: 300,
                    useNativeDriver: true,
                }).start();
            });

            this.step = this.step + 1;
        }, 300);
    };

    render() {
        const { loaderColor } = this.props
        return (
            <View style={styles.mainContainer}>
                {this.dotOpacities.map((opacity, index) => (
                    <Animated.View
                        key={index}
                        style={[styles.dotView(loaderColor), { opacity }]}
                    />
                ))}
            </View>
        );
    }
}

const styles = StyleSheet.create({
    mainContainer: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        height: 22
    },
    dotView: (loaderColor) => {
        return {
            width: 10,
            height: 10,
            borderRadius: 5,
            backgroundColor: loaderColor ? loaderColor : '#FFFFFF',
            marginHorizontal: 5,
        }
    },
});
