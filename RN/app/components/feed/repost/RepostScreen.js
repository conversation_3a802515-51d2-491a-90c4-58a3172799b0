
import React, { Component } from 'react'
import { StyleSheet, SafeAreaView, BackHandler, Platform } from 'react-native'
import { View } from 'native-base'
import TextInputComponent from '../../../config/TextInputComponent'
import { LoadingIndicator } from '../../LoadingIndicator'
import { connect } from 'react-redux'
import { repostComic, editRepostedComic, getStoryDetails } from '../../../redux/actions/actions'
import { settings } from '../../../config/settings'
import { Color } from '../../../config/Color'
import { SystemFont } from '../../../config/Typography'
import { scale } from 'react-native-size-matters'
import FirebaseManager from '../../../config/FirebaseManager'
import { CommonActions } from '@react-navigation/native'
import StoryHeaderBar from '../../friendsPackage/shared/StoryHeaderBar'
import { Utils } from '../../../config/Utils'
import { Constants } from '../../../config/Constants'
import StoryPanel from '../../ComicPanel/StoryPanel'
import NetworkUtils from '../../../config/NetworkUtils';

class RepostScreen extends Component {

  static navigationOptions = {
    header: (props) => <StoryHeaderBar {...props} />
  }

  constructor(props) {
    super(props)

    let panelItems = null
    this.params = this.props.route.params

    if (this.params && this.params.panelItems) {
      panelItems = this.params.panelItems
    }

    this.state = {
      repostCaption: '',
      nextDisabled: true,
      repostData: panelItems,
      showLoadingIndicator: false,
    }

    if (this.params) {
      this.storyData = this.params.storyData ? this.params.storyData : null
    }

    if (!Utils.checkData(this.storyData)) {
      this.storyData = panelItems.storyID ? panelItems.storyID : null
    }

    this.isEditingRepost = this.props.route.params.isEditingRepost
    this.currentUserId = Utils.getCurrentUserId()
    this.onStoryUpdated = this.props.route.params.onStoryUpdated

    this.onBackPress = this.onBackPress.bind(this)
    this.repostComic = this.repostComic.bind(this)
    this.checkIsFormFilled = this.checkIsFormFilled.bind(this)
    this.getStoryDetails = this.getStoryDetails.bind(this)
    this.onFetchStoryDetails = this.onFetchStoryDetails.bind(this)
    this.setLoaderVisibility = this.setLoaderVisibility.bind(this)
    this.onChange = this.onChange.bind(this)
    this.onInputBlur = this.onInputBlur.bind(this)
    this.openChapter = this.openChapter.bind(this)
  }

  componentDidMount() {
    this.props.navigation.setParams({
      title: Constants.REPOST,
      leftButtonText: "Back",
      onBackPress: this.onBackPress,
      isRightDisabled: this.state.nextDisabled,
      isLoading: this.state.showLoadingIndicator,
      rightButtonText: "Done",
      onRightClick: this.repostComic,
    })

    if (this.isEditingRepost && this.state.repostData) {
      this.setState({ repostCaption: this.state.repostData.comment })
    }

    if (FirebaseManager.instance.isUserAnonymous()) {
      FirebaseManager.instance.currentUser().getIdToken().then((token) => {
        SessionManager.instance.anonymousUserIdToken = token;
      }); // Anonymous user ID Token if user already linked
    }

    this.getStoryDetails()
  }

  UNSAFE_componentWillMount() {
    BackHandler.addEventListener('hardwareBackPress', this.onBackPress);
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.onBackPress);
  }

  onBackPress() {
    this.props.navigation.dispatch(CommonActions.goBack())
    if (this.onStoryUpdated) {
      this.onStoryUpdated(this.state.repostCaption)
    }
    return true
  }

  getStoryDetails() {
    if (this.state.repostData != null) { return }
    const storyID = (this.state.repostData && Utils.checkData(this.state.repostData.storyID)) ? this.state.repostData.storyID : this.storyID
    let data = { data: { storyID } }
    this.props.getStoryDetails(data, this.onFetchStoryDetails)
  }

  onFetchStoryDetails(res) {
    this.hasMoreData = true
    if (!res.storyID) {
      res.storyID = this.state.repostData ? this.state.repostData.storyID : this.storyID
    }
    this.setState({ repostData: res })
  }

  setLoaderVisibility(value) {
    this.setState({ showLoadingIndicator: value })
  }

  onChange(key, value) {
    this.setState({ [key]: value }, () => {
      this.onInputBlur();
    })
  }

  onInputBlur() {
    const isDisable = this.checkIsFormFilled();
    if (this.state.nextDisabled != isDisable) {
      this.props.navigation.setParams({
        isRightDisabled: isDisable,
      })
    }
    this.setState({ nextDisabled: isDisable });
  }

  checkIsFormFilled() {
    var isDisable = true;
    if (this.state.repostCaption && this.state.repostCaption != '') {
      isDisable = false
    }
    return isDisable;
  }

  repostComic() {
    const isConnected = NetworkUtils.instance.isAvailable()
    if (!isConnected) {
      return Utils.showError(Constants.INTERNET_ERROR)
    }
    this.setLoaderVisibility(true)
    let data = { data: { storyID: this.state.repostData.storyID, comment: this.state.repostCaption, userID: this.currentUserId } }
    if (this.isEditingRepost) {
      this.props.editRepostedComic(data, this.onBackPress)
    } else {
      this.props.repostComic(data, this.onBackPress)
    }

  }

  openChapter(pathURL, item) {
    const redirectPath = Utils.resolvePath(settings.apiBaseURL, pathURL)
    if (pathURL == settings.getInfluencePageURL()) {
      Utils.navigateToInfluencePage(this.props)
    } else if (Utils.isComicURL(redirectPath)) {
      this.props.navigation.navigate('Home', { comicHome: redirectPath, hasUserInfo: true })
    } else {
      const seriesId = Utils.getChannelName(redirectPath)
      this.props.navigation.navigate('Home', { seriesID: seriesId, hasUserInfo: true })
    }
  }

  renderStoryPanel(item) {
    if (!item.item || !item.item) {
      return null;
    }
    const valueProps = { item, index: item.item.index, hideSeriesProfilePic: true, hideBottomView: true, showComments: false, hideCaption: true, pathUrl: settings.apiBaseURL + settings.homePath }
    return (
      <StoryPanel {...this.props} {...valueProps} openChapter={this.openChapter} />
    )
  }

  render() {
    const { repostCaption } = this.state

    return (
      <SafeAreaView style={styles.viewStyle}>
        <View style={{ flex: 0.1, marginEnd: scale(20), marginBottom: scale(20) }}>
          <TextInputComponent
            inputRef={(r) => { this.repostCaptionText = r }}
            multiline={false}
            refState="repostCaption"
            autoFocus={true}
            fontSize={20}
            selectionColor={Color.RED_TEXT_COLOR}
            maxLength={100}
            onChange={(refState, text) => {
              this.onChange(refState, text);
            }}
            onBlur={(refState) => {
              this.onInputBlur();
            }}
            value={repostCaption}
            placeHolder={"Write a caption..."}
            placeholderTextColor={Color.LIGHT_GREY}
            textColor={Color.BLACK_COLOR}
            textInputStyle={styles.captionTextField}
          />
        </View>
        {this.renderStoryPanel({ item: this.state.repostData })}
        {this.state.showLoadingIndicator && <LoadingIndicator />}
      </SafeAreaView >
    )
  }
}

const styles = StyleSheet.create({
  viewStyle: {
    flex: 1,
    marginTop: scale(10)
  },
  textStyle: {
    alignSelf: 'center',
    color: Color.BLACK_COLOR,
    fontSize: scale(20),
    fontFamily: SystemFont.SELECTED_FONT,
    marginBottom: Platform.OS === 'ios' ? scale(4) : scale(5)
  },
  captionTextField: {
    width: "100%",
    marginStart: scale(10),
    paddingStart: scale(0),
    paddingBottom: 0,
  },
  signUpText: {
    textAlign: 'center',
    color: Color.LIGHT_GREY,
    fontFamily: SystemFont.SELECTED_FONT,
    fontWeight: 'bold',
    fontSize: scale(20),
  },
  stepsTextStyle: {
    alignSelf: 'center',
    color: Color.LIGHT_GREY,
    fontSize: scale(16),
    fontFamily: SystemFont.SELECTED_FONT,
    fontWeight: 'bold'
  },
  errorTextStyle: {
    textAlign: 'left',
    alignSelf: 'center',
    marginTop: 20,
    color: Color.LOGIN_ERROR_COLOR,
    fontSize: Platform.OS == 'ios' ? scale(16) : scale(15),
    fontFamily: SystemFont.SELECTED_FONT
  },
  phoneText: {
    color: Color.BLACK_COLOR,
    fontFamily: SystemFont.SELECTED_FONT,
    fontWeight: 'bold',
    fontSize: scale(22),
    marginBottom: scale(12),
    marginTop: scale(15),
    alignSelf: 'center'
  },
  nextButton: (loginDisabled) => {
    return {
      marginTop: scale(70),
      height: scale(40),
      width: "40%",
      backgroundColor: loginDisabled ? Color.DARK_GREY : Color.NEXT_BUTTON_COLOR
    }
  },
  nextButtonText: {
    fontSize: scale(19),
    fontWeight: '400',
    color: 'white',
    alignSelf: 'center',
  },

});

const mapStateToProps = (state) => {
  return {
    isLogInProcess: state.loginInfo.isLogInProcess,
  }
}

const mapDispatchToProps = (dispatch) => {
  return {
    repostComic(data, callBack) {
      dispatch(
        repostComic(data, callBack)
      )
    },
    editRepostedComic(data, callBack) {
      dispatch(
        editRepostedComic(data, callBack)
      )
    },
    getStoryDetails(data, callBack) {
      dispatch(
        getStoryDetails(data, callBack)
      )
    },
  }
}
export default connect(mapStateToProps, mapDispatchToProps)(RepostScreen)