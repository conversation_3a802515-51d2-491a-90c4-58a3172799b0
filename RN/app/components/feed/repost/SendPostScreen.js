
import React, { Component } from 'react'
import { Dimensions, StyleSheet, Image, BackHandler, Platform, Keyboard, KeyboardAvoidingView, TouchableWithoutFeedback, } from 'react-native'
import { Button, View, Text } from 'native-base'
import TextInputComponent from '../../../config/TextInputComponent'
import { Color } from '../../../config/Color'
import { SystemFont } from '../../../config/Typography'
import { scale } from 'react-native-size-matters'
import { Constants } from '../../../config/Constants'
import { connect } from 'react-redux'
import { navigationStyle } from '../../../config/styles'
import InviteFriendsPage from '../../friendsPackage/page/InviteFriendsPage'
import { getFriendList, sendPost, repostComic, shareInviteLink } from '../../../redux/actions/actions'
import { LoadingIndicator } from '../../LoadingIndicator'
import { Utils } from '../../../config/Utils'
import FriendsHeaderBar from '../../friendsPackage/shared/FriendsHeaderBar'
import DeepLinkManager from '../../../config/DeepLinkManager'
import { CommonActions } from '@react-navigation/native';
import NetworkUtils from '../../../config/NetworkUtils'
import { ThemeContext } from '../../../Contexts'
import ShareBottomSheet from '../../ShareBottomSheet'


class SendPostScreen extends Component {

  static navigationOptions = {
    header: (props) => <FriendsHeaderBar {...props} />
  }

  constructor(props) {
    super(props)

    let params = this.props.route.params

    this.state = {
      messageText: '',
      showLoadingIndicator: false,
      allContacts: [],
      selectedContacts: [],
      showTextInputAboveKB: false,
      sendToAllFriend: params && params.sendToAllFriend,
      showBottomSheetFooter: false,
      showBottomSheet: false,
      showInviteBottomSheet: false
    }

    this.currentUserId = Utils.getCurrentUserId()
    this.clickedStoryID = params && params.clickedStoryID
    this.clickedPanelItems = params && params.clickedPanelItems
    this.respotType = params && params.respotType
    this.showBottomSheet = false
    this.configBottomSheet = ''

    this.onInviteFriendButtonTap = this.onInviteFriendButtonTap.bind(this)
    this.navigateToUserProfile = this.navigateToUserProfile.bind(this)
    this.sendPost = this.sendPost.bind(this)
    this.repostComic = this.repostComic.bind(this)
    this.getFriendList = this.getFriendList.bind(this)
    this.sortListAlphabatically = this.sortListAlphabatically.bind(this)
    this.updateListData = this.updateListData.bind(this)
    this.onBackPress = this.onBackPress.bind(this)
    this._keyboardDidShow = this._keyboardDidShow.bind(this)
    this._keyboardDidHide = this._keyboardDidHide.bind(this)
    this.setLoaderVisibility = this.setLoaderVisibility.bind(this)
    this.methodCallBack = this.methodCallBack.bind(this)
    this.navigateToPostCommentScreen = this.navigateToPostCommentScreen.bind(this)
    this.getContactsToSendRequest = this.getContactsToSendRequest.bind(this)
    this.updateSelectedContacts = this.updateSelectedContacts.bind(this)
    this.onChange = this.onChange.bind(this)
    this._listEmptyComponent = this._listEmptyComponent.bind(this)
    this.renderInviteActionSheet = this.renderInviteActionSheet.bind(this)
    this.closeBottomSheet = this.closeBottomSheet.bind(this)

    this.onStoryUpdated = params && params.onStoryUpdated

  }

  componentDidMount() {
    this.props.navigation.setParams({
      onBackPress: this.onBackPress,
      isLoading: this.state.showLoadingIndicator,
      leftButtonText: "Back",
      title: Constants.SEND_POST,
    })

    this.getFriendList()
  }

  UNSAFE_componentWillMount() {
    this.keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', this._keyboardDidShow);
    this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', this._keyboardDidHide);
    BackHandler.addEventListener('hardwareBackPress', this.onBackPress);
  }

  componentWillUnmount() {
    this.keyboardDidShowListener.remove();
    this.keyboardDidHideListener.remove();
    BackHandler.removeEventListener('hardwareBackPress', this.onBackPress);
  }

  onBackPress() {
    this.props.navigation.dispatch(CommonActions.goBack())
    return true
  }

  _keyboardDidShow() {
    this.setState({ showTextInputAboveKB: true })
  }

  _keyboardDidHide() {
    this.setState({ showTextInputAboveKB: false })
  }

  setLoaderVisibility(value) {
    this.setState({ showLoadingIndicator: value }, () => {
      this.props.navigation.setParams({
        isLoading: this.state.showLoadingIndicator,
      })
    })
  }

  getFriendList() {
    this.setLoaderVisibility(true)
    this.props.getFriendList(this.updateListData)
  }

  updateListData(data, key) {
    this.sortListAlphabatically(data) // need to remove this method from here in future release.
    this.setState({ [key]: data })
    this.setLoaderVisibility(false)
  }

  sortListAlphabatically(data) {
    data.sort((a, b) => {
      if (a.firstName) {
        if (b.firstName) {
          return a.firstName.localeCompare(b.firstName)
        } else {
          return a.firstName.localeCompare(b.lastName)
        }
      } else if (a.lastName) {
        return a.lastName.localeCompare(b.lastName)
      }
    })
  }

  methodCallBack(data) {
    if (data) {
      if (this.clickedPanelItems && this.onStoryUpdated) {
        let panelItems = JSON.parse(JSON.stringify(this.clickedPanelItems))
        panelItems.repostCount++
        this.onStoryUpdated(panelItems, false)
      }
      this.navigateToPostCommentScreen(data)
    }
    if (this.state.showLoadingIndicator) this.setLoaderVisibility(false)
  }

  navigateToPostCommentScreen(data) {
    const { repostedStoryID, sentStoryID } = data
    var storyData = { storyID: repostedStoryID ? repostedStoryID : sentStoryID, forceOpen: true }
    Utils.navigateToSubRouteWithParams('DrawerHome', Constants.POST_COMMENTS_SCREEN, this.props, { storyData })
  }

  async sendPost(buttonAction) {
    this.setLoaderVisibility(true)
    let sendType = Constants.INDIVIDUAL
    if (buttonAction == Constants.SEND_TO_GROUP) {
      sendType = Constants.GROUP
    }

    let data = {
      data: {
        storyID: this.clickedStoryID,
        comment: this.state.messageText,
        sendType: sendType,
        friends: await this.getContactsToSendRequest(),
      }
    }

    this.props.sendPost(data, this.methodCallBack)
  }

  repostComic() {
    const isConnected = NetworkUtils.instance.isAvailable()
    if (!isConnected) {
      return Utils.showError(Constants.INTERNET_ERROR)
    }
    this.setLoaderVisibility(true)
    let data = { data: { storyID: this.clickedStoryID, comment: this.state.messageText, userID: this.currentUserId } }
    this.props.repostComic(data, this.methodCallBack)
  }

  async getContactsToSendRequest() {
    let requests = []
    if (this.state.selectedContacts.length > 0) {
      for (const index in this.state.selectedContacts) {
        let phoneNumber = this.state.selectedContacts[index].selContact
        let shouldCreateLink = true
        let deepLinkURL = {}
        for (const key in this.state.friends) {
          let friendNumber = this.state.friends[key].primaryContact
          if (phoneNumber == friendNumber) {
            shouldCreateLink = false
            break
          }
        }
        if (shouldCreateLink) {
          deepLinkURL = await DeepLinkManager.instance.getBranchLinkURL(Constants.ADD_FRIEND, null, this.state.selectedContacts[index].selContact)
        }

        let fullName = ''

        if (this.state.selectedContacts[index].fullName) {
          fullName = this.state.selectedContacts[index].fullName
        }

        if (this.state.selectedContacts[index].firstName) {
          fullName = this.state.selectedContacts[index].firstName
        }
        if (this.state.selectedContacts[index].lastName) {
          fullName = fullName + " " + this.state.selectedContacts[index].lastName
        }

        let data = {
          name: fullName,          
          image: '',
          deepLinkURL: deepLinkURL,
          receiverUID: this.state.selectedContacts[index].uid ? this.state.selectedContacts[index].uid : null
        }
        requests.push(data)
      }
    }
    return requests
  }


  updateSelectedContacts(selectedContacts) {
    this.setState({ selectedContacts }, () => {
      this.setState({ showBottomSheetFooter: this.state.selectedContacts.length > 0 })
    })
  }

  onInviteFriendButtonTap() {
    this.configBottomSheet = Constants.INVITE_FRIENDS
    this.setState({ showInviteBottomSheet: true })
  }

  navigateToUserProfile(item) {
    let userId = item.userId ? item.userId : item.item ? item.item.user.userId : (item.user && item.user.userId) ? item.user.userId : item.uid ? item.uid : null
    if (userId) {
      var subRoutesParams = { clickedUserID: userId }
      Utils.navigateToSubRouteWithParams(Constants.DRAWER_HOME, Constants.USER_PROFILE_SCREEN, this.props, subRoutesParams)
    }
  }

  onChange(key, value) {
    this.setState({ [key]: value })
  }

  closeBottomSheet() {
    this.setState({ showInviteBottomSheet: false })
  }

  renderInviteActionSheet() {
    const valueProps = { closeBottomSheet: this.closeBottomSheet, userDetails: this.props.userDetails, shareInviteLink: this.props.shareInviteLink }
    return (
      <ShareBottomSheet {...valueProps} configShareSheetFor={this.configBottomSheet} />
    )
  }

  renderBottomSheetFotter(moreFlex) {
    const { sendToAllFriend, showTextInputAboveKB, messageText } = this.state
    return (
      <View style={{ flex: moreFlex ? 0.5 : 0.25, marginLeft: navigationStyle.panelsMargin, marginRight: navigationStyle.panelsMargin }}>
        <View style={{ flex: moreFlex ? 0.7 : showTextInputAboveKB ? 0.5 : 0.3, marginTop: scale(10), marginBottom: scale(10) }}>
          <TextInputComponent
            multiline={true}
            maxLength={1000}
            refState="messageText"
            selectionColor={Color.RED_TEXT_COLOR}
            onChange={(refState, text) => {
              this.onChange(refState, text);
            }}
            fontSize={Platform.OS == "ios" ? scale(15) : scale(14)}
            value={messageText}
            keyboardType="default"
            placeHolder={"Write a message..."}
            placeholderTextColor={Color.LIGHT_GREY}
            textColor={Color.BLACK_COLOR}
            textInputStyle={styles.messageInput(moreFlex)}
          />
        </View>
        {!sendToAllFriend ?
          (this.state.selectedContacts.length > 1 ?
            <View style={{ flex: moreFlex ? 0.3 : showTextInputAboveKB ? 0.5 : 0.7, marginTop: showTextInputAboveKB ? scale(5) : 0, flexDirection: 'row', justifyContent: 'space-around' }}>
              <Button
                variant='outline'
                style={styles.whiteButtonStyle}
                onPress={() => this.sendPost(Constants.SEND_TO_EACH, messageText)}
              >
                <Text style={this.context.pBold}>{Constants.SEND_TO_EACH}</Text>
              </Button>
              < Button
                variant='solid'
                style={styles.redButtonStyle}
                onPress={() => this.sendPost(Constants.SEND_TO_GROUP, messageText)}
              >
                <Text style={[this.context.pBold, { color: this.context.colors.textInverse }]}>{Constants.SEND_TO_GROUP}</Text>
              </Button>
            </View>
            :
            <View style={{ flex: 0.7, flexDirection: 'row', justifyContent: 'space-around' }}>
              <Button
                variant='solid'
                style={[styles.redButtonStyle, { flex: 1, justifyContent: 'center', marginTop: scale(10) }]}
                onPress={() => this.sendPost(Constants.SEND_TO_EACH, messageText)}
              >
                <Text style={[this.context.pBold, { color: this.context.colors.textInverse }]}>{Constants.SEND}</Text>
              </Button>
            </View>
          )
          :
          <View style={{ flex: 0.7, flexDirection: 'row', justifyContent: 'space-around' }}>
            <Button
              variant='solid'
              style={[styles.redButtonStyle, { flex: 1, justifyContent: 'center', marginTop: scale(10) }]}
              onPress={() => this.repostComic()}
            >
              <Text style={[this.context.pBold, { color: this.context.colors.textInverse }]}>{Constants.SEND}</Text>
            </Button>
          </View>
        }
      </View>
    )
  }

  imageStyle = (w = 800, h = 600, margin = scale(15)) => {
    let dimensions = Dimensions.get('window')

    let aspectRatio = (dimensions.width - ((margin) * 2)) / w

    return {
      marginTop: scale(15),
      width: w * aspectRatio,
      height: h * aspectRatio,
      resizeMode: 'contain'
    }
  }

  _listEmptyComponent() {
    return (
      <View style={{ marginLeft: scale(15) }}>
        <TouchableWithoutFeedback
          onPress={() => this.onInviteFriendButtonTap()}>
          <Image
            style={this.imageStyle()}
            source={{ uri: Utils.getPanelURL(Constants.WHY_INVITE_FRIEND_WITH_BUTTON_IMAGE) }}
          />
        </TouchableWithoutFeedback>
      </View>
    )
  }

  renderBottomSheet() {
    const { showTextInputAboveKB, sendToAllFriend, showBottomSheetFooter, showLoadingIndicator } = this.state
    const valueProps = {
      ...this.state, ...this.props, updateSelectedContacts: this.updateSelectedContacts, isForRepost: true,
      navigateToUserProfile: this.navigateToUserProfile, sendPost: this.sendPost, repostComic: this.repostComic,
      onInviteFriendButtonTap: this.onInviteFriendButtonTap
    }
    let moreFlex = this.respotType == Constants.ALL_FRIENDS

    return (
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? !moreFlex ? "padding" : null : null}
        style={{ flex: 1 }}
      >
        <View style={{ flex: 1, marginBottom: showTextInputAboveKB ? navigationStyle.navHeight + scale(50) : 0 }}>
          {(!sendToAllFriend && this.state.allContacts.length > 0) &&
            <View style={{ flex: !showBottomSheetFooter ? 1 : 0.75, marginLeft: navigationStyle.panelsMargin, marginRight: navigationStyle.panelsMargin, }}>
              <InviteFriendsPage {...valueProps} />
            </View>
          }
          {(this.state.allContacts.length == 0 && !showLoadingIndicator) && this._listEmptyComponent()}
          {!(this.state.allContacts.length == 0 && !showLoadingIndicator) &&
            <View style={[styles.separatorStyle, { backgroundColor: this.context.colors.separators }]} />
          }
          {(showBottomSheetFooter || this.respotType == Constants.ALL_FRIENDS) && this.renderBottomSheetFotter(moreFlex)}
        </View>
        {this.state.showInviteBottomSheet && this.renderInviteActionSheet()}
        {showLoadingIndicator && <LoadingIndicator />}
      </KeyboardAvoidingView >
    )
  }


  render() {
    return (
      this.renderBottomSheet()
    )
  }
}

SendPostScreen.contextType = ThemeContext

const styles = StyleSheet.create({
  messageInput: (moreFlex) => {
    return {
      height: moreFlex ? scale(150) : scale(40),
      flexDirection: "row",
      justifyContent: "center",
      alignItems: "center",
      fontFamily: SystemFont.SELECTED_FONT,
      paddingEnd: scale(8),
      paddingStart: scale(8),
      paddingBottom: scale(2),
    }
  },
  redButtonStyle: {
    alignSelf: 'flex-start',
    borderRadius: 6,
    backgroundColor: Color.RED_BACKGROUND_COLOR,
    height: 40,
    borderColor: Color.RED_BACKGROUND_COLOR,
    elevation: 0,
    borderLeftWidth: 0,
    borderTopWidth: 0,
    borderBottomWidth: 0,
    borderRightWidth: 0,
    borderWidth: 0,
    paddingTop: 0,
    paddingBottom: 0,
  },
  whiteButtonStyle: {
    alignSelf: 'flex-start',
    backgroundColor: 'transparent',
    height: 40,
    borderColor: Color.LIGHTER_GREY,
    elevation: 0,
    borderRadius: 6,
    paddingTop: 0,
    paddingBottom: 0,
  },
  separatorStyle: {
    height: scale(1),
    width: '100%'
  },
});

const mapStateToProps = (state) => {
  return {
    userDetails: state.loginInfo.userDetails,
  }
}

const mapDispatchToProps = (dispatch) => {
  return {
    getFriendList(callback) {
      dispatch(
        getFriendList(callback)
      )
    },
    sendPost(data, callBack) {
      dispatch(
        sendPost(data, callBack)
      )
    },
    repostComic(data, callBack) {
      dispatch(
        repostComic(data, callBack)
      )
    },
    shareInviteLink(requestedData, callback) {
      dispatch(
        shareInviteLink(requestedData, callback)
      )
    }
  }
}
export default connect(mapStateToProps, mapDispatchToProps)(SendPostScreen)