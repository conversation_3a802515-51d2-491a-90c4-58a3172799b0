import React, { Component } from 'react';
import { StyleSheet, Image, Platform, TouchableOpacity } from 'react-native'
import { scale } from 'react-native-size-matters';
import { SystemFont } from '../../config/Typography';
import { Color } from '../../config/Color';
import { Text, View, Button } from 'native-base';
import ImagePlaceHolder from '../ImagePlaceHolder';
import { Constants } from '../../config/Constants';
import { Utils } from '../../config/Utils';
import SessionManager from '../../config/SessionManager';
import BadgesView from '../BadgesView';
import { userProfilePic } from '../../config/styles';
import { ThemeContext } from '../../Contexts';

export default class UserListPanel extends Component {
  constructor(props) {
    super(props)

    this.state = {
      errorInImageLoading: false
    }

    this.renderProfileImage = this.renderProfileImage.bind(this)
    this.renderImagePlaceHolder = this.renderImagePlaceHolder.bind(this)
    this.renderReactionImage = this.renderReactionImage.bind(this)
    this.giftItems = SessionManager.instance.getGiftItemList()

    this.currentUserId = Utils.getCurrentUserId()
  }

  renderImagePlaceHolder(fullName, size = 32) {
    return (
      Utils.checkData(fullName)
        ?
        <ImagePlaceHolder
          backgroundColor={Color.PROFILE_PLACE_HOLDER_BG}
          showCircularBorder={true}
          textColor={Color.COMMENT_TEXT_COLOR}
          size={size}
          type={'circle'}>{fullName}</ImagePlaceHolder>
        : null
    )
  }

  renderProfileImage(size = 32) {
    const { photoURL = null } = this.props.userDetails
    const { user } = this.props.item.item
    const { userId, image } = user
    const fullName = user.name ? user.name : Constants.NON_SIGNED_IN_USER

    return (
      Utils.checkData(user.image)
        ?
        !this.state.errorInImageLoading
          ? <Image resizeMode={'cover'} style={[userProfilePic(size), { borderColor: this.context.colors.chatBubbles }]} source={{ uri: this.currentUserId == userId ? photoURL : image }} onError={() => { this.setState({ errorInImageLoading: true }) }} />
          : this.renderImagePlaceHolder(fullName, size)
        : this.renderImagePlaceHolder(fullName, size)
    )
  }

  renderReactionImage(item) {
    return this.giftItems.map((value, index) => {
      const { image, reactionName } = value
      if (item.reaction != reactionName) {
        return null
      }
      return (
        <View>
          <Image source={item.reaction.toLowerCase() == Constants.LIKE.toLowerCase() ? require('../../../assets/like_fill.png') : { uri: image }} style={reactionName.toLowerCase() == Constants.LIKE.toLowerCase() ? styles.redHeartIcon : styles.navigationIcons} />
        </View>
      )
    })
  }

  render() {
    const { item } = this.props.item
    const { user } = this.props.item.item
    let fullName = user.name ? user.name : Constants.NON_SIGNED_IN_USER

    if (user.userId == this.currentUserId && fullName == Constants.NON_SIGNED_IN_USER) {
      fullName = `${Constants.NON_SIGNED_IN_USER} (You)`
    }

    const strDate = Utils.convertTimeSpanIntoTimeAgo(item.createdAt)

    return (
      <View style={styles.mainContainer}>
        <View style={{ flexDirection: 'row', flex: 1 }}>
          <TouchableOpacity
            style={styles.profileView}
            onPress={() => this.props.navigateToUserProfile(item)}
            transparent>
            {this.renderProfileImage()}
          </TouchableOpacity>
          <View style={{ flex: 1, alignSelf: 'center' }}>
            <View style={{ flexDirection: 'row', flex: 0.75, alignItems: 'center' }}>
              <TouchableOpacity
                onPress={() => this.props.navigateToUserProfile(item)}
                style={{ flex: 0.75 }}
                transparent>
                <View>
                  <View style={{ flexDirection: 'row' }}>
                    <Text style={styles.userText}>{fullName ? fullName.trim() : fullName}</Text>
                    {<BadgesView badges={user.badges} />}
                  </View>
                  <Text style={styles.dateTimeText}>{strDate}</Text>
                </View>
              </TouchableOpacity>
              <View style={{ flex: 0.25, flexDirection: 'row', justifyContent: 'flex-end', marginRight: scale(10) }}>
                {this.renderReactionImage(item)}
              </View>
              {(user.userId != this.currentUserId && false) &&
                <Button
                  variant='solid'
                  onPress={() => this.props.sendFriendRequest(item)}
                  style={[styles.inviteFriendButton(Color.RED_BACKGROUND_COLOR), { flex: 0.4, height: scale(30) }]}
                >
                  <Text style={styles.addFButtonText(this.context.colors.textInverse)}>{Constants.ADD_FRIEND_TITLE_CASE}</Text>
                </Button>
              }
            </View>
            <View style={styles.seprator} />
          </View>
        </View>
      </View >
    )
  }
}

UserListPanel.contextType = ThemeContext;

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    alignItems: 'center',
    marginLeft: scale(10),
    marginRight: scale(10),
    marginBottom: scale(10),
    marginTop: scale(10),
  },
  profileView: {
    flex: 0.25,
    alignContent: 'flex-end'
  },
  navigationIcons: {
    alignSelf: 'center',
    width: scale(20),
    height: scale(20),
  },
  redHeartIcon: {
    width: scale(20),
    height: scale(20),
    tintColor: Color.TOP_NAVBAR_ICON_TINT_COLOR,
  },
  userText: {
    color: Color.DESCRIPTION_FONT_COLOR,
    fontFamily: SystemFont.SELECTED_FONT,
    fontSize: Platform.OS == 'ios' ? scale(13) : scale(12),
    fontWeight: 'bold',
    textAlign: 'left',
    alignSelf: 'flex-start'
  },
  dateTimeText: {
    color: Color.DATE_TIME_COLOR,
    fontFamily: SystemFont.SELECTED_FONT,
    textAlign: 'left',
    fontSize: Platform.OS == 'ios' ? scale(11) : scale(10),
  },
  seprator: {
    height: 0.5,
    width: '100%',
    backgroundColor: Color.LIGHTER_GREY,
    marginTop: scale(10)
  },
  inviteFriendButton: (colorType) => {
    return {
      flex: 0.25,
      backgroundColor: colorType,
      height: 30,
      borderRadius: 6,
      borderColor: colorType,
      elevation: 0,
      justifyContent: 'center',
      alignSelf: 'center',
    }
  },
  addFButtonText: (colorType) => {
    return {
      color: colorType,
      fontFamily: SystemFont.SELECTED_FONT,
      fontWeight: 'bold',
      fontSize: Platform.OS == 'ios' ? scale(13) : scale(12),
    }
  },
})