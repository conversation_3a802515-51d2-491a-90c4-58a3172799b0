import { Component } from 'react';
import { Constants } from '../../config/Constants';
import { Utils } from '../../config/Utils';
import NetworkUtils from '../../config/NetworkUtils';
import UserSession from '../../config/UserSession';
import FileCache from '../../config/FileCache';
import { Ajax } from '../../api/ajax';
import SessionManager from '../../config/SessionManager';
import FirebaseManager from '../../config/FirebaseManager';
import DeepLinkManager from '../../config/DeepLinkManager';
import moment from 'moment';
import Types from '../../redux/actions/types';

export default class FeedComponent extends Component {

  constructor(props) {
    super(props)
    this.state = {
      editCommentItems: {},
      sheetMessage: Constants.SIGN_IN_ACTION_SHEET_MESSAGE,
      sheetOptions: [Constants.SIGN_IN, Constants.CANCEL],
      cancelOptionIndex: 1,
      showPageRefresh: false
    }

    this.hasMoreData = true
    this.isAPIInProgress = false
    this.isRefreshingInProgress = false
    this.isCompletePageLoadInProgress = false
    this.performanceTrace = null
    this.isEmptyFeedDisplayed = false

    this.reqData = {}
    this.comicURL = null
    this.initialPanels = []
    this.nonStoryPanelsMap = new Map()
    this.currentPageStatus = {}

    this.postUserComment = this.postUserComment.bind(this)
    this.updateFeeds = this.updateFeeds.bind(this)
    this.updateComments = this.updateComments.bind(this)
    this.getUserFeeds = this.getUserFeeds.bind(this)
    this.likeDislikeFeed = this.likeDislikeFeed.bind(this)
    this.likeDislikeComment = this.likeDislikeComment.bind(this)
    this.onIconPress = this.onIconPress.bind(this)
    this.onCommentLikeCountPress = this.onCommentLikeCountPress.bind(this)
    this.onShowCommentPress = this.onShowCommentPress.bind(this)
    this.updateLikeData = this.updateLikeData.bind(this)
    this.getIsStoryLiked = this.getIsStoryLiked.bind(this)
    this.onReplyPress = this.onReplyPress.bind(this)
    this.navigateToUserProfile = this.navigateToUserProfile.bind(this)
    this.updateCommentID = this.updateCommentID.bind(this)
    this.editComment = this.editComment.bind(this)
    this.deleteComment = this.deleteComment.bind(this)
    this.deleteRepost = this.deleteRepost.bind(this)
    this.editRepost = this.editRepost.bind(this)
    this.onFlagCommentTap = this.onFlagCommentTap.bind(this)
    this.onBlockUserTap = this.onBlockUserTap.bind(this)
    this.loadSeriesData = this.loadSeriesData.bind(this)
    this.fetchComic = this.fetchComic.bind(this)
    this.isFeedEmpty = this.isFeedEmpty.bind(this)
    this.navigateToListScreen = this.navigateToListScreen.bind(this)
    this.getStoryIndex = this.getStoryIndex.bind(this)
    this.getStoryIndexByActionURL = this.getStoryIndexByActionURL.bind(this)
    this.onStoryUpdated = this.onStoryUpdated.bind(this)
    this.addCommentInStory = this.addCommentInStory.bind(this)
    this.addSentPostInStory = this.addSentPostInStory.bind(this)
    this.updateGiftsData = this.updateGiftsData.bind(this)
    this.onGiftItemSent = this.onGiftItemSent.bind(this)
    this.addLikeInStory = this.addLikeInStory.bind(this)
    this.removeLikeInStory = this.removeLikeInStory.bind(this)
    this.canUserComment = this.canUserComment.bind(this)
    this.addLikeInComment = this.addLikeInComment.bind(this)
    this.removeLikeInComment = this.removeLikeInComment.bind(this)
    this.getMonthWiseComics = this.getMonthWiseComics.bind(this)

    if (this.props.navigation.params) {
      this.prevOnStoryUpdated = this.props.route.params.onStoryUpdated
    }

    this.isUpdatingCommentCount = false
  }

  getUserFeeds(switchValue, forceReload = false) {
    if (!forceReload && (!this.hasMoreData || this.isAPIInProgress || Utils.isComicURL(this.comicURL))) {
      return
    }

    const isHomePage = Utils.isHomeURL(this.comicURL)

    if (isHomePage && forceReload) {
      this.state.comicData.panels = []
    }

    this.setState({ showFooterPlaceHolder: true })
    this.isAPIInProgress = true

    let lastPanelId = ''
    if (this.state.comicData.panels && this.state.comicData.panels.length > 0) {
      var feedLength = this.state.comicData.panels.length
      lastPanelId = this.state.comicData.panels[feedLength - 1].myFeedID
      if (!lastPanelId) {
        lastPanelId = this.state.comicData.panels[feedLength - 1].storyID
      }
    }

    let mainData = { startAfter: lastPanelId, records: !isHomePage ? Constants.SERIES_EPISODES_PAGINATION_COUNT : Constants.FEED_PAGINATION_COUNT, likeInfo: 1 } // Remove linkInfo attribute to not fetch the story liked or not

    if (!isHomePage) {
      mainData.series = this.reqData.series
      mainData.domain = this.reqData.domain
    }

    if (SessionManager.instance.getActiveSeriesTab() === Constants.SHOW_UNREAD_COMICS) {
      mainData.onlyUnread = true
    }

    if (Utils.isChannelURL(this.comicURL)) {
      mainData.type = Constants.TEMPLATE_TYPE_LIST
    }

    let data = { data: mainData }
    if (isHomePage) {
      if (!switchValue) {
        this.props.getUserFeeds(data, this.updateFeeds)
      } else {
        this.props.getAllComics(data, this.updateFeeds)
      }
    } else {
      this.props.getSeriesComics(data, this.updateFeeds)
      this.loadSeriesData(this.comicURL)
    }
  }

  updateFeeds(newFeed, loadMore = true) {
    this.isCompletePageLoadInProgress = false
    this.isAPIInProgress = false
    if (newFeed.status == 500) {
      return
    }
    if (!newFeed || newFeed.length <= 0 || !loadMore) {
      this.hasMoreData = false
    }

    this.getMonthWiseComics(newFeed)

    var newPanels = []
    if (this.state.comicData && this.state.comicData.panels.length == 0) {
      newPanels = newPanels.concat(this.initialPanels)
    }

    if (this.isFeedEmpty() && (!newFeed || newFeed.length <= 0) && (!this.isEmptyFeedDisplayed || (this.state.comicData && this.state.comicData.panels.length == 0))) {
      newFeed = Utils.getNotFollowSeriesPanel();
      this.isEmptyFeedDisplayed = true
    }

    newPanels = newPanels.concat(this.state.comicData.panels).concat(newFeed)
    const newData = JSON.parse(JSON.stringify(this.state.comicData))
    newData.panels = newPanels
    this.setState({ comicData: newData, showFooterPlaceHolder: false }, () => {
      FirebaseManager.instance.stopTrace(this.performanceTrace)
      const pendingNotif = SessionManager.instance.getPendingNotification()
      const pendingDynamicLink = SessionManager.instance.getPendingDynamicLink()

      if (pendingNotif != null) {
        FirebaseManager.instance.handleNotification(pendingNotif, false)
        SessionManager.instance.setPendingNotification(null)
      } else if (pendingDynamicLink != null) {
        FirebaseManager.instance.handleEmailAuthLink(pendingDynamicLink)
        SessionManager.instance.setPendingDynamicLink(null)
      } else {
        const pendingBranchLinkData = SessionManager.instance.getPendingBranchLink()
        if (pendingBranchLinkData != null) {
          DeepLinkManager.instance.handleBranchEvent(pendingBranchLinkData)
          SessionManager.instance.setPendingBranchLink(null)
        }
      }
    })
    this.isRefreshingInProgress = false
  }

  getMonthWiseComics(comicList) {
    if (!comicList || comicList.length === 0 || !Utils.isChannelURL(this.comicURL)) {
      return null;
    }

    const { comicData } = this.state;
    const comicPanels = comicData && comicData.panels;

    try {
      let monthWiseComics = [];
      let lastFetchedMonthComics = []

      comicList.forEach(seriesComic => {
        const createdAtDate = new Date(seriesComic.createdAt);

        // Skip invalid or future dates
        if (isNaN(createdAtDate.getTime()) || (seriesComic.updatedAt && new Date(seriesComic.updatedAt) > new Date())) {
          return;
        }

        const monthKey = new Date(createdAtDate.getFullYear(), createdAtDate.getMonth());
        const monthTimestamp = monthKey.getTime();

        const isLastListPanel = comicPanels.length > 0 && comicPanels[comicPanels.length - 1].template === Constants.TEMPLATE_TYPE_LIST;
        const lastRenderedDate = isLastListPanel ? comicPanels[comicPanels.length - 1].createdAt : null;

        const fromRenderedMonth = lastRenderedDate && Utils.isSameMonthAndYear(monthTimestamp, lastRenderedDate);

        // If the comic belongs to the same month
        if (fromRenderedMonth) {
          const lastPanel = comicPanels[comicPanels.length - 1];
          if (lastPanel && lastPanel["isLastComic"]) {
            delete lastPanel.isLastComic; // Remove the `isLastComic` property if it exists
          }
          lastFetchedMonthComics.push(seriesComic)
        } else {
          // Add the comic to its respective month
          const lastComicMonth = monthWiseComics[monthWiseComics.length - 1] && monthWiseComics[monthWiseComics.length - 1]["comicForMonth"];
          const isSameMonthComic = lastComicMonth && Utils.isSameMonthAndYear(monthTimestamp, lastComicMonth);

          if (isSameMonthComic) {
            monthWiseComics[monthWiseComics.length - 1].comicsForCurrentMonth.push(seriesComic);
          } else {
            monthWiseComics.push({
              comicForMonth: monthTimestamp,
              monthToDisplay: moment(monthKey).format('MMMM YYYY'),
              comicsForCurrentMonth: [seriesComic]
            });
          }
        }
      });

      if (lastFetchedMonthComics.length > 0) {
        lastFetchedMonthComics[lastFetchedMonthComics.length - 1].isLastComic = true;
      }

      // Mark the first and last comic in each month
      monthWiseComics.forEach(monthData => {
        const comicsForCurrentMonth = monthData.comicsForCurrentMonth;
        if (comicsForCurrentMonth.length > 0) {
          comicsForCurrentMonth[0].isFirstComic = true;
          comicsForCurrentMonth[0].dateTitle = monthData.monthToDisplay;
          comicsForCurrentMonth[comicsForCurrentMonth.length - 1].isLastComic = true;
        }
      });
    } catch (error) {
      console.error('Error processing comic:', comic, error);
    }
  }

  async loadSeriesData(url) {
    try {
      FileCache.default.isFileStaleForURL(url)
        .then(async isStale => {
          const comics = await this.fetchComic(url, isStale)

          const index = comics.panels.findIndex((item) => {
            return item && item.title && item.title.toLowerCase() == "latest comics"
          })

          const length = comics.panels.length
          let episodPanels = comics.panels.splice(index + 1, length)

          let formattedPanels = []
          if (episodPanels) {
            episodPanels.forEach(element => {
              formattedPanels.push({
                image: element.panelItem, ...element
              })
            });
          }
          let channelName = Utils.getChannelName(url)
          await FileCache.default.writeFile(channelName, JSON.stringify(formattedPanels.reverse()))
        })
    } catch (error) {
      Utils.log(error)
    }
  }

  async fetchComic(url, isStale) {
    if (isStale) {
      return Ajax.fetchAndSaveComic(url, true, false)
    } else {
      return FileCache.default.getFileContentForURL(url)
        .then(
          result => {
            let responseJson = JSON.parse(result)
            return responseJson.comics;
          }
        )
        .catch(error => Utils.log(error))
    }
  }

  isFeedEmpty() {
    if (Utils.isNotFollowingAnySeries(this.props.alerts) && (!this.props.userDetails.friendCount || this.props.userDetails.friendCount == 0) && !Utils.isChannelURL(this.props.pathUrl)) {
      return true
    }

    if (Utils.isNotFollowingAnySeries(this.props.alerts) && this.props.userDetails.friendCount && this.props.userDetails.friendCount > 0 && this.state.comicData && this.state.comicData.panels && this.state.comicData.panels.length == 0 && !Utils.isChannelURL(this.props.pathUrl)) {
      return true
    }

    return false
  }

  getIsStoryLiked(storyIDs) {
    let data = { data: { storyIDs: storyIDs } }
    this.props.getFeedLikes(data, this.updateLikeData)
  }

  updateLikeData(requestedData, likeData = []) {

    if (Utils.isEmptyObject(this.state.comicData)) {
      return
    }

    var feedsData = JSON.parse(JSON.stringify(this.state.comicData))
    if (likeData && likeData.length > 0) {
      for (const index in likeData) {

        let actionURL = likeData[index].action
        let completeURL = Utils.resolvePath(this.comicURL, actionURL)  //merging giftedItem/like data for comic page
        if (Utils.isComicURL(actionURL) && this.comicURL == completeURL) {
          feedsData = { ...likeData[index], ...feedsData }
          break
        }

        let storyID = likeData[index].storyId
        for (const key in feedsData.panels) {
          let feedID = feedsData.panels[key].storyID
          if (feedID == storyID) {
            feedsData.panels[key].isLiked = likeData[index].isLiked
            feedsData.panels[key].giftedItems = likeData[index].giftedItems ? likeData[index].giftedItems : {}
            break
          }
        }
      }
    }
    this.setState({ comicData: feedsData })
  }

  getStoryComments(requestedData) {
    this.getFeedComments(requestedData)
  }

  getFeedComments(requestedData) {
    if (requestedData.data) {
      for (const key in requestedData.data.storyIDs) {
        let data = { data: { storyID: requestedData.data.storyIDs[key], records: Constants.FEED_PAGE_COMMENT_PAGINATION_COUNT, startAfter: "", sortingOrder: Constants.DESC } }
        this.props.getFeedComments(data, this.updateComments)
      }
    }
  }

  updateComments(requestedData, commentData) {
    if (Utils.isEmptyObject(this.state.comicData)) {
      return
    }
    var feedsData = JSON.parse(JSON.stringify(this.state.comicData))
    if (commentData && commentData.length > 0) {
      for (const key in feedsData.panels) {
        if (feedsData.panels[key]) {
          let feedID = feedsData.panels[key].storyID
          if (requestedData.data.storyID === feedID) {
            feedsData.panels[key].userComments = commentData
            break
          }
        }
      }

    }
    this.setState({ comicData: feedsData })
  }

  deleteRepost(item) {
    const isConnected = NetworkUtils.instance.isAvailable()
    if (!isConnected) {
      return Utils.showError(Constants.INTERNET_ERROR)
    }

    var feedsData = JSON.parse(JSON.stringify(this.state.comicData))
    for (const key in feedsData.panels) {
      if (feedsData.panels[key].storyID == item.storyID) {
        feedsData.panels.splice(key, 1)
        let data = {
          data: {
            userID: Utils.getCurrentUserId(),
            storyID: item.storyID
          }
        }

        this.setState({ comicData: feedsData })

        if (feedsData.panels[key].refType == Constants.GROUP_SENT_POST || feedsData.panels[key].refType == Constants.SENT_POST) {
          this.props.deleteSentPost(data)
        } else {
          this.props.deleteRepostedComic(data)
        }
        break
      }
    }
  }

  editRepost(item) {
    const isConnected = NetworkUtils.instance.isAvailable()
    if (!isConnected) {
      return Utils.showError(Constants.INTERNET_ERROR)
    }

    if (!UserSession.instance.checkPrevilegeAndShowMessage(this.props, this.actionSheet) || this.isUserDetailsEmpty()) {
      return
    }

    var parentRoute = Constants.FEED_SCREEN
    var subRoute = Constants.REPOST_SCREEN
    var subParams = { title: Constants.REPOST, panelItems: item, isEditingRepost: true }

    //this.props.navigation.push(subRoute, {...subParams})
    Utils.navigateToSubRouteWithParams(parentRoute, subRoute, this.props, subParams)
  }

  likeDislikeFeed(isLiked, item, index) {
    const isConnected = NetworkUtils.instance.isAvailable()
    if (!isConnected) {
      return Utils.showError(Constants.INTERNET_ERROR)
    }
    if (!isLiked) {
      Utils.vibrateDevice()
    }
    const { storyID, action, pageURL, refType = Constants.UPPERCASE_STORY } = item
    let data = { "data": { storyID: storyID, reactionType: 'LIKE', pageURL, refType } }

    let panelURL = !action ? pageURL : action
    let panelPathUrl = Utils.resolvePath(this.comicURL, panelURL)
    if (this.comicURL != panelPathUrl) {
      if (!Utils.checkData(storyID)) {
        return null
      }

      data = { "data": { storyID: storyID, reactionType: 'LIKE', action, refType } }
    }
    this.props.likeDislikeFeed(data, isLiked)
  }

  likeDislikeComment(item, isLike, subCommentIndex) {
    const isConnected = NetworkUtils.instance.isAvailable()
    if (!isConnected) {
      return Utils.showError(Constants.INTERNET_ERROR)
    }

    const { commentId, storyId } = item
    let subCommentId = subCommentIndex != null ? item.subComments[subCommentIndex].subCommentId : null
    let feedsData = JSON.parse(JSON.stringify(this.state.comicData))
    let storyIndex = null
    if (feedsData.panels.length > 0) {
      const searchStoryIndex = feedsData => feedsData.storyID === storyId;
      storyIndex = feedsData.panels.findIndex(searchStoryIndex)
      if (storyIndex >= 0 && feedsData.panels[storyIndex].userComments && feedsData.panels[storyIndex].userComments.length > 0) {
        for (const commentIndex in feedsData.panels[storyIndex].userComments) {
          const commentID = feedsData.panels[storyIndex].userComments[commentIndex].commentId
          if (subCommentIndex === null && commentID === commentId) {
            feedsData.panels[storyIndex].userComments[commentIndex].isLiked = !feedsData.panels[storyIndex].userComments[commentIndex].isLiked
            if (feedsData.panels[storyIndex].userComments[commentIndex].isLiked) {
              feedsData.panels[storyIndex].userComments[commentIndex].reactionsCount++
            } else {
              feedsData.panels[storyIndex].userComments[commentIndex].reactionsCount--
            }
            break
          } else if (commentID == commentId && subCommentId && feedsData.panels[storyIndex].userComments[commentIndex].subComments && feedsData.panels[storyIndex].userComments[commentIndex].subComments.length > 0) {
            for (const subCommentIndex in feedsData.panels[storyIndex].userComments[commentIndex].subComments) {
              const subCommentID = feedsData.panels[storyIndex].userComments[commentIndex].subComments[subCommentIndex].subCommentId
              if (subCommentID === subCommentId) {
                feedsData.panels[storyIndex].userComments[commentIndex].subComments[subCommentIndex].isLiked = !feedsData.panels[storyIndex].userComments[commentIndex].subComments[subCommentIndex].isLiked
                if (feedsData.panels[storyIndex].userComments[commentIndex].subComments[subCommentIndex].isLiked) {
                  feedsData.panels[storyIndex].userComments[commentIndex].subComments[subCommentIndex].reactionsCount++
                } else {
                  feedsData.panels[storyIndex].userComments[commentIndex].subComments[subCommentIndex].reactionsCount--
                }
                break
              }
            }
          }
        }
        this.setState({ comicData: feedsData })
      }
    }

    let data = { data: { storyID: storyId, commentID: commentId, subCommentID: subCommentId } }
    this.props.likeDislikeComment(data, isLike)
  }


  postUserComment(storyIndex, comment, isReply, commentIndex) {
    const isConnected = NetworkUtils.instance.isAvailable()
    if (!isConnected) {
      return Utils.showError(Constants.INTERNET_ERROR)
    }
    const { photoURL, displayName } = this.props.userDetails
    let feedsData = JSON.parse(JSON.stringify(this.state.comicData))
    let userComments = {
      user: {
        name: displayName,
        image: photoURL,
        userId: Utils.getCurrentUserId()
      },
      storyId: feedsData.panels[storyIndex].storyID,
      commentText: comment,
      createdAt: Date.now(),
      isLiked: false,
      reactionsCount: 0
    }
    let commentID = null
    if (storyIndex != null && feedsData && feedsData[storyIndex].userComments && feedsData[storyIndex].userComments.length > 0) {
      if (isReply) {
        commentID = feedsData[storyIndex].userComments[commentIndex].commentId
        if (feedsData[storyIndex].userComments[commentIndex].subComments && feedsData[storyIndex].userComments[commentIndex].subComments.length > 0) {
          feedsData[storyIndex].userComments[commentIndex].subComments.psuh(userComments)
        } else {
          feedsData[storyIndex].userComments[commentIndex].subComments = []
          feedsData[storyIndex].userComments[commentIndex].subComments.push(userComments)
        }
      } else {
        feedsData[storyIndex].userComments.splice(0, 0, userComments)
        feedsData[storyIndex].commentCount++
      }
    } else {
      feedsData[storyIndex].userComments = []
      feedsData[storyIndex].userComments.push(userComments)
      feedsData[storyIndex].commentCount++
    }

    this.setState({ comicData: feedsData })
    let data = { data: { storyID: feedsData[storyIndex].storyID, commentText: comment, commentID: commentID } }
    this.props.postCommentOnFeed(data, this.updateCommentID)
  }


  updateCommentID(requestedData, responseData) {
    const { storyID, commentID = null } = requestedData.data
    const { commentId } = responseData
    let feedsData = JSON.parse(JSON.stringify(this.state.comicData))
    if (feedsData) {
      const searchStoryIndex = feedsData => feedsData.storyID === storyID;
      storyIndex = feedsData.panels.findIndex(searchStoryIndex)
      if (storyIndex >= 0) {
        for (const commentKey in feedsData.panels[storyIndex].userComments) {
          if (!feedsData.panels[storyIndex].userComments[commentKey].commentId) {
            feedsData.panels[storyIndex].userComments[commentKey].commentId = commentId
            break
          } else if (feedsData.panels[storyIndex].userComments[commentKey].commentId && feedsData.panels[storyIndex].userComments[commentKey].subComments) {
            for (const subCommentKey in feedsData.panels[storyIndex].userComments[commentKey].subComments) {
              if (!feedsData.panels[storyIndex].userComments[commentKey].subComments[subCommentKey].subCommentId) {
                feedsData.panels[storyIndex].userComments[commentKey].subComments[subCommentKey].subCommentId = subCommentId
              }
            }
          }
        }
      }
    }
    this.setState({ comicData: feedsData })
  }

  deleteComment(item) {
    const isConnected = NetworkUtils.instance.isAvailable()
    if (!isConnected) {
      return Utils.showError(Constants.INTERNET_ERROR)
    }

    let commentIDParam = null
    let subCommentIDParam = null
    let feedsData = JSON.parse(JSON.stringify(this.state.comicData))
    const searchStoryIndex = feedsData => feedsData.storyID === item.storyId;
    let storyIndex = feedsData.panels.findIndex(searchStoryIndex)
    if (storyIndex >= 0 && feedsData.panels && feedsData.panels[storyIndex].userComments && feedsData.panels[storyIndex].userComments.length > 0) {
      for (const commentKey in feedsData.panels[storyIndex].userComments) {
        let commentId = feedsData.panels[storyIndex].userComments[commentKey].commentId
        if (item.commentId && commentId == item.commentId) {
          commentIDParam = commentId
          if (feedsData.panels[storyIndex].userComments[commentKey].subComments && feedsData.panels[storyIndex].userComments[commentKey].subComments.length > 0) {
            feedsData.panels[storyIndex].commentCount = feedsData.panels[storyIndex].commentCount - feedsData.panels[storyIndex].userComments[commentKey].subComments.length
            if (this.currentPageStatus && Utils.checkObject(this.currentPageStatus.commentCount)) {
              this.currentPageStatus.commentCount = this.currentPageStatus.commentCount - feedsData.panels[storyIndex].userComments[commentKey].subComments.length
            }
          }
          if (Utils.checkObject(feedsData.panels[storyIndex].commentCount)) {
            feedsData.panels[storyIndex].commentCount--
          }

          feedsData.panels[storyIndex].userComments.splice(commentKey, 1)
          break
        } else if (feedsData.panels[storyIndex].userComments[commentKey].subComments && feedsData.panels[storyIndex].userComments[commentKey].subComments.length > 0) {
          for (const subCommentKey in feedsData.panels[storyIndex].userComments[commentKey].subComments) {
            let subCommentID = feedsData.panels[storyIndex].userComments[commentKey].subComments[subCommentKey].subCommentId
            if (item.subCommentId && item.subCommentId == subCommentID) {
              subCommentIDParam = subCommentID
              feedsData.panels[storyIndex].userComments[commentKey].subComments.splice(subCommentKey, 1)
              feedsData.panels[storyIndex].commentCount--
              commentIDParam = feedsData.panels[storyIndex].userComments[commentKey].commentId
              break
            }
          }
        }
      }
      if (this.currentPageStatus && Utils.checkObject(this.currentPageStatus.commentCount)) {
        this.currentPageStatus.commentCount--
      }

      this.setState({ comicData: feedsData })
    }

    let data = { data: { storyID: feedsData.panels[storyIndex].storyID, commentID: commentIDParam, subCommentID: subCommentIDParam } }
    this.props.deleteComment(data)
  }

  onFlagCommentTap(item) {
    const isConnected = NetworkUtils.instance.isAvailable()
    if (!isConnected) {
      return Utils.showError(Constants.INTERNET_ERROR)
    }

    this.setState({ showPageRefresh: true })

    const data = {
      data: {
        "reason": "Flag comment",
        "commentID": item.commentId,
      }
    }

    if (Utils.checkData(item.subCommentId)) {
      data.data.subCommentID = item.subCommentId
    }

    this.props.flagUserComment(data, (isSuccess) => {
      this.setState({ showPageRefresh: false })
      if (isSuccess) {
        Utils.showFlagCommentAlert()
      }
    })
  }

  onBlockUserTap(item) {
    const isConnected = NetworkUtils.instance.isAvailable()
    if (!isConnected) {
      return Utils.showError(Constants.INTERNET_ERROR)
    }

    if (!Utils.checkIsTinyviewAdminUser()) {
      return
    }

    this.setState({ showPageRefresh: true })

    const data = {
      data: {
        "userID": item.user.userId,
        "permissionToRemove": "comment"
      }
    }

    this.props.blockUserAction(data, (message) => {
      this.setState({ showPageRefresh: false })
      if (message != null) {
        Utils.showBlockUserAlert(message)
      }
    })
  }

  editComment(storyIndex, comment) {
    const isConnected = NetworkUtils.instance.isAvailable()
    if (!isConnected) {
      return Utils.showError(Constants.INTERNET_ERROR)
    }

    let feedsData = JSON.parse(JSON.stringify(this.state.comicData))
    let subCommentId = null
    let commentId = null
    if (storyIndex != null && feedsData.panels && feedsData.panels[storyIndex].userComments && feedsData.panels[storyIndex].userComments.length > 0) {
      for (const commentKey in feedsData.panels[storyIndex].userComments) {
        if (this.state.editCommentItems && this.state.editCommentItems.subCommentId) {
          for (const subCommentKey in feedsData.panels[storyIndex].userComments[commentKey].subComments) {
            let subCommentID = feedsData.panels[storyIndex].userComments[commentKey].subComments[subCommentKey].subCommentId
            if (this.state.editCommentItems.subCommentId == subCommentID) {
              subCommentId = subCommentID
              commentId = feedsData.panels[storyIndex].userComments[commentKey].commentId
              feedsData.panels[storyIndex].userComments[commentKey].subComments[subCommentKey].commentText = comment
              break
            }
          }
        } else if (this.state.editCommentItems && this.state.editCommentItems.commentId) {
          let commentID = feedsData.panels[storyIndex].userComments[commentKey].commentId
          if (this.state.editCommentItems.commentId == commentID) {
            commentId = commentID
            feedsData.panels[storyIndex].userComments[commentKey].commentText = comment
            break
          }
        }
      }
    }

    this.setState({ comicData: feedsData })
    let data = { data: { storyID: feedsData.panels[storyIndex].storyID, commentText: comment, commentID: commentId, subCommentID: subCommentId } }
    this.props.editComment(data)
  }

  navigateToUserProfile(item) {
    let userId = item.userId ? item.userId : item.item ? item.item.user.userId : (item.user && item.user.userId) ? item.user.userId : item.uid ? item.uid : null
    if (userId) {
      var subRoutesParams = { clickedUserID: userId, onStoryUpdated: this.prevOnStoryUpdated }
      this.props.navigation.push(Constants.USER_PROFILE_SCREEN, { ...subRoutesParams })
    }
  }

  onCommentLikeCountPress(actionName, commentID, subCommentId, storyID) {
    let navTitle = Constants.LIKES
    var parentRoute = Constants.DRAWER_NAVIGATOR
    var subParams = { title: navTitle, configFor: actionName, commentID: commentID, storyID: storyID, subCommentId }
    this.navigateToListScreen(subParams, parentRoute)
  }

  navigateToListScreen(subParams, parentRoute = {}, subRoute = Constants.USER_LIST_SCREEN) {
    this.props.navigation.push(subRoute, { ...subParams })
  }

  onShowCommentPress(actionName, panelItem, selectedIndex, commentItem) {
    if (panelItem && !Utils.checkData(panelItem.storyID)) {
      return null
    }

    let autoShowIndex = -1
    if (actionName == Constants.SHOW_REPLIED_COMMENT) {
      autoShowIndex = selectedIndex
    }

    let parentRoute = Constants.FEED_SCREEN
    let subRoute = Constants.POST_COMMENTS_SCREEN
    let subParams = {
      actionType: actionName, selectedIndex, showRepliedIndex: autoShowIndex, commentItem, panelItems: JSON.parse(JSON.stringify(panelItem)), onStoryUpdated: this.onStoryUpdated,
      deleteRepost: this.deleteRepost, editRepost: this.editRepost,
    }

    this.props.navigation.push(subRoute, { ...subParams })
    //Utils.navigateToSubRouteWithParams(parentRoute, subRoute, this.props, subParams, {})
  }

  getStoryIndex(storyID) {
    if (Utils.isEmptyObject(this.state.comicData)) {
      return -1
    }
    const index = this.state.comicData.panels && this.state.comicData.panels.findIndex(item =>
      item.storyID === storyID
    )

    return index;
  }

  getStoryIndexByActionURL(action, refType = "STORY") {
    if (Utils.isEmptyObject(this.state.comicData)) {
      return -1
    }
    const index = this.state.comicData.panels && this.state.comicData.panels.findIndex(item =>
      item.action === action && item.refType === refType
    )

    return index;
  }

  onStoryUpdated(panelItem) {
    if (!panelItem) {
      return
    }
    const index = this.getStoryIndex(panelItem.storyID)
    if (index < 0) {
      return
    }

    const feed = JSON.parse(JSON.stringify(this.state.comicData))
    if (!Utils.isEmptyObject(feed)) {
      panelItem.userComments = [] // clearing comments data to load new comments.
      feed.panels[index] = panelItem
      this.setState({ comicData: feed }, () => {
        let data = { data: { storyID: panelItem.storyID, records: Constants.FEED_PAGE_COMMENT_PAGINATION_COUNT, startAfter: "", sortingOrder: Constants.DESC } }
        this.props.getFeedComments(data, this.updateComments)
      })
    }

    if (this.prevOnStoryUpdated) {
      this.prevOnStoryUpdated(panelItem)
    }
  }

  addCommentInStory(storyID = null) {
    if (!storyID) {
      return
    }

    const index = this.getStoryIndex(storyID)

    if (index < 0) {
      return
    }

    if (this.isUpdatingCommentCount) {
      return
    }

    this.isUpdatingCommentCount = true

    const feed = JSON.parse(JSON.stringify(this.state.comicData))
    if (!Utils.isEmptyObject(feed)) {
      feed.panels[index].commentCount++
      this.setState({ comicData: feed })
    }

    setTimeout(() => {
      this.isUpdatingCommentCount = false
    }, 2000);
  }

  addSentPostInStory(storyID = null) {
    if (!storyID) {
      return
    }

    const index = this.getStoryIndex(storyID)

    if (index < 0) {
      return
    }

    const feed = JSON.parse(JSON.stringify(this.state.comicData))

    if (!Utils.isEmptyObject(feed)) {
      feed.panels[index].repostCount++
      this.setState({ comicData: feed })
    }
  }

  updateGiftsData(reactionName, action, storyID, actionType) {
    if (!action && !storyID && !reactionName) {
      return
    }

    var index = -1

    if (action) {
      index = this.getStoryIndexByActionURL(action)
    } else if (storyID) {
      index = this.getStoryIndex(storyID)
    }

    if (index < 0 && this.comicURL.includes(action)) {

      if (actionType == Types.ADD_GIFT) {
        if (!this.currentPageStatus.giftedItems) {
          this.currentPageStatus.giftedItems = {}
        }

        if (!this.currentPageStatus.giftsCount[reactionName]) {
          this.currentPageStatus.giftsCount[reactionName] = 0
        }

        if (!this.currentPageStatus.giftedItems[reactionName]) {
          this.currentPageStatus.giftsCount[reactionName]++

          this.currentPageStatus.likeCount++
          this.currentPageStatus.commentCount++

        }

        if (!this.currentPageStatus.giftedItems[reactionName]) {
          this.currentPageStatus.giftedItems[reactionName] = true
        }

        if (!this.currentPageStatus.giftsCount) {
          this.currentPageStatus.giftsCount = {}
        }
      } else if (actionType == Types.REMOVE_GIFT) {
        if (!this.currentPageStatus.giftedItems) {
          this.currentPageStatus.giftedItems = {}
        }

        if (!this.currentPageStatus.giftsCount[reactionName]) {
          this.currentPageStatus.giftsCount[reactionName] = 0
        }

        if (this.currentPageStatus.giftedItems[reactionName]) {
          this.currentPageStatus.giftsCount[reactionName]--

          this.currentPageStatus.likeCount--
          this.currentPageStatus.commentCount--


          delete this.currentPageStatus.giftedItems[reactionName]
        }

        if (!this.currentPageStatus.giftsCount) {
          this.currentPageStatus.giftsCount = {}
        }
      }
    }

    if (index < 0) {
      return
    }

    const feed = JSON.parse(JSON.stringify(this.state.comicData))
    if (!Utils.isEmptyObject(feed)) {
      if (actionType == Types.ADD_GIFT) {
        feed.panels[index].likeCount++
        feed.panels[index].commentCount++

        if (!feed.panels[index].giftedItems) {
          feed.panels[index].giftedItems = {}
        }

        if (!feed.panels[index].giftedItems[reactionName]) {
          feed.panels[index].giftedItems[reactionName] = true
        }

        if (!feed.panels[index].giftsCount) {
          feed.panels[index].giftsCount = {}
        }

        if (!feed.panels[index].giftsCount[reactionName]) {
          feed.panels[index].giftsCount[reactionName] = 0
        }

        feed.panels[index].giftsCount[reactionName]++
        this.setState({ comicData: feed })
      } else if (actionType == Types.REMOVE_GIFT) {
        feed.panels[index].likeCount--
        feed.panels[index].commentCount--

        if (!feed.panels[index].giftedItems) {
          feed.panels[index].giftedItems = {}
        }

        if (feed.panels[index].giftedItems[reactionName]) {
          delete feed.panels[index].giftedItems[reactionName]
        }

        if (!feed.panels[index].giftsCount) {
          feed.panels[index].giftsCount = {}
        }

        if (!feed.panels[index].giftsCount[reactionName]) {
          feed.panels[index].giftsCount[reactionName] = 0
        }

        feed.panels[index].giftsCount[reactionName]--
        this.setState({ comicData: feed })
      }
    }
  }

  onGiftItemSent(responseData = null, feedData = null) {
    let giftedStory = { data: { storyIDs: [feedData?.storyID] } }
    this.getStoryComments(giftedStory)
  }

  addLikeInComment(storyID, commentID, subCommentID = null) {
    if (!storyID || !commentID) {
      return
    }

    var storyIndex = -1
    storyIndex = this.getStoryIndex(storyID)

    if (storyIndex < 0) {
      return
    }

    const feedsData = JSON.parse(JSON.stringify(this.state.comicData))
    if (!Utils.isEmptyObject(feedsData)) {
      for (const commentIndex in feedsData.panels[storyIndex].userComments) {
        const commentId = feedsData.panels[storyIndex].userComments[commentIndex].commentId
        if (subCommentID === null && commentId === commentID && !feedsData.panels[storyIndex].userComments[commentIndex].isLiked) {
          feedsData.panels[storyIndex].userComments[commentIndex].isLiked = true
          feedsData.panels[storyIndex].userComments[commentIndex].reactionsCount++
        } else if (subCommentID && commentId === commentID && feedsData.panels[storyIndex].userComments[commentIndex].subComments && feedsData.panels[storyIndex].userComments[commentIndex].subComments.length > 0) {
          for (const subCommentIndex in feedsData.panels[storyIndex].userComments[commentIndex].subComments) {
            const subCommentId = feedsData.panels[storyIndex].userComments[commentIndex].subComments[subCommentIndex].subCommentId
            if (subCommentID === subCommentId) {
              feedsData.panels[storyIndex].userComments[commentIndex].subComments[subCommentIndex].isLiked = true
              feedsData.panels[storyIndex].userComments[commentIndex].subComments[subCommentIndex].reactionsCount++
              break
            }
          }
        }
        this.setState({ comicData: feedsData })
      }
    }
  }

  removeLikeInComment(storyID, commentID, subCommentID = null) {
    if (!storyID || !commentID) {
      return
    }

    var storyIndex = -1
    storyIndex = this.getStoryIndex(storyID)

    if (storyIndex < 0) {
      return
    }

    const feedsData = JSON.parse(JSON.stringify(this.state.comicData))
    if (!Utils.isEmptyObject(feedsData)) {
      for (const commentIndex in feedsData.panels[storyIndex].userComments) {
        const commentId = feedsData.panels[storyIndex].userComments[commentIndex].commentId
        if (subCommentID === null && commentId == commentID && feedsData.panels[storyIndex].userComments[commentIndex].isLiked) {
          feedsData.panels[storyIndex].userComments[commentIndex].isLiked = false
          feedsData.panels[storyIndex].userComments[commentIndex].reactionsCount--
        } else if (subCommentID && commentId === commentID && feedsData.panels[storyIndex].userComments[commentIndex].subComments && feedsData.panels[storyIndex].userComments[commentIndex].subComments.length > 0) {
          for (const subCommentIndex in feedsData.panels[storyIndex].userComments[commentIndex].subComments) {
            const subCommentId = feedsData.panels[storyIndex].userComments[commentIndex].subComments[subCommentIndex].subCommentId
            if (subCommentID === subCommentId) {
              feedsData.panels[storyIndex].userComments[commentIndex].subComments[subCommentIndex].isLiked = false
              feedsData.panels[storyIndex].userComments[commentIndex].subComments[subCommentIndex].reactionsCount--
              break
            }
          }
        }
      }
      this.setState({ comicData: feedsData })
    }
  }

  addLikeInStory(actionURL = null, storyID = null) {
    if (!actionURL && !storyID) {
      return
    }
    var index = -1

    if (actionURL) {
      index = this.getStoryIndexByActionURL(actionURL)
    } else if (storyID) {
      index = this.getStoryIndex(storyID)
    }

    if (index < 0) {
      return
    }

    const feed = JSON.parse(JSON.stringify(this.state.comicData))
    if (!Utils.isEmptyObject(feed) && !feed.panels[index].isLiked) {
      feed.panels[index].isLiked = true
      feed.panels[index].isLikeChanged = true
      feed.panels[index].likeCount++

      if (!feed.panels[index].giftsCount) {
        feed.panels[index].giftsCount = {}
      }

      if (!Utils.checkData(feed.panels[index].giftsCount[Constants.LIKE.toUpperCase()])) {
        feed.panels[index].giftsCount[Constants.LIKE.toUpperCase()] = 0
      }

      feed.panels[index].giftsCount[Constants.LIKE.toUpperCase()]++

      this.setState({ comicData: feed })
    }
  }

  removeLikeInStory(actionURL = null, storyID = null) {
    if (!actionURL && !storyID) {
      return
    }

    var index = -1

    if (actionURL) {
      index = this.getStoryIndexByActionURL(actionURL)
    } else if (storyID) {
      index = this.getStoryIndex(storyID)
    }
    if (index < 0) {
      return
    }

    const feed = JSON.parse(JSON.stringify(this.state.comicData))
    if (!Utils.isEmptyObject(feed) && feed.panels[index].isLiked) {
      feed.panels[index].isLikeChanged = false
      feed.panels[index].isLiked = false
      if (feed.panels[index].likeCount > 0) feed.panels[index].likeCount--
      if (!feed.panels[index].giftsCount) {
        feed.panels[index].giftsCount = {}
      }

      if (Utils.checkData(feed.panels[index].giftsCount[Constants.LIKE.toUpperCase()])) {
        feed.panels[index].giftsCount[Constants.LIKE.toUpperCase()]--
      } else {
        feed.panels[index].giftsCount[Constants.LIKE.toUpperCase()] = 0
      }

      this.setState({ comicData: feed })
    }
  }

  onReplyPress(commentItems, selectedIndex) {
    if (!this.canUserComment()) {
      return
    }

    const { storyId } = commentItems
    let feedsData = JSON.parse(JSON.stringify(this.state.comicData))
    const searchStoryIndex = feedsData => feedsData.storyID === storyId;
    let storyIndex = feedsData.panels.findIndex(searchStoryIndex)
    let panelItems = { ...feedsData.panels[storyIndex], index: storyIndex }
    this.onShowCommentPress(Constants.REPLY, panelItems, selectedIndex, commentItems)
  }

  canUserComment(showActionSheet = true) {
    let isEligibleToComment = Utils.isUserEligibleToComment()

    if (isEligibleToComment == null) {
      return true
    } else {
      this.changeActionSheetTitleAndButton('', isEligibleToComment.sheetOptions, showActionSheet, isEligibleToComment.sheetMessage)
      return false
    }
  }
}