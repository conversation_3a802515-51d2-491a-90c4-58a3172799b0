import React, { Component } from 'react'
import { <PERSON>, FlatList, BackH<PERSON>ler, SafeAreaView } from 'react-native'
import { CommonActions } from '@react-navigation/native';
import UserListPanel from './UserListPanel'
import { Constants } from '../../config/Constants';
import { connect } from 'react-redux';
import { fetchFeedLikedUserList, fetchCommentLikedUserList, fetchRepostedUserList, fetchWithWhomStorySharedUL, sendFriendRequest, getFriendList } from '../../redux/actions/actions'
import { LoadingIndicator } from '../LoadingIndicator';
import { Utils } from '../../config/Utils';
import { scale } from 'react-native-size-matters';
import FriendsPanel from '../friendsPackage/panels/FriendsPanel';
import GiftIcons from '../reactionUI/GiftIcons';
import FirebaseManager from '../../config/FirebaseManager';

class UserListScreen extends Component {

  constructor(props) {
    super(props)
    this.state = {
      userList: [],
      anonymousUserList: [],
      showLoadingIndicator: false,
      errorRes: null
    }

    this.configFor = null
    this.storyID = null
    this.refType = null
    this.panelItems = null
    this.subCommentId = null
    this.commentID = null
    this.anonymousUserCount = null
    this.anonymousUserCountSuffix = null
    this.formatAnonymousUserCount = null
    this.hasMoreData = true
    this.isAPIInProgress = false
    this.currentUser = FirebaseManager.instance.currentUser()

    this.onBackPress = this.onBackPress.bind(this)
    this.renderItem = this.renderItem.bind(this)
    this.updateUserList = this.updateUserList.bind(this)
    this.fetchAppropiateAPI = this.fetchAppropiateAPI.bind(this)
    this.reloadUserList = this.reloadUserList.bind(this)
    this.navigateToUserProfile = this.navigateToUserProfile.bind(this)
    this.setLoaderVisibility = this.setLoaderVisibility.bind(this)
    this.updateLikedUserList = this.updateLikedUserList.bind(this)
    this.sendFriendRequest = this.sendFriendRequest.bind(this)
    this.renderFriends = this.renderFriends.bind(this)
    this.renderHeader = this.renderHeader.bind(this)
  }

  UNSAFE_componentWillMount() {
    BackHandler.addEventListener('hardwareBackPress', this.onBackPress);
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.onBackPress);
  }

  onBackPress() {
    this.props.navigation.dispatch(CommonActions.goBack())
    return true;
  }

  componentDidMount() {
    const { configFor, storyID, subCommentId, commentID, refType = null, panelItems } = this.props.route.params
    this.configFor = configFor
    this.storyID = storyID
    this.refType = refType
    this.panelItems = panelItems
    if (commentID) {
      this.commentID = commentID
    }

    if (subCommentId) {
      this.subCommentId = subCommentId
    }

    this.props.navigation.setParams({
      leftButtonText: "Back",
      onBackPress: this.onBackPress,
    })

    this.reloadUserList()
  }

  // API Calls
  fetchAppropiateAPI() {
    if (!this.hasMoreData || this.isAPIInProgress) {
      return
    }
    this.setLoaderVisibility(true)
    this.isAPIInProgress = true
    let innerData = null
    if (this.configFor == Constants.FEED_LIKE) {

      let lastLikeID = null
      if (this.state.userList.length > 0) {
        lastLikeID = this.state.userList[this.state.userList.length - 1].likeID ? this.state.userList[this.state.userList.length - 1].likeID : null
      }
      innerData = {
        storyID: this.storyID,
        records: Constants.FEED_USER_LIST_PAGINATION_COUNT,
        startAfter: lastLikeID
      }
      let data = {
        data: innerData
      }

      this.props.fetchFeedLikedUserList(data, this.updateLikedUserList)
    } else if (this.configFor == Constants.WITH_WHOM_STORY_SHARED) {

      let lastUserID = null
      if (this.state.userList.length > 0) {
        lastUserID = this.state.userList[this.state.userList.length - 1].repostID ? this.state.userList[this.state.userList.length - 1].repostID : null
      }
      innerData = {
        storyID: this.storyID,
        records: Constants.FEED_USER_LIST_PAGINATION_COUNT,
        startAfter: lastUserID,
        refType: this.refType == Constants.UPPERCASE_REPOST ? this.refType : null
      }
      let data = {
        data: innerData
      }
      this.props.fetchWithWhomStorySharedUL(data, this.updateUserList)
    } else if (this.configFor == Constants.SEE_FRIENDS) {
      this.props.getFriendList(this.updateUserList, true)
    } else if (this.configFor == Constants.REPOSTED) {

      let lastRepostID = null
      if (this.state.userList.length > 0) {
        lastRepostID = this.state.userList[this.state.userList.length - 1].repostID ? this.state.userList[this.state.userList.length - 1].repostID : null
      }
      innerData = {
        storyID: this.storyID,
        records: Constants.FEED_USER_LIST_PAGINATION_COUNT,
        startAfter: lastRepostID
      }
      let data = {
        data: innerData
      }

      this.props.fetchRepostedUserList(data, this.updateUserList)
    } else {

      let lastLikeID = null
      if (this.state.userList.length > 0) {
        lastLikeID = this.state.userList[this.state.userList.length - 1].likeID ? this.state.userList[this.state.userList.length - 1].likeID : null
      }

      innerData = {
        storyID: this.storyID,
        commentID: this.commentID,
        subCommentID: this.subCommentId || "",
        records: Constants.FEED_USER_LIST_PAGINATION_COUNT,
        startAfter: lastLikeID ? lastLikeID : ""
      }
      let data = {
        data: innerData
      }

      this.props.fetchCommentLikedUserList(data, this.updateUserList)
    }
  }

  updateUserList(data) {
    if (!data || data.length < Constants.FEED_USER_LIST_PAGINATION_COUNT) {
      this.hasMoreData = false
    }
    if (data && data.length > 0 && Array.isArray(data)) {
      let userList = [...this.state.userList, ...data]
      this.setState({ userList: userList }, () => {
        this.isAPIInProgress = false
      })
    } else {
      this.isAPIInProgress = false
    }
    this.setLoaderVisibility(false)
  }

  updateLikedUserList(data) {
    if (!data || data.likesList.length < Constants.FEED_USER_LIST_PAGINATION_COUNT) {
      this.hasMoreData = false
    }
    if (data) {
      if (data.likesList && data.likesList.length > 0 && Array.isArray(data.likesList)) {
        let userList = [...this.state.userList, ...data.likesList]
        this.setState({ userList: userList }, () => {
          this.isAPIInProgress = false
        })
      } else {
        this.isAPIInProgress = false
      }

      if (this.anonymousUserCount != data.anonymousUserCount) {
        this.anonymousUserCount = data.anonymousUserCount
        this.anonymousUserCountSuffix = this.anonymousUserCount == 1 ? Constants.NON_SIGNED_IN_USER : Constants.GUEST_USERS
        this.formatAnonymousUserCount = Utils.formatViewCount(this.anonymousUserCount)
      }

    } else {
      this.isAPIInProgress = false
    }

    this.setLoaderVisibility(false)
  }

  navigateToUserProfile(item) {
    let userId = this.configFor == Constants.SEE_FRIENDS ? item.uid : item.userId ? item.userId : item.item ? item.item.user.userId : item.user.userId ? item.user.userId : null
    let userName = this.configFor == Constants.SEE_FRIENDS ? item.name : item.name ? item.name : item.item ? item.item.user.name : item.user.name ? item.user.name : null
    if (userName && userName.toLowerCase() == Constants.NON_SIGNED_IN_USER.toLowerCase() || !userId || !userName) {
      return
    }

    var subRoutesParams = { clickedUserID: userId }
    Utils.navigateToSubRouteWithParams(Constants.DRAWER_HOME, Constants.USER_PROFILE_SCREEN, this.props, subRoutesParams)

  }

  sendFriendRequest(item) {
    const { user } = item
    const { displayName, photoURL, phoneNumber } = this.props.userDetails
    let sendFriendRequestParams = {
      data: {
        requests: [user.phoneNumber],
        senderDetails: {
          senderName: displayName,
          senderPhoneNumber: phoneNumber,
          senderProfilePic: photoURL,
          senderUID: this.currentUser.uid
        },
      },
    }

    this.props.sendFriendRequest(sendFriendRequestParams.data, sendTextToContactsParams, this.onSuccess)

  }

  reloadUserList() {
    this.hasMoreData = true
    this.setState({ errorRes: null })
    this.fetchAppropiateAPI()
  }

  setLoaderVisibility(value) {
    this.setState({ showLoadingIndicator: value })
  }

  renderItem(item) {
    return (
      <UserListPanel item={item} {...this.props} sendFriendRequest={this.sendFriendRequest} navigateToUserProfile={this.navigateToUserProfile} />
    )
  }

  renderFriends(item) {
    if (!item.item || Object.keys(item.item).length == 0) {
      return null
    }
    return <FriendsPanel item={item.item} {...this.props} navigateToUserProfile={this.navigateToUserProfile} showSeprator={true} />
  }

  renderHeader() {
    if (this.configFor != Constants.FEED_LIKE) {
      return null
    }

    return (
      <View style={{ marginLeft: scale(10), marginRight: scale(10), }}>
        <GiftIcons {...this.props} isEnable={false} panelItems={this.panelItems} />
      </View>
    )
  }

  render() {

    return (
      <SafeAreaView style={{ flex: 1 }}>
        <View style={{ marginTop: 10, flex: 1 }}>
          <FlatList
            data={this.state.userList}
            ListHeaderComponent={this.renderHeader()}
            renderItem={(item) => this.configFor == Constants.SEE_FRIENDS ? this.renderFriends(item) : this.renderItem(item)}
            keyExtractor={(item, index) => '' + index}
            onEndReached={() => this.fetchAppropiateAPI()}
            onEndReachedThreshold={0.1}
          />
        </View>
        {this.state.showLoadingIndicator && <LoadingIndicator />}
      </SafeAreaView>
    )
  }
}

const mapStateToProps = (state) => {
  return {
    userDetails: state.loginInfo.userDetails,
  }
}

const mapDispatchToProps = (dispatch) => {
  return {
    fetchFeedLikedUserList(data, callBack) {
      dispatch(
        fetchFeedLikedUserList(data, callBack)
      )
    },
    fetchCommentLikedUserList(data, callBack) {
      dispatch(
        fetchCommentLikedUserList(data, callBack)
      )
    },
    fetchRepostedUserList(data, callBack) {
      dispatch(
        fetchRepostedUserList(data, callBack)
      )
    },
    fetchWithWhomStorySharedUL(data, callBack) {
      dispatch(
        fetchWithWhomStorySharedUL(data, callBack)
      )
    },
    sendFriendRequest(sendFriendRequestParams, sendTextToContactsParams, callback) {
      dispatch(
        sendFriendRequest(sendFriendRequestParams, sendTextToContactsParams, callback)
      )
    },
    getFriendList(callback, forceLoad) {
      dispatch(
        getFriendList(callback, forceLoad)
      )
    },
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(UserListScreen)

