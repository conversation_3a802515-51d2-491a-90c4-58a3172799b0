import React, { Component } from 'react'
import { connect } from 'react-redux';
import { StyleSheet, View, FlatList, Keyboard, RefreshControl, Platform, KeyboardAvoidingView, TouchableWithoutFeedback, Alert, BackHandler, Text, TouchableOpacity, Dimensions, Image, Animated, AppState } from 'react-native'
import { StackActions } from '@react-navigation/native';
import { shareApp, getFeedComments, postCommentOnFeed, editComment, openWebViewAction, likeDislikeFeed, recordPageView, likeDislikeComment, deleteComment, getStoryDetails, deleteRepostedComic, deleteSentPost, getUserDetails, requestPurchase, getFeedLikes, flagUserComment, blockUserAction, updateAlertSubscription, shareImage, getFeedEpisodes, getNavigationComics, addNewTransaction } from '../../../redux/actions/actions'
import CommentsPanel from './CommentsPanel'
import { scale } from "react-native-size-matters"
import { Color } from '../../../config/Color';
import { navigationStyle, bannerMsgContainer, bannerTextView, bannerCrossIconView, bannerCrossIcon, buyButton, loadingViewStyle } from '../../../config/styles';
import StoryPanel from '../../ComicPanel/StoryPanel';
import { Constants } from '../../../config/Constants';
import { Utils } from '../../../config/Utils';
import { settings } from '../../../config/settings';
import CommentTextInputComponent from '../../../config/CommentTextInputComponent';
import NetworkUtils from '../../../config/NetworkUtils';
import { LoadingIndicator } from '../../LoadingIndicator';
import SessionManager from '../../../config/SessionManager';
import ActionSheet from '../../actionSheet/ActionSheetCustom';
import GiftBottomSheet from '../../GiftUI/GiftBottomSheet';
import UserSession from '../../../config/UserSession';
import { SharedPreferences } from '../../../config/SharedPreferences';
import { updateShowAllComicsSettings } from '../../../redux/actions/actions';
import ShareBottomSheet from '../../ShareBottomSheet';
import { DimenContext, ThemeContext } from '../../../Contexts';
import ComicBottomBar from '../../ComicBottomBar';
import FastImage from 'react-native-fast-image';
import { SafeAreaView } from 'react-native-safe-area-context';
import NavigationService from '../../../config/NavigationService';
import moment from "moment";

const dimensions = Dimensions.get('window')
class PostCommentsScreen extends Component {

  constructor(props) {
    super(props)

    this.rightIcon = null
    this.storyType = null
    this.comicURL = null
    this.channelName = null

    let panelItems = null
    this.params = this.props.route.params

    if (this.params && this.params.panelItems) {
      panelItems = this.params.panelItems
      this.comicURL = Utils.resolvePath(settings.apiBaseURL, panelItems.action)
      this.channelName = panelItems.series ? panelItems.series : Utils.getChannelName(this.comicURL)
      panelItems.userComments = [] // clearing userComments array for showing oldest comments first.
    }

    let hasPermissionToComment = true
    if (Utils.checkObject(this.props.userDetails) && this.props.userDetails.permissions && this.props.userDetails.permissions.length > 0) {
      hasPermissionToComment = this.props.userDetails.permissions.includes(Constants.COMMENT_PERMISSION_KEY)
    }

    this.state = {
      panelItems: panelItems,
      userComment: '',
      showRefresh: false,
      showPageRefresh: Utils.checkData(panelItems) ? false : true,
      sheetTitle: '',
      sheetMessage: Constants.SIGN_IN_ACTION_SHEET_MESSAGE,
      sheetOptions: [Constants.SIGN_IN, Constants.CANCEL],
      cancelOptionIndex: 1,
      commentPlaceHolder: '',
      showBottomSheet: false,
      canUserComment: hasPermissionToComment,
      showShareBottomSheet: false,
      repliedLabel: null,
      isKeyboardUp: false,
      comicFeedEpisodes: {},
      viewUpdated: false,
      isEpisodsAPIResponded: false,
      showNotificationMessage: SessionManager.instance.needsToShowAlertMessage(),
      bottomTabAnimation: new Animated.Value(1)
    }

    this.isRefreshing = false
    this.isNewStackPushed = true
    this.isComponentDidUpdateOnce = false

    if (this.params) { // for tapping comment with in the app
      this.selectedIndex = this.params.selectedIndex
      this.selCommentItem = this.params.commentItem
      this.actionType = this.params.actionType
      this.showRepliedIndex = this.params.showRepliedIndex
    }

    if (this.params) { // When opens from notification
      this.storyData = this.params.storyData
    }

    this.newlyAddedCommentsCount = 0
    this.actionSheet = null
    this.clickedStoryID = null
    this.clickedPanelItems = null
    this.hasMoreData = true
    this.isEditingComment = false
    this.userCommentTextInput = null
    this.tappedComicData = null
    this.currentUserId = Utils.getCurrentUserId()
    this.isCommentsFetched = false
    this.isIOSPlatform = Platform.OS === "ios"
    this.isFetchingCommentsInProgress = false
    this.seriesToFollowData = null

    this.onBackPress = this.onBackPress.bind(this)
    this.onIconPress = this.onIconPress.bind(this)
    this.onSubmitComment = this.onSubmitComment.bind(this)
    this.onChange = this.onChange.bind(this)
    this.getFeedComments = this.getFeedComments.bind(this)
    this.updateComments = this.updateComments.bind(this)
    this.onReplyPress = this.onReplyPress.bind(this)
    this.likeDislikeUserComment = this.likeDislikeUserComment.bind(this)
    this.onEditComment = this.onEditComment.bind(this)
    this.onEditPress = this.onEditPress.bind(this)
    this.navigateToUserProfile = this.navigateToUserProfile.bind(this)
    this.countPress = this.countPress.bind(this)
    this.onCommentLikeCountPress = this.onCommentLikeCountPress.bind(this)
    this.subscribeToAlert = this.subscribeToAlert.bind(this)
    this.likeDislikeFeed = this.likeDislikeFeed.bind(this)
    this.deleteComment = this.deleteComment.bind(this)
    this.deleteRepost = this.deleteRepost.bind(this)
    this.editRepost = this.editRepost.bind(this)
    this.onActionSheetButtonPress = this.onActionSheetButtonPress.bind(this)
    this.getUserDetails = this.getUserDetails.bind(this)
    this.checkPostRefType = this.checkPostRefType.bind(this)
    this.navigateToUserListScreen = this.navigateToUserListScreen.bind(this)
    this.onFlagCommentTap = this.onFlagCommentTap.bind(this)
    this.onBlockUserTap = this.onBlockUserTap.bind(this)
    this.shareImage = this.shareImage.bind(this)
    this._keyboardDidShow = this._keyboardDidShow.bind(this)
    this._keyboardDidHide = this._keyboardDidHide.bind(this)
    this.onUserDetailsFetch = this.onUserDetailsFetch.bind(this)
    this.getStoryDetails = this.getStoryDetails.bind(this)
    this.onShowAllComicSwitchTap = this.onShowAllComicSwitchTap.bind(this)
    this.onFetchStoryDetails = this.onFetchStoryDetails.bind(this)
    this.scrollListView = this.scrollListView.bind(this)
    this.openHomePage = this.openHomePage.bind(this)
    this.reloadStory = this.reloadStory.bind(this)
    this.updateCommentID = this.updateCommentID.bind(this)
    this.resetCommentLabelText = this.resetCommentLabelText.bind(this)
    this.onRepostedStoryUpdated = this.onRepostedStoryUpdated.bind(this)
    this.navigateToAppropriateScreen = this.navigateToAppropriateScreen.bind(this)
    this.resetActionSheet = this.resetActionSheet.bind(this)
    this.navigateToSubscribePage = this.navigateToSubscribePage.bind(this)
    this.copyLink = this.copyLink.bind(this)
    this.requestPurchase = this.requestPurchase.bind(this)
    this.onGiftItemSent = this.onGiftItemSent.bind(this)
    this.isUserDetailsEmpty = this.isUserDetailsEmpty.bind(this)
    this.canUserComment = this.canUserComment.bind(this)
    this.onFollowPress = this.onFollowPress.bind(this)
    this.isSubscribedToAlert = this.isSubscribedToAlert.bind(this)
    this.getAlertChannelName = this.getAlertChannelName.bind(this)
    this.onShowCommentPress = this.onShowCommentPress.bind(this)
    this.closeBottomSheet = this.closeBottomSheet.bind(this)
    this.onLikePress = this.onLikePress.bind(this)
    this.getRepliedText = this.getRepliedText.bind(this)
    this.openChapter = this.openChapter.bind(this)
    this.signInSheet = this.signInSheet.bind(this)
    this.onReplyCancel = this.onReplyCancel.bind(this)
    this._listEmptyComponent = this._listEmptyComponent.bind(this)
    this.renderFooter = this.renderFooter.bind(this)
    this.onSeriesHomeClicked = this.onSeriesHomeClicked.bind(this)
    this.renderPrevComment = this.renderPrevComment.bind(this)
    this.onPrevCommentsButtonTap = this.onPrevCommentsButtonTap.bind(this)
    this.getFeedPanelEpisodes = this.getFeedPanelEpisodes.bind(this)
    this.onFeedEpisodesFetched = this.onFeedEpisodesFetched.bind(this)
    this.onViewFocus = this.onViewFocus.bind(this)
    this.renderComicBottomBar = this.renderComicBottomBar.bind(this)
    this.hidePushNotificationMsg = this.hidePushNotificationMsg.bind(this)
    this.renderFollowSeriesMsg = this.renderFollowSeriesMsg.bind(this)
    this.renderManageAlertMsg = this.renderManageAlertMsg.bind(this)
    this.renderButton = this.renderButton.bind(this)
    this.navigateToScreens = this.navigateToScreens.bind(this)
    this.renderUpgradeSubsMsg = this.renderUpgradeSubsMsg.bind(this)
    this.onAppStateChange = this.onAppStateChange.bind(this)
    this.checkIfNeedToReloadHomePage = this.checkIfNeedToReloadHomePage.bind(this)

    this.onStoryUpdated = this.props.route.params.onStoryUpdated

    this.valueProps = {
      onCountPress: this.countPress, onCommentLikeCountPress: this.onCommentLikeCountPress, deleteRepost: this.deleteRepost,
      editRepost: this.editRepost, onIconPress: this.onIconPress, likeDislikeFeed: this.likeDislikeFeed,
      likeDislikeComment: this.likeDislikeUserComment, onEditPress: this.onEditPress, onFlagCommentTap: this.onFlagCommentTap,
      onBlockUserTap: this.onBlockUserTap, navigateToUserProfile: this.navigateToUserProfile, onShowCommentPress: this.onShowCommentPress,
      onLikePress: this.onLikePress, currentUserId: this.currentUserId, deleteComment: this.deleteComment,
      onReplyPress: this.onReplyPress, updateItemHeight: this.updateItemHeight, openWebView: this.props.openWebView, recordPageView: this.props.recordPageView, navigateToSubscribePage: this.navigateToSubscribePage
    }

    this.aspectRatio = dimensions.width / Constants.LOADING_GIF_DIMEN.width
  }

  componentDidUpdate() {
    this.isComponentDidUpdateOnce = true
    const currentParams = this.props.route && this.props.route.params
    if (this.state.panelItems && currentParams.storyData && currentParams.storyData.forceOpen && this.state.panelItems.storyID != currentParams.storyData.storyID) {
      this.props.navigation.setParams({ storyData: null })
      this.props.navigation.push(Constants.POST_COMMENTS_SCREEN, { storyData: { storyID: currentParams.storyData.storyID, fromNotification: currentParams.storyData.fromNotification } })
    } else if (!this.isNewStackPushed && !this.isRefreshing && this.state.panelItems && currentParams.storyData && currentParams.storyData.actionType && currentParams.storyData.fromBackground && this.state.panelItems.storyID == currentParams.storyData.storyID) {
      this.isRefreshing = true
      this.props.navigation.setParams({ storyData: null })
      this.reloadStory()
    }
  }

  componentDidMount() {
    this.props.navigation.setParams({
      leftButtonText: "Back",
      onBackPress: this.onBackPress,
      rightButtonText: this.storyType,
      rightIcon: this.rightIcon,
      onRightClick: () => { }, // this.navigateToUserListScreen() 
      isRightDisabled: true, // this.state.panelItems && this.state.panelItems.refType == Constants.UPPERCASE_STORY ? true : false,
      title: Utils.getStoryPageTitle(this.state.panelItems),
      openHomePage: this.openHomePage,
      onShowAllComicSwitchTap: this.onShowAllComicSwitchTap,
      openChapter: this.openChapter,
    })

    this.keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', this._keyboardDidShow);
    this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', this._keyboardDidHide);

    this.getStoryDetails()
    this.checkPostRefType(this.state.panelItems)
    const userProfilePicURL = SessionManager.instance.profileImageURL
    if (Utils.isEmptyObject(this.props.userDetails) && !Utils.checkData(userProfilePicURL)) {
      this.getUserDetails(this.onUserDetailsFetch)
    }

    if (this.actionType) {
      if (this.actionType == Constants.EDIT) {
        //this.onEditPress(this.selCommentItem)
      } else if (this.actionType == Constants.REPLY) {
        //this.onReplyPress(this.selCommentItem, this.selectedIndex)
      } else if (this.actionType == Constants.SHOW_REPLIED_COMMENT) {
        setTimeout(() => {
          // this.scrollListView(this.selectedIndex)
          // this.selectedIndex = null
        }, 500);
      }
    }

    this._focusUnsubscribe = this.props.navigation.addListener('focus', () => {
      if (this.isComponentDidUpdateOnce) {
        this.onViewFocus()
      }
    });

    this.appListener = AppState.addEventListener("change", this.onAppStateChange)
  }

  onViewFocus() {
    this.setState({ viewUpdated: !this.state.viewUpdated })
  }

  UNSAFE_componentWillMount() {
    BackHandler.addEventListener('hardwareBackPress', this.onBackPress);
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.onBackPress);
    this.keyboardDidShowListener.remove();
    this.keyboardDidHideListener.remove();
    if (this._focusUnsubscribe) {
      this._focusUnsubscribe()
    }
    if (this.appListener) {
      this.appListener.remove()
    }
  }

  onAppStateChange(nextAppState) {
    if (nextAppState === "active") {
      this.checkIfNeedToReloadHomePage()
    }
  }

  checkIfNeedToReloadHomePage() {
    try {
      const lastBGTime = SessionManager.instance.getBGStayTime()
      if (moment().diff(lastBGTime, "m") >= settings.feedResetTime) { //Take user to the home page and refresh the feed.
        let currentRoute = NavigationService.getCurrentRoute()
        if (currentRoute && currentRoute == Constants.POST_COMMENTS_SCREEN) {
          this.reloadStory()
        }
      }
    } catch (error) {

    }

  }

  _keyboardDidShow() {
    this.scrollListView(this.selectedIndex)
    Animated.timing(this.state.bottomTabAnimation, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true
    }).start(() => {
      this.setState({ isKeyboardUp: true })
    });
  }

  _keyboardDidHide() {
    Utils.log("_keyboardDidHide")
    Animated.timing(this.state.bottomTabAnimation, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true
    }).start(() => {
      this.setState({ isKeyboardUp: false })
    });
  }

  onBackPress(isPanelDeleted = false) {
    if (this.onStoryUpdated) {
      const panelRef = JSON.parse(JSON.stringify(this.state.panelItems))
      this.resetCommentLabelText(panelRef)
      this.onStoryUpdated(panelRef, isPanelDeleted)
    }
    this.props.navigation.goBack()
    return true;
  }

  async getUserDetails(callBack = null) {
    await this.props.getUserDetails(callBack)
  }

  onUserDetailsFetch(userData) {
    let hasPermissionToComment = true
    if (Utils.checkObject(userData) && userData.permissions && userData.permissions.length > 0) {
      hasPermissionToComment = userData.permissions.includes(Constants.COMMENT_PERMISSION_KEY)
    }

    let commentPlaceHolder = this.state.commentPlaceHolder
    if (!hasPermissionToComment) {
      commentPlaceHolder = Constants.DISABLED_COMMENT
    }

    this.setState({ canUserComment: hasPermissionToComment, commentPlaceHolder })
  }

  getFeedPanelEpisodes() {
    if (Utils.isHomeURL(this.comicURL) || Utils.isTVSeriesURL(this.comicURL)) {
      this.setState({ isEpisodsAPIResponded: true })
      return
    }

    const storyID = (this.storyData && Utils.checkData(this.storyData.storyID)) ? this.storyData.storyID : this.state.panelItems ? this.state.panelItems.storyID : null
    let requestedData = {
      action: Utils.getMeaningFullURL(this.comicURL),
      series: this.channelName,
      episodeID: storyID
    }
    this.props.getFeedEpisodes(requestedData, this.onFeedEpisodesFetched)
  }

  onFeedEpisodesFetched(response) {
    this.setState({ comicFeedEpisodes: response ? response : {}, isEpisodsAPIResponded: true })
  }

  getStoryDetails(shouldRefresh = false) {
    if (this.state.panelItems && !shouldRefresh) {
      this.getFeedComments()
      this.getFeedPanelEpisodes()
      return
    }

    const storyID = (this.storyData && Utils.checkData(this.storyData.storyID)) ? this.storyData.storyID : this.state.panelItems ? this.state.panelItems.storyID : null
    const action = (this.storyData && Utils.checkData(this.storyData.action)) ? this.storyData.action : null
    const series = (this.storyData && Utils.checkData(this.storyData.series)) ? this.storyData.series : null
    let data = { data: { storyID, action, series } }
    this.props.getStoryDetails(data, this.onFetchStoryDetails)
  }

  onShowAllComicSwitchTap(value) {
    SessionManager.instance.showAllComics = value
    this.props.updateShowAllComicsSettings(value)
    SharedPreferences.setShowAllComicsValue(value)

    this.props.navigation.dispatch(StackActions.popToTop());
  }

  onFetchStoryDetails(res) {
    if (res.message) {
      Utils.showToast(res.message)
      this.props.navigation.goBack()
      return true
    }

    this.comicURL = Utils.resolvePath(settings.apiBaseURL, res.action)
    this.channelName = res && res.series ? res.series : Utils.getChannelName(this.comicURL)

    this.hasMoreData = true
    if (!res.storyID) {
      res.storyID = this.storyData ? this.storyData.storyID : this.state.panelItems.storyID
    }

    this.checkPostRefType(res)

    if (res) {
      this.props.navigation.setParams({
        title: Utils.getStoryPageTitle(res),
        rightButtonText: this.storyType,
      })
    }
    this.setState({ panelItems: res, showRefresh: false, showPageRefresh: false }, () => {
      this.props.navigation.setParams({ storyData: null })
      this.getFeedComments()
    })

    const comicEndPoint = Utils.getMeaningFullURL(this.comicURL)
    this.props.recordPageView(comicEndPoint, 0, res.storyID)
    this.getFeedPanelEpisodes()
  }

  checkPostRefType(panelItems) {
    let commentPlaceHolder = ''
    if (panelItems && panelItems.refType) {
      if (panelItems.refType == Constants.UPPERCASE_STORY) {
        this.rightIcon = require("../../../../assets/public_icon.png")
        this.storyType = Constants.PUBLIC
        const isUserGiftGiver = UserSession.instance.getCurrentUserBadges() && UserSession.instance.getCurrentUserBadges().length > 0
        if (isUserGiftGiver) {
          commentPlaceHolder = Constants.WRITE_PUBLIC_COMMENT
        } else {
          commentPlaceHolder = Constants.WRITE_COMMENT_FOR_NON_SUBSCRIBERS
        }
      } else if (panelItems.refType == Constants.UPPERCASE_REPOST) {
        this.storyType = Constants.FRIENDS //All Friends
        this.rightIcon = require("../../../../assets/friends_icon_menu.png")
        commentPlaceHolder = Constants.WRITE_COMMENT_FOR_ALL_FRIENDS
      } else if (panelItems.refType == Constants.SENT_POST) {
        this.storyType = Constants.SENT
        this.rightIcon = require("../../../../assets/share_each_icon.png")
        commentPlaceHolder = Constants.WRITE_PERSONAL_COMMENT
      } else if (panelItems.refType == Constants.GROUP_SENT_POST) {
        this.storyType = Constants.GROUP_SENT_POST
        this.rightIcon = require("../../../../assets/friends_icon_menu.png")
        commentPlaceHolder = Constants.WRITE_GROUP_COMMENT
      }
    }

    if (!this.state.canUserComment) {
      commentPlaceHolder = Constants.DISABLED_COMMENT
    }
    this.setState({ commentPlaceHolder: commentPlaceHolder }, () => {
      this.props.navigation.setParams({
        rightButtonText: this.storyType,
        rightIcon: this.rightIcon,
        isRightDisabled: true//panelItems && panelItems.refType == Constants.UPPERCASE_STORY ? true : false,
      })
    })
  }

  scrollListView(selectedIndex) {
    try {
      if (this.flatlist) {
        if (selectedIndex != null) {
          setTimeout(() => {
            this.flatlist.scrollToEnd()
          }, 1500);
        } else {
          if (this.state.panelItems && this.state.panelItems.userComments && this.state.panelItems.userComments.length > 0) {
            this.flatlist.scrollToEnd()
          }
        }
      }
    } catch (e) { }
  }

  openHomePage() {
    this.props.navigation.dispatch(StackActions.popToTop());
  }

  reloadStory() {
    this.isCommentsFetched = false
    this.setState({ showRefresh: true })
    this.getStoryDetails(true)
  }

  navigateToUserProfile(item) {
    let userId = item.userId ? item.userId : item.item ? item.item.user.userId : item.user.userId ? item.user.userId : null
    if (userId) {
      var subRoutesParams = { clickedUserID: userId }
      this.props.navigation.push(Constants.USER_PROFILE_SCREEN, { ...subRoutesParams })
    }
  }

  getFeedComments() {

    const isConnected = NetworkUtils.instance.isAvailable()
    if (!isConnected) {
      return Utils.showError(Constants.INTERNET_ERROR)
    }

    if (!this.hasMoreData || this.isFetchingCommentsInProgress) {
      return
    }

    this.isFetchingCommentsInProgress = true

    if (this.state.panelItems) {
      let lastCommentID = ''
      if (this.state.panelItems.userComments && this.state.panelItems.userComments.length > 0) {
        let exactIndex = 0 //As we are reversing the comments array
        if (this.state.panelItems.userComments[exactIndex]) {
          lastCommentID = this.state.panelItems.userComments[exactIndex].commentId
        } else if (exactIndex < 0) {
          return
        }
      }

      let data = { data: { storyID: this.state.panelItems.storyID, records: Constants.STORY_PAGE_COMMENT_PAGINATION_COUNT, startAfter: lastCommentID, sortingOrder: Constants.DESC } }
      this.props.getFeedComments(data, this.updateComments)
    }
  }

  updateComments(requestedData, commentData) {
    var panelsData = JSON.parse(JSON.stringify(this.state.panelItems))
    if (commentData && commentData.length > 0) {

      commentData = commentData.reverse()

      if (commentData.length < Constants.STORY_PAGE_COMMENT_PAGINATION_COUNT) {
        this.hasMoreData = false
      }

      if (!panelsData.userComments) panelsData.userComments = []

      let commentLength = panelsData.userComments.length
      let newCommentCount = this.newlyAddedCommentsCount

      for (let i = 1; i <= newCommentCount; i++) {
        let alreadycommentedItem = panelsData.userComments[commentLength - i]
        for (const index in commentData) {
          if (alreadycommentedItem && alreadycommentedItem.commentId == commentData[index].commentId) {
            commentData[index].isLiked = panelsData.userComments[commentLength - i].isLiked
            if (panelsData.userComments[commentLength - i].subComments) {
              commentData[index].subComments = panelsData.userComments[commentLength - i].subComments
            }
            panelsData.userComments.splice(commentLength - i, 1)
            this.newlyAddedCommentsCount--
            break
          }
        }
      }

      panelsData.userComments = [...commentData, ...panelsData.userComments]

    } else {
      this.hasMoreData = false
    }

    this.isCommentsFetched = true
    this.setState({ panelItems: panelsData, showPageRefresh: false }, () => {
      this.isNewStackPushed = false
      this.isRefreshing = false
      this.isFetchingCommentsInProgress = false
    })
  }

  likeDislikeUserComment(item, isLike, subCommentIndex) {
    const isConnected = NetworkUtils.instance.isAvailable()
    if (!isConnected) {
      return Utils.showError(Constants.INTERNET_ERROR)
    }

    const { commentId } = item
    let subCommentId = subCommentIndex != null ? item.subComments[subCommentIndex].subCommentId : null
    var panelsData = JSON.parse(JSON.stringify(this.state.panelItems))
    if (panelsData) {
      if (panelsData.userComments && panelsData.userComments.length > 0) {
        for (const commentIndex in panelsData.userComments) {
          const commentID = panelsData.userComments[commentIndex].commentId
          if (subCommentIndex == null && commentID === commentId) {
            panelsData.userComments[commentIndex].isLiked = !panelsData.userComments[commentIndex].isLiked
            if (panelsData.userComments[commentIndex].isLiked) {
              panelsData.userComments[commentIndex].reactionsCount++
            } else {
              panelsData.userComments[commentIndex].reactionsCount--
            }
            break
          } else if (commentID == commentId && subCommentId && panelsData.userComments[commentIndex].subComments && panelsData.userComments[commentIndex].subComments.length > 0) {
            for (const subCommentindex in panelsData.userComments[commentIndex].subComments) {
              const subCommentID = panelsData.userComments[commentIndex].subComments[subCommentindex].subCommentId
              if (subCommentID === subCommentId) {
                panelsData.userComments[commentIndex].subComments[subCommentindex].isLiked = !panelsData.userComments[commentIndex].subComments[subCommentindex].isLiked
                if (panelsData.userComments[commentIndex].subComments[subCommentindex].isLiked) {
                  panelsData.userComments[commentIndex].subComments[subCommentindex].reactionsCount++
                } else {
                  panelsData.userComments[commentIndex].subComments[subCommentindex].reactionsCount--
                }
                break
              }
            }
          }
        }
        this.setState({ panelItems: panelsData })
      }
    }
    let data = { data: { storyID: this.state.panelItems.storyID, commentID: commentId, subCommentID: subCommentId } }
    this.props.likeDislikeComment(data, isLike)
  }

  onChange(key, value) {
    this.setState({ [key]: value })
  }

  onSubmitComment() {
    Keyboard.dismiss()
    if (!this.canUserComment()) {
      return
    }
    if (!this.state.canUserComment) {
      Alert.alert("Permission Denied", "You are not allowed to post comments.")
      return
    }


    const isConnected = NetworkUtils.instance.isAvailable()
    if (!isConnected) {
      return Utils.showError(Constants.INTERNET_ERROR)
    }

    if (!this.state.userComment || this.state.userComment.trim() == '') {
      return
    }

    if (this.isEditingComment) {
      return this.onEditComment()
    }

    const { photoURL, displayName } = this.props.userDetails
    let panelsData = JSON.parse(JSON.stringify(this.state.panelItems))
    let userComments = {
      user: {
        name: displayName,
        image: photoURL,
        userId: this.currentUserId,
        badges: UserSession.instance.getCurrentUserBadges()
      },
      commentText: this.state.userComment.replace(/^\s+|\s+$/g, ''),
      createdAt: Date.now(),
      isLiked: false,
      reactionsCount: 0,
      shouldSubCommentVisible: true
    }
    let commentID = null
    let isForReply = false
    if (panelsData && panelsData.userComments && panelsData.userComments.length > 0) {
      for (const commentKey in panelsData.userComments) {
        if (panelsData.userComments[commentKey].isReplying) {
          isForReply = true
          panelsData.userComments[commentKey].shouldSubCommentVisible = panelsData.userComments[commentKey].shouldSubCommentVisible ? false : true
          commentID = panelsData.userComments[commentKey].commentId
          if (panelsData.userComments[commentKey].subComments && panelsData.userComments[commentKey].subComments.length > 0) {
            panelsData.userComments[commentKey].subComments.push(userComments)
          } else {
            panelsData.userComments[commentKey].subComments = []
            panelsData.userComments[commentKey].subComments.push(userComments)
          }
          break
        }
      }

      if (!isForReply) {
        panelsData.userComments.push(userComments)
        this.newlyAddedCommentsCount++
      }
      panelsData.commentCount++
    } else {
      panelsData.userComments = []
      panelsData.userComments.push(userComments)
      panelsData.commentCount++
      this.newlyAddedCommentsCount++
    }

    let data = { data: { storyID: this.state.panelItems.storyID, commentText: userComments.commentText, commentID } }
    this.props.postCommentOnFeed(data, this.updateCommentID)

    this.resetCommentLabelText(panelsData)
    this.setState({ panelItems: panelsData, userComment: '' }, () => {
      if (!isForReply) {
        this.scrollListView(this.state.panelItems.length - 1)
      }
    })
  }

  updateCommentID(requestedData, responseData) {
    const { commentId, subCommentId } = responseData
    const { panelItems } = this.state
    for (const commentKey in panelItems.userComments) {
      if (!panelItems.userComments[commentKey].commentId) {
        panelItems.userComments[commentKey].commentId = commentId
        break
      } else if (panelItems.userComments[commentKey].commentId && panelItems.userComments[commentKey].subComments) {
        for (const subCommentKey in panelItems.userComments[commentKey].subComments) {
          if (!panelItems.userComments[commentKey].subComments[subCommentKey].subCommentId) {
            panelItems.userComments[commentKey].subComments[subCommentKey].subCommentId = subCommentId
          }
        }
      }
    }
    this.setState({ panelItems })
  }

  onReplyPress(item, commentIndex) {
    if (!this.canUserComment()) {
      return
    }

    this.isEditingComment = false
    this.selectedIndex = commentIndex
    this.selCommentItem = item
    this.getRepliedText()
    this.userCommentTextInput && this.userCommentTextInput.focus()
    var panelsData = JSON.parse(JSON.stringify(this.state.panelItems))
    if (item) {
      for (const index in panelsData.userComments) {
        if (commentIndex == parseInt(index)) {
          panelsData.userComments[index].isReplying = true
        } else {
          panelsData.userComments[index].isReplying = false
        }
      }
    }
    this.setState({ panelItems: panelsData })
  }

  onEditPress(item) {
    this.isEditingComment = true
    this.resetCommentLabelText()
    this.selCommentItem = item
    this.onChange('userComment', item.commentText)
    this.userCommentTextInput && this.userCommentTextInput.focus()
  }

  resetCommentLabelText(panelsData = this.state.panelItems) {
    this.selCommentItem = null
    if (panelsData && panelsData.userComments && panelsData.userComments.length > 0) {
      for (const commentKey in panelsData.userComments) {
        panelsData.userComments[commentKey].isReplying = false
      }
    }
    this.getRepliedText()
  }

  onEditComment() {

    const isConnected = NetworkUtils.instance.isAvailable()
    if (!isConnected) {
      return Utils.showError(Constants.INTERNET_ERROR)
    }

    if (!this.state.userComment || this.state.userComment.trim() == '') {
      return
    }

    const { panelItems } = this.state

    let panelsData = JSON.parse(JSON.stringify(this.state.panelItems))
    let isReply = false
    let subCommentIDParam = null
    let commentIDParam = null

    if (panelsData && panelsData.userComments && panelsData.userComments.length > 0) {
      for (const commentKey in panelsData.userComments) {
        if (this.selCommentItem && this.selCommentItem.subCommentId) { // for inner comment
          for (const subCommentKey in panelsData.userComments[commentKey].subComments) {
            let subCommentID = panelsData.userComments[commentKey].subComments[subCommentKey].subCommentId
            if (this.selCommentItem.subCommentId == subCommentID) {
              panelsData.userComments[commentKey].subComments[subCommentKey].commentText = this.state.userComment
              subCommentIDParam = subCommentID
              commentIDParam = panelsData.userComments[commentKey].commentId
              isReply = true
              break
            }
          }
        } else if (this.selCommentItem) { // for main comment
          let commentID = panelsData.userComments[commentKey].commentId
          if (this.selCommentItem.commentId == commentID) {
            panelsData.userComments[commentKey].commentText = this.state.userComment
            commentIDParam = commentID
            break
          }
        }
      }
    }

    this.isEditingComment = false
    this.setState({ panelItems: panelsData, userComment: '' })
    let data = { data: { storyID: panelItems.storyID, commentText: this.state.userComment, commentID: commentIDParam, subCommentID: subCommentIDParam } }
    this.props.editComment(data)
  }

  deleteComment(item) {

    const isConnected = NetworkUtils.instance.isAvailable()
    if (!isConnected) {
      return Utils.showError(Constants.INTERNET_ERROR)
    }

    let commentIDParam = null
    let subCommentIDParam = null

    let panelsData = JSON.parse(JSON.stringify(this.state.panelItems))
    if (panelsData && panelsData.userComments && panelsData.userComments.length > 0) {
      for (const commentKey in panelsData.userComments) {
        let commentId = panelsData.userComments[commentKey].commentId
        if (item.commentId && commentId == item.commentId) {
          commentIDParam = commentId
          panelsData.commentCount--
          if (panelsData.userComments[commentKey].subComments && panelsData.userComments[commentKey].subComments.length > 0) {
            panelsData.commentCount = panelsData.commentCount - panelsData.userComments[commentKey].subComments.length
          }
          panelsData.userComments.splice(commentKey, 1)
          break
        } else if (panelsData.userComments[commentKey].subComments && panelsData.userComments[commentKey].subComments.length > 0) {
          for (const subCommentKey in panelsData.userComments[commentKey].subComments) {
            let subCommentID = panelsData.userComments[commentKey].subComments[subCommentKey].subCommentId
            if (item.subCommentId && item.subCommentId == subCommentID) {
              subCommentIDParam = subCommentID
              panelsData.userComments[commentKey].subComments.splice(subCommentKey, 1)
              panelsData.commentCount--
              commentIDParam = panelsData.userComments[commentKey].commentId
              break
            }
          }
        }
      }
      this.setState({ panelItems: panelsData })
    }

    let data = { data: { storyID: panelsData.storyID, commentID: commentIDParam, subCommentID: subCommentIDParam } }
    this.props.deleteComment(data)
  }

  onFlagCommentTap(item) {
    const isConnected = NetworkUtils.instance.isAvailable()
    if (!isConnected) {
      return Utils.showError(Constants.INTERNET_ERROR)
    }

    this.setState({ showPageRefresh: true })

    const data = {
      data: {
        "reason": "Flag comment",
        "commentID": item.commentId
      }
    }

    if (Utils.checkData(item.subCommentId)) {
      data.data.subCommentID = item.subCommentId
    }

    this.props.flagUserComment(data, (isSuccess) => {
      this.setState({ showPageRefresh: false })
      if (isSuccess) {
        Utils.showFlagCommentAlert()
      }
    })
  }

  onBlockUserTap(item) {
    const isConnected = NetworkUtils.instance.isAvailable()
    if (!isConnected) {
      return Utils.showError(Constants.INTERNET_ERROR)
    }

    if (!Utils.checkIsTinyviewAdminUser()) {
      return
    }

    this.setState({ showPageRefresh: true })

    const data = {
      data: {
        "userID": item.user.userId,
        "permissionToRemove": "comment"
      }
    }

    this.props.blockUserAction(data, (message) => {
      this.setState({ showPageRefresh: false })
      if (message != null) {
        Utils.showBlockUserAlert(message)
      }
    })
  }

  deleteRepost(item) {

    const isConnected = NetworkUtils.instance.isAvailable()
    if (!isConnected) {
      return Utils.showError(Constants.INTERNET_ERROR)
    }

    let panelsData = JSON.parse(JSON.stringify(this.state.panelItems))
    if (panelsData.storyID == item.storyID) {
      let data = {
        data: {
          userID: this.currentUserId,
          storyID: item.storyID
        }
      }
      if (panelsData.refType == Constants.GROUP_SENT_POST || panelsData.refType == Constants.SENT_POST) {
        this.props.deleteSentPost(data)
      } else {
        this.props.deleteRepostedComic(data)
      }
      this.onBackPress(true)
    }
  }

  editRepost(item) {
    var parentRoute = Constants.FEED_SCREEN
    var subRoute = Constants.REPOST_SCREEN
    var subParams = { title: Constants.REPOST, panelItems: item, updateItemHeight: this.updateItemHeight, isEditingRepost: true, onStoryUpdated: this.onRepostedStoryUpdated }
    Utils.navigateToSubRouteWithParams(parentRoute, subRoute, this.props, subParams)
  }

  onRepostedStoryUpdated(editedText) {
    let panelsData = JSON.parse(JSON.stringify(this.state.panelItems))
    if (panelsData) {
      panelsData.comment = editedText
    }
    this.setState({ panelItems: panelsData })
  }

  likeDislikeFeed(isLiked) {
    const isConnected = NetworkUtils.instance.isAvailable()
    if (!isConnected) {
      return Utils.showError(Constants.INTERNET_ERROR)
    }

    if (!isLiked) {
      Utils.vibrateDevice()
    }

    var panelItems = JSON.parse(JSON.stringify(this.state.panelItems))
    panelItems.isLiked = !panelItems.isLiked
    panelItems.isLikeChanged = panelItems.isLiked
    if (panelItems.isLiked) {
      panelItems.likeCount++
      panelItems.isLikeChanged = true
      if (!panelItems.giftsCount) {
        panelItems.giftsCount = {}
      }
      if (!Utils.checkData(panelItems.giftsCount[Constants.LIKE.toUpperCase()])) {
        panelItems.giftsCount[Constants.LIKE.toUpperCase()] = 0
      }
      panelItems.giftsCount[Constants.LIKE.toUpperCase()]++
    } else {
      panelItems.likeCount--
      panelItems.isLikeChanged = false
      if (!panelItems.giftsCount) {
        panelItems.giftsCount = {}
      }
      if (Utils.checkData(panelItems.giftsCount[Constants.LIKE.toUpperCase()])) {
        panelItems.giftsCount[Constants.LIKE.toUpperCase()]--
      } else {
        panelItems.giftsCount[Constants.LIKE.toUpperCase()] = 0
      }
    }
    this.setState({ panelItems })

    let data = { "data": { storyID: panelItems.storyID, action: panelItems.action, reactionType: 'LIKE', refType: panelItems.refType } }
    this.props.likeDislikeFeed(data, isLiked)
  }

  async navigateToAppropriateScreen() {
    let routeName = Constants.USER_PROFILE_SCREEN
    let params = { isupdatingProfile: true }
    let data = this.props.userDetails

    // if (!data || !data.dob) {
    //   routeName = Constants.DOB_SCREEN
    // }

    if (!data.displayName || !data.gender) {
      routeName = Constants.EDIT_PROFILE_SCREEN
    } else if (!data.aboutMe || data.aboutMe == '') {
      routeName = Constants.EDIT_SOCIAL_LINKS
    }

    if (routeName == Constants.USER_PROFILE_SCREEN) {
      this.props.navigation.push(routeName, { ...params })
    } else {
      Utils.navigateToDrawerLoginRoute(this.props, routeName, params)
    }
  }

  resetActionSheet() {
    let sheetTitle = ''
    let sheetMessage = Constants.SIGN_IN_ACTION_SHEET_MESSAGE
    let sheetOptions = [Constants.SIGN_IN, Constants.CANCEL]
    this.changeActionSheetTitleAndButton(sheetTitle, sheetOptions, false, sheetMessage)
  }

  navigateToSubscribePage() {
    this.openChapter(settings.getSubscribeURL())
  }

  onActionSheetButtonPress(index) {
    if (index == 0) {
      if (this.state.sheetOptions[0] == Constants.ALL_FRIENDS) {
        const params = {
          respotType: this.state.sheetOptions[0], sendToAllFriend: true,
          clickedStoryID: this.clickedStoryID, onStoryUpdated: this.onStoryUpdated,
          clickedPanelItems: this.clickedPanelItems
        }
        Utils.navigateToSubRouteWithParams(Constants.FEED_SCREEN, Constants.SEND_POST_SCREEN, this.props, params)
      } else if (this.state.sheetOptions[0] == Constants.EDIT_PROFILE) {
        this.navigateToAppropriateScreen()
      } else if (this.state.sheetOptions[0] == Constants.SIGN_IN) {
        const params = { isForLoginProcess: true }
        this.closeBottomSheet()
        Utils.navigateToDrawerLoginRoute(this.props, Constants.LOGIN_SCREEN, params)
      } else if (this.state.sheetOptions[0] == Constants.UPGRADE_TO_A_PREMIUM_PLAN) {
        this.navigateToSubscribePage()
      } else if (this.state.sheetOptions[0] == Constants.COPY_LINK) {
        this.copyLink()
      } else if (this.state.sheetOptions[0] == Constants.MANAGE_ALERTS) {
        Utils.navigateToManageAlertsPage(this.props, { configFromFollowTab: true, seriesToFollowData: this.seriesToFollowData })
      }
    } else if (index == 1) {
      this.resetActionSheet()
    } else if (index == 2) {
      if (this.state.sheetOptions[2] == Constants.SELECT_FRIENDS) {
        const params = {
          respotType: this.state.sheetOptions[2], sendToAllFriend: false,
          clickedStoryID: this.clickedStoryID, onStoryUpdated: this.onStoryUpdated,
          clickedPanelItems: this.clickedPanelItems
        }
        Utils.navigateToSubRouteWithParams(Constants.FEED_SCREEN, Constants.SEND_POST_SCREEN, this.props, params)
      } else if (this.state.sheetOptions[2] == Constants.SHARE_VIA) {
        this.props.share(this.clickedPanelItems)
      } else if (this.state.sheetOptions[2] == Constants.UPGRADE_TO_A_PREMIUM_PLAN) {
        this.navigateToSubscribePage()
      } else if (this.state.sheetOptions[2] == Constants.COPY_LINK) {
        this.copyLink()
      }
    } else if (index == 3) {
      if (this.state.sheetOptions[3] == Constants.SHARE_VIA) {
        this.props.share(this.clickedPanelItems)
      } else if (this.state.sheetOptions[3] == Constants.COPY_LINK) {
        this.copyLink()
      }
    } else if (index == 4) {
      this.props.share(this.clickedPanelItems)
    }
  }

  copyLink() {
    let actionURL = this.clickedPanelItems && this.clickedPanelItems.action
    if (!Utils.checkData(actionURL)) {
      actionURL = settings.webBaseURL + Utils.getWebURL(Utils.getMeaningFullURL(this.comicURL))
    }

    let completeURL = actionURL
    if (!actionURL.startsWith(settings.webBaseURL) && this.clickedPanelItems && this.clickedPanelItems.actionType != "website") {
      completeURL = settings.webBaseURL + Utils.getWebURL(actionURL)
    }

    Utils.copyToClipboard(completeURL, "Link Copied")
  }

  onIconPress(actionName, panelItems) {
    if (actionName === Constants.COMMENT) {
      this.userCommentTextInput && this.userCommentTextInput.focus()
    } else if (actionName === Constants.SHARE) {
      this.props.share(panelItems)
    } else if (actionName === Constants.REPOST) {
      this.clickedStoryID = panelItems != null ? panelItems.storyID : storyID
      this.clickedPanelItems = panelItems != null ? panelItems : null
      this.isUserDetailsEmpty(false)

      this.setState({ showShareBottomSheet: true })
    }
  }

  changeActionSheetTitleAndButton(sheetTitle, sheetOptions, showActionSheet = false, sheetMessage = '') {
    this.setState({ sheetTitle: sheetTitle, sheetMessage: sheetMessage, sheetOptions: sheetOptions, showShareBottomSheet: false }, () => {
      if (showActionSheet) {
        this.actionSheet.show()
      }
    })
  }

  countPress(actionName, storyID, subCommentId) {
    if (actionName === Constants.COMMENT) {
      this.userCommentTextInput && this.userCommentTextInput.focus()
    } else {
      let navTitle = actionName === Constants.REPOSTED ? actionName : Constants.REACTIONS
      let subRoute = actionName == Constants.FEED_LIKE ? Constants.STORY_REACTIONS_LIST_SCREEN : Constants.USER_LIST_SCREEN
      var params = { title: navTitle, configFor: actionName, storyID: storyID, panelItems: this.state.panelItems }
      this.navigateToUserListScreen(params, subRoute)
    }
  }

  onCommentLikeCountPress(actionName, commentID, subCommentId, storyID) {
    let navTitle = Constants.LIKES
    var params = { title: navTitle, configFor: actionName, commentID: commentID, storyID: storyID, subCommentId }
    this.navigateToUserListScreen(params)
  }

  navigateToUserListScreen(params, subRoute = Constants.USER_LIST_SCREEN) {
    var parentRoute = Constants.FEED_SCREEN
    var subRoute = subRoute
    var refType = this.state.panelItems ? this.state.panelItems.refType : null
    if (!params) {
      var storyID = this.state.panelItems ? this.state.panelItems.storyID : this.clickedStoryID
      var whoSharedTheStoryUID = this.state.panelItems && this.state.panelItems.user ? this.state.panelItems.user.userId : null
      var configFor = Constants.WITH_WHOM_STORY_SHARED
      if (whoSharedTheStoryUID == this.currentUserId && refType == Constants.UPPERCASE_REPOST) {
        configFor = Constants.SEE_FRIENDS
      }

      params = { configFor: configFor, storyID: storyID, refType: refType }
    }
    this.props.navigation.push(subRoute, { ...params })
  }

  // getItemLayout(data, index) {
  //   if (index >= 0 && data.length > 0 && index < data.length) {
  //     const { newOffset, height } = this.getOffsetAndHeightForPosition(data, index);

  //     return {
  //       length: height,
  //       offset: newOffset,
  //       index
  //     }
  //   }
  //   return { index, length: 0, offset: 0 };
  // }

  // getOffsetAndHeightForPosition(data, index) {
  //   let height = 0;
  //   let newOffset = 0;
  //   for (const key in data) {
  //     const item = data[key];
  //     let currentHeight = 0
  //     if (item.viewHeight) {
  //       currentHeight = item.viewHeight
  //     } else {
  //       currentHeight = 150 // Static Height
  //     }

  //     if (key == index) {
  //       height = currentHeight
  //       break;
  //     }
  //     if (key == 0) {
  //       newOffset = 400// starting offset after Header
  //     }
  //     newOffset = newOffset + currentHeight;
  //   }
  //   if (index == 0 && newOffset == 0) {
  //     newOffset = 400
  //   }
  //   return { newOffset, height };
  // }

  updateItemHeight(index, height) {
    if (!this.panelItems || !this.panelItems.userComments) { return }

    const panelsData = this.panelItems.userComments[index]
    if (panelsData != null) {
      panelsData.viewHeight = height;
    }
  }

  requestPurchase(product, feedData) {
    const userBalance = UserSession.instance.getUserReferralBalance()
    const referralBalance = parseFloat(userBalance.toFixed(2))
    const productCost = product?.cost ? parseFloat(product.cost.toFixed(2)) : null;
    if (productCost && referralBalance >= productCost) {
      const storyID = feedData?.storyID
      const transaction = { productId: product?.productId }
      const reqData = { data: { storyID, platform: Constants.INTERNAL_PAYMENT_PLATFORM, productInfo: product, transaction } }
      this.props.addNewTransaction(reqData, feedData, this.onGiftItemSent)
    } else {
      this.props.requestPurchase(product, feedData, this.onGiftItemSent)
    }
  }

  onGiftItemSent() {
    this.reloadStory()
  }

  isUserDetailsEmpty(showActionSheet = true) {
    if (UserSession.instance.isUserDetailsEmpty()) {
      let sheetTitle = ''
      let sheetMessage = 'You need to complete your profile first.'
      let sheetOptions = [Constants.EDIT_PROFILE, Constants.CANCEL]
      this.changeActionSheetTitleAndButton(sheetTitle, sheetOptions, showActionSheet, sheetMessage)
      return true
    }
    return false
  }

  canUserComment(showActionSheet = true) {
    let isEligibleToComment = Utils.isUserEligibleToComment()

    if (isEligibleToComment == null) {
      return true
    } else {
      this.changeActionSheetTitleAndButton('', isEligibleToComment.sheetOptions, showActionSheet, isEligibleToComment.sheetMessage)
      return false
    }
  }

  onFollowPress(pathUrl, isFollowing, forceFollowSeries = false, forSync = false, showActionSheet = true) {
    const isConnected = NetworkUtils.instance.isAvailable()
    if (!isConnected) {
      SessionManager.instance.showErrorAlert("No Internet Connection", "An error occurred while trying to access the server. Please check your Internet connection and try again.")
      return;
    }

    if (!forSync && (Utils.isHomeURL(pathUrl) || Utils.isTVSeriesURL(pathUrl))) { // Syncing Home and What's Tinyview comic follow/unfollow
      if (Utils.isHomeURL(pathUrl)) {
        this.onFollowPress(settings.getWhatsTinyviewURL(), isFollowing, forceFollowSeries, true, true)
      } else {
        this.onFollowPress(settings.getComicHomeURL(), isFollowing, forceFollowSeries, true, true)
      }
    } else {
      if (showActionSheet && !isFollowing && !UserSession.instance.isAnyUserAlertsEnabled()) {
        const channelName = Utils.getUserVisibleChannelName(pathUrl)
        this.seriesToFollowData = { title: channelName, action: Utils.getMeaningFullURL(pathUrl) }
        let isTinyviewChannel = channelName == Constants.LETTER_CASE_TINYVIEW
        let sheetTitle = Constants.MANAGE_ALERTS_ACTIONSHEET_TITLE
        let sheetMessage = isTinyviewChannel ? `You want to follow ${channelName} but we have no way of reaching you. Manage alerts and let us know how to send you updates from ${channelName}.` : `You want to follow ${channelName} but we have no way of reaching you. Manage alerts and know when ${channelName} publishes a new comic.`
        let sheetOptions = [Constants.MANAGE_ALERTS, Constants.NOT_NOW]
        this.changeActionSheetTitleAndButton(sheetTitle, sheetOptions, true, sheetMessage)
      } else {
        let isSubscribed = this.isSubscribedToAlert(pathUrl)
        if (isFollowing) {
          if (isSubscribed) this.subscribeToAlert(false, pathUrl)
        } else if (!isSubscribed) this.subscribeToAlert(true, pathUrl)
      }
    }
  }

  subscribeToAlert(isSubscribe, seriesURL = null) {
    this.props.updateAlertSubscription(isSubscribe, seriesURL)
  }

  isSubscribedToAlert(seriesURL = null, seriesID = null) {
    let alertName = seriesID
    if (seriesID == null || seriesID == undefined) {
      alertName = this.getAlertChannelName(seriesURL);
    }

    return this.props.alerts && this.props.alerts[alertName] == true ? true : false
  }

  getAlertChannelName(seriesURL = null) {
    let alertName = this.comicURL && Utils.getChannelName(this.comicURL)
    if (seriesURL) {
      alertName = Utils.getChannelName(Utils.resolvePath(this.props.pathUrl, seriesURL))
    }
    return alertName;
  }

  onShowCommentPress(actionName, panelItem, selectedIndex, commentItem, storyData = null, showComments = true) {
    if (!showComments && !this.canUserComment()) {
      return
    }

    if (storyData) {
      const { storyID = null, action = null, series = null } = storyData
      const { panelItems } = this.state

      if (panelItems) {
        if (Utils.checkData(storyID) && panelItems.storyID) {
          if (storyID == panelItems.storyID) {
            this.resetCommentLabelText()
            this.userCommentTextInput && this.userCommentTextInput.focus()
            return null
          }
        }
      }

      let storyReqData = null
      if (Utils.checkData(storyID)) {
        storyReqData = { storyID: storyID, onStoryUpdated: this.onStoryUpdated }
      } else if (Utils.checkData(action)) {
        storyReqData = { action: action, onStoryUpdated: this.onStoryUpdated, series }
      } else {
        return null
      }

      return this.props.navigation.push(Constants.POST_COMMENTS_SCREEN, { storyData: storyReqData })
    } else {
      this.resetCommentLabelText()
      this.userCommentTextInput && this.userCommentTextInput.focus()
    }
  }

  closeBottomSheet(fromGiftTap = false, item, feedData) {
    this.setState({ showBottomSheet: false, showShareBottomSheet: false }, () => {
      if (fromGiftTap) {
        this.requestPurchase(item, feedData)
      }
    })
  }

  onLikePress(selComic) {
    this.setState({ showBottomSheet: true })
    this.tappedComicData = selComic
  }

  shareImage(item) {
    if (Platform.OS === 'ios' && parseInt(Platform.Version, 10) <= 12) {
      ActionSheetIOS.showActionSheetWithOptions(
        {
          options: ["Cancel", "Share"],
          cancelButtonIndex: 0
        },
        buttonIndex => {
          if (buttonIndex === 0) {
            Utils.log("shareImage", buttonIndex)
          }
          else if (buttonIndex === 1) {
            this.props.shareImage(item)
          }
        }
      )
    } else {
      this.props.shareImage(item)
    }
  }

  onPrevCommentsButtonTap() {
    this.setState({ showPageRefresh: true })
    this.getFeedComments()
  }

  renderItem(item) {
    return (
      <View style={styles.commentView}>
        <CommentsPanel panelItems={this.state.panelItems} shouldSubCommentVisible={item.item.shouldSubCommentVisible} item={item.item} index={item.index} {...this.valueProps} />
      </View>
    )
  }

  renderHeader(item) {
    if (!item) {
      return null;
    }

    let userComments = this.state.panelItems && this.state.panelItems.userComments
    const valueProps = { item: { item: item }, index: item.index, ...this.valueProps, userDetails: this.props.userDetails }
    const keyValue = (item) ? (item.title + item.image) : ""
    const isSharedStory = item.refType != Constants.UPPERCASE_STORY ? true : false
    return (
      <View>
        <StoryPanel shareImage={this.shareImage} isPostCommentStory={true} showSeriesFollowButton={!isSharedStory} onFollowPress={this.onFollowPress} alerts={this.props.alerts} isSubscribed={this.isSubscribedToAlert} key={keyValue} {...valueProps} openChapter={this.openChapter} showComments={false} pathUrl={this.comicURL} onSeriesHomeClicked={this.onSeriesHomeClicked} />
        {userComments && userComments.length > 0 && this.hasMoreData && this.renderPrevComment()}
      </View>
    )
  }

  renderPrevComment() {
    return (
      <TouchableOpacity style={styles.prevCommentsTextStyle}
        onPress={this.onPrevCommentsButtonTap}>
        <Text style={this.context.p}>{Constants.SEE_PREVIOUS_COMMENT}</Text>
      </TouchableOpacity>
    )
  }

  renderFooter(item) {
    if (!item || item.actionType == Constants.TINYVIEW) {
      return null;
    }

    return (
      <View>
        {!this.isCommentsFetched &&
          <Image style={loadingViewStyle(this.aspectRatio)} source={require('../../../../assets/comments_loading_view.gif')} />}
      </View>
    )
  }

  onSeriesHomeClicked() {
    const { panelItems } = this.state
    const isExternalLink = panelItems && panelItems.actionType == "website"

    let comicSeriesPath = Utils.getComicSeriesURL(this.comicURL)
    if (isExternalLink && panelItems && panelItems.series) {
      comicSeriesPath = `/${panelItems.series}/index.json`
    }
    const redirectPath = Utils.resolvePath(this.props.pathUrl, comicSeriesPath)
    this.props.navigation.push('Home', { comicHome: redirectPath, hasUserInfo: true })
  }

  getRepliedText() {
    if (this.isEditingComment) {
      this.setState({ repliedLabel: null })
      return
    }
    let replylabelText = this.selCommentItem ? (this.selCommentItem.user.name) : null
    this.setState({ repliedLabel: replylabelText })
  }

  onReplyCancel() {
    let panelsData = JSON.parse(JSON.stringify(this.state.panelItems))
    for (const index in panelsData.userComments) {
      panelsData.userComments[index].isReplying = false
    }
    this.setState({ panelItems: panelsData, repliedLabel: null })
  }

  openChapter(pathURL, item = null) {
    if (pathURL == settings.getInfluencePageURL()) {
      Utils.navigateToInfluencePage(this.props)
    } else if (!Utils.isHomeURL(pathURL) && !Utils.isChannelURL(pathURL) && !Utils.isComicURL(pathURL) && item && item.item.user && item.item.user.userId) {
      this.navigateToUserProfile(item)
    } else {
      const redirectPath = Utils.resolvePath(settings.apiBaseURL, pathURL)
      this.props.navigation.push('Home', { comicHome: redirectPath, hasUserInfo: true })
    }
  }

  renderBottomSheet() {
    const valueProps = {
      closeBottomSheet: this.closeBottomSheet, tappedComicData: this.tappedComicData, requestPurchase: this.requestPurchase, likeDislikeFeed: this.likeDislikeFeed
    }
    return (
      <GiftBottomSheet {...valueProps} />
    )
  }

  signInSheet() {
    let sheetTitle = ''
    let sheetMessage = Constants.SIGN_IN_ACTION_SHEET_MESSAGE
    let sheetOptions = [Constants.SIGN_IN, Constants.CANCEL]
    this.changeActionSheetTitleAndButton(sheetTitle, sheetOptions, true, sheetMessage)
  }

  renderShareActionSheet() {
    const valueProps = { closeBottomSheet: this.closeBottomSheet, showShareBottomSheet: this.state.showShareBottomSheet, clickedPanelItems: this.clickedPanelItems, share: this.props.share, clickedStoryID: this.clickedStoryID, onStoryUpdated: this.onStoryUpdated, navigation: this.props.navigation, signInSheet: this.signInSheet, pathUrl: this.comicURL }
    return (
      <ShareBottomSheet {...valueProps} />
    )
  }

  _listEmptyComponent() {
    if (this.state.panelItems) {
      return (
        <View style={{ marginTop: 4, marginLeft: navigationStyle.panelsMargin }}>
          <Text style={this.context.p}>{Constants.EMPTY_COMMENTS_TEXT}</Text>
        </View>
      )
    }
  }

  onFocus() {
    if (UserSession.instance.isLoggedInUser() && UserSession.instance.getCurrentUserBadges().length > 0) {
      this.userCommentTextInput && this.userCommentTextInput.focus()
    } else {
      this.userCommentTextInput && this.userCommentTextInput.blur()
    }
  }

  onClickTextInput() {
    if (UserSession.instance.isLoggedInUser() && UserSession.instance.getCurrentUserBadges().length > 0) {
      this.userCommentTextInput && this.userCommentTextInput.focus()
    } else {
      !this.canUserComment()
    }
  }

  navigateToScreens(navigationScreen) {
    if (navigationScreen == Constants.FOLLOW) {
      const isExternalLink = this.state.panelItems && this.state.panelItems.actionType == "website"
      var urlToFollow = this.comicURL
      if (isExternalLink) {
        urlToFollow = Utils.resolvePath(settings.apiBaseURL, Utils.getChannelURL(this.state.panelItems.series))
      }
      this.onFollowPress(urlToFollow)
    } else if (navigationScreen == Constants.MANAGE_ALERTS) {
      Utils.navigateToManageAlertsPage()
    } else if (navigationScreen == Constants.UPGRADE) {
      this.navigateToSubscribePage()
    }
  }

  renderButton(item) {
    return (
      <TouchableOpacity
        style={[buyButton, styles.buyButton, { backgroundColor: this.context.colors.logoRed, borderColor: this.context.colors.logoRed }]}
        onPress={() => this.navigateToScreens(item)}>
        <Text style={[this.context.bodyMini, { color: this.context.colors.textInverse }]}>{item}</Text>
      </TouchableOpacity>
    )
  }

  hidePushNotificationMsg() {
    SessionManager.instance.shownAlertMessage = false
    this.setState({ showNotificationMessage: false })
  }

  renderManageAlertMsg(seriesID = null) {
    let isAnyAlertsEnabled = UserSession.instance.isAnyUserAlertsEnabled()
    if (isAnyAlertsEnabled || !this.isSubscribedToAlert(this.comicURL, seriesID)) {
      return;
    }

    return (
      <View>
        <View style={[bannerMsgContainer, { backgroundColor: this.context.colors.bannerBackgroundError }]}>
          <View
            style={bannerTextView}>
            <Text style={[this.context.bodyMini, { color: this.context.colors.bannerTextError }]} adjustsFontSizeToFit>{Constants.MANAGE_ALERTS_ACTIONSHEET_TITLE}</Text>
          </View>
          {this.renderButton(Constants.MANAGE_ALERTS)}
          <TouchableOpacity
            style={bannerCrossIconView}
            onPress={() => this.hidePushNotificationMsg()}>
            <FastImage
              style={bannerCrossIcon}
              tintColor={this.context.colors.bannerTextError}
              source={require('../../../../assets/close_window.png')}
            />
          </TouchableOpacity>
        </View>
      </View>
    )
  }

  renderFollowSeriesMsg(seriesID = null) {
    const isSubscribed = this.isSubscribedToAlert(this.comicURL, seriesID)
    const channelName = Utils.getUserVisibleChannelName(this.comicURL, seriesID)
    let message = `Never miss a new comic from ${channelName}!`;
    let isTinyviewSeries = Utils.isTinyviewPage(this.comicURL) && !Utils.isChannelURL(this.comicURL)
    if (isTinyviewSeries) {
      message = `Never miss an update from ${channelName}!`;
    }
    if ((isSubscribed) || this.props.alerts == null) {
      return null;
    }

    return (
      <View>
        <View style={[bannerMsgContainer, { backgroundColor: this.context.colors.bannerBackgroundError }]}>
          <View
            style={bannerTextView}>
            <Text style={[this.context.bodyMini, { color: this.context.colors.bannerTextError }]} adjustsFontSizeToFit>{message}</Text>
          </View>
          {this.renderButton(Constants.FOLLOW)}
          <TouchableOpacity
            style={bannerCrossIconView}
            onPress={() => this.hidePushNotificationMsg()}>
            <FastImage
              style={bannerCrossIcon}
              tintColor={this.context.colors.bannerTextError}
              source={require('../../../../assets/close_window.png')}
            />
          </TouchableOpacity>
        </View>
      </View>
    )
  }

  renderUpgradeSubsMsg(seriesID = null) {
    const isAnyAlertsEnabled = UserSession.instance.isAnyUserAlertsEnabled()
    const hasAnySubscription = SessionManager.instance.hasAnySubscriptionPurchase()
    if (hasAnySubscription || !isAnyAlertsEnabled || !this.isSubscribedToAlert(this.comicURL, seriesID)) {
      return;
    }

    let message = Constants.UPGRADE_SUBSCRIPTION_TITLE_MESSAGE
    return (
      <View>
        <View style={[bannerMsgContainer, { backgroundColor: this.context.colors.bannerBackgroundError }]}>
          <View
            style={bannerTextView}>
            <Text style={[this.context.bodyMini, { color: this.context.colors.bannerTextError }]} adjustsFontSizeToFit>{message}</Text>
          </View>
          {this.renderButton(Constants.UPGRADE)}
          <TouchableOpacity
            style={bannerCrossIconView}
            onPress={() => this.hidePushNotificationMsg()}>
            <FastImage
              style={bannerCrossIcon}
              tintColor={this.context.colors.bannerTextError}
              source={require('../../../../assets/close_window.png')}
            />
          </TouchableOpacity>
        </View>
      </View>
    )
  }

  renderComicBottomBar() {
    const { panelItems, comicFeedEpisodes, isEpisodsAPIResponded, isKeyboardUp, bottomTabAnimation } = this.state
    if (!panelItems || (Utils.isEmptyObject(comicFeedEpisodes) && !isEpisodsAPIResponded)) {
      return null
    }

    return (
      <Animated.View
        style={{ opacity: bottomTabAnimation, position: 'absolute', width: '100%', ...(isKeyboardUp ? {} : { bottom: 0 }) }}>
        <ComicBottomBar hideBottomBar={false} isPostCommentStory={true} commentsDisabled={true} onIconPress={this.onIconPress} onShowCommentPress={this.onShowCommentPress} onLikePress={this.onLikePress} comicData={this.state.panelItems} onBackPress={this.onBackPress} isSubscribed={this.isSubscribedToAlert()} onFollowPress={this.onFollowPress} navigateToStackTop={this.openHomePage} onSeriesHomeClicked={this.onSeriesHomeClicked} comicFeedEpisodes={this.state.comicFeedEpisodes} pathUrl={this.comicURL} openChapter={this.openChapter} recordPageView={this.props.recordPageView} openWebView={this.props.openWebView} />
      </Animated.View>
    )
  }

  render() {
    let userComments = this.state.panelItems && this.state.panelItems.userComments
    if (!userComments) {
      userComments = []
    }
    const { commentPlaceHolder, panelItems, showRefresh, userComment, isKeyboardUp, showNotificationMessage, repliedLabel, showBottomSheet, showShareBottomSheet, showPageRefresh } = this.state

    const shouldHideBanner = !this.comicURL || !panelItems
    const seriesID = panelItems && panelItems.series

    return (
      <KeyboardAvoidingView
        behavior={this.isIOSPlatform ? "padding" : isKeyboardUp ? "padding" : undefined}
        style={{ flex: 1 }}>
        <SafeAreaView style={{ flex: 1 }}>
          {showNotificationMessage && !shouldHideBanner && this.renderFollowSeriesMsg(seriesID)}
          {showNotificationMessage && !shouldHideBanner && this.renderManageAlertMsg(seriesID)}
          {showNotificationMessage && !shouldHideBanner && this.renderUpgradeSubsMsg(seriesID)}
          <View style={styles.mainContainer}>
            <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
              <FlatList
                ListHeaderComponent={this.renderHeader(panelItems)}
                ListFooterComponent={this.renderFooter(panelItems)}
                initialNumToRender={5}
                scrollEventThrottle={16}
                maxToRenderPerBatch={5}
                windowSize={21}
                data={userComments}
                ref={ref => {
                  this.flatlist = ref
                }}
                refreshControl={
                  <RefreshControl
                    refreshing={showRefresh}
                    onRefresh={() => {
                      this.reloadStory()
                    }}
                    progressViewOffset={settings.PROGRESS_VIEW_OFFSET}
                    tintColor={Color.CIRCLE_PROGRESS_BAR_COLOR}
                  />
                }
                renderItem={(item) => this.renderItem(item)}
                extraData={this.state}
                keyExtractor={(item, index) => item.commentId + item.commentText + index}
                keyboardShouldPersistTaps={"never"}
                ListEmptyComponent={this.isCommentsFetched && userComment.length == 0 && this._listEmptyComponent}
                contentContainerStyle={{
                  paddingBottom: isKeyboardUp ? navigationStyle.panelsMargin + navigationStyle.navHeight : 0
                }}
              />
            </TouchableWithoutFeedback>
            <View style={[styles.separatorStyle, { backgroundColor: this.context.colors.separators }]} />
            <View style={[styles.commentInputView(isKeyboardUp), { backgroundColor: this.context.colors.textInverse }]}>
              {isKeyboardUp &&
                <TouchableOpacity onPress={() => Keyboard.dismiss()}>
                  <View style={styles.barContainer}>
                    <View style={[styles.horizontalBar, { backgroundColor: this.context.colors.separators }]} />
                  </View>
                </TouchableOpacity>
              }
              <CommentTextInputComponent
                topLabel={repliedLabel}
                inputRef={(r) => { this.userCommentTextInput = r }}
                refState="userComment"
                autoFocus={false}
                onFocus={() => this.onFocus()}
                onClickTextInput={() => this.onClickTextInput()}
                selectionColor={Color.RED_TEXT_COLOR}
                onChange={(refState, text) => {
                  this.onChange(refState, text)
                }}
                multiline={true}
                fontSize={this.context.p.fontSize}
                maxLength={Constants.COMMENT_LIMIT}
                value={userComment}
                keyValue={commentPlaceHolder}
                placeHolder={commentPlaceHolder}
                placeholderTextColor={Color.DARK_GREY}
                textColor={Color.NORMAL_TEXT_COLOR}
                textInputStyle={styles.commentInput}
                onSendIconPress={() => { this.onSubmitComment() }}
                editable={this.state.canUserComment}
                userDetails={this.props.userDetails}
                navigation={this.props.navigation}
                onReplyCancel={this.onReplyCancel}
                onBackPress={this.onBackPress}
              />
            </View>
          </View>
          {showBottomSheet && this.renderBottomSheet()}
          {showShareBottomSheet && this.renderShareActionSheet()}
          <ActionSheet
            ref={o => this.actionSheet = o}
            title={this.state.sheetTitle}
            message={this.state.sheetMessage}
            options={this.state.sheetOptions}
            cancelButtonIndex={this.state.cancelOptionIndex}
            tintColor={this.context.colors.logoRed}
            onPress={(index) => { this.onActionSheetButtonPress(index) }}
            useNativeDriver={false}
            styles={{ titleText: this.context.h2, messageText: this.context.p, messageBox: { paddingTop: this.state.sheetTitle == "" ? 20 : 0 } }}
          />
          {this.renderComicBottomBar()}
        </SafeAreaView>
        {(showPageRefresh || this.props.isPurchaseInProgress || this.props.friendsActivityInProgress) && <LoadingIndicator />}
      </KeyboardAvoidingView >
    )
  }
}

PostCommentsScreen.contextType = ThemeContext;

const styles = StyleSheet.create({
  commentView: {
    marginLeft: 16,
    marginRight: 16,
  },
  commentInputView: (isKeyboardUp = false) => {
    return {
      marginBottom: (isKeyboardUp) ? settings.isAndroid14OrBelowDevice ? 20 : 0 : navigationStyle.navHeight + (navigationStyle.secondaryBottomNavHeight - SessionManager.instance.getContainerInsets().bottom),
      paddingTop: isKeyboardUp ? 6 : 12,
      paddingBottom: isKeyboardUp ? 0 : 12,
    }
  },
  seperatorStyle: {
    height: 1,
    backgroundColor: Color.BOTTOM_NAVBAR_BORDER_COLOR
  },
  commentInput: {
    borderRadius: scale(10),
    borderWidth: 0.5,
    borderColor: "transparent",
    backgroundColor: Color.VERY_LIGHT_GREY,
    paddingStart: 10,
    paddingEnd: 10,
    paddingTop: 10,
    paddingBottom: 10,
  },
  navigationIcons: {
    marginLeft: 10,
    width: scale(25),
    height: scale(25),
    opacity: 1,
    tintColor: Color.DARK_GREY,
    alignSelf: "center",
  },
  mainContainer: {
    flex: 1,
  },
  prevCommentsTextStyle: {
    marginLeft: 20,
    marginBottom: 4
  },
  separatorStyle: {
    height: 1,
    width: '100%'
  },
  buyButton: {
    height: 25,
    marginLeft: 10,
    marginRight: 8,
    position: 'relative'
  },
  barContainer: {
    justifyContent: 'center',
    alignItems: 'center'
  },
  horizontalBar: {
    marginBottom: 10,
    height: 5,
    borderRadius: 5,
    width: 60,
  }
})

const mapStateToProps = (state) => {
  return {
    userDetails: state.loginInfo.userDetails,
    isPurchaseInProgress: state.purchaseIndicators.isPurchaseInProgress,
    pathUrl: state.readComic.pathUrl,
    alerts: state.userInfo.alerts,
    friendsActivityInProgress: state.friendActivityIndicator.friendsActivityInProgress
  }
}

const mapDispatchToProps = (dispatch) => {
  return {
    postCommentOnFeed(data, callback) {
      dispatch(
        postCommentOnFeed(data, callback)
      )
    },
    deleteComment(data) {
      dispatch(
        deleteComment(data)
      )
    },
    editComment(data) {
      dispatch(
        editComment(data)
      )
    },
    flagUserComment(data, callback) {
      dispatch(
        flagUserComment(data, callback)
      )
    },
    blockUserAction(data, callback) {
      dispatch(
        blockUserAction(data, callback)
      )
    },
    likeDislikeFeed(data, isLike, callback) {
      dispatch(
        likeDislikeFeed(data, isLike, callback)
      )
    },
    likeDislikeComment(data, isLike, callback) {
      dispatch(
        likeDislikeComment(data, isLike, callback)
      )
    },
    share(panelItems, actionType, callback) {
      dispatch(
        shareApp(panelItems, actionType, callback)
      )
    },
    deleteRepostedComic(data, callBack) {
      dispatch(
        deleteRepostedComic(data, callBack)
      )
    },
    deleteSentPost(data, callBack) {
      dispatch(
        deleteSentPost(data, callBack)
      )
    },
    getUserDetails(callback) {
      dispatch(
        getUserDetails(callback)
      )
    },
    getFeedComments(data, callBack) {
      dispatch(
        getFeedComments(data, callBack)
      )
    },
    getStoryDetails(data, callBack) {
      dispatch(
        getStoryDetails(data, callBack)
      )
    },
    requestPurchase(product, feedData, callback) {
      dispatch(
        requestPurchase(product, feedData, callback)
      )
    },
    getFeedLikes(data, callBack) {
      dispatch(
        getFeedLikes(data, callBack)
      )
    },
    recordPageView(url, isRead, storyID) {
      dispatch(
        recordPageView(url, isRead, storyID)
      )
    },
    openWebView(url) {
      dispatch(
        openWebViewAction(url)
      )
    },
    updateShowAllComicsSettings(value) {
      dispatch(
        updateShowAllComicsSettings(value)
      )
    },
    updateAlertSubscription(subscribe, seriesURL) {
      dispatch(
        updateAlertSubscription(subscribe, seriesURL)
      )
    },
    shareImage(item) {
      dispatch(
        shareImage(item)
      )
    },
    getFeedEpisodes(requestedData, callback) {
      dispatch(
        getFeedEpisodes(requestedData, callback)
      )
    },
    getNavigationComics(requestedData, callback) {
      dispatch(
        getNavigationComics(requestedData, callback)
      )
    },
    addNewTransaction(requestedData, feedData, callback) {
      dispatch(
        addNewTransaction(requestedData, feedData, callback)
      )
    }
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(PostCommentsScreen)