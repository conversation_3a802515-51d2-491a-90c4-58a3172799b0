import React, { Component } from 'react'
import { scale } from 'react-native-size-matters'
import { StyleSheet, Image, TouchableOpacity, TouchableWithoutFeedback, Alert } from 'react-native'
import { Text, View } from 'native-base'
import { Color } from '../../../config/Color'
import { SystemFont } from '../../../config/Typography'
import ImagePlaceHolder from '../../ImagePlaceHolder'
import CommentLikeButtonView from '../../CommentLikeButtonView'
import { Constants } from '../../../config/Constants'
import { userProfilePic } from '../../../config/styles'
import { Utils } from '../../../config/Utils'
import ActionSheet from '../../actionSheet/ActionSheetCustom'
import { settings } from '../../../config/settings'
import UserSession from '../../../config/UserSession'
import BadgesView from '../../BadgesView'
import { ThemeContext } from '../../../Contexts'
import FastImage from 'react-native-fast-image'
import ReadMore from '@fawazahmed/react-native-read-more';

export default class CommentsPanel extends Component {

  constructor(props) {
    super(props)

    this.state = {
      showRepliedComments: false,
      options: [Constants.COPY, Constants.EDIT, Constants.DELETE, Constants.CANCEL],
      isCommentImageLoaded: true,
      cancelIndex: 3
    }

    this.currentUserId = Utils.getCurrentUserId()
    this.selectedItem = null

    this.onCountPress = this.onCountPress.bind(this)
    this.setReplyVisibility = this.setReplyVisibility.bind(this)
    this.onShowReplyPress = this.onShowReplyPress.bind(this)
    this.onCommentLongPress = this.onCommentLongPress.bind(this)
    this.showBlockUserConfirmationDialog = this.showBlockUserConfirmationDialog.bind(this)
    this.showReportCommentConfirmationDialog = this.showReportCommentConfirmationDialog.bind(this)
    this.showDeleteCommentConfirmationDialog = this.showDeleteCommentConfirmationDialog.bind(this)
    this.onImageError = this.onImageError.bind(this)
    this.renderLikeButton = this.renderLikeButton.bind(this)
    this.onActionSheetPress = this.onActionSheetPress.bind(this)

    this.isSystemComment = false
    if (this.props.item) {
      this.isSystemComment = (this.props.item.commentType && this.props.item.commentType == "system") ? true : false
    }
  }

  onCountPress(actionName, commentId, subCommentId, storyID) {
    if (!commentId) {
      return
    }
    this.props.onCommentLikeCountPress(actionName, commentId, subCommentId, storyID)
  }

  setReplyVisibility(value) {
    this.setState({ showRepliedComments: value })
  }

  onShowReplyPress(openSubComments) {
    if (this.props.onListedCommentTap && !openSubComments) {
      const { index } = this.props
      this.props.onListedCommentTap(index)
    } else {
      this.setReplyVisibility(true)
    }
  }

  onCommentLongPress(item, userId, isSysComment) {
    if (isSysComment) {
      return
    }

    let actionURL = this.props.panelItems ? this.props.panelItems.action : null
    let comicURL = settings.apiBaseURL + actionURL

    if (this.actionSheet) {
      this.selectedItem = item
      if (userId == this.currentUserId) {
        this.setState({ options: [Constants.COPY, Constants.EDIT, Constants.DELETE, Constants.CANCEL], cancelIndex: 3 }, () => {
          this.actionSheet.show()
        })
      } else if (Utils.checkIsTinyviewAdminUser(comicURL)) {
        this.setState({ options: [Constants.COPY, Constants.DELETE, Constants.REPORT_COMMENT, Constants.BLOCK_USER, Constants.CANCEL], cancelIndex: 4 }, () => {
          this.actionSheet.show()
        })
      } else if (this.props.panelItems && this.props.panelItems.refType == Constants.UPPERCASE_STORY && Utils.checkIsSeriesAdminUser(comicURL)) {
        this.setState({ options: [Constants.COPY, Constants.DELETE, Constants.REPORT_COMMENT, Constants.CANCEL], cancelIndex: 3 }, () => {
          this.actionSheet.show()
        })
      } else if (UserSession.instance.hasPrevilage() && !UserSession.instance.isUserBlocked()) {
        this.setState({ options: [Constants.COPY, Constants.REPORT_COMMENT, Constants.CANCEL], cancelIndex: 2 }, () => {
          this.actionSheet.show()
        })
      } else {
        this.setState({ options: [Constants.COPY, Constants.CANCEL], cancelIndex: 1 }, () => {
          this.actionSheet.show()
        })
      }
    }
  }

  showBlockUserConfirmationDialog() {
    Alert.alert("Please Confirm", "Do you really want to Block this user?",
      [{
        text: "Yes", onPress: () => {
          this.props.onBlockUserTap(this.selectedItem)
        }
      }, { text: "No" }])
  }

  showReportCommentConfirmationDialog() {
    Alert.alert("Please Confirm", "Are you sure you want to report this comment?",
      [{
        text: "Yes", onPress: () => {
          if (!this.selectedItem.commentId) { // Adding the commentId
            this.selectedItem.commentId = this.props.item.commentId
          }
          this.props.onFlagCommentTap(this.selectedItem)
        }
      }, { text: "No" }])
  }

  showDeleteCommentConfirmationDialog(selectedItem) {
    Alert.alert(
      "Please Confirm",
      "Are you sure you want to delete this comment?",
      [{
        text: "No"
      },
      {
        text: "Yes",
        onPress: () => {
          this.props.deleteComment(selectedItem)
        },
      }
      ]
    )
  }

  onImageError() {
    this.setState({ isCommentImageLoaded: false })
  }

  renderProfileImage(item, size = 32) {
    const { name, image } = item.user && item.user
    return (
      <TouchableOpacity style={styles.profilePicView} onPress={() => { this.props.navigateToUserProfile(item) }} >
        {(Utils.checkData(image) && this.state.isCommentImageLoaded)
          ?
          <Image source={{ uri: image }} resizeMode="cover" style={[userProfilePic(size), { borderColor: this.context.colors.chatBubbles }]} onError={this.onImageError} />
          : Utils.checkData(name)
            ?
            <ImagePlaceHolder
              backgroundColor={Color.PROFILE_PLACE_HOLDER_BG}
              showCircularBorder={true}
              insidetextStyle={this.context.bodyMini}
              size={size}
              type={'circle'}>{name}</ImagePlaceHolder>
            : null
        }
      </TouchableOpacity>
    )
  }

  renderNameAndComments(mainItem, name, commentText, userId, isSysComment = false) {
    return (
      <View style={[styles.commentView, { backgroundColor: isSysComment ? this.context.colors.bannerBackgroundSuccess : this.context.colors.chatBubbles }]}>
        <TouchableWithoutFeedback onPress={this.props.onListedCommentTap} onLongPress={() => this.onCommentLongPress(mainItem.item, userId, isSysComment)}>
          <View style={{ flex: 1 }}>
            {!isSysComment &&
              <View style={{ flexDirection: 'row', marginRight: 12 }}>
                <Text style={this.context.pBold}>{name ? name.trim() : name}</Text>
                {<BadgesView badges={mainItem.item.user.badges} />}
              </View>
            }
            <ReadMore
              numberOfLines={4}
              style={[this.context.p, { color: this.context.colors.textBold, marginTop: isSysComment ? 0 : 4 }]}
              seeMoreStyle={[this.context.bodyMiniBold, { color: this.context.colors.text, alignSelf: 'flex-end' }]}
              seeMoreText={Constants.VIEW_MORE}
              expandOnly={true}
              ellipsis='...'>
              {commentText}
            </ReadMore>
          </View>
        </TouchableWithoutFeedback>
      </View>
    )
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (Utils.checkObject(nextProps.shouldSubCommentVisible) && nextProps.shouldSubCommentVisible != this.props.shouldSubCommentVisible) {
      this.setState({ showRepliedComments: true })
    }

    if (this.props.item && nextProps.item && (this.props.item.commentType != nextProps.item.commentType)) {
      this.isSystemComment = (nextProps.item.commentType && nextProps.item.commentType == "system") ? true : false
    }
  }

  onActionSheetPress(index) {
    if (!this.selectedItem || this.state.cancelIndex == index) { return }
    if (index == 0) {
      Utils.copyToClipboard(this.selectedItem.commentText)
    } else if (index == 1) {
      if (this.state.options[index] == Constants.EDIT) {
        this.props.onEditPress(this.selectedItem)
      } else if (this.state.options[index] == Constants.DELETE) {
        this.showDeleteCommentConfirmationDialog(this.selectedItem)
      } else if (this.state.options[index] == Constants.REPORT_COMMENT) {
        this.showReportCommentConfirmationDialog()
      }
    } else if (index == 2) {
      if (this.state.options[index] == Constants.DELETE) {
        this.showDeleteCommentConfirmationDialog(this.selectedItem)
      } else if (this.state.options[index] == Constants.REPORT_COMMENT) {
        this.showReportCommentConfirmationDialog()
      }
    } else if (index == 3) {
      if (this.state.options[index] == Constants.BLOCK_USER) {
        this.showBlockUserConfirmationDialog()
      } else { }
    }
  }

  renderComments(item) {
    if (!item || !item.commentText) {
      return null;
    }

    const { commentText } = item
    const { name, userId } = item.user
    const { showRepliedComments } = this.state
    const repliesCountMsg = Utils.getReplyMsgForCount(item.subComments)
    const subCommentsCount = item.subComments ? item.subComments.length : 0
    const openSubComments = subCommentsCount <= 2

    return (
      <View style={{ flexDirection: 'row', marginTop: scale(12) }}>
        {this.renderProfileImage(item)}
        {!this.isSystemComment &&
          <FastImage
            source={require("../../../../assets/beak.png")}
            style={styles.beakIcon}
          />
        }

        <View style={[styles.commentTextView, { marginLeft: this.isSystemComment ? 10 : 0 }]}>
          {this.renderNameAndComments({ item: item }, name, commentText, userId, this.isSystemComment)}
          {this.renderCommentsOptions({ item: item }, false, null, this.isSystemComment)}
          {(item.subComments && item.subComments.length > 0) &&
            (showRepliedComments
              ?
              <>
                <TouchableOpacity style={{ marginLeft: scale(12), marginTop: 4 }} onPress={() => { this.setReplyVisibility(false) }} >
                  <Text style={this.context.bodyMini}>{item.subComments.length == 1 ? "Hide Reply" : "Hide Replies"} </Text>
                </TouchableOpacity>
                {this.renderRepliedComments(item)}
              </>
              : <TouchableOpacity style={{ marginLeft: scale(12), marginTop: 4 }} onPress={() => { this.onShowReplyPress(openSubComments) }} >
                <Text style={this.context.bodyMini}>{repliesCountMsg}</Text>
              </TouchableOpacity>
            )
          }
        </View>
      </View >
    )
  }

  renderLikeButton(item, subCommentIndex) {
    let { storyID } = this.props.panelItems
    let { isLiked = false, reactionsCount = 0 } = item
    let commentId = this.props.item.commentId
    let subCommentId = null
    if (Utils.checkData(item.subCommentId)) {
      subCommentId = item.subCommentId
    }

    return (
      <CommentLikeButtonView
        isComicURL={true}
        isFollowing={isLiked}
        pageLikeCount={reactionsCount}
        showCountSuffix={reactionsCount > 0 ? true : false}
        containerStyles={styles.likeButtonContainer}
        iconStyle={styles.navigationIcons}
        onCountPress={() => this.onCountPress(Constants.LIKE, commentId, subCommentId, storyID)}
        onPress={() => { this.onCountPress(Constants.LIKE, commentId, subCommentId, storyID) }}
      />
    )
  }

  renderCommentsOptions(commentItems, renderingForReply = false, subCommentIndex = null, isSysComment = false) {
    let { isLiked = false, createdAt, user } = commentItems.item
    const { index } = this.props

    let timeAgeText = Utils.convertTimeSpanForComments(createdAt)

    return (
      <View style={{ flexDirection: 'row' }}>
        <View style={styles.bottomViewContainer}>
          <Text style={[this.context.bodyMini, { marginEnd: scale(16) }]}>{timeAgeText}</Text>
          <View style={styles.bottomView}>
            <TouchableOpacity onPress={() => { this.props.likeDislikeComment(this.props.item, isLiked, subCommentIndex) }} >
              <Text style={[this.context.bodyMini, styles.bottomTextView]} >{isLiked ? Constants.LIKED : Constants.LIKE}</Text>
            </TouchableOpacity>
          </View>
          {(!renderingForReply)  //&& this.props.currentUserId != user.userId)
            &&
            <View style={styles.bottomView}>
              <TouchableOpacity onPress={() => { this.props.onReplyPress(commentItems.item, index) }} >
                <Text style={[this.context.bodyMini, styles.bottomTextView]}>{Constants.REPLY}</Text>
              </TouchableOpacity>
            </View>
          }
          {!isSysComment &&
            <View style={styles.bottomView}>
              <TouchableOpacity onPress={() => { this.onCommentLongPress(commentItems.item, commentItems.item.user.userId, isSysComment) }} >
                <Text style={[this.context.bodyMini, { fontSize: 25, fontWeight: '800' }]}>...</Text>
              </TouchableOpacity>
            </View>
          }

        </View>

        {commentItems.item.reactionsCount > 0 &&
          <View style={styles.heartContainer}>
            {this.renderLikeButton(commentItems.item, subCommentIndex)}
          </View>
        }

      </View >
    )
  }

  renderRepliedComments(mainItem) {
    return (
      <View style={{ marginLeft: scale(5) }}>
        {mainItem && mainItem.subComments && mainItem.subComments.map((subItem, index) => {
          return (
            <View onLayout={event => {
              const layout = event.nativeEvent.layout;
              subItem.positionY = layout.y
              subItem.itemHeight = layout.height
            }} style={{ flexDirection: 'row', marginTop: scale(15) }}>
              {this.renderProfileImage(subItem, 32)}
              <FastImage
                source={require("../../../../assets/beak.png")}
                style={styles.beakIcon}
              />
              <View style={[styles.commentTextView]}>
                {this.renderNameAndComments({ item: subItem }, subItem.user.name, subItem.commentText, subItem.user.userId)}
                {this.renderCommentsOptions({ item: subItem }, false, index)}
              </View>
            </View >
          )
        })}
      </View>
    )
  }

  render() {
    const { item, index } = this.props
    return (
      <View style={styles.mainContainer}>
        {this.renderComments(item)}
        <ActionSheet
          ref={o => this.actionSheet = o}
          options={this.state.options}
          cancelButtonIndex={this.state.cancelIndex}
          tintColor={this.context.colors.logoRed}
          useNativeDriver={false}
          styles={{ titleText: this.context.h2, messageText: this.context.p, messageBox: { paddingTop: 20 } }}
          onPress={this.onActionSheetPress}
        />
      </View>
    )
  }
}

CommentsPanel.contextType = ThemeContext;

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  commentView: {
    flex: 1,
    flexDirection: 'row',
    borderRadius: 12,
    borderColor: 'transparent',
    padding: 10
  },
  trippleDotIcon: {
    width: scale(16),
    height: scale(16),
    alignSelf: 'center',
    tintColor: Color.DATE_TIME_COLOR
  },
  commentTextView: {
    flex: 1,
    flexDirection: 'column',
    alignContent: 'center',
  },
  bottomViewContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginTop: 2,
    marginLeft: 12
  },
  profileView: {
    flex: 0.1,
    alignContent: 'center',
    justifyContent: 'flex-start',
  },
  bottomView: {
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16
  },
  heartContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomTextView: {
    paddingLeft: scale(5),
    paddingRight: scale(5)
  },
  navigationIcons: {
    width: scale(10),
    height: scale(10)
  },
  headerText: {
    alignSelf: 'center',
    marginStart: scale(3)
  },
  likeButtonContainer: {
    alignSelf: 'center',
    alignItems: 'flex-end',
  },
  beakIcon: {
    height: 20,
    width: 10,
    marginTop: 10
  },
  profilePicView: {
    marginRight: 2
  }
});
