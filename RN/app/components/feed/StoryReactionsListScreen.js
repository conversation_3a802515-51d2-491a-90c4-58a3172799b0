import React, { Component } from 'react'
import { View, FlatList, BackHandler, SafeAreaView } from 'react-native'
import { CommonActions } from '@react-navigation/native';
import UserListPanel from './UserListPanel'
import { Constants } from '../../config/Constants';
import { connect } from 'react-redux';
import { fetchFeedLikedUserList, fetchCommentLikedUserList, fetchRepostedUserList, fetchWithWhomStorySharedUL, sendFriendRequest, getFriendList } from '../../redux/actions/actions'
import { LoadingIndicator } from '../LoadingIndicator';
import { Utils } from '../../config/Utils';
import GiftIcons from '../reactionUI/GiftIcons';
import SessionManager from '../../config/SessionManager';

class StoryReactionsListScreen extends Component {

  constructor(props) {
    super(props)

    const allGifts = SessionManager.instance.getGiftItemList()
    const { storyID, refType = null, panelItems, selectedTab } = this.props.route.params

    this.storyID = storyID
    this.refType = refType
    this.panelItems = panelItems

    let selTab = ''

    if (this.panelItems && !selectedTab && !Utils.isEmptyObject(this.panelItems.giftsCount) && allGifts.length > 0) {
      let giftsLength = allGifts.length
      for (var i = 1; i <= allGifts.length; i++) {
        if (Object.keys(this.panelItems.giftsCount).includes(allGifts[giftsLength - i].reactionName) && this.panelItems.giftsCount[allGifts[giftsLength - i].reactionName] > 0) {
          selTab = allGifts[giftsLength - i].reactionName
          break
        }
      }
    } else {
      selTab = selectedTab ? selectedTab : Constants.HEART
    }

    this.state = {
      filteredList: [],
      showLoadingIndicator: false,
      gifts: allGifts,
      selectedTab: selTab,
    }

    this.hasMoreData = true
    this.isAPIInProgress = false

    this.onBackPress = this.onBackPress.bind(this)
    this.renderItem = this.renderItem.bind(this)
    this._listEmptyComponent = this._listEmptyComponent.bind(this)
    this.fetchUserListAPI = this.fetchUserListAPI.bind(this)
    this.reloadUserList = this.reloadUserList.bind(this)
    this.navigateToUserProfile = this.navigateToUserProfile.bind(this)
    this.setLoaderVisibility = this.setLoaderVisibility.bind(this)
    this.updateLikedUserList = this.updateLikedUserList.bind(this)
    this.sendFriendRequest = this.sendFriendRequest.bind(this)
    this.getLastLikeID = this.getLastLikeID.bind(this)
    this.onGiftIconPress = this.onGiftIconPress.bind(this)
    this.renderGiftTab = this.renderGiftTab.bind(this)
  }

  UNSAFE_componentWillMount() {
    BackHandler.addEventListener('hardwareBackPress', this.onBackPress);
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.onBackPress);
  }

  onBackPress() {
    this.props.navigation.dispatch(CommonActions.goBack())
    return true;
  }

  componentDidMount() {
    this.props.navigation.setParams({
      leftButtonText: "Back",
      onBackPress: this.onBackPress,
    })

    this.reloadUserList()
  }

  // API Calls
  fetchUserListAPI() {
    if (!this.hasMoreData || this.isAPIInProgress) {
      return
    }

    this.setLoaderVisibility(true)
    this.isAPIInProgress = true

    let innerData = {
      storyID: this.storyID,
      records: Constants.FEED_USER_LIST_PAGINATION_COUNT,
      startAfter: this.getLastLikeID(),
      reactionType: this.state.selectedTab
    }
    let data = {
      data: innerData
    }

    this.props.fetchFeedLikedUserList(data, this.updateLikedUserList)
  }

  getLastLikeID() {
    if (this.state.filteredList.length > 0) {
      return this.state.filteredList[this.state.filteredList.length - 1].likeID ? this.state.filteredList[this.state.filteredList.length - 1].likeID : ""
    }
    return ""
  }

  updateLikedUserList(data) {
    if (!data || data.likesList.length < Constants.FEED_USER_LIST_PAGINATION_COUNT) {
      this.hasMoreData = false
    }

    if (data) {
      if (data.likesList && data.likesList.length > 0 && Array.isArray(data.likesList)) {
        let dataList = [...this.state.filteredList, ...data.likesList]
        this.setState({ filteredList: dataList }, () => {
          this.isAPIInProgress = false
        })
      } else {
        this.isAPIInProgress = false
      }

    } else {
      this.isAPIInProgress = false
    }

    this.setLoaderVisibility(false)
  }

  onGiftIconPress(reactionName) {
    this.setState({ selectedTab: reactionName })
    this.hasMoreData = true
    if (reactionName == this.state.selectedTab) {
      return
    }

    this.setState({ filteredList: [] }, () => {
      this.fetchUserListAPI()
    })
  }

  navigateToUserProfile(item) {
    let userId = item.user.userId ? item.user.userId : null
    let userName = item.user.name ? item.user.name : null

    if (userName && userName.toLowerCase() == Constants.NON_SIGNED_IN_USER.toLowerCase() || !userId) {
      return
    }

    var subRoutesParams = { clickedUserID: userId }
    Utils.navigateToSubRouteWithParams(Constants.DRAWER_HOME, Constants.USER_PROFILE_SCREEN, this.props, subRoutesParams)
  }

  sendFriendRequest(item) {
  }

  reloadUserList() {
    this.hasMoreData = true
    this.fetchUserListAPI()
  }

  setLoaderVisibility(value) {
    this.setState({ showLoadingIndicator: value })
  }

  renderItem(item) {
    return (
      <UserListPanel item={item} {...this.props} sendFriendRequest={this.sendFriendRequest} navigateToUserProfile={this.navigateToUserProfile} />
    )
  }

  renderGiftTab() {
    return (
      <View style={{ flex: 0.09 }}>
        <GiftIcons selectedTab={this.state.selectedTab} showWithTabs={true} onGiftIconPress={this.onGiftIconPress} {...this.props} isEnable={false} panelItems={this.panelItems} />
      </View>
    )
  }

  _listEmptyComponent() {
    return null
  }

  render() {
    return (
      <SafeAreaView style={{ flex: 1 }}>
        {this.renderGiftTab()}
        <View style={{ flex: .9 }}>
          <FlatList
            data={this.state.filteredList}
            renderItem={(item) => this.renderItem(item)}
            keyExtractor={(item, index) => '' + index}
            onEndReached={() => this.fetchUserListAPI()}
            onEndReachedThreshold={0.1}
          />
        </View>
        {this.state.showLoadingIndicator && <LoadingIndicator />}
      </SafeAreaView>
    )
  }
}

const mapStateToProps = (state) => {
  return {
    userDetails: state.loginInfo.userDetails,
  }
}

const mapDispatchToProps = (dispatch) => {
  return {
    fetchFeedLikedUserList(data, callBack) {
      dispatch(
        fetchFeedLikedUserList(data, callBack)
      )
    },
    fetchCommentLikedUserList(data, callBack) {
      dispatch(
        fetchCommentLikedUserList(data, callBack)
      )
    },
    fetchRepostedUserList(data, callBack) {
      dispatch(
        fetchRepostedUserList(data, callBack)
      )
    },
    fetchWithWhomStorySharedUL(data, callBack) {
      dispatch(
        fetchWithWhomStorySharedUL(data, callBack)
      )
    },
    sendFriendRequest(sendFriendRequestParams, sendTextToContactsParams, callback) {
      dispatch(
        sendFriendRequest(sendFriendRequestParams, sendTextToContactsParams, callback)
      )
    },
    getFriendList(callback, forceLoad) {
      dispatch(
        getFriendList(callback, forceLoad)
      )
    },
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(StoryReactionsListScreen)

