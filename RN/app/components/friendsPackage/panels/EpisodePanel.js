import React from 'react'
import {
  StyleSheet,
  FlatList,
  Text,
  TouchableOpacity,
  Dimensions
} from 'react-native'
import { View } from 'native-base';
import { navigationStyle } from '../../../config/styles';
import { ThemeContext } from '../../../Contexts';
import { Utils } from '../../../config/Utils';
import EpisodeCard from './EpisodeCard';
import SelectableText from '../../../config/SelectableText';
import Panel from '../../ComicPanel/Panel';
import HTML from 'react-native-render-html';
import { Constants } from '../../../config/Constants';
import { settings } from '../../../config/settings';

let dimensions = Dimensions.get('window')
export default class EpisodePanel extends Panel {

  constructor(props) {
    super(props)

    const { item } = this.props.item
    let episodesList = []
    if (item && item.carouselType == "list" && item.list) {
      episodesList = item.list
    }

    this.state = {
      episodePanels: episodesList
    }

    this.isEpisodesCarousel = item?.carouselType == "episodes"
    let aspectRatio = this.isEpisodesCarousel ? (dimensions.width - ((2) * 2) / 220) / 1.8 : (dimensions.width - ((2) * 2) / 128) / 2.5  // Change width and margin here, if imageWidth changes.
    this.panelWidth = aspectRatio + 12 // marginLeft = 12. So, panelWidth = aspectRatio + left margin
    this.allSeriesData = []

    this.currentComicIndex = 0
    this.episodLeftMargin = 2
    this.isFlatListIntialized = false
    this.renderItems = this.renderItems.bind(this)
    this.renderTextPanel = this.renderTextPanel.bind(this)
    this.renderButton = this.renderButton.bind(this)
    this.onEpisodesFetched = this.onEpisodesFetched.bind(this)
    this.navigateToAppropriateScreen = this.navigateToAppropriateScreen.bind(this)
    this.scrollToCurrentComic = this.scrollToCurrentComic.bind(this)
    this.getItemLayout = this.getItemLayout.bind(this)
  }

  componentDidMount() {
    const { item } = this.props.item

    let requestedData = {}
    if (item.carouselType === Constants.CONTINUE_READING) {
      this.props.getSeriesCarousel(requestedData, this.onEpisodesFetched)
    } else if (item.carouselType === Constants.OTHER_SERIES) {
      requestedData = { type: "series" }
      this.props.getSeriesCarousel(requestedData, this.onEpisodesFetched)
    }
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps) {
      const { item } = nextProps.item
      if (item && item.carouselType == "episodes" && nextProps.comicFeedEpisodes && nextProps.comicFeedEpisodes.episodes && this.state.episodePanels != nextProps.comicFeedEpisodes.episodes) {
        this.onEpisodesFetched(nextProps.comicFeedEpisodes.episodes)
      } else if (item && item.carouselType == Constants.CONTINUE_READING && JSON.stringify(this.state.episodePanels) !== JSON.stringify(nextProps.continueReadPanelEpisodes)) {
        this.onEpisodesFetched(nextProps.continueReadPanelEpisodes)
      } else if (item && item.carouselType == Constants.OTHER_SERIES && JSON.stringify(this.props.alerts) !== JSON.stringify(nextProps.alerts)) {
        this.onEpisodesFetched(this.allSeriesData, nextProps.alerts)
      }
    }
  }

  onEpisodesFetched(response, updatedAlerts = null) {
    if (!response) {
      return
    }

    const { item } = this.props.item
    let validPanels = response
    if (item.carouselType === Constants.OTHER_SERIES) {
      this.allSeriesData = response
      let currAlerts = updatedAlerts ? updatedAlerts : this.props.alerts
      validPanels = response.filter(panels => {
        const seriesName = panels.series;
        return !currAlerts.hasOwnProperty(seriesName)
      });
    } else if (item.carouselType === "episodes") {
      const currentStoryID = this.props.currentPageStatus ? this.props.currentPageStatus.storyID : null
      for (const episodeIndex in validPanels) {
        const element = validPanels[episodeIndex]
        if (element.id === currentStoryID) {
          this.currentComicIndex = episodeIndex
          element.shouldFaded = true
          break
        }
      }
    }

    const maxPanels = item.showMax ? item.showMax : -1
    const visiblePanels = maxPanels !== -1 ? validPanels.slice(0, maxPanels) : validPanels;

    this.setState({ episodePanels: visiblePanels })
  }

  shouldUpdated(nextProps, nextState) { // Restricting rerendering of rendered component
    return true
  }

  getItemLayout(data, index) {
    return {
      length: this.panelWidth,
      offset: this.panelWidth * index,
      index
    }
  }

  scrollToCurrentComic() {
    const { episodePanels } = this.state
    if (this.flatlist && episodePanels.length > 0) {
      this.isFlatListIntialized = true
      this.flatlist.scrollToIndex({ index: this.currentComicIndex, viewPosition: this.isEpisodesCarousel ? 1 : 0.55 })
    } else {
      this.isFlatListIntialized = false
    }
  }

  navigateToAppropriateScreen(item) {
    if (item.button === Constants.ALL_SERIES) {
      this.props.openChapter(settings.DIRECTORY_COMIC_END_POINT_URL)
    } else if (item.button === "All Episodes") {
      this.props.onSeriesHomeClicked()
    }
  }

  renderButton(item) {
    if (!item.button) {
      return
    }

    return (
      <TouchableOpacity
        style={[styles.buttonView, { borderColor: this.context.colors.text }]}
        onPress={() => this.navigateToAppropriateScreen(item)}>
        <Text style={[this.context.bodyMini, styles.buttonTextView, { color: this.context.colors.textBold }]}>{item.button}</Text>
      </TouchableOpacity>
    )
  }

  renderTextPanel(item) {
    if (!item.title) {
      return
    }

    return (
      <View style={styles.mainTextView}>
        <View style={styles.headingView}>
          <SelectableText textValue={item.title} textStyle={[this.context.h2, { paddingTop: 0 }]} multiline={true} />
          {item.button && this.renderButton(item)}
        </View>
        {(Utils.checkData(item.description)) &&
          <View style={{ marginTop: 8 }}>
            <HTML contentWidth={dimensions.width} textSelectable={true} allowFontScaling baseStyle={this.context.p} source={{ html: item.description }} tagsStyles={{ "b": this.context.pBold, "p": { paddingTop: 0, marginBottom: 0, marginTop: 0 } }} />
          </View>
        }
      </View>
    )
  }

  renderItems(item) {
    const isCurrentEpisode = item.index == this.currentComicIndex

    return (
      <EpisodeCard item={item} shareImage={this.props.shareImage} pathUrl={this.props.pathUrl} onPanelTap={this.onPanelTap} index={item.index} isCurrentEpisode={isCurrentEpisode} isEpisodesCarousel={this.isEpisodesCarousel} />
    )
  }

  render() {
    const { item, index } = this.props.item
    const { episodePanels } = this.state

    if (!episodePanels || episodePanels.length === 0) {
      return null
    }

    return (
      <View style={{ backgroundColor: item.bgColor }}>
        <View style={styles.mainEpisodeView}>
          {this.renderTextPanel(item)}
          <FlatList
            ref={ref => {
              this.flatlist = ref

              setTimeout(() => {
                if (!this.isFlatListIntialized) {
                  this.scrollToCurrentComic()
                }
              }, 2000)
            }}
            data={episodePanels}
            horizontal={true}
            showsHorizontalScrollIndicator={false}
            renderItem={(item) => this.renderItems(item)}
            keyExtractor={(item, id) => '' + id}
            getItemLayout={this.getItemLayout}
          />
        </View>
      </View>
    )
  }
}

EpisodePanel.contextType = ThemeContext;

const styles = StyleSheet.create({
  mainEpisodeView: {
    marginTop: navigationStyle.panelsMargin,
    marginBottom: navigationStyle.panelsMargin
  },
  buttonView: {
    borderWidth: 1,
    borderRadius: 6
  },
  headingView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  mainTextView: {
    marginLeft: navigationStyle.panelLeftRightMargin,
    marginRight: navigationStyle.panelLeftRightMargin
  },
  buttonTextView: {
    paddingTop: 4,
    paddingBottom: 4,
    paddingLeft: 8,
    paddingRight: 8
  }
})