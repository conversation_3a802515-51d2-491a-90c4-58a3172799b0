import React, { Component } from 'react';
import { StyleSheet, Image, TouchableOpacity } from 'react-native'
import { scale } from 'react-native-size-matters';
import { Color } from '../../../config/Color';
import { Text, View } from 'native-base';
import ImagePlaceHolder from '../../ImagePlaceHolder'
import { userProfilePic } from '../../../config/styles';
import { Utils } from '../../../config/Utils';
import { Constants } from '../../../config/Constants';
import FastImage from 'react-native-fast-image';
import { ThemeContext } from '../../../Contexts';

export default class InviteFriendsPanel extends Component {

  constructor(props) {
    super(props)

    this.onPanelTap = this.onPanelTap.bind(this)
    this.renderRightIcon = this.renderRightIcon.bind(this)
    this.isDisable = this.isDisable.bind(this)
    this.renderProfileImage = this.renderProfileImage.bind(this)
  }

  onPanelTap(item, shouldRemove = true) {
    if (!this.props.isForRepost && !item.status) {
      Utils.showToast("Please wait! We are fetching your data.")
      return;
    }
    this.props.tapOnContactRow(item, shouldRemove)
  }

  shouldComponentUpdate(nextProps) {
    const { item } = this.props
    return this.props.searchChar !== nextProps.searchChar || item.isSelected !== nextProps.isSelected || item.status !== nextProps.item.status
  }

  renderRightIcon(item) {
    let isSelected = item.isSelected ? item.isSelected : false
    if (item.status == null || item.status == undefined || item.status == Constants.AVAILABLE || this.props.isForRepost) {
      return (
        <View style={{ flex: 0.1 }}>
          {isSelected ?
            <Image style={[styles.navigationIcons, { tintColor: Color.RED_BACKGROUND_COLOR }]} source={require('../../../../assets/checked_radio.png')} />
            :
            <Image style={[styles.navigationIcons, { tintColor: Color.LIGHT_GREY }]} source={require('../../../../assets/uncheck_radio.png')} />
          }
        </View>
      )
    } else if (item.status == Constants.ACCEPTED || item.status == Constants.FRIENDS) {
      return (
        <View style={{ flex: 0.1 }}>
          <Image style={[styles.navigationIcons, { tintColor: Color.RED_BACKGROUND_COLOR }]} source={require('../../../../assets/checked_radio.png')} />
        </View>
      )
    } else if (item.status == Constants.PENDING) {
      return (
        <View style={{ flex: 0.1 }}>
          <Image style={[styles.navigationIcons, { tintColor: Color.RED_BACKGROUND_COLOR }]} source={require('../../../../assets/pending_request_icon.png')} />
        </View>
      )
    } else {
      return null
    }
  }

  isDisable(item) {
    if (item.status == null || item.status == undefined || this.props.isForRepost) {
      return false
    } else if (item.status == Constants.ACCEPTED) {
      return true
    } else if (item.status == Constants.PENDING) {
      return true
    } else if (item.status == Constants.FRIENDS) {
      return true
    }
  }

  renderProfileImage(size = 35) {
    const { item } = this.props
    const fullName = item.getFullName()
    const isfirstNameNotANumber = Utils.checkData(item.firstName) && isNaN(item.firstName.charAt(0)) && item.firstName.charAt(0) != '+'
    const islastNameNotANumber = Utils.checkData(item.lastName) && isNaN(item.lastName.charAt(0))

    return (
      Utils.checkData(item.profilePic)
        ?
        <Image source={{ uri: item.profilePic }} resizeMode={'cover'} style={[userProfilePic(size), { borderColor: this.context.colors.chatBubbles }]} />
        : Utils.checkData(fullName) && (isfirstNameNotANumber || islastNameNotANumber || this.props.isForRepost)
          ?
          <ImagePlaceHolder
            backgroundColor={item.profileBGColor}
            textColor={'#FFF'}
            size={scale(size)}
            type={'circle'}>{fullName}</ImagePlaceHolder>
          :
          <View
            style={styles.emptyImageView(item.profileBGColor, size)}>
            <FastImage
              style={styles.imageInnerView(size)}
              tintColor={this.context.colors.textInverse}
              source={require('../../../../assets/white_user_icon.png')}
            />
          </View>
    )
  }

  render() {
    const { item } = this.props
    const fullName = item.getFullName();
    const isIConClickDisable = (item.uid && item.uid != null) ? false : true
    const phoneNumber = item.primaryContact ? item.primaryContact : item.userPhoneNumber
    return (
      <View style={styles.mainContainer}>
        <View style={styles.listContainer(this.isDisable(item))}>
          <TouchableOpacity
            style={styles.profileButtonView}
            onPress={() => this.props.navigateToUserProfile(item)}
            disabled={isIConClickDisable}
            transparent>
            {this.renderProfileImage()}
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.userNameView}
            onPress={() => this.onPanelTap(item)}
            disabled={this.isDisable(item)}
          >
            <View style={{ flex: 1, flexDirection: 'row' }}>
              <View style={{ flexDirection: 'column', flex: 0.85 }}>
                <Text numberOfLines={1} style={[this.context.pBold, styles.userText]}>{fullName}</Text>
                <Text numberOfLines={1} style={[this.context.p, styles.userText]}>{phoneNumber} {(item.contactType != null && item.contactType != undefined) && <Text style={[this.context.p, styles.userText]}>({item.contactType})</Text>}</Text>
              </View>
              <View style={{ justifyContent: 'center', flex: 0.15 }}>
                {this.renderRightIcon(item)}
              </View>
            </View>
          </TouchableOpacity>
        </View>
      </View >
    )
  }
}

InviteFriendsPanel.contextType = ThemeContext;

const styles = StyleSheet.create({
  mainContainer: {
    height: scale(50),
    flexDirection: 'column',
    marginLeft: scale(10),
    justifyContent: 'center',
    alignItems: 'center'
  },
  profileButtonView: {
    flex: 0.2,
    alignContent: 'center',
    justifyContent: 'center'
  },
  listContainer: (isDisable) => {
    return {
      flexDirection: 'row',
      alignItems: 'center',
      opacity: isDisable ? 0.5 : 1
    }
  },
  userNameView: {
    flex: 0.8,
  },
  navigationIcons: {
    alignSelf: 'flex-end',
    width: scale(20),
    height: scale(20),
  },
  checkBox: {
    alignSelf: 'center',
    width: scale(20),
    height: scale(20),
    opacity: 1,
  },
  userText: {
    width: '100%',
    textAlign: 'left',
  },
  emptyImageView: (profileBGColor, size) => {
    return {
      width: scale(size),
      height: scale(size),
      overflow: 'hidden',
      borderRadius: scale(size) / 2,
      backgroundColor: profileBGColor,
      justifyContent: 'center'
    }
  },
  imageInnerView: (size) => {
    return {
      height: scale(size / 2),
      width: scale(size / 2),
      alignSelf: 'center'
    }
  }
})