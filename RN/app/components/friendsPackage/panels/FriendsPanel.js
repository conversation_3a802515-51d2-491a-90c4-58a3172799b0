import React, { Component } from 'react';
import { StyleSheet, Alert, TouchableOpacity } from 'react-native'
import { Color } from '../../../config/Color';
import { Text, View, Button } from 'native-base';
import ImagePlaceHolder from '../../ImagePlaceHolder'
import { settings } from '../../../config/settings';
import { Constants } from '../../../config/Constants';
import { Utils } from '../../../config/Utils';
import { userProfilePic } from '../../../config/styles';
import BadgesView from '../../BadgesView';
import { ThemeContext } from '../../../Contexts';
import FastImage from 'react-native-fast-image';
import DeepLinkManager from '../../../config/DeepLinkManager';
import UserSession from '../../../config/UserSession';

export default class FriendsPanel extends Component {

  constructor(props) {
    super(props)

    this.state = {
      errorInImageLoading: false
    }

    this.updateFriendRequest = this.updateFriendRequest.bind(this)
    this.unfriend = this.unfriend.bind(this)
    this.showAlert = this.showAlert.bind(this)
    this.renderProfileImage = this.renderProfileImage.bind(this)
    this.onImageError = this.onImageError.bind(this)
    this.resendFriendRequest = this.resendFriendRequest.bind(this)
  }

  updateFriendRequest(item, requestType) {
    if (!UserSession.instance.isLoggedInUser()) {
      this.props.signInSheet()
      return
    }

    let status = null
    if (requestType == Constants.ACCEPT) {
      status = 'accepted'
    } else if (requestType == Constants.REJECT) {
      status = 'rejected'
    } else if (requestType == Constants.DELETE) {
      status = 'delete'
    } else if (requestType == Constants.RESEND) {
      return this.resendFriendRequest()
    } else if (requestType == Constants.ADD_FRIEND_TITLE_CASE) {
      this.props.sendTextToContacts(item)
      return
    }

    let requestId = item.friendshipStatus && item.friendshipStatus.requestDocID ? item.friendshipStatus.requestDocID : item.docId
    let data = { "data": { "requestID": requestId, "status": status } }
    this.props.updateFriendRequest(data.data)
  }

  async resendFriendRequest() {
    const deepLinkURL = await DeepLinkManager.instance.getBranchLinkURL(Constants.INVITE_FRIEND_ACTION_TYPE, this.props.userDetails, null)
    let displayName = this.props.userDetails.displayName ? this.props.userDetails.displayName : Constants.TINYVIEW_USER
    let fullMessageText = `${Constants.inviteFriendTemplateStart} ${displayName} ${Constants.inviteFriendTemplateEnd}`

    let data = {
      title: Constants.INVITE_FRIENDS,
      message: fullMessageText,
      url: deepLinkURL
    }
    this.props.resendFriendRequest(data)
  }

  unfriend(item) {
    const { isAnotherUserProfile = false } = this.props
    let data = { "data": { "friendUID": item.uid, "documentId": item.docId } }
    this.props.unfriendUser(data.data, isAnotherUserProfile)
  }

  showAlert(item) {
    const title = `Unfriend ${item.fullName}?`
    const msg = `Are you sure you want to remove ${item.fullName} as your friend?`
    const buttons = [
      { text: "Cancel" },
      {
        text: "Unfriend", onPress: () => {
          this.unfriend(item)
        }
      }
    ];

    Alert.alert(title, msg, buttons);
  }

  onImageError() {
    this.setState({ errorInImageLoading: true })
  }

  renderProfileImage(size = 32) {
    const { item } = this.props
    const { errorInImageLoading } = this.state
    const fullName = item.getFullName()
    const isNameNotANumber = Utils.checkData(fullName) && isNaN(fullName.charAt(0)) && fullName.charAt(0) != '+'

    return (
      (Utils.checkData(item.userProfilePic) && !errorInImageLoading)
        ?
        <FastImage source={{ uri: item.userProfilePic }} resizeMode={'cover'} style={[userProfilePic(size), { borderColor: this.context.colors.chatBubbles }]} onError={() => { this.onImageError() }} />
        : Utils.checkData(fullName) && isNameNotANumber
          ?
          <ImagePlaceHolder
            backgroundColor={Color.PROFILE_PLACE_HOLDER_BG}
            showCircularBorder={true}
            textColor={Color.COMMENT_TEXT_COLOR}
            size={size}
            type={'circle'}>{fullName}</ImagePlaceHolder>
          :
          <View
            style={[styles.emptyImageView(size), { borderColor: this.context.colors.chatBubbles }]}>
            <FastImage
              style={styles.imageInnerView(size)}
              tintColor={Color.COMMENT_TEXT_COLOR}
              source={require('../../../../assets/white_user_icon.png')}
            />
          </View>
    )
  }

  render() {
    const { item, selectedTab, showSeprator = false, isAnotherUserProfile = false } = this.props;
    const fullName = item.getFullName();
    const isIConClickDisable = !item.uid;
    const selectedTabLower = selectedTab && selectedTab.toLowerCase();
    const isReceivedTab = selectedTabLower === settings.RECEIVED.toLowerCase();
    const isSentTab = selectedTabLower === settings.SENT.toLowerCase();
    const isAddFriendTab = selectedTabLower === Constants.ADD_FRIEND.toLowerCase();
    const isFriendTab = selectedTabLower === Constants.FRIENDS.toLowerCase();

    return (
      <View style={styles.mainContainer}>
        <TouchableOpacity
          style={styles.profileView}
          onPress={() => this.props.navigateToUserProfile(item)}
          disabled={isIConClickDisable}
          transparent>
          {this.renderProfileImage()}
          <View style={styles.nameButtonView}>
            <Text style={[this.context.p, styles.userText, { color: this.context.colors.textBold }]}>{fullName ? fullName.trim() : fullName}</Text>
            {<BadgesView badges={item.badges} />}
          </View>
        </TouchableOpacity>
        <View style={styles.buttonsView}>
          {isFriendTab &&
            <Button
              variant='solid'
              onPress={() => this.showAlert(item)}
              style={[styles.confirmButton, { borderColor: this.context.colors.separators, backgroundColor: this.context.colors.textInverse, }]}>
              <Text style={[this.context.bodyMini, { color: this.context.colors.textBold }]}>{`✓ ${Constants.FRIEND}`}</Text>
            </Button>
          }

          {isAddFriendTab &&
            <Button
              variant='solid'
              onPress={() => this.updateFriendRequest(item, Constants.ADD_FRIEND_TITLE_CASE)}
              style={[styles.confirmButton, { borderColor: this.context.colors.logoRed, backgroundColor: this.context.colors.logoRed }]}>
              <Text style={[this.context.bodyMini, { color: this.context.colors.textInverse }]}>{Constants.ADD_FRIEND_TITLE_CASE}</Text>
            </Button>
          }

          {isReceivedTab &&
            <View style={{ flexDirection: 'row' }}>
              <Button
                variant='solid'
                onPress={() => this.updateFriendRequest(item, Constants.ACCEPT)}
                style={[styles.confirmButton, { borderColor: this.context.colors.logoRed, backgroundColor: this.context.colors.logoRed }]}>
                <Text style={[this.context.bodyMini, { color: this.context.colors.textInverse }]}>{Constants.ACCEPT}</Text>
              </Button>
              {!isAnotherUserProfile &&
                <Button
                  variant='solid'
                  onPress={() => this.updateFriendRequest(item, Constants.REJECT)}
                  style={[styles.confirmButton, { borderColor: this.context.colors.separators, backgroundColor: this.context.colors.textInverse, marginLeft: 10 }]} >
                  <Text style={[this.context.bodyMini, { color: this.context.colors.textBold }]}>{Constants.DELETE}</Text>
                </Button>
              }
            </View>
          }

          {isSentTab &&
            <View style={{ flexDirection: 'row' }}>
              {!isAnotherUserProfile &&
                <Button
                  variant='solid'
                  onPress={() => this.updateFriendRequest(item, Constants.RESEND)}
                  style={[styles.confirmButton, { borderColor: this.context.colors.logoRed, backgroundColor: this.context.colors.logoRed }]}>
                  <Text style={[this.context.bodyMini, { color: this.context.colors.textInverse }]}>{Constants.RESEND}</Text>
                </Button>
              }
              <Button
                variant='solid'
                onPress={() => { isAnotherUserProfile ? this.props.navigateToInvitesSent() : this.updateFriendRequest(item, Constants.DELETE) }}
                style={[styles.confirmButton, { borderColor: this.context.colors.separators, backgroundColor: this.context.colors.textInverse, marginLeft: 10 }]} >
                <Text style={[this.context.bodyMini, { color: this.context.colors.textBold }]}>{isAnotherUserProfile ? Constants.REQUEST_SENT : Constants.CANCEL}</Text>
              </Button>
            </View>
          }
          {showSeprator && <View style={styles.seprator} />}
        </View>
      </View>
    )
  }
}

FriendsPanel.contextType = ThemeContext

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    marginLeft: 16,
    marginRight: 16,
    marginBottom: 12,
    marginTop: 12,
    flexDirection: 'row'
  },
  seprator: {
    height: 1,
    width: '100%',
    backgroundColor: Color.LIGHTER_GREY,
    marginTop: 10
  },
  profileView: {
    flex: 1,
    flexDirection: 'row',
  },
  nameButtonView: {
    flex: 1,
    marginLeft: 8,
    flexDirection: 'row',
    alignItems: 'center'
  },
  confirmButton: {
    height: 32,
    borderRadius: 4,
    elevation: 0,
    borderWidth: 1,
    paddingTop: 4,
    paddingBottom: 4,
    paddingLeft: 8,
    paddingRight: 8
  },
  userText: {
    textAlign: 'left',
    flexShrink: 1
  },
  emptyImageView: (size) => {
    return {
      width: size,
      height: size,
      overflow: 'hidden',
      borderRadius: size / 2,
      backgroundColor: Color.PROFILE_PLACE_HOLDER_BG,
      justifyContent: 'center',
      borderWidth: 0.5
    }
  },
  imageInnerView: (size) => {
    return {
      height: size / 2,
      width: size / 2,
      alignSelf: 'center'
    }
  },
  buttonsView: {
    marginLeft: 5
  }
})