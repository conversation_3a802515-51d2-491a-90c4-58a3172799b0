import React, { Component } from 'react'
import {
  TouchableWithoutFeedback,
  StyleSheet,
  Dimensions,
  ImageBackground
} from 'react-native'
import { View } from 'native-base';
import { Utils } from '../../../config/Utils';
import ImageDownloader from '../../ImageDownloader';
import { navigationStyle } from '../../../config/styles'
import { ThemeContext } from '../../../Contexts';
import FastImage from 'react-native-fast-image';

export default class EpisodeCard extends Component {

  constructor(props) {
    super(props)

    this.state = {
      imagePath: null,
      errorInImageLoading: false,
    }

    this.isImageLoaded = true;
    const { item, pathUrl, isEpisodesCarousel } = this.props
    this.generateImagePath(item.item, pathUrl)

    this.imageWidth = isEpisodesCarousel ? 220 : 128 //Also change panelWidth in episodePanel and aspectRatio
    this.imageHeight = isEpisodesCarousel ? 110 : 68
    this.renderImage = this.renderImage.bind(this)
    this.imageStyle = this.imageStyle.bind(this)
    this.generateImagePath = this.generateImagePath.bind(this)
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps && nextProps.item && nextProps.item.item) {
      const currentItem = this.props.item && this.props.item.item;
      const nextItem = nextProps.item.item;

      if (JSON.stringify(currentItem) !== JSON.stringify(nextItem)) {
        const { item } = nextProps.item;
        const imageUrl = nextProps.pathUrl
        this.generateImagePath(item, imageUrl)
      }
    }
  }

  generateImagePath(item, pathUrl) {
    if (item && item.image) {
      const panelData = item;
      this.isImageLoaded = false;

      const finalUrl = Utils.resolvePath(pathUrl, panelData.image);
      ImageDownloader.downloadPanelImage(finalUrl, panelData.md5sum)
        .then((filePath) => {
          this.setState({ imagePath: filePath });
          item.imageLocalPath = filePath;
        });
      filePath = panelData.image;
    }
  }

  renderImage() {
    const { isCurrentEpisode, isEpisodesCarousel } = this.props
    const { item } = this.props.item
    const { imagePath } = this.state

    const isAlreadyReadEpisode = Utils.checkObject(item.isRead) && item.isRead

    return (
      <TouchableWithoutFeedback
        onPress={() => {
          if (!item.shouldFaded) {
            this.props.onPanelTap(item)
          }
        }}>
        <View style={styles.imageContainerStyle(item)}>
          <ImageBackground
            imageStyle={{ opacity: item.shouldFaded ? 0.5 : 1 }}
            style={[this.imageStyle(this.imageWidth, this.imageHeight, navigationStyle.panelsMargin), styles.episodeImageView, { borderWidth: isCurrentEpisode && isEpisodesCarousel ? 3 : 1, borderColor: isCurrentEpisode && isEpisodesCarousel ? this.context.colors.green : this.context.colors.chatBubbles }]}
            source={imagePath ? { uri: imagePath } : require('../../../../assets/image_unavailable.jpg')}
            onLoad={() => {
              this.isImageLoaded = true;
            }}
            onError={() => {
              this.setState({ errorInImageLoading: true })
            }}
          >
            {(item.shouldFaded || isAlreadyReadEpisode) &&
              <View style={{ flex: 1 }}>
                <View style={styles.overlay} />
                <FastImage
                  style={styles.readIndicatorIcon}
                  source={require('../../../../assets/white_double_check_icon.png')} />
              </View>
            }
          </ImageBackground>
        </View>
      </TouchableWithoutFeedback>
    )
  }

  imageStyle(w, h, margin = 2) {
    const { isEpisodesCarousel } = this.props
    let dimensions = Dimensions.get('window')
    let aspectRatio = isEpisodesCarousel ? ((dimensions.width - ((margin) * 2)) / w) / 1.8 : ((dimensions.width - ((margin) * 2)) / w) / 2.5

    return {
      width: w * aspectRatio,
      height: h * aspectRatio,
      resizeMode: 'contain'
    }
  }

  render() {
    if (this.state.errorInImageLoading) {
      return null
    }

    return (
      <View style={{ paddingStart: this.props.index == 0 ? navigationStyle.panelsMargin : 0 }}>
        <View style={styles.mainView}>
          {this.renderImage()}
        </View>
      </View>
    )
  }
}

EpisodeCard.contextType = ThemeContext

const styles = StyleSheet.create({
  imageContainerStyle: (item) => {
    if (!item.border) {
      return
    }

    const width = item.border["border-width"];
    const color = item.border["border-color"];
    const hasTopBorder = item.border["border-top"];
    const hasLeftBorder = item.border["border-left"];
    const hasRightBorder = item.border["border-right"];
    const hasBottomBorder = item.border["border-bottom"];

    return {
      borderColor: color,
      borderTopWidth: hasTopBorder ? width : 0,
      borderLeftWidth: hasLeftBorder ? width : 0,
      borderRightWidth: hasRightBorder ? width : 0,
      borderBottomWidth: hasBottomBorder ? width : 0,
    }
  },
  mainView: {
    marginTop: navigationStyle.panelsMargin,
    marginRight: 15
  },
  episodeImageView: {
    borderRadius: 8,
    overflow: 'hidden'
  },
  readIndicatorIcon: {
    position: 'absolute',
    top: 2,
    right: 5,
    width: 24,
    height: 24,
  },
  overlay: {
    opacity: 0.5,
    position: 'absolute',
    width: "100%",
    height: "100%",
    backgroundColor: "#626262"
  }
})