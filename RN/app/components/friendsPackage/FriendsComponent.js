import React, { Component } from 'react';
import { StyleSheet, BackHandler, TouchableOpacity, Image, Dimensions } from 'react-native'
import { resendFriendRequest, getFriendRequest, getFriendList, updateFriendRequest, unfriend, shareInviteLink, getUserDetails, openWebViewAction, updateShowAllComicsSettings, sendFriendRequest } from '../../redux/actions/actions'
import { connect } from 'react-redux';
import { View, Text } from 'native-base';
import { CommonActions } from '@react-navigation/native'
import FriendsListPage from './page/FriendsListPage';
import RequestsPage from './page/RequestsPage';
import { Utils } from '../../config/Utils';
import { Constants } from '../../config/Constants';
import ShareBottomSheet from '../ShareBottomSheet';
import SessionManager from '../../config/SessionManager';
import { bannerMsgContainer, bannerCrossIconView, bannerCrossIcon, buyButton } from '../../config/styles';
import { ThemeContext, bannerTextView } from '../../Contexts';
import HTML from 'react-native-render-html';
import { settings } from '../../config/settings';
import BottomTabBar from '../BottomTabBar';
import { StackActions } from '@react-navigation/native';
import { SharedPreferences } from '../../config/SharedPreferences';
import { LoadingIndicator } from '../LoadingIndicator';
import FirebaseManager from '../../config/FirebaseManager';
import { SafeAreaView } from 'react-native-safe-area-context';
import ActionSheet from '../actionSheet/ActionSheetCustom';
import UserSession from '../../config/UserSession';


const dimensions = Dimensions.get('window')
class FriendsComponent extends Component {
  constructor(props) {
    super(props)

    this.state = {
      isLoading: false,
      friendsCount: 0,
      requests: [],
      showInviteBottomSheet: false,
      showInviteFriendMessage: true,
      sheetTitle: '',
      sheetMessage: Constants.INVITE_FRIENDS_ACTION_SHEET_MESSAGE,
      sheetOptions: [Constants.SIGN_IN, Constants.CANCEL],
      cancelOptionIndex: 1
    };

    const params = props.route.params
    this.componentToRender = params && params.componentToRender
    let profileUserData = params && params.profileUserData
    this.profileUserID = profileUserData && profileUserData.profileUserID
    this.profileUserName = profileUserData && profileUserData.profileUserName

    let currentUser = FirebaseManager.instance.currentUser()
    let UID = currentUser.uid
    this.isCurrentUser = this.profileUserID && UID !== this.profileUserID ? false : true

    this.isLoggedInUser = UserSession.instance.isLoggedInUser()
    this.configBottomSheet = ''
    this.isLoginFromInviteLink = params ? params.isLoginFromInviteLink : false
    this.bgColor = Utils.getSeriesSpecificStyle(settings.TINYVIEW_CHANNEL_NAME).backgroundColor

    this.navigateToSendInvite = this.navigateToSendInvite.bind(this)
    this.navigateToUserProfile = this.navigateToUserProfile.bind(this)
    this.navigateToInviteBottomSheet = this.navigateToInviteBottomSheet.bind(this)
    this.updateFriendsCount = this.updateFriendsCount.bind(this)
    this.onBackPress = this.onBackPress.bind(this)
    this.renderInviteActionSheet = this.renderInviteActionSheet.bind(this)
    this.closeBottomSheet = this.closeBottomSheet.bind(this)
    this.renderHeaderMessage = this.renderHeaderMessage.bind(this)
    this.hideNotificationMessage = this.hideNotificationMessage.bind(this)
    this.renderHeader = this.renderHeader.bind(this)
    this.renderMainBottomBar = this.renderMainBottomBar.bind(this)
    this.openWebView = this.openWebView.bind(this)
    this.navigateToStackTop = this.navigateToStackTop.bind(this)
    this.onShowAllComicSwitchTap = this.onShowAllComicSwitchTap.bind(this)
    this.openChapter = this.openChapter.bind(this)
    this.setLoaderVisibility = this.setLoaderVisibility.bind(this)
    this.navigateToSignIn = this.navigateToSignIn.bind(this)
    this.onActionSheetPress = this.onActionSheetPress.bind(this)
    this.changeActionSheetTitleAndButton = this.changeActionSheetTitleAndButton.bind(this)
    this.inviteFriendsSheet = this.inviteFriendsSheet.bind(this)
    this.renderSignInMsg = this.renderSignInMsg.bind(this)
    this.renderBannerButton = this.renderBannerButton.bind(this)
    this.signInSheet = this.signInSheet.bind(this)
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.componentToRender != nextProps.route.params.componentToRender) {
      this.componentToRender = nextProps.route.params.componentToRender
    }
  }

  onBackPress() {
    if (this.isLoginFromInviteLink) {
      SessionManager.instance.setIsInviteLinkLogin(false)
      this.props.navigation.navigate('Home', { 'forceReload': true })
    } else {
      this.props.navigation.dispatch(CommonActions.goBack())
    }
    return true;
  }

  UNSAFE_componentWillMount() {
    BackHandler.addEventListener('hardwareBackPress', this.onBackPress);
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.onBackPress);
  }

  navigateToSendInvite() {
    this.props.navigation.navigate('SendInvitationScreen')
  }

  navigateToUserProfile(item) {
    let phoneNumber = null

    if (item.primaryContact) {
      phoneNumber = item.primaryContact
    }

    if (item.userPhoneNumber) {
      phoneNumber = item.userPhoneNumber
    }

    var subRoutesParams = { clickedUserID: item.uid, contactToSendRequest: phoneNumber }
    this.props.navigation.push(Constants.USER_PROFILE_SCREEN, { ...subRoutesParams })
  }

  navigateToInviteBottomSheet() {
    this.configBottomSheet = Constants.INVITE_FRIENDS
    this.setState({ showInviteBottomSheet: true })
  }

  closeBottomSheet() {
    this.setState({ showInviteBottomSheet: false })
  }

  renderInviteActionSheet() {
    const valueProps = { closeBottomSheet: this.closeBottomSheet, userDetails: this.props.userDetails, shareInviteLink: this.props.shareInviteLink }
    return (
      <ShareBottomSheet {...valueProps} configShareSheetFor={this.configBottomSheet} />
    )
  }

  hideNotificationMessage() {
    this.setState({ showInviteFriendMessage: false })
  }

  renderSignInMsg() {
    if (this.isLoggedInUser || !this.isCurrentUser) {
      return null
    }

    let message = "You must sign in to see or invite friends"

    return (
      <View style={[bannerMsgContainer, { backgroundColor: this.context.colors.bannerBackgroundError }]}>
        <View style={[bannerTextView, { flex: 1 }]}>
          <Text style={[this.context.bodyMini, { color: this.context.colors.bannerTextError }]} numberOfLines={1} adjustsFontSizeToFit>{message}</Text>
        </View>
        {this.renderBannerButton(Constants.SIGN_IN)}
        <TouchableOpacity
          style={bannerCrossIconView}
          onPress={() => this.hideNotificationMessage()}>
          <Image
            style={[bannerCrossIcon, { tintColor: this.context.colors.bannerTextError }]}
            source={require('../../../assets/close_window.png')}
          />
        </TouchableOpacity>
      </View>
    )
  }

  renderHeaderMessage() {
    if (!this.isCurrentUser || !this.isLoggedInUser) {
      return null
    }

    let diffActionsInfluencePoints = SessionManager.instance.getInfluencePointsValuesForActions()
    let message = `Earn <b>${diffActionsInfluencePoints.FRIEND_REQUEST_SENDER} influence points</b> for every invite accepted`
    if (SessionManager.instance.hasAnySubscriptionPurchase()) {
      message = "See likes and comments of your friends"
    }

    return (
      <View style={[bannerMsgContainer, { backgroundColor: this.context.colors.bannerBackgroundError }]}>
        <TouchableOpacity
          style={bannerTextView}
          disabled={true}>
          <HTML contentWidth={dimensions.width} textSelectable={true} allowFontScaling baseStyle={{ ...this.context.bodyMini, color: this.context.colors.bannerTextError }} source={{ html: message }} tagsStyles={{ "b": { ...this.context.bodyMiniBold }, "p": { paddingTop: 0, marginBottom: 0, marginTop: 0 } }} />
        </TouchableOpacity>
        <TouchableOpacity
          style={bannerCrossIconView}
          onPress={() => this.hideNotificationMessage()}>
          <Image
            style={[bannerCrossIcon, { tintColor: this.context.colors.bannerTextError }]}
            source={require('../../../assets/close_window.png')}
          />
        </TouchableOpacity>
      </View>
    )
  }

  updateFriendsCount(data = {}) {
    const { friendsCount = 0 } = data
    let formatedFriendsCount = friendsCount != null ? friendsCount : 0
    this.setState({ friendsCount: formatedFriendsCount })
  }

  setLoaderVisibility(value) {
    this.setState({ isLoading: value })
  }

  openWebView(url) {
    this.props.openWebView(url)
  }

  renderHeader() {
    const { friendsCount } = this.state
    const friendsCountSuffix = friendsCount != 1 ? Constants.FRIENDS : Constants.FRIEND
    const isFriendsList = this.componentToRender == Constants.FRIENDS

    return (
      <View style={{ backgroundColor: this.bgColor }}>
        <View style={[styles.headerContainer, { marginBottom: 12 }]}>
          <Text style={this.context.h2}>{isFriendsList ? (!this.isCurrentUser && this.profileUserName) ? `${this.profileUserName}'s ${friendsCountSuffix} (${friendsCount})` : (this.isCurrentUser && !this.isLoggedInUser) ? `${friendsCountSuffix}` : `${friendsCountSuffix} (${friendsCount})` : Constants.FRIEND_REQUESTS}</Text>
          {this.isCurrentUser &&
            <TouchableOpacity
              style={styles.inviteFriendsView}
              onPress={() => { this.isLoggedInUser ? this.navigateToInviteBottomSheet() : this.inviteFriendsSheet() }}>
              <Image
                style={[styles.inviteFriendsImage, { tintColor: this.context.colors.textBold }]}
                source={require('../../../assets/invite_friends_icon.png')} />
              <Text style={[this.context.p, styles.inviteFriendsText]}>{Constants.INVITE_FRIENDS}</Text>
            </TouchableOpacity>
          }
        </View>
      </View>
    )
  }

  onShowAllComicSwitchTap(value) {
    SessionManager.instance.showAllComics = value
    this.props.updateShowAllComicsSettings(value)
    SharedPreferences.setShowAllComicsValue(value)

    this.props.navigation.dispatch(StackActions.popToTop());
  }

  openChapter(path) {
    const redirectPath = Utils.resolvePath(settings.apiBaseURL, path)
    this.props.navigation.push('Home', { comicHome: redirectPath, hasUserInfo: true })
  }

  navigateToStackTop() {
    this.props.navigation.dispatch(StackActions.popToTop());
  }

  navigateToSignIn(paramsData = {}) {
    let params = { isForLoginProcess: true, ...paramsData }
    Utils.navigateToDrawerLoginRoute(this.props, Constants.LOGIN_SCREEN, params)
  }

  inviteFriendsSheet() {
    let sheetTitle = ''
    let sheetMessage = Constants.INVITE_FRIENDS_ACTION_SHEET_MESSAGE
    let sheetOptions = [Constants.SIGN_IN, Constants.CANCEL]
    this.changeActionSheetTitleAndButton(sheetTitle, sheetOptions, sheetMessage)
  }

  signInSheet() {
    let sheetTitle = ''
    let sheetMessage = Constants.SIGN_IN_ACTION_SHEET_MESSAGE
    let sheetOptions = [Constants.SIGN_IN, Constants.CANCEL]
    this.changeActionSheetTitleAndButton(sheetTitle, sheetOptions, sheetMessage)
  }

  changeActionSheetTitleAndButton(sheetTitle, sheetOptions, sheetMessage = '') {
    this.setState({ sheetTitle: sheetTitle, sheetMessage: sheetMessage, sheetOptions: sheetOptions }, () => {
      this.actionSheet.show()
    })
  }

  onActionSheetPress(index) {
    if (index == 0) {
      if (this.state.sheetOptions[0] == Constants.SIGN_IN) {
        const params = { postSignInFriendsPath: Constants.INVITE_FRIEND }
        this.navigateToSignIn(params)
      } else if (this.state.sheetOptions[0] == Constants.MANAGE_ALERTS) {
        Utils.navigateToManageAlertsPage(this.props, { configFromFollowTab: true, seriesToFollowData: { title: Constants.LETTER_CASE_TINYVIEW, action: "/tinyview/index.json" } })
      }
    }
  }

  renderBannerButton(item) {
    return (
      <TouchableOpacity
        style={[buyButton, styles.buyButton, { backgroundColor: this.context.colors.logoRed, borderColor: this.context.colors.logoRed }]}
        onPress={() => { this.navigateToSignIn() }}>
        <Text style={[this.context.bodyMini, { color: this.context.colors.textInverse }]}>{item}</Text>
      </TouchableOpacity>
    )
  }

  renderMainBottomBar() {
    return (
      <BottomTabBar navigation={this.props.navigation} openWebView={this.openWebView} navigateToStackTop={this.navigateToStackTop} onBackPress={this.onBackPress} openChapter={this.openChapter} onShowAllComicSwitchTap={this.onShowAllComicSwitchTap} userDetails={this.props.userDetails} />
    )
  }

  render() {
    const { isLoading } = this.state
    const params = { onBackPress: this.onBackPress, navigateToUserProfile: this.navigateToUserProfile, updateFriendsCount: this.updateFriendsCount, navigateToInviteBottomSheet: this.navigateToInviteBottomSheet, bgColor: this.bgColor, setLoaderVisibility: this.setLoaderVisibility, showLoadingIndicator: isLoading, profileUserID: this.profileUserID, isCurrentUser: this.isCurrentUser, navigateToSignIn: this.navigateToSignIn, inviteFriendsSheet: this.inviteFriendsSheet, isLoggedInUser: this.isLoggedInUser, signInSheet: this.signInSheet, ...this.props }
    const isFriendsList = this.componentToRender == Constants.FRIENDS

    return (
      <SafeAreaView style={styles.mainContainer}>
        {this.state.showInviteFriendMessage && this.renderSignInMsg()}
        {this.state.showInviteFriendMessage && this.isCurrentUser && this.isLoggedInUser && this.renderHeaderMessage()}
        {this.renderHeader()}

        {isFriendsList &&
          <FriendsListPage {...params} />
        }

        {!isFriendsList &&
          <RequestsPage {...params} />
        }

        <ActionSheet
          ref={o => this.actionSheet = o}
          title={this.state.sheetTitle}
          message={this.state.sheetMessage}
          options={this.state.sheetOptions}
          cancelButtonIndex={this.state.cancelOptionIndex}
          tintColor={this.context.colors.logoRed}
          onPress={(index) => { this.onActionSheetPress(index) }}
          useNativeDriver={true}
          styles={{ titleText: this.context.h2, messageText: this.context.p, messageBox: { paddingTop: this.state.sheetTitle ? 0 : 20 } }}
        />
        {this.state.showInviteBottomSheet && this.renderInviteActionSheet()}
        {(this.props.isLogInProcess || this.props.friendsActivityInProgress) && <LoadingIndicator />}
        {this.renderMainBottomBar()}
      </SafeAreaView>
    )
  }
}

FriendsComponent.contextType = ThemeContext;

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
    marginLeft: 20,
    marginRight: 20
  },
  inviteFriendsView: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  inviteFriendsImage: {
    height: 18,
    width: 18
  },
  inviteFriendsText: {
    marginLeft: 8
  },
  buyButton: {
    height: 25,
    paddingLeft: 8,
    paddingRight: 8,
    position: 'relative'
  }
})

const mapStateToProps = (state) => {
  return {
    userDetails: state.loginInfo.userDetails,
    isLogInProcess: state.loginInfo.isLogInProcess,
    friendsActivityInProgress: state.friendActivityIndicator.friendsActivityInProgress
  }
}

const mapDispatchToProps = (dispatch) => {
  return {
    getFriendList(callback, forceLoad, data) {
      dispatch(
        getFriendList(callback, forceLoad, data)
      )
    },
    getUserDetails() {
      dispatch(
        getUserDetails()
      )
    },
    getFriendRequest(requstType, callback) {
      dispatch(
        getFriendRequest(requstType, callback)
      )
    },
    unfriend(data, callback) {
      dispatch(
        unfriend(data, callback)
      )
    },
    resendFriendRequest(data) {
      dispatch(
        resendFriendRequest(data)
      )
    },
    updateFriendRequest(data, callback) {
      dispatch(
        updateFriendRequest(data, callback)
      )
    },
    shareInviteLink(requestedData, callback) {
      dispatch(
        shareInviteLink(requestedData, callback)
      )
    },
    openWebView(url) {
      dispatch(
        openWebViewAction(url)
      )
    },
    updateShowAllComicsSettings(value) {
      dispatch(
        updateShowAllComicsSettings(value)
      )
    },
    sendFriendRequest(data, sendTextToContactsParams, callback) {
      dispatch(
        sendFriendRequest(data, sendTextToContactsParams, callback)
      )
    }
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(FriendsComponent)