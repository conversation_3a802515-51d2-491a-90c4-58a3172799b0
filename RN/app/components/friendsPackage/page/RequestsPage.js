import React, { Component } from 'react';
import { StyleSheet, Dimensions, SafeAreaView } from 'react-native'
import { View } from 'native-base';
import FriendsPanel from '../panels/FriendsPanel';
import FriendsSharedList from '../shared/FriendsSharedList';
import { TabView, TabBar } from 'react-native-tab-view';
import { Utils } from '../../../config/Utils';
import { Constants } from '../../../config/Constants';
import { ThemeContext } from '../../../Contexts';
import { navigationStyle } from '../../../config/styles';
import FirebaseManager from '../../../config/FirebaseManager';

const dimensions = Dimensions.get('window')
export default class RequestsPage extends Component {
  constructor(props) {
    super(props)

    let currentUser = FirebaseManager.instance.currentUser()
    this.UID = currentUser.uid

    let selTab = Constants.RECEIVED
    if (this.props.route.params && (this.props.navigation.state || this.props.route.params.selectedTab)) {
      selTab = this.props.route.params.selectedTab || Constants.RECEIVED
    }

    this.state = {
      receivedRequests: [],
      sendRequests: [],
      shouldRefresh: false,
      selectedTab: selTab,
      index: selTab == Constants.SENT ? 1 : 0,
      routes: [
        { key: 'first', title: `${Constants.INVITES_RECEIVED}` },
        {
          key: 'second', title: `${Constants.INVITES_SENT}`
        },
      ]
    }

    this.selectedItem = null
    this._unsubscribe = null
    this.hasMoreReceivedData = props.isCurrentUser && !props.isLoggedInUser ? false : true
    this.hasMoreSentData = props.isCurrentUser && !props.isLoggedInUser ? false : true
    this.isSentAPIInProgress = false
    this.isReceivedAPIInProgress = false
    this.bgColor = props.bgColor
    this.refreshData = this.refreshData.bind(this)
    this.updateFriendRequest = this.updateFriendRequest.bind(this)
    this.removeListData = this.removeListData.bind(this)
    this.updateShouldRefresh = this.updateShouldRefresh.bind(this)
    this.onTabPress = this.onTabPress.bind(this)
    this.onTabChanged = this.onTabChanged.bind(this)
    this.renderSentRequest = this.renderSentRequest.bind(this)
    this.renderReceivedRequest = this.renderReceivedRequest.bind(this)
    this.renderScene = this.renderScene.bind(this)
    this.renderTabBar = this.renderTabBar.bind(this)
    this.renderItem = this.renderItem.bind(this)
    this.updateRoutes = this.updateRoutes.bind(this)
    this.fetchInvitesReceived = this.fetchInvitesReceived.bind(this)
    this.fetchInvitesSent = this.fetchInvitesSent.bind(this)
    this.updateInvitesSent = this.updateInvitesSent.bind(this)
    this.updateInvitesReceived = this.updateInvitesReceived.bind(this)
  }

  componentDidMount() {
    this._unsubscribe = this.props.navigation.addListener("focus", async () => {
      this.refreshData()
    });
  }

  componentWillUnmount() {
    if (this._unsubscribe) {
      this._unsubscribe()
    }
  }

  refreshData() {
    const { isCurrentUser, isLoggedInUser } = this.props
    if (isCurrentUser && !isLoggedInUser) {
      this.hasMoreReceivedData = false
      this.hasMoreSentData = false
      return
    }

    this.props.setLoaderVisibility(true)
    this.isReceivedAPIInProgress = true
    this.isSentAPIInProgress = true
    this.hasMoreReceivedData = true
    this.hasMoreSentData = true
    this.setState({ receivedRequests: [], sendRequests: [] }, () => {
      Promise.allSettled([
        new Promise((resolve) => {
          this.props.getFriendRequest({ startAfter: "", records: Constants.FRIENDS_LIST_PAGINATION_COUNT, requestType: Constants.RECEIVED.toLowerCase() },
            (data) => {
              this.updateInvitesReceived(data, false)
              resolve()
            })
        })
        ,
        new Promise((resolve) => {
          this.props.getFriendRequest({ startAfter: "", records: Constants.FRIENDS_LIST_PAGINATION_COUNT, requestType: Constants.SENT.toLowerCase() },
            (data) => {
              this.updateInvitesSent(data, false)
              resolve()
            })
        })
      ]).then(() => {
        this.props.setLoaderVisibility(false)
      })
    })
  }

  fetchInvitesReceived() {
    if (this.isReceivedAPIInProgress) {
      return
    }

    const { receivedRequests } = this.state
    this.isReceivedAPIInProgress = true
    let lastInviteReceivedID = ""
    if (receivedRequests && receivedRequests[receivedRequests.length - 1]) {
      lastInviteReceivedID = receivedRequests[receivedRequests.length - 1].docId
    }
    this.props.getFriendRequest({ startAfter: lastInviteReceivedID, records: Constants.FRIENDS_LIST_PAGINATION_COUNT, requestType: Constants.RECEIVED.toLowerCase() }, this.updateInvitesReceived)
  }

  fetchInvitesSent() {
    if (this.isSentAPIInProgress) {
      return
    }

    const { sendRequests } = this.state
    this.isSentAPIInProgress = true
    let lastInviteSendID = ""
    if (sendRequests && sendRequests[sendRequests.length - 1]) {
      lastInviteSendID = sendRequests[sendRequests.length - 1].docId
    }
    this.props.getFriendRequest({ startAfter: lastInviteSendID, records: Constants.FRIENDS_LIST_PAGINATION_COUNT, requestType: Constants.SENT.toLowerCase() }, this.updateInvitesSent)
  }

  updateFriendRequest(data) {
    this.selectedItem = data
    this.props.updateFriendRequest(data, this.removeListData)
  }

  removeListData(success = false, status = null) {
    if (success) {
      if (this.state.selectedTab == Constants.RECEIVED) {
        for (const key in this.state.receivedRequests) {
          let id = this.state.receivedRequests[key].docId
          if (this.selectedItem.requestID == id) {
            if (status === Constants.ACCEPTED) {
              let userName = this.state.receivedRequests[key]?.fullName
              Utils.showToast(`You are friends with ${userName} now.`)
            }
            this.state.receivedRequests.splice(key, 1)
            let updatedRoutes = this.updateRoutes(this.state.receivedRequests, 'first', Constants.INVITES_RECEIVED)
            this.setState({ routes: updatedRoutes })
            break
          }
        }
      } else if (this.state.selectedTab == Constants.SENT) {
        for (const key in this.state.sendRequests) {
          let id = this.state.sendRequests[key].docId
          if (this.selectedItem.requestID == id) {
            this.state.sendRequests.splice(key, 1)
            let updatedRoutes = this.updateRoutes(this.state.sendRequests, 'second', Constants.INVITES_SENT)
            this.setState({ routes: updatedRoutes })
            break
          }
        }
      }
      this.updateShouldRefresh(true)
    }
  }

  updateShouldRefresh(value) {
    this.setState({ shouldRefresh: value })
  }

  updateRoutes(friendsData, titleKey, titleText) {
    let friendsCount = friendsData.length !== 0 ? `(${Utils.formatViewCount(friendsData.length)})` : `(0)`
    let updatedRoutes = this.state.routes.map(data => data.key === titleKey ? { ...data, title: `${titleText} ${friendsCount}` } : data);
    return updatedRoutes
  }

  updateInvitesReceived(data, hideLoader = true) {
    if (data && data.length < Constants.FRIENDS_LIST_PAGINATION_COUNT) {
      this.hasMoreReceivedData = false
    }

    let updatedData = [...this.state.receivedRequests, ...data]
    let updatedRoutes = this.updateRoutes(updatedData, 'first', Constants.INVITES_RECEIVED)
    this.setState({ "receivedRequests": updatedData, routes: updatedRoutes }, () => {
      this.isReceivedAPIInProgress = false
      if (hideLoader) {
        this.props.setLoaderVisibility(false)
      }
    })
  }

  updateInvitesSent(data, hideLoader = true) {
    if (data && data.length < Constants.FRIENDS_LIST_PAGINATION_COUNT) {
      this.hasMoreSentData = false
    }

    let updatedData = [...this.state.sendRequests, ...data]
    let updatedRoutes = this.updateRoutes(updatedData, 'second', Constants.INVITES_SENT)
    this.setState({ "sendRequests": updatedData, routes: updatedRoutes }, () => {
      this.isSentAPIInProgress = false
      if (hideLoader) {
        this.props.setLoaderVisibility(false)
      }
    })
  }

  onTabPress(index) {
    this.setState({ index: index, selectedTab: index == 0 ? Constants.RECEIVED : Constants.SENT }, () => {
      this.onTabChanged(index)
    })
  }

  onTabChanged(index) {
    this.props.setLoaderVisibility(true)
    if (index == 0) {
      this.hasMoreReceivedData = true
      this.setState({ "receivedRequests": [] }, () => {
        this.fetchInvitesReceived()
      })
    } else if (index == 1) {
      this.hasMoreSentData = true
      this.setState({ "sendRequests": [] }, () => {
        this.fetchInvitesSent()
      })
    }
  }

  renderSentRequest() {
    const { sendRequests, shouldRefresh } = this.state

    return (
      <FriendsSharedList
        data={sendRequests}
        keyExtractor={(item, index) => item.uid + index.toString()}
        renderItem={this.renderItem}
        extraData={shouldRefresh}
        emptyText={"You haven't sent any invites"}
        setLoaderVisibility={this.props.setLoaderVisibility}
        showLoadingIndicator={this.props.showLoadingIndicator}
        navigateToInviteBottomSheet={this.props.navigateToInviteBottomSheet}
        hasMoreData={this.hasMoreSentData}
        isAPIInProgress={this.isSentAPIInProgress}
        refreshUserFriends={this.fetchInvitesSent}
        inviteFriendsSheet={this.props.inviteFriendsSheet}
        isLoggedInUser={this.props.isLoggedInUser}
      />
    )
  }

  renderReceivedRequest() {
    const { receivedRequests, shouldRefresh } = this.state

    return (
      <FriendsSharedList
        data={receivedRequests}
        keyExtractor={(item, index) => item.uid + index.toString()}
        renderItem={this.renderItem}
        extraData={shouldRefresh}
        emptyText={"You haven't received any invites"}
        setLoaderVisibility={this.props.setLoaderVisibility}
        showLoadingIndicator={this.props.showLoadingIndicator}
        navigateToInviteBottomSheet={this.props.navigateToInviteBottomSheet}
        hasMoreData={this.hasMoreReceivedData}
        isAPIInProgress={this.isReceivedAPIInProgress}
        refreshUserFriends={this.fetchInvitesReceived}
        inviteFriendsSheet={this.props.inviteFriendsSheet}
        isLoggedInUser={this.props.isLoggedInUser}
      />
    )
  }

  renderScene(route) {
    switch (route.key) {
      case 'first':
        return this.renderReceivedRequest();
      case 'second':
        return this.renderSentRequest();
    }
  }

  renderTabBar(props) {
    return (
      <TabBar
        {...props}
        style={[styles.tabBarView, { backgroundColor: this.bgColor }]}
        inactiveColor={this.context.colors.text}
        activeColor={this.context.colors.textBold}
        tabStyle={{ width: 'auto' }}
        contentContainerStyle={{ marginLeft: 8 }}
        scrollEnabled
        renderIndicator={props => {
          const index = props.navigationState.index;
          const tabWidth = props.getTabWidth(index);

          return (
            <View
              style={{
                position: 'absolute',
                left: index * tabWidth + (index == 1 ? 48 : 20), // adjust left padding
                width: tabWidth - 20,        // reduce width for left & right
                height: 2,
                backgroundColor: this.context.colors.textBold,
                bottom: 5,
              }}
            />)
        }}
      />
    )
  }

  renderItem(item) {
    if (!item.item || Object.keys(item.item).length == 0) {
      return null;
    }

    const { receivedRequests, sendRequests, selectedTab } = this.state
    const isFirstItem = item.index === 0
    const isLastItem = (selectedTab === Constants.RECEIVED && item.index === receivedRequests.length - 1) || (selectedTab === Constants.SENT && item.index === sendRequests.length - 1)

    return (
      <View style={[styles.friendsListView(isFirstItem, isLastItem), { backgroundColor: this.context.colors.textInverse }]}>
        <FriendsPanel item={item.item} resendFriendRequest={this.props.resendFriendRequest} updateFriendRequest={this.updateFriendRequest} navigateToUserProfile={this.props.navigateToUserProfile} showRequestsIcon={true} selectedTab={this.state.selectedTab} userDetails={this.props.userDetails} signInSheet={this.props.signInSheet} />
      </View>
    )
  }

  render() {
    return (
      <SafeAreaView style={[styles.mainContainer, { backgroundColor: this.bgColor }]}>
        {!this.props.isLoggedInUser &&
          <FriendsSharedList
            data={[]}
            setLoaderVisibility={this.props.setLoaderVisibility}
            showLoadingIndicator={this.props.showLoadingIndicator}
            navigateToInviteBottomSheet={this.props.navigateToInviteBottomSheet}
            navigateToSignIn={this.props.navigateToSignIn}
            inviteFriendsSheet={this.props.inviteFriendsSheet}
            isLoggedInUser={this.props.isLoggedInUser}
          />
        }
        {this.props.isLoggedInUser &&
          <TabView
            navigationState={this.state}
            renderTabBar={(props) => this.renderTabBar(props)}
            renderScene={({ route }) => this.renderScene(route)}
            onIndexChange={this.onTabPress}
            initialLayout={{ width: dimensions.width }}
          />
        }
      </SafeAreaView>
    )
  }
}

RequestsPage.contextType = ThemeContext;

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1
  },
  friendsListView: (isFirstItem, isLastItem) => {
    return {
      borderTopLeftRadius: isFirstItem ? navigationStyle.panelRadius : 0,
      borderTopRightRadius: isFirstItem ? navigationStyle.panelRadius : 0,
      borderBottomLeftRadius: isLastItem ? navigationStyle.panelRadius : 0,
      borderBottomRightRadius: isLastItem ? navigationStyle.panelRadius : 0
    }
  },
  tabBarView: {
    elevation: 0,
    shadowOpacity: 0,
    marginBottom: 12
  }
})