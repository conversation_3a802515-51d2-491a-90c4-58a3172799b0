import React, { Component } from "react"
import { StyleSheet, Image, TouchableWithoutFeedback, Dimensions, TouchableOpacity } from "react-native"
import { scale } from "react-native-size-matters"
import { Text, View } from "native-base"
import InviteFriendsPanel from "../panels/InviteFriendsPanel"
import SearchBar from "../../SearchBar"
import AlphabetFlatList from "../../alphabetflatlist"
import HorizontalFriendsList from "../HorizontalFriendsList"
import { LoadingIndicator } from "../../LoadingIndicator"
import SessionManager from "../../../config/SessionManager"
import { Constants } from "../../../config/Constants"
import { ThemeContext } from "../../../Contexts"
import { Utils } from "../../../config/Utils"
import { bannerMsgContainer, bannerTextView, bannerCrossIconView, bannerCrossIcon } from "../../../config/styles"

export default class InviteFriendsPage extends Component {
  constructor(props) {
    super(props)

    this.userContacts = []
    this.searchResult = ""
    this.searchChar = ""
    this.diffActionsInfluencePoints = SessionManager.instance.getInfluencePointsValuesForActions()
    this.state = {
      selectedContacts: [],
      filteredContacts: [],
      shouldRefresh: false,
      showLoadingIndicator: true,
      showInviteFriendMessage: true
    }

    this.onBackPress = this.onBackPress.bind(this)
    this.updateSelectedContacts = this.updateSelectedContacts.bind(this)
    this.imageStyle = this.imageStyle.bind(this)
    this.getMainListRef = this.getMainListRef.bind(this)
    this.tapOnContactRow = this.tapOnContactRow.bind(this)
    this.addSelection = this.addSelection.bind(this)
    this.removeSelectedContact = this.removeSelectedContact.bind(this)
    this.searchUpdated = this.searchUpdated.bind(this)
    this._listEmptyComponent = this._listEmptyComponent.bind(this)
    this.hideNotification = this.hideNotification.bind(this)
    this.setLoaderVisibility = this.setLoaderVisibility.bind(this)
    this.getItemLayout = this.getItemLayout.bind(this)
    this.renderHorizontalList = this.renderHorizontalList.bind(this)
    this.renderItem = this.renderItem.bind(this)
    this.renderInviteFriendMessage = this.renderInviteFriendMessage.bind(this)
  }

  async componentDidMount() {
    if (this.props.isForRepost) {
      this.setState({ filteredContacts: this.props.allContacts })
      this.userContacts = this.props.allContacts
      this.setLoaderVisibility(false)
    }
  }

  onBackPress() {
    this.props.onBackPress()
  }

  updateSelectedContacts(selectedContacts) {
    this.setState({ selectedContacts, shouldRefresh: !this.state.shouldRefresh }, () => {
      const isRightDisabled = this.state.selectedContacts.length > 0 ? false : true
      this.props.setHeaderParams && this.props.setHeaderParams({ isRightDisabled })
      this.props.updateState && this.props.updateState({ selectedContacts })
    })
    this.props.updateSelectedContacts && this.props.updateSelectedContacts(selectedContacts)
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.isListUpdated && nextProps.userContacts && nextProps.userContacts.length > 0) {
      this.setState({})
    }
  }

  imageStyle(w = 800, h = 600, margin = scale(15)) {
    let dimensions = Dimensions.get('window')

    let aspectRatio = (dimensions.width - ((margin) * 2)) / w

    return {
      marginTop: scale(15),
      width: w * aspectRatio,
      height: h * aspectRatio,
      resizeMode: 'contain'
    }
  }

  getMainListRef() {
    return this._mainList
  }

  tapOnContactRow(item) {
    if (this.userContacts.length > 0) {
      for (const key in this.userContacts) {
        let contact = this.userContacts[key]
        if (contact.uid == item.uid) {
          item.index = parseInt(key)
        }
      }
    }

    let indexToRemove = null
    if (this.state.selectedContacts.length > 0) {
      let isPreSelected = false
      for (const selIndex in this.state.selectedContacts) {
        let value = this.state.selectedContacts[selIndex]
        if (item.uid == value.uid) {
          indexToRemove = selIndex
          isPreSelected = true
          break
        }
      }

      if (indexToRemove && isPreSelected) {
        this.removeSelectedContact(item, indexToRemove)
      } else {
        this.addSelection(item)
      }
    } else {
      this.addSelection(item)
    }
  }

  addSelection(item) {
    if (!item.isSelected) {
      this.userContacts[item.index].isSelected = !item.isSelected
      this.state.selectedContacts.push({ ...item, selContact: item.primaryContact })
      this.updateSelectedContacts(this.state.selectedContacts)
    }
  }

  removeSelectedContact(item, selIndexes) {
    //selIndex is the index of selectedContacts
    this.state.selectedContacts.splice(selIndexes, 1)
    item.isSelected = false
    this.userContacts[item.index].isSelected = false
    this.updateSelectedContacts(this.state.selectedContacts)
  }

  searchUpdated(term) {
    let matchedItemsArray = []
    this.searchChar = term
    if (term === "") {
      this.setState({ filteredContacts: this.userContacts })
    } else {
      this.userContacts.map((item) => {
        let userName = item.getFullName()
        let splitedName = userName ? userName.split(" ") : []
        if (splitedName.length > 0) {
          let shouldAdd = false
          for (const index in splitedName) {
            let name = splitedName[index]
            if (name.toLowerCase().startsWith(term.toLowerCase())) {
              shouldAdd = true
              break
            }
          }

          if (userName.toLowerCase().includes(term.toLowerCase()) || item.primaryContact.includes(term)) {
            shouldAdd = true
          }

          if (shouldAdd) {
            matchedItemsArray.push(item)
          }
        }
      })
      this.setState({ filteredContacts: matchedItemsArray })
    }
    if (matchedItemsArray.length > 1) {
      this.searchResult = "Search results"
    } else if (matchedItemsArray.length == 1) {
      this.searchResult = "Search result"
    } else {
      this.searchResult = ""
    }
  }

  _listEmptyComponent() {
    if (this.state.showLoadingIndicator) {
      return null
    }

    if (!this.state.filteredContacts.length > 0 && !this.userContacts.length > 0) {
      return (
        <View style={{ marginLeft: scale(15) }}>
          <TouchableWithoutFeedback
            onPress={() => this.onInviteFriendButtonTap()}>
            <Image
              style={this.imageStyle()}
              source={{ uri: Utils.getPanelURL(Constants.WHY_INVITE_FRIEND_WITH_BUTTON_IMAGE) }}
            />
          </TouchableWithoutFeedback>
        </View>
      )
    }

    if (!this.state.filteredContacts.length > 0) {
      return (
        <View style={{ marginLeft: scale(15) }}>
          <Text style={this.context.p}>No results found</Text>
        </View>
      )
    }

  }

  hideNotification() {
    this.setState({ showInviteFriendMessage: false })
  }

  renderInviteFriendMessage() {
    let isAnyContactSelected = this.state.selectedContacts && this.state.selectedContacts.length > 0
    return (
      <View style={[bannerMsgContainer, { backgroundColor: this.context.colors.bannerBackgroundError }]}>
        <TouchableOpacity
          style={bannerTextView}
          disabled={true}>
          <Text style={[this.context.bodyMini, { color: this.context.colors.bannerTextError }]} numberOfLines={1} adjustsFontSizeToFit>{isAnyContactSelected ? `Earn ${this.diffActionsInfluencePoints.FRIEND_REQUEST_SENDER} influence points for every invite accepted!` : ""}</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={bannerCrossIconView}
          onPress={() => this.hideNotification()}>
          <Image
            style={[bannerCrossIcon, { tintColor: this.context.colors.bannerTextError }]}
            source={require('../../../../assets/close_window.png')}
          />
        </TouchableOpacity>
      </View>
    )
  }

  setLoaderVisibility(value) {
    this.setState({ showLoadingIndicator: value }, () => {
      this.props.setHeaderParams && this.props.setHeaderParams({ isLoading: this.state.showLoadingIndicator })
    })
  }

  getItemLayout(data, index) {
    const itemHeight = scale(51)
    return {
      length: itemHeight,
      offset: itemHeight * index,
      index,
    }
  }

  renderHorizontalList() {
    if (!this.state.selectedContacts || this.state.selectedContacts.length <= 0) {
      return null
    }
    return (
      <HorizontalFriendsList isForRepost={this.props.isForRepost} showSeparator={true} extraData={this.state.shouldRefresh} selectedContacts={this.state.selectedContacts} removeSelectedContact={this.removeSelectedContact} />
    )
  }

  renderItem(item) {
    if (!item.item || Object.keys(item.item).length == 0) {
      return null
    }
    return (
      <InviteFriendsPanel
        isForRepost={this.props.isForRepost}
        searchChar={this.searchChar}
        item={item.item}
        tapOnContactRow={this.tapOnContactRow}
        onContactNamePress={this.onContactNamePress}
        navigateToUserProfile={this.props.navigateToUserProfile}
      />
    )
  }

  render() {
    const { showLoadingIndicator, filteredContacts, shouldRefresh, selectedContacts } = this.state

    return (
      <View style={styles.mainContainer}>
        {!this.props.isForRepost && this.state.showInviteFriendMessage && !SessionManager.instance.hasAnySubscriptionPurchase() && this.renderInviteFriendMessage()}
        {this.renderHorizontalList()}
        <View style={styles.contactsContainer}>
          <SearchBar showCancelIcon={true} searchUpdated={(term) => this.searchUpdated(term)} />
          <View style={styles.listContainer}>
            {this.searchResult != "" && <Text style={[this.context.p, styles.searchResultTest]}>{this.searchResult}</Text>}
            <AlphabetFlatList
              data={filteredContacts}
              keyExtractor={(item, index) => item.primaryContact + item.status + index.toString()}
              renderItem={this.renderItem}
              getItemLayout={this.getItemLayout}
              ListEmptyComponent={this._listEmptyComponent}
              extraData={shouldRefresh}
              inputRef={(ref) => (this._mainList = ref)}
              getMainListRef={this.getMainListRef}
              showSmallText={selectedContacts.length > 0}
            />
          </View>
        </View>
        {showLoadingIndicator && <LoadingIndicator />}
      </View>
    )
  }
}

InviteFriendsPage.contextType = ThemeContext;

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  contactsContainer: {
    flex: 1,
    marginRight: scale(10),
  },
  listContainer: {
    flex: 1,
    marginTop: scale(10),
  },
  searchResultTest: {
    marginBottom: scale(5),
    marginLeft: scale(10),
    width: "100%",
    justifyContent: "center",
  },
  emptyContainer: {
    alignSelf: "center",
  },
  buttonStyle: (color) => {
    return {
      borderRadius: 6,
      backgroundColor: color,
      height: 40,
      borderColor: color,
      elevation: 0,
      borderLeftWidth: 0,
      borderTopWidth: 0,
      borderBottomWidth: 0,
      borderRightWidth: 0,
      borderWidth: 0,
    }
  }
})