import React, { Component } from "react"
import { StyleSheet, SafeAreaView } from "react-native"
import { View } from "native-base"
import FriendsPanel from "../panels/FriendsPanel"
import FriendsSharedList from "../shared/FriendsSharedList"
import { Constants } from "../../../config/Constants"
import FileCache from "../../../config/FileCache"
import { ThemeContext } from "../../../Contexts"
import { navigationStyle } from "../../../config/styles"
import FirebaseManager from "../../../config/FirebaseManager"
import DeepLinkManager from "../../../config/DeepLinkManager"
import { Utils } from "../../../config/Utils"

export default class FriendsListPage extends Component {
  constructor(props) {
    super(props)

    this.state = {
      friends: [],
      shouldRefresh: false,
    }

    let currentUser = FirebaseManager.instance.currentUser()
    this.UID = currentUser.uid
    this.hasMoreData = props.isCurrentUser && !props.isLoggedInUser ? false : true
    this.isAPIInProgress = false
    this.selectedItem = null
    this._unsubscribe = null
    this.bgColor = props.bgColor
    this.refreshData = this.refreshData.bind(this)
    this.unfriend = this.unfriend.bind(this)
    this.removeListData = this.removeListData.bind(this)
    this.updateListData = this.updateListData.bind(this)
    this.updateShouldRefresh = this.updateShouldRefresh.bind(this)
    this.renderItem = this.renderItem.bind(this)
    this.refreshUserFriends = this.refreshUserFriends.bind(this)
    this.sendTextToContacts = this.sendTextToContacts.bind(this)
    this.getContactsToSendRequest = this.getContactsToSendRequest.bind(this)
    this.onSuccessFriendActivity = this.onSuccessFriendActivity.bind(this)
    this.updateFriendRequest = this.updateFriendRequest.bind(this)
  }

  componentDidMount() {
    this._unsubscribe = this.props.navigation.addListener("focus", async () => {
      this.refreshData()
    });
  }

  componentWillUnmount() {
    if (this._unsubscribe) {
      this._unsubscribe()
    }
  }

  refreshData() {
    const { isCurrentUser, isLoggedInUser } = this.props
    if (isCurrentUser && !isLoggedInUser) {
      this.hasMoreData = false
      return
    }

    this.hasMoreData = true
    this.props.setLoaderVisibility(true)
    this.setState({ friends: [] }, () => {
      this.refreshUserFriends()
      this.props.updateFriendsCount()
      this.props.getUserDetails()
    })
  }

  refreshUserFriends() {
    const { friends } = this.state
    const { profileUserID } = this.props

    let lastFriendID = ""
    if (friends && friends[friends.length - 1]) {
      lastFriendID = friends[friends.length - 1].docId
    }
    this.isAPIInProgress = true
    this.props.getFriendList(this.updateListData, true, { startAfter: lastFriendID, records: Constants.FRIENDS_LIST_PAGINATION_COUNT, ...(profileUserID ? { userID: profileUserID } : {}) })
  }

  async sendTextToContacts(receiverData = null) {
    let extraData = {}
    let data = this.props.userDetails

    let sendTextToContactsParams = null
    const contactToSendRequest = receiverData?.userPhoneNumber
    if (contactToSendRequest) {
      const deepLinkURL = await DeepLinkManager.instance.getBranchLinkURL(Constants.ADD_FRIEND, null, contactToSendRequest)
      extraData[contactToSendRequest] = deepLinkURL

      let displayName = data.displayName ? data.displayName : Constants.TINYVIEW_USER
      let fullMessageText = `${Constants.inviteFriendTemplateStart} ${displayName} ${Constants.inviteFriendTemplateEnd}`


      sendTextToContactsParams = {
        body: fullMessageText,
        invitedContacts: [`${contactToSendRequest}`],
        extraMessage: extraData, // will add contact is appContact or non-appContact
      }
    }

    let senderName = (data && data.displayName) ? data.displayName : ''
    let senderProfilePic = (data && data.photoURL) ? data.photoURL : ''
    let senderPhoneNumber = (data && data.phoneNumber) ? data.phoneNumber : ''
    let sendFriendRequestParams = {
      data: {
        requests: this.getContactsToSendRequest(receiverData),
        senderDetails: {
          senderName: senderName,
          senderPhoneNumber: senderPhoneNumber,
          senderProfilePic: senderProfilePic,
          senderUID: this.UID
        },
      },
    }

    this.props.sendFriendRequest(sendFriendRequestParams.data, sendTextToContactsParams, this.onSuccessFriendActivity)
  }

  getContactsToSendRequest(receiverData = null) {
    let requests = []
    let data = {}
    if (receiverData) {
      data = {
        receiverUID: receiverData.uid,
        receiverName: receiverData.fullName,
        receiverPhoneNumber: receiverData.userPhoneNumber,
        receiverProfilePic: receiverData.userProfilePic,
        status: "pending"
      }
    }
    requests.push(data)
    return requests
  }

  updateFriendRequest(data) {
    this.selectedItem = data
    this.props.updateFriendRequest(data, this.onSuccessFriendActivity)
  }

  onSuccessFriendActivity(isSuccess = false, status = null) {
    if (!isSuccess) {
      return
    }

    if (status == Constants.SEND_FRIEND_REQUEST) {
      Utils.showToast("Friend request sent")
    }

    this.refreshData()
  }

  unfriend(data) {
    this.selectedItem = data
    this.props.unfriend(data, this.removeListData)
  }

  removeListData(success = false, status = null) {
    if (success) {
      if (!this.props.isCurrentUser) {
        this.refreshData()
        return
      }
      for (const key in this.state.friends) {
        let id = this.state.friends[key].uid
        if (this.selectedItem.friendUID == id) {
          this.state.friends.splice(key, 1)
          break
        }
      }
      this.updateShouldRefresh(true)

      let data = { friendsCount: this.state.friends.length }
      FileCache.default.writeFile(Constants.ALL_FRIENDS, JSON.stringify(this.state.friends))
      this.props.updateFriendsCount(data)
    }

  }

  updateListData(data) {
    if (data && data.length < Constants.FRIENDS_LIST_PAGINATION_COUNT) {
      this.hasMoreData = false
    }

    if (data) {
      let updatedData = [...this.state.friends, ...data]
      this.setState({ friends: updatedData }, () => {
        this.isAPIInProgress = false
        this.props.setLoaderVisibility(false)
        let data = { friendsCount: this.state.friends.length }
        this.props.updateFriendsCount(data)
      })
    }
  }

  updateShouldRefresh(value) {
    this.setState({ shouldRefresh: value })
  }

  renderItem(item) {
    if (!item.item || Object.keys(item.item).length == 0) {
      return null
    }

    const { friends } = this.state
    const isFirstItem = item.index === 0
    const isLastItem = item.index === friends.length - 1

    let listType = Constants.FRIENDS
    const { friendshipStatus } = item.item
    if (this.UID == item.item.uid) {
      listType = null
    } else if (this.props.isCurrentUser) {
      listType = Constants.FRIENDS.toLowerCase()
    } else if (this.props.profileUserID) {
      listType = friendshipStatus?.status ? friendshipStatus.status : Constants.ADD_FRIEND
    }

    return (
      <View style={[styles.friendsListView(isFirstItem, isLastItem), { backgroundColor: this.context.colors.textInverse }]}>
        <FriendsPanel item={item.item} unfriendUser={this.unfriend} navigateToUserProfile={this.props.navigateToUserProfile} selectedTab={listType} resendFriendRequest={this.props.resendFriendRequest} userDetails={this.props.userDetails} sendTextToContacts={this.sendTextToContacts} updateFriendRequest={this.updateFriendRequest} signInSheet={this.props.signInSheet} />
      </View>
    )
  }

  render() {
    const { friends, shouldRefresh } = this.state
    const { isCurrentUser, isLoggedInUser } = this.props

    return (
      <SafeAreaView style={[styles.mainContainer, { backgroundColor: this.bgColor }]}>
        <FriendsSharedList
          data={friends}
          keyExtractor={(item, index) => item.userPhoneNumber + index.toString()}
          renderItem={this.renderItem}
          extraData={shouldRefresh}
          emptyText={(isCurrentUser && !isLoggedInUser) ? "" : "You don’t have any friends yet"}
          showLoadingIndicator={this.props.showLoadingIndicator}
          setLoaderVisibility={this.props.setLoaderVisibility}
          navigateToInviteBottomSheet={this.props.navigateToInviteBottomSheet}
          hasMoreData={this.hasMoreData}
          isAPIInProgress={this.isAPIInProgress}
          refreshUserFriends={this.refreshUserFriends}
          navigateToSignIn={this.props.navigateToSignIn}
          inviteFriendsSheet={this.props.inviteFriendsSheet}
          isLoggedInUser={isLoggedInUser}
        />
      </SafeAreaView >
    )
  }
}

FriendsListPage.contextType = ThemeContext;
const styles = StyleSheet.create({
  mainContainer: {
    flex: 1
  },
  friendsListView: (isFirstItem, isLastItem) => {
    return {
      borderTopLeftRadius: isFirstItem ? navigationStyle.panelRadius : 0,
      borderTopRightRadius: isFirstItem ? navigationStyle.panelRadius : 0,
      borderBottomLeftRadius: isLastItem ? navigationStyle.panelRadius : 0,
      borderBottomRightRadius: isLastItem ? navigationStyle.panelRadius : 0
    }
  }
})
