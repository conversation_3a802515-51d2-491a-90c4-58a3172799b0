import { Constants } from "../../../config/Constants";
import { settings } from "../../../config/settings";


export default class FriendRequestModal {

    constructor(data, requestType) {
        if (data == null || data == undefined) {
            return;
        }
        if (requestType == settings.RECEIVED.toLowerCase()) {
            this.fullName = data.senderName ? data.senderName : Constants.NON_SIGNED_IN_USER
            this.userProfilePic = data.senderProfilePic
            this.userPhoneNumber = data.senderPhoneNumber
            this.uid = data.senderUID
            this.docId = data.id
            this.status = data.status
            this.senderEmail = data.senderEmail
        } else if (requestType == settings.SENT.toLowerCase()) {
            this.fullName = data.receiverName ? data.receiverName : Constants.NON_SIGNED_IN_USER
            this.userProfilePic = data.receiverProfilePic
            this.userPhoneNumber = data.receiverPhoneNumber
            this.uid = data.receiverUID
            this.docId = data.id
            this.status = data.status
            this.receiverEmail = data.receiverEmail
        }

        this.getFullName = this.getFullName.bind(this)
    }

    getFullName() {
        return this.fullName;
    }
}