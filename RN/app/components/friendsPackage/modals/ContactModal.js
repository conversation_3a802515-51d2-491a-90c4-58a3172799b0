import { Utils } from "../../../config/Utils";

export default class ContactModal {

    constructor(contacts) {
        if (contacts == null || contacts == undefined || (Array.isArray(contacts) && contacts.length <= 0)) {
            return;
        }

        if (Array.isArray(contacts)) {
            const firstContact = contacts[0]

            this.uid = firstContact.uid ? firstContact.uid : null
            this.firstName = firstContact.firstName
            this.lastName = firstContact.lastName
            this.primaryContact = firstContact.phoneNumber ? firstContact.phoneNumber : firstContact.primaryContact
            this.contactType = firstContact.contactType
            this.documentId = firstContact.documentId
            firstContact.status && (this.status = firstContact.status)
        } else {
            this.uid = contacts.uid
            this.firstName = contacts.firstName
            this.lastName = contacts.lastName
            this.primaryContact = contacts.phoneNumber ? contacts.phoneNumber : contacts.primaryContact
            this.uid = contacts.uid
            this.contactType = contacts.contactType
            this.documentId = contacts.documentId
            this.status = contacts.status || null;
        }

        this.profileBGColor = Utils.getRandomColor();
        this.getFullName = this.getFullName.bind(this)
        this.getShortName = this.getShortName.bind(this)
    }

    getFullName() {
        let fullName = null;
        if (this.firstName) {
            fullName = this.firstName
        }
        if (this.lastName) {
            fullName = (fullName ? fullName + " " : "") + this.lastName
        }

        return fullName
    }

    getShortName() {
        let fullName = null;
        if (this.firstName) {
            fullName = this.firstName
        }
        if (this.lastName) {
            fullName = (fullName ? fullName + " " : "") + this.lastName
        }

        if (fullName.length > 8) {
            return fullName.substring(0, 8)
        } else {
            return fullName
        }
    }
}