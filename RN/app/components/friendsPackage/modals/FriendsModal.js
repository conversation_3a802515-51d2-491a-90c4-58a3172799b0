export default class FriendsModal {

  constructor(data) {
    if (data == null || data == undefined) {
      return;
    }

    this.badges = data.badges
    this.fullName = data.name
    this.userProfilePic = data.userProfilePic
    this.userPhoneNumber = data.phoneNumber
    this.primaryContact = data.phoneNumber
    this.uid = data.uid
    this.docId = data.id
    this.status = data.status
    this.friendshipStatus = data.friendshipStatus

    this.getFullName = this.getFullName.bind(this)
    this.getShortName = this.getShortName.bind(this)

  }

  getFullName() {
    return this.fullName;
  }

  getShortName() {
    if (this.fullName.length > 8) {
      return this.fullName.substring(0, 8)
    } else {
      return this.fullName
    }
  }
}