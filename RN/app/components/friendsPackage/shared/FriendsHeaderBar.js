import React, { Component } from "react"
import { StyleSheet, StatusBar, Image, TouchableOpacity, TouchableWithoutFeedback, Platform, SafeAreaView } from "react-native"
import { Text, View } from "native-base"
import { scale } from "react-native-size-matters"
import { navigationStyle } from "../../../config/styles"
import { Color } from "../../../config/Color"
import { SystemFont } from "../../../config/Typography"
import { Utils } from "../../../config/Utils"
import { Constants } from "../../../config/Constants"
import { ThemeContext } from "../../../Contexts"
import SessionManager from "../../../config/SessionManager"

export default class FriendsHeaderBar extends Component {

  onBackPress() {
    if (this.props.navigation && this.props.navigation.getParent()) {
      let leftButtonText = this.props.leftButtonText ? this.props.leftButtonText : this.props.route.params ? this.props.route.params.leftButtonText : null
      if (leftButtonText == Constants.SKIP) {
        this.props.navigation.navigate('Home', { 'forceReload': true })
      } else {        
        if (this.props.isLoginFromInviteLink) {
          SessionManager.instance.setIsInviteLinkLogin(false)
          this.props.navigation.navigate('Home', { 'forceReload': true })
        } else {
          this.props.navigation.getParent().goBack();
        }
      }
    }
  }

  render() {
    const params = this.props.route.params
    let leftButtonText = this.props.leftButtonText ? this.props.leftButtonText : params ? params.leftButtonText : null
    let rightButtonText = this.props.rightButtonText ? this.props.rightButtonText : params ? params.rightButtonText : null
    let title = this.props.title ? this.props.title : params ? params.title : null

    const navigateToSendInvite = params && params.navigateToSendInvite
    const sendTextToContacts = params && params.sendTextToContacts
    const onRightClick = params && params.onRightClick
    const onCenterIconClick = params && params.onCenterIconClick
    const onBackPress = params && params.onBackPress
    const navigateToInviteBottomSheet = params && params.navigateToInviteBottomSheet

    const hasRightButton = Utils.checkData(rightButtonText)
    const rightText = hasRightButton ? rightButtonText : null
    const isRightDisabled = params && params.isRightDisabled
    const isLoading = params && params.isLoading
    const shouldDisableRightButton = isRightDisabled || isLoading

    return (
      <SafeAreaView
        forceInset={{ top: "always", bottom: "never" }}
        style={Platform.OS === 'ios' ? [styles.iosStyle, { backgroundColor: this.context.colors.textInverse }] : [styles.androidStyle, { backgroundColor: this.context.colors.textInverse, marginTop: navigationStyle.androidTopMargin }]}
      >
        <View>
          <StatusBar backgroundColor={this.context.colors.textInverse} barStyle="dark-content" />
        </View>
        <View style={[styles.headerContainer]}>
          <View style={styles.headerItem}>
            <TouchableOpacity
              style={styles.navigationButton}
              onPress={() => {
                onBackPress ? this.onBackPress() : this.props.navigation.goBack()
              }}
              disabled={isLoading}
              transparent
            >
              {leftButtonText && leftButtonText.toLowerCase() == "back" ? (
                <Image style={styles.headerBackIcon(isLoading)} source={require("../../../../assets/back_left_arrow.png")} />
              ) : (
                <View style={{ alignItems: "center", justifyContent: "center" }}>
                  <Text style={isLoading ? styles.disabledText : [styles.text, { color: Color.RED_TEXT_COLOR }]}>{leftButtonText}</Text>
                </View>
              )}
            </TouchableOpacity>

            <View style={{ height: 50, alignItems: "center", justifyContent: "center" }}>
              {Utils.checkData(title) ? (
                <Text style={this.context.h2}>{title}</Text>
              ) : (
                <TouchableWithoutFeedback
                  style={{ alignSelf: "center" }}
                  onPress={() => {
                    onCenterIconClick ? onCenterIconClick() : this.onBackPress()
                  }}>
                  <Image style={styles.headerImage} source={require("../../../../assets/tinyview_header_logo.png")} />
                </TouchableWithoutFeedback>
              )}
            </View>

            {Utils.checkData(rightText) &&
              <TouchableOpacity
                style={styles.navigationButton}
                onPress={() => {
                  if (navigateToSendInvite) navigateToSendInvite()
                  if (sendTextToContacts) sendTextToContacts()
                  if (onRightClick) onRightClick()
                }}
                disabled={shouldDisableRightButton}
                transparent
              >
                <View style={{ alignItems: "center", justifyContent: "center", }}>
                  <Text style={shouldDisableRightButton ? styles.disabledText : [styles.text, { color: Color.RED_TEXT_COLOR }]}>{rightText}</Text>
                </View>
              </TouchableOpacity>
            }
            {!rightText && navigateToInviteBottomSheet &&
              <TouchableOpacity
                style={styles.navigationButton}
                onPress={() => {
                  navigateToInviteBottomSheet && navigateToInviteBottomSheet()
                }}
                disabled={isLoading}
                transparent
              >
                <Image style={styles.headerRightIcon(isLoading)} source={require("../../../../assets/invite_friends_icon.png")} />
              </TouchableOpacity>
            }
            {!navigateToInviteBottomSheet && !rightText &&
              <View style={{ width: scale(30) }} />
            }
          </View>
        </View>
      </SafeAreaView >
    )
  }
}

FriendsHeaderBar.contextType = ThemeContext

const styles = StyleSheet.create({
  headerContainer: {
    flex: 1,
    borderBottomWidth: 0.5,
    borderBottomColor: Color.TOP_NAVBAR_BORDER_BOTTOM_COLOR,
  },
  androidStyle: {
    height: navigationStyle.navHeight,
  },
  iosStyle: {
    height: navigationStyle.iOSNavHeight,
  },
  navigationButton: {
    justifyContent: "center",
    alignItems: "center",
    marginLeft: 10,
    marginRight: 10,
  },
  headerItem: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginLeft: 10,
    marginRight: 10,
  },
  headerBackIcon: (isDisable) => {
    return {
      width: scale(25),
      height: scale(25),
      tintColor: isDisable ? Color.DISABLE_BOTTOM_ICON_TINT_COLOR : Color.TOP_NAVBAR_ICON_TINT_COLOR,
    }
  },
  headerRightIcon: (isDisable) => {
    return {
      width: scale(25),
      height: scale(25),
      tintColor: isDisable ? Color.DISABLE_BOTTOM_ICON_TINT_COLOR : Color.TOP_NAVBAR_ICON_TINT_COLOR,
    }
  },
  headerImage: {
    width: scale(100),
    height: scale(50),
    resizeMode: "contain",
    tintColor: Color.TOP_NAVBAR_ICON_TINT_COLOR,
  },
  text: {
    color: Color.TITLE_COLOR,
    textAlign: "center",
    fontSize: scale(16),
    fontWeight: 'bold',
    fontFamily: SystemFont.SELECTED_FONT,
  },
  disabledText: {
    textAlign: "center",
    fontSize: scale(16),
    color: Color.DISABLE_BOTTOM_ICON_TINT_COLOR,
    fontFamily: SystemFont.SELECTED_FONT,
  },
})
