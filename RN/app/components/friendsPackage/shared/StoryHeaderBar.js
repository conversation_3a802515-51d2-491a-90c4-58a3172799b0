import React, { Component } from "react"
import { StyleSheet, StatusBar, Image, TouchableOpacity, Platform, SafeAreaView } from "react-native"
import { Button, Text, View } from "native-base"
import { scale } from "react-native-size-matters"
import { navigationStyle } from "../../../config/styles"
import { Color } from "../../../config/Color"
import { SystemFont } from "../../../config/Typography"
import { Utils } from "../../../config/Utils"
import FastImage from "react-native-fast-image"
import SessionManager from "../../../config/SessionManager"
import { ThemeContext } from "../../../Contexts"

export default class StoryHeaderBar extends Component {
  
  constructor(props) {
    super(props)

    this.onBackPress = this.onBackPress.bind(this)
  }

  onBackPress() {
    if (this.props.navigation && this.props.navigation.getParent()) {
      this.props.navigation.getParent().goBack();
    }
  }

  render() {
    const params = this.props.route.params

    let rightButtonText = this.props.rightButtonText ? this.props.rightButtonText : params ? params.rightButtonText : null
    let rightIcon = params && params.rightIcon
    let title = this.props.title ? this.props.title : params ? params.title : null

    const onRightClick = params && params.onRightClick
    const onBackPress = params && params.onBackPress
    const navigateToFriendsComponent = params && params.navigateToFriendsComponent

    const hasRightButton = Utils.checkData(rightButtonText)
    const rightText = hasRightButton ? rightButtonText : null
    const isRightDisabled = params && params.isRightDisabled
    const isLoading = params && params.isLoading
    const shouldDisableRightButton = isRightDisabled || isLoading

    const userProfilePicURL = SessionManager.instance.profileImageURL

    return (
      <SafeAreaView
        style={[Platform.OS === 'ios' ? [styles.iosStyle, { backgroundColor: this.context.colors.textInverse }] : styles.androidStyle, this.props.mainContainerStyle]}
      >
        <StatusBar backgroundColor={this.context.colors.textInverse} barStyle="dark-content" />
        <View style={[styles.headerContainer]}>
          <View style={styles.headerItem}>
            <Button
              variant='unstyled'
              style={styles.navigationButton}
              onPress={() => {
                onBackPress ? onBackPress() : this.props.navigation.goBack()
              }}
              disabled={isLoading}
              transparent>
              <Image resizeMode="cover" style={styles.headerBackIcon(isLoading)} source={require("../../../../assets/back_left_arrow.png")} />
            </Button>

            <View style={{ height: 50, alignItems: "center", justifyContent: "center" }}>
              <Text style={[styles.text, { color: this.context.colors.textBold }]}>{title}</Text>
            </View>

            <View style={{ flexDirection: 'row' }}>
              {Utils.checkData(rightText) &&
                <TouchableOpacity
                  style={styles.navigationButton}
                  onPress={() => { if (onRightClick) onRightClick() }}
                  disabled={shouldDisableRightButton}
                  transparent>
                  <View style={{ alignItems: "center", justifyContent: "center", }}>
                    {
                      Utils.checkData(rightIcon)
                        ? <Image style={styles.headerRightIcon(isLoading ? true : shouldDisableRightButton ? true : false)} source={rightIcon} />
                        : <Text style={shouldDisableRightButton ? styles.disabledText : [styles.text, { color: Color.RED_TEXT_COLOR, fontWeight: '500' }]}>{rightText}</Text>
                    }
                  </View>
                </TouchableOpacity>
              }

              {this.props.showProfileImage &&
                <Button
                  variant='unstyled'
                  onPress={() => this.props.navigation.toggleDrawer()}
                  transparent>
                  <View style={styles.profileIcon}>
                    <FastImage resizeMode="cover" style={[styles.avatar, { backgroundColor: this.context.colors.textInverse }]} source={userProfilePicURL ? { uri: userProfilePicURL } : require('../../../../assets/user_icon.png')} />
                    <View style={{ position: 'absolute', alignItems: 'center', bottom: -6, right: -5, justifyContent: 'center', backgroundColor: Color.GREY_999999, width: 20, height: 20, borderRadius: 10, borderWidth: 2, borderColor: this.context.colors.textInverse }}>
                      <Image resizeMode="cover" style={{ width: 8, height: 8, alignSelf: 'center', tintColor: this.context.colors.textInverse }} source={require('../../../../assets/menu.png')} />
                    </View>
                  </View>
                </Button>
              }

              {!navigateToFriendsComponent && !rightText &&
                <View style={{ width: scale(30) }} />
              }
            </View>
          </View>
        </View>
      </SafeAreaView >
    )
  }
}

StoryHeaderBar.contextType = ThemeContext;

StoryHeaderBar.defaultProps = {
  headerContainer: {},
}

const styles = StyleSheet.create({
  headerContainer: {
    flex: 1,
    borderBottomWidth: 0.5,
    borderBottomColor: Color.TOP_NAVBAR_BORDER_BOTTOM_COLOR,
  },
  avatar: {
    width: scale(32),
    height: scale(32),
    borderRadius: scale(16),
    borderWidth: 2,
    borderColor: Color.GREY_999999,
  },
  androidStyle: {
    backgroundColor: Color.TOP_NAVBAR_BACKGROUND_COLOR,
    height: navigationStyle.navHeight,
    marginTop: navigationStyle.androidTopMargin,
  },
  iosStyle: {    
    height: navigationStyle.iOSNavHeight
  },
  navigationButton: {
    justifyContent: "center",
    alignItems: "center",
  },
  text: {
    textAlign: "center",
    fontSize: scale(16),
    fontWeight: 'bold',
    fontFamily: SystemFont.SELECTED_FONT,
  },
  disabledText: {
    textAlign: "center",
    fontSize: scale(16),
    color: Color.GREY_999999,
    fontFamily: SystemFont.SELECTED_FONT,
  },
  headerItem: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginLeft: 10,
    marginRight: 10,
  },
  headerBackIcon: (isDisable) => {
    return {
      width: scale(25),
      height: scale(25),
      tintColor: isDisable ? Color.DISABLE_BOTTOM_ICON_TINT_COLOR : Color.TOP_NAVBAR_ICON_TINT_COLOR,
    }
  },
  headerRightIcon: (isDisable) => {
    return {
      width: scale(25),
      height: scale(25),
      marginLeft: 10,
      tintColor: isDisable ? Color.GREY_999999 : Color.TOP_NAVBAR_ICON_TINT_COLOR,
    }
  },
  profileIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  }
})
