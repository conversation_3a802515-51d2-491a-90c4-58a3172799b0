import React, { Component } from "react"
import { StyleSheet, StatusBar, Image, TouchableOpacity, Platform, SafeAreaView } from "react-native"
import { Text, View } from "native-base"
import { scale } from "react-native-size-matters"
import { navigationStyle } from "../../../config/styles"
import { Color } from "../../../config/Color"
import { SystemFont } from "../../../config/Typography"
import { ThemeContext } from "../../../Contexts"

export default class SigningHeaderBar extends Component {

  render() {
    let leftButtonText = this.props.leftButtonText ? this.props.leftButtonText : this.props.route.params ? this.props.route.params.leftButtonText : null
    let title = this.props.title ? this.props.title : this.props.route.params ? this.props.route.params.title : null

    const onBackPress = this.props.route.params ? this.props.route.params.onBackPress : null
    const isLoading = this.props.route.params ? this.props.route.params.isLoading : null

    return (
      <SafeAreaView        
        style={Platform.OS === 'ios' ? [styles.iosStyle, { backgroundColor: this.context.colors.textInverse,  }] : [styles.androidStyle, { backgroundColor: this.context.colors.textInverse, marginTop: navigationStyle.androidTopMargin }]}>
        <View>
          <StatusBar backgroundColor={this.context.colors.textInverse} barStyle="dark-content" />
        </View>
        <View style={[styles.headerContainer]}>
          <View style={styles.headerItem}>
            <TouchableOpacity
              style={styles.navigationButton}
              onPress={() => {
                onBackPress ? onBackPress() : this.props.navigation.goBack()
              }}
              disabled={isLoading}
              transparent>
              {leftButtonText && leftButtonText.toLowerCase() == "back" ? (
                <Image style={styles.headerBackIcon(isLoading)} source={require("../../../../assets/back_left_arrow.png")} />
              ) : (
                <View style={{ alignItems: "center", justifyContent: "center" }}>
                  <Text style={isLoading ? styles.disabledText : [styles.text, { color: Color.BLACK_COLOR }]}>{leftButtonText}</Text>
                </View>
              )}
            </TouchableOpacity>

            <View style={{ height: 50, alignItems: "center", justifyContent: "center" }}>
              <Text style={this.context.h1}>{title}</Text>
            </View>
            <View style={{ width: scale(30) }} />
          </View>
        </View>
      </SafeAreaView>
    )
  }
}

SigningHeaderBar.contextType = ThemeContext

const styles = StyleSheet.create({
  headerContainer: {
    flex: 1
  },
  iosStyle: {
    height: navigationStyle.iOSNavHeight,
  },
  androidStyle: {
    height: navigationStyle.navHeight,
  },
  navigationButton: {
    justifyContent: "center",
    alignItems: "center",
    marginLeft: 10,
    marginRight: 10,
  },
  headerItem: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginLeft: 10,
    marginRight: 10,
  },
  headerBackIcon: (isDisable) => {
    return {
      width: scale(25),
      height: scale(25),
      tintColor: isDisable ? Color.DISABLE_BOTTOM_ICON_TINT_COLOR : Color.BLACK_COLOR,
    }
  },
  text: {
    color: Color.TITLE_COLOR,
    textAlign: "center",
    fontSize: scale(16),
    fontWeight: 'bold',
    fontFamily: SystemFont.SELECTED_FONT,
  },
  disabledText: {
    textAlign: "center",
    fontSize: scale(16),
    color: Color.DISABLE_BOTTOM_ICON_TINT_COLOR,
    fontFamily: SystemFont.SELECTED_FONT,
  },
})
