import { But<PERSON>, Text, View } from 'native-base';
import React, { Component } from 'react';
import { StyleSheet, FlatList, Dimensions, Image } from 'react-native'
import { scale } from 'react-native-size-matters';
import { Constants } from '../../../config/Constants';
import { ThemeContext } from '../../../Contexts';
import { Utils } from '../../../config/Utils';
import { buyButton } from '../../../config/styles';
import SessionManager from '../../../config/SessionManager';
import { navigationStyle } from '../../../config/styles';

let dimensions = Dimensions.get('window')
export default class FriendsSharedList extends Component {

  constructor(props) {
    super(props)

    this._listEmptyComponent = this._listEmptyComponent.bind(this)
    this.renderEmptyCards = this.renderEmptyCards.bind(this)
    this.renderSeparator = this.renderSeparator.bind(this)
    this._footerPlaceHolderComponent = this._footerPlaceHolderComponent.bind(this)
    this.loadingViewStyle = this.loadingViewStyle.bind(this)
    this._onEndReached = this._onEndReached.bind(this)
  }

  renderSeparator() {
    return (
      <View style={[styles.separatorStyle, { backgroundColor: this.context.colors.chatBubbles }]} />
    );
  }

  imageStyle = (w = 800, h = 600, margin = scale(15)) => {
    let dimensions = Dimensions.get('window')

    let aspectRatio = (dimensions.width - ((margin) * 2)) / w

    return {
      marginTop: 20,
      width: w * aspectRatio,
      height: h * aspectRatio,
      resizeMode: 'contain'
    }
  }

  renderEmptyPanels(icon, title, description, buttonLabel) {
    const isSignInButton = buttonLabel == Constants.SIGN_IN

    return (
      <View style={[styles.emptyPanelOuterView, { backgroundColor: this.context.colors.textInverse }]}>
        <View style={styles.emptyPanelInnerView}>
          <View style={{ marginRight: 20 }}>
            <Image style={[styles.emptyIconView, { tintColor: this.context.colors.textBold }]} source={icon} />
          </View>
          <View style={styles.emptyTextView}>
            <Text style={this.context.pBold}>{title}</Text>
            <Text style={[this.context.p, styles.emptyDescription]}>{description}</Text>
            <View style={styles.emptyButtonView}>
              <Button
                variant='solid'
                style={[buyButton, { backgroundColor: this.context.colors.logoRed, borderColor: this.context.colors.logoRed }]}
                onPress={() => { isSignInButton ? this.props.navigateToSignIn() : this.props.isLoggedInUser ? this.props.navigateToInviteBottomSheet() : this.props.inviteFriendsSheet() }}>
                <Text style={[this.context.p, { color: this.context.colors.textInverse }]}>{buttonLabel}</Text>
              </Button>
            </View>
          </View>
        </View>
      </View>
    )
  }

  renderEmptyCards() {
    const { isLoggedInUser } = this.props
    const diffActionsInfluencePoints = SessionManager.instance.getInfluencePointsValuesForActions()
    const invitePanelDescription = SessionManager.instance.hasAnySubscriptionPurchase() ? 'Know when a friend likes or comments on a comic.' : `Know when a friend likes or comments on a comic. Invite friends and earn ${diffActionsInfluencePoints.FRIEND_REQUEST_SENDER} influence points for every invite accepted!`
    const signInPanelDescription = `Sign in so you can access your account from any device or Web. Earn ${diffActionsInfluencePoints.SIGNUP} influence points.`

    return (
      <View>
        {!isLoggedInUser && this.renderEmptyPanels(require("../../../../assets/signin-red-100.png"), Constants.SIGN_IN, signInPanelDescription, Constants.SIGN_IN)}
        {this.renderEmptyPanels(require("../../../../assets/invite_friends_icon.png"), Constants.INVITE_FRIENDS, invitePanelDescription, Constants.INVITE_FRIENDS)}
      </View>
    )
  }

  _listEmptyComponent() {
    if (this.props.showLoadingIndicator) {
      return null
    }

    return (
      <View>
        {this.props.emptyText &&
          <Text style={[this.context.p, { color: this.context.colors.textBold }]}>{this.props.emptyText}</Text>}
        <Image
          style={this.imageStyle()}
          source={{ uri: Utils.getPanelURL(Constants.WHY_INVITE_FRIEND_IMAGE) }}
        />
        {this.renderEmptyCards()}
      </View>
    )
  }

  _footerPlaceHolderComponent() {
    const { showLoadingIndicator, data } = this.props
    if (!showLoadingIndicator) {
      return
    }

    const isEmptyData = data.length === 0
    return (
      <View style={styles.friendsLoaderView(isEmptyData)}>
        <Image style={this.loadingViewStyle()} source={require('./../../../../assets/friends_loader_view.gif')} />
      </View>
    )
  }

  _onEndReached() {
    if (!this.props.hasMoreData || this.props.isAPIInProgress) {
      return
    }

    this.props.setLoaderVisibility(true)
    this.props.refreshUserFriends()
  }

  render() {
    return (
      <FlatList
        data={this.props.data}
        keyExtractor={this.props.keyExtractor}
        ItemSeparatorComponent={this.renderSeparator}
        extraData={this.props.extraData}
        renderItem={this.props.renderItem}
        ListEmptyComponent={this._listEmptyComponent}
        onEndReached={this._onEndReached}
        ListFooterComponent={this._footerPlaceHolderComponent}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          paddingTop: this.props.emptyText ? 8 : 0,
          paddingBottom: navigationStyle.panelsMargin + navigationStyle.navHeight,
          paddingLeft: navigationStyle.panelsMargin,
          paddingRight: navigationStyle.panelsMargin
        }}
      />
    )
  }

  loadingViewStyle() {
    let width = dimensions.width - 40
    let aspectRatio = width / Constants.LOADING_GIF_DIMEN.width

    return {
      width: width,
      height: 450 * aspectRatio,
      resizeMode: 'contain',
      overflow: 'hidden'
    }
  }
}

FriendsSharedList.contextType = ThemeContext

const styles = StyleSheet.create({
  separatorStyle: {
    height: 1,
    width: "100%",
  },
  emptyPanelInnerView: {
    flexDirection: 'row',
    margin: 20
  },
  emptyIconView: {
    height: 32,
    width: 32
  },
  emptyPanelOuterView: {
    marginTop: 20,
    borderRadius: 12,
    elevation: 12,
  },
  emptyButtonView: {
    marginTop: 12,
    marginBottom: 30
  },
  emptyDescription: {
    marginTop: 4
  },
  emptyTextView: {
    flex: 1
  },
  friendsLoaderView: (isEmptyData) => {
    return {
      marginTop: !isEmptyData ? -8 : 0
    }
  }
})