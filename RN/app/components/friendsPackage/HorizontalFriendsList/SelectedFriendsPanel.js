import React, { Component } from 'react';
import { StyleSheet, Image } from 'react-native'
import { scale } from 'react-native-size-matters';
import { Text, View } from 'native-base';
import { TouchableOpacity } from 'react-native-gesture-handler';
import ImagePlaceHolder from '../../ImagePlaceHolder';
import { userProfilePic } from '../../../config/styles';
import { Utils } from '../../../config/Utils';
import { ThemeContext } from '../../../Contexts';
import FastImage from 'react-native-fast-image';

export default class SelectedFriendsPanel extends Component {

  constructor(props) {
    super(props)

    this.onPanelTap = this.onPanelTap.bind(this)
    this.renderProfileImage = this.renderProfileImage.bind(this)
  }

  onPanelTap() {
    const { item, index } = this.props
    this.props.removeSelectedContact(item, [index])
  }

  render() {
    const { item, index } = this.props
    let isTapDisabled = this.props.removeSelectedContact ? false : true
    const shortName = item.getShortName && item.getShortName()
    const contactType = item.contactType
    return (
      <TouchableOpacity
        disabled={isTapDisabled}
        style={index <= 0 ? styles.mainContainer : styles.mainContainerSpacing}
        onPress={() => this.onPanelTap()}
        transparent>
        <View style={styles.navigationIcons} >
          {this.renderProfileImage(40)}
        </View>
        <Text numberOfLines={1} style={[this.context.p, styles.contact]}>{shortName}</Text>
        <Text numberOfLines={1} style={[this.context.p, styles.contact]}>{contactType ? `(${contactType})` : ``}</Text>
        {!isTapDisabled && <Image style={styles.closeIcon} source={require('../../../../assets/cancel.png')} />}
      </TouchableOpacity>
    )
  }

  renderProfileImage(size = 35) {
    const { item } = this.props
    let fullName = item.firstName + " " + item.lastName;
    if (item.firstName == undefined) {
      fullName = item.fullName ? item.fullName : ''
    }
    const isfirstNameNotANumber = Utils.checkData(item.firstName) && isNaN(item.firstName.charAt(0)) && item.firstName.charAt(0) != '+'
    const islastNameNotANumber = Utils.checkData(item.lastName) && isNaN(item.lastName.charAt(0))

    return (
      Utils.checkData(item.profilePic)
        ?
        <Image source={{ uri: item.profilePic }} resizeMode={'cover'} style={[userProfilePic(size), { borderColor: this.context.colors.chatBubbles }]} />
        : Utils.checkData(fullName) && (isfirstNameNotANumber || islastNameNotANumber || this.props.isForRepost)
          ?
          <ImagePlaceHolder
            backgroundColor={item.profileBGColor}
            textColor={'#FFF'}
            size={scale(size)}
            type={'circle'}>{fullName}</ImagePlaceHolder>
          :
          <View
            style={styles.emptyImageView(item.profileBGColor, size)}>
            <FastImage
              style={styles.imageInnerView(size)}
              tintColor={this.context.colors.textInverse}
              source={require('../../../../assets/white_user_icon.png')}
            />
          </View>
    )
  }
}

SelectedFriendsPanel.contextType = ThemeContext

const styles = StyleSheet.create({
  mainContainer: {
    marginBottom: scale(4),
    marginLeft: scale(10),
    justifyContent: 'center',
    alignItems: 'center',
  },
  mainContainerSpacing: {
    marginBottom: scale(4),
    marginLeft: scale(20),
    justifyContent: 'center',
    alignItems: 'center',
  },
  contact: {
    marginTop: scale(2),
    textAlign: 'left',
  },
  navigationIcons: {
    alignSelf: 'center',
    marginTop: scale(3)
  },
  closeIcon: {
    position: 'absolute',
    top: -3,
    right: 5,
    width: scale(20),
    height: scale(20),
  },
  emptyImageView: (profileBGColor, size) => {
    return {
      width: scale(size),
      height: scale(size),
      overflow: 'hidden',
      borderRadius: scale(size) / 2,
      backgroundColor: profileBGColor,
      justifyContent: 'center'
    }
  },
  imageInnerView: (size) => {
    return {
      height: scale(size / 2),
      width: scale(size / 2),
      alignSelf: 'center'
    }
  }
})