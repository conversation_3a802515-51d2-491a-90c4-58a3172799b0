import React, { Component } from 'react';
import { StyleSheet, FlatList, SafeAreaView } from 'react-native'
import { View } from 'native-base';
import SelectedFriendsPanel from './SelectedFriendsPanel'
import { Color } from '../../../config/Color';
import { scale } from 'react-native-size-matters';

export default class HorizontalFriendsList extends Component {

  constructor(props) {
    super(props)

    this.state = {
      selectedContacts: []
    }

    this.renderSeparator = this.renderSeparator.bind(this)
    this.renderHorizontalPanel = this.renderHorizontalPanel.bind(this)
    this.scrollToEnd = this.scrollToEnd.bind(this)
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.selectedContacts && nextProps.selectedContacts.length > 0) {
      this.setState({ selectedContacts: nextProps.selectedContacts })
    }
  }

  renderSeparator() {
    return (
      <View style={styles.separatorStyle} />
    )
  }

  renderHorizontalPanel(item) {
    if (!item.item.uid) {
      return null
    }
    return (
      <View style={{ alignSelf: 'center', marginTop: scale(10) }} >
        <SelectedFriendsPanel isForRepost={this.props.isForRepost} item={item.item} index={item.index} removeSelectedContact={this.props.removeSelectedContact} />
      </View>
    )
  }

  scrollToEnd() {
    if (this.flatList) {
      this.flatList.scrollToEnd({ animated: true })
    }
  }

  render() {
    const { showSeparator } = this.props

    return (
      <SafeAreaView>
        <FlatList
          horizontal
          ref={ref => this.flatList = ref}
          onContentSizeChange={() => this.scrollToEnd()}
          onLayout={() => this.scrollToEnd()}
          data={this.props.selectedContacts}
          renderItem={this.renderHorizontalPanel}
          keyExtractor={(item, index) => index.toString()}
          extraData={this.props.extraData}
          showsHorizontalScrollIndicator={false}
        />
        {showSeparator && this.renderSeparator()}
      </SafeAreaView>
    )
  }
}

const styles = StyleSheet.create({
  separatorStyle: {
    backgroundColor: Color.PANEL_SEPARATOR_COLOR,
    height: 0.5,
  },
})