import React, { Component } from 'react'
import { Text, StyleSheet, View, TouchableOpacity, Dimensions } from 'react-native'
import { scale } from 'react-native-size-matters'
import { navigationStyle, roundImage, linkIcons, followingButton, socialIcons, buyButton, comicConfigContainer, comicConfigBadgeView, comicConfigIcon, bannerMsgContainer, bannerTextView, bannerCrossIconView, bannerCrossIcon, mainHeaderContainer } from '../config/styles'
import { Color } from '../config/Color';
import HTML, { defaultSystemFonts } from 'react-native-render-html';
import { Utils } from '../config/Utils';
import SessionManager from '../config/SessionManager';
import SelectableText from '../config/SelectableText';
import { Constants } from '../config/Constants';
import FastImage from 'react-native-fast-image'
import { ThemeContext } from '../Contexts.js';
import UserSession from '../config/UserSession'
import ReactionComponent from './reactionUI/ReactionComponent.js';

const dimensions = Dimensions.get('window');
export default class ComicHeaderView extends Component {

  constructor(props) {
    super(props)

    this.getCurrentPageData = this.getCurrentPageData.bind(this)
    this.hasAnySubscription = SessionManager.instance.hasAnySubscriptionPurchase()
    const currentData = this.getCurrentPageData(this.props)
    this.seriesName = Utils.getChannelName(this.props.pathUrl)
    this.bgColor = Utils.getSeriesSpecificStyle(this.seriesName).backgroundColor
    this.remainingFreeComics = -1
    this.isToastMsgShown = false
    this.isTopMessageShown = false
    this.isSeriesQuotaEnabled = false
    this.isComicLocked = false
    this.comicConfig = SessionManager.instance.getComicsConfigList()

    if (!Utils.isTinyviewPage(this.props.pathUrl) || Utils.isChannelURL(this.props.pathUrl)) {  //Free comics calculation for comic and series page.
      let readedFreeComics = UserSession.instance.ReadedFreeComicsCount()
      let monthlyFreeComics = UserSession.instance.getMonthlyMaxComicReadQuota()
      let seriesReadQuota = SessionManager.instance.getSeriesFreeEpisodes(this.seriesName);

      this.remainingFreeComics = monthlyFreeComics - readedFreeComics

      if (monthlyFreeComics === -1 && (seriesReadQuota && seriesReadQuota !== -1)) {
        this.isSeriesQuotaEnabled = true
        const seriesComicReaded = props.currentPageStatus && props.currentPageStatus.seriesReadCount ? props.currentPageStatus.seriesReadCount : 0
        this.remainingFreeComics = seriesReadQuota - seriesComicReaded
      }
    }

    this.state = {
      showNotificationMessage: SessionManager.instance.needsToShowAlertMessage(),
      showSubscribeMessage: true,
      ...currentData
    }

    this.onPanelTap = this.onPanelTap.bind(this)
    this.hidePushNotificationMsg = this.hidePushNotificationMsg.bind(this)
    this.onCommentPress = this.onCommentPress.bind(this)
    this.onFollowPress = this.onFollowPress.bind(this)
    this.renderFollowSeriesMsg = this.renderFollowSeriesMsg.bind(this)
    this.renderWebLinkButton = this.renderWebLinkButton.bind(this)
    this.renderSocialIcon = this.renderSocialIcon.bind(this)
    this.renderSeriesHome = this.renderSeriesHome.bind(this)
    this.renderDateTime = this.renderDateTime.bind(this)
    this.renderSeparator = this.renderSeparator.bind(this)
    this.renderViewsAndFollowers = this.renderViewsAndFollowers.bind(this)
    this.renderHeader = this.renderHeader.bind(this)
    this.renderHomeSeriesComponents = this.renderHomeSeriesComponents.bind(this)
    this.renderComicComponents = this.renderComicComponents.bind(this)
    this.renderSubscribeMsg = this.renderSubscribeMsg.bind(this)
    this.renderButton = this.renderButton.bind(this)
    this.hideSubscribeMessage = this.hideSubscribeMessage.bind(this)
    this.navigateToScreens = this.navigateToScreens.bind(this)
    this.renderManageAlertMsg = this.renderManageAlertMsg.bind(this)
    this.renderComicConfigDesc = this.renderComicConfigDesc.bind(this)
    this.renderUpgradeSubsMsg = this.renderUpgradeSubsMsg.bind(this)
    this.renderCreditsSection = this.renderCreditsSection.bind(this)

    this.pagesRequiredComments = (Utils.isComicURL(props.pathUrl))
  }

  onPanelTap(item) {
    if (item.action) {
      if (item.action == "staticPanel") {
        this.props.share()
      } else if (item.actionType == "website") {
        this.props.recordPageView(item.action, null, item.storyID)
        this.props.openWebView(item.action)
      } else if (item.actionType == "tinyview" && item.action == "share") {
        this.props.onIconPress(Constants.SHARE, item)
      } else if (item.actionType == "tinyview" && item.action == "signup") {
        this.props.navigateToSignUp()
      } else if (item.actionType == "tinyview" && item.action == "restore-purchases") {
        this.props.restorePurchase(false)
      }
      else if (item.action || item.userId) {
        this.props.openChapter(item.action, this.props.item)
      }
    } else if (item.actionType == "appStore") {
      Utils.openStore()
    } else if (item.userId) {
      this.props.navigateToUserProfile(item)
    }
  }

  hidePushNotificationMsg() {
    SessionManager.instance.shownAlertMessage = false
    this.setState({ showNotificationMessage: false })
  }

  hideSubscribeMessage() {
    this.setState({ showSubscribeMessage: false })
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    this.hasAnySubscription = SessionManager.instance.hasAnySubscriptionPurchase()
    if (nextProps.currentPageStatus && JSON.stringify(this.props.currentPageStatus) != JSON.stringify(nextProps.currentPageStatus)) {
      const currentData = this.getCurrentPageData(nextProps)
      this.setState({ ...currentData })
    }
  }

  getCurrentPageData(nextProps) {
    if (nextProps.currentPageStatus) {
      let nextPropsViewCount = nextProps.currentPageStatus.viewCount
      let nextPropsLikeCount = nextProps.currentPageStatus.likeCount
      let nextPropsCommentCount = nextProps.currentPageStatus.commentCount ? nextProps.currentPageStatus.commentCount : 0
      let nextPropsRepostCount = nextProps.currentPageStatus.repostCount ? nextProps.currentPageStatus.repostCount : 0
      let nextPropsStoryID = nextProps.currentPageStatus.storyID ? nextProps.currentPageStatus.storyID : null

      let convertedCommentCount = nextPropsCommentCount == 0 ? 0 : Utils.formatViewCount(nextPropsCommentCount)
      let convertedRepostCount = nextPropsRepostCount == 0 ? 0 : Utils.formatViewCount(nextPropsRepostCount)

      if (nextPropsViewCount == 0) nextPropsViewCount = nextPropsViewCount + 1

      return {
        pageViewCount: Utils.formatViewCount(nextPropsViewCount),
        pageLikeCount: Utils.formatViewCount(nextPropsLikeCount),
        storyID: nextPropsStoryID,
        commentCount: convertedCommentCount,
        repostCount: convertedRepostCount,
        likeCount: nextPropsLikeCount, // numeric value
      }
    }
  }

  onCommentPress(storyId, showToAnonymousUser = true) {
    const { storyID } = this.state
    let storyData = { storyID: storyID, refType: Constants.STORY, action: this.props.pathUrl }
    this.props.onShowCommentPress(Constants.COMMENT, null, null, null, storyData, showToAnonymousUser)
  }

  onFollowPress() {
    const { isSubscribed } = this.props;
    const isFollowEnabled = isSubscribed;
    let seriesURL = this.props.pathUrl;
    const isComicURL = Utils.isComicURL(this.props.pathUrl)
    if (isComicURL) {
      seriesURL = Utils.resolvePath(this.props.pathUrl, Utils.getComicSeriesURL(this.props.pathUrl));
    }
    this.props.onFollowPress(seriesURL, isFollowEnabled, false, false)
  }

  renderFollowSeriesMsg() {
    const { isSubscribed, isComicURL, pathUrl } = this.props;

    const channelName = Utils.getUserVisibleChannelName(pathUrl)
    let message = `Never miss a new comic from ${channelName}!`;
    let isTinyviewSeries = Utils.isTinyviewPage(pathUrl) && !Utils.isChannelURL(pathUrl)
    if (isTinyviewSeries) {
      message = `Never miss an update from ${channelName}!`;
    }
    if ((isSubscribed) || (isSubscribed && (isComicURL)) || this.props.alerts == null) {
      return null;
    }

    return (
      <View>
        <View style={[bannerMsgContainer, styles.notificationMsgContainer, { backgroundColor: this.context.colors.bannerBackgroundError }]}>
          <View
            style={bannerTextView}>
            <Text style={[this.context.bodyMini, { color: this.context.colors.bannerTextError }]} adjustsFontSizeToFit>{message}</Text>
          </View>
          {this.renderButton(Constants.FOLLOW)}
          <TouchableOpacity
            style={bannerCrossIconView}
            onPress={() => this.hidePushNotificationMsg()}>
            <FastImage
              style={bannerCrossIcon}
              tintColor={this.context.colors.bannerTextError}
              source={require('../../assets/close_window.png')}
            />
          </TouchableOpacity>
        </View>
      </View>
    )
  }

  navigateToScreens(navigationScreen) {
    if (navigationScreen == Constants.SIGN_UP) {
      this.props.getUnlimitedComicAccessSheet()
    } else if (navigationScreen == Constants.FOLLOW) {
      this.onFollowPress()
    } else if (navigationScreen == Constants.MANAGE_ALERTS) {
      Utils.navigateToManageAlertsPage()
    } else if (navigationScreen == Constants.UPGRADE) {
      this.props.navigateToSubscribePage()
    }
  }

  renderButton(item) {
    return (
      <TouchableOpacity
        style={[buyButton, styles.buyButton, { backgroundColor: this.context.colors.logoRed, borderColor: this.context.colors.logoRed }]}
        onPress={() => this.navigateToScreens(item)}>
        <Text style={[this.context.bodyMini, { color: this.context.colors.textInverse }]}>{item}</Text>
      </TouchableOpacity>
    )
  }

  renderSubscribeMsg() {
    const { pathUrl, comicData } = this.props
    const { isComicUnlocked, isUserAlreadyReadComic, canUserReadComic, isSharedByPremiumUser } = this.props.comicData

    const isComicAccessWithinTime = Utils.isComicAccessibleWithinTime(comicData.datetime, this.seriesName)
    const shouldHideHeaderForMonthlyLimit = !this.isSeriesQuotaEnabled && (Utils.isTinyviewPage(pathUrl) || this.remainingFreeComics < 0 || this.remainingFreeComics % 2 != 0 || Utils.isPremiumComic(comicData))  //When monthly free comic limit is active.
    const shouldHideHeaderForSeriesLimit = this.isSeriesQuotaEnabled && ((Utils.isTinyviewPage(pathUrl) && !Utils.isChannelURL(pathUrl)) || this.hasAnySubscription || Utils.isPremiumComic(comicData) || isSharedByPremiumUser || (this.remainingFreeComics === 0 && canUserReadComic) || (this.remainingFreeComics < 0 && (canUserReadComic || isComicAccessWithinTime)))  //When series free comic limit is active.
    if (isUserAlreadyReadComic || isComicUnlocked || shouldHideHeaderForMonthlyLimit || shouldHideHeaderForSeriesLimit) {
      return null
    }

    this.isTopMessageShown = true
    const channelName = Utils.getUserVisibleChannelName(pathUrl)
    const freeComicTiming = SessionManager.instance.getSeriesFreeHours(this.seriesName);
    let message = this.isSeriesQuotaEnabled ? `You’ve <b>${this.remainingFreeComics} free</b> comics left for ${channelName}.` : `<b>${this.remainingFreeComics} comics</b> left as a guest user`
    if (!this.isSeriesQuotaEnabled && this.remainingFreeComics === 0 && canUserReadComic) {  //When monthly free limit is active.
      message = `<b>Last comic</b> left as a guest user`
    } else if (this.isSeriesQuotaEnabled && this.remainingFreeComics <= 0 && !canUserReadComic && !isComicAccessWithinTime && !Utils.isPremiumComic(comicData)) {  //When series free limit of active.
      this.isComicLocked = true
      message = `${channelName} comics are for premium users only after <b>${freeComicTiming} hours</b>.`
    }

    const buttonName = this.isSeriesQuotaEnabled ? Constants.UPGRADE : Constants.SIGN_UP

    return (
      <View style={[bannerMsgContainer, styles.notificationMsgContainer, { backgroundColor: this.context.colors.bannerBackgroundError }]}>
        <View style={bannerTextView}>
          <HTML contentWidth={dimensions.width} textSelectable={true} allowFontScaling baseStyle={{ ...this.context.bodyMini, color: this.context.colors.bannerTextError }} source={{ html: message ? message : " " }} imagesMaxWidth={dimensions.width} tagsStyles={{ "b": { ...this.context.bodyMiniBold }, "p": { paddingTop: 0, marginBottom: 0, marginTop: 0 } }} />
        </View>
        {this.renderButton(buttonName)}
        <TouchableOpacity
          style={bannerCrossIconView}
          onPress={this.hideSubscribeMessage}>
          <FastImage
            style={bannerCrossIcon}
            tintColor={this.context.colors.bannerTextError}
            source={require('../../assets/close_window.png')}
          />
        </TouchableOpacity>
      </View>
    )
  }

  renderWebLinkButton(websiteUrl, linkIcon) {
    const formatURL = Utils.getDomainName(websiteUrl)
    return (
      <TouchableOpacity
        variant='solid'
        style={[followingButton, styles.webLinkButton]}
        onPress={() => this.props.openWebView(websiteUrl)}>
        <View style={styles.websiteButtonView}>
          <FastImage source={linkIcon} style={linkIcons} />
          <Text style={[this.context.p, styles.urlTextView]}>{formatURL}</Text>
        </View>
      </TouchableOpacity>
    )
  }

  renderSocialIcon(url, iconSource) {
    return (
      <TouchableOpacity
        style={styles.socMediaButton}
        disabled={this.props.isLoading ? true : false}
        onPress={() =>
          this.props.openWebView(url)}>
        <FastImage style={socialIcons} source={iconSource} />
      </TouchableOpacity>
    )
  }

  renderSeriesHome() {
    const { seriesHomeName, isSubscribed } = this.props
    const isDirectoryPage = Utils.isDirectoryPageURL(this.props.pathUrl)
    const isSubscribePage = Utils.isSubscriptionURL(this.props.pathUrl)

    if (!seriesHomeName && !isDirectoryPage && !isSubscribePage) {
      return null
    }

    var seriesName = seriesHomeName //+ " " + settings.forwardIconText;
    if (isDirectoryPage || isSubscribePage) {
      seriesName = Constants.LETTER_CASE_TINYVIEW
    }

    return (
      <View style={styles.homeButtonContainer}>
        <TouchableOpacity style={{ flex: 1 }} onPress={this.props.onSeriesHomeClicked}>
          <View style={styles.container}>
            <Text style={this.context.h1}>{seriesName}</Text>
          </View>
        </TouchableOpacity>
        <TouchableOpacity style={styles.comicFollowView}
          onPress={() => { this.props.onFollowPress(this.props.pathUrl, isSubscribed, false, false) }}>
          {this.renderFollowButton()}
        </TouchableOpacity>
      </View>
    )
  }

  renderDateTime(datetime) {
    if (!datetime) {
      return null
    }
    const strDate = Utils.getFormattedDate(datetime, Constants.TIME_DATE_FORMAT)
    return (
      <Text style={[this.context.bodyMini, { marginTop: 5 }]}>{strDate}</Text>
    )
  }

  renderSeparator() {
    return (
      <View style={[styles.separatorStyle, { backgroundColor: this.context.colors.separators }]} />
    )
  }

  renderFollowButton() {
    const { isSubscribed, pathUrl } = this.props;
    const isComicPage = Utils.isComicPage(pathUrl)
    const isSubsSeries = isComicPage && isSubscribed
    const buttonName = !isSubscribed ? Constants.FOLLOW : isComicPage ? Constants.EPISODES : Constants.FOLLOWING

    return (
      <TouchableOpacity
        style={[styles.followButtonView, { backgroundColor: isSubscribed ? this.context.colors.textInverse : this.context.colors.logoRed, borderColor: isSubscribed ? this.context.colors.separators : this.context.colors.logoRed }]}
        onPress={() => { isSubsSeries ? this.props.onSeriesHomeClicked() : this.onFollowPress() }}>
        <FastImage
          style={styles.followButtonIcon(isSubsSeries)}
          source={isSubsSeries ? require('../../assets/episodes_list_icon.png') : isSubscribed ? require('../../assets/tick_mark_icon.png') : require('../../assets/plus_icon.png')} />
        <Text style={[this.context.bodyMini, { color: isSubscribed ? this.context.colors.textBold : this.context.colors.textInverse }]}>{buttonName}</Text>
      </TouchableOpacity>
    )
  }

  renderViewsAndFollowers(pageViewCountSuffix, pageViewCount, pageLikeCountSuffix, pageLikeCount) {
    var formattedPageViewCountSuffix = Utils.capitalizeFirstLetter(pageViewCountSuffix)
    return (
      <View style={styles.viewsFollowersStyle}>
        <Text style={this.context.pBold}>{pageViewCount}</Text>
        <Text style={[this.context.p, styles.countText]}>{formattedPageViewCountSuffix}</Text>
        <Text style={[this.context.pBold, { marginLeft: scale(12) }]}>{pageLikeCount}</Text>
        <Text style={[this.context.p, styles.countText]}>{pageLikeCountSuffix}</Text>
      </View>
    )
  }

  renderHomeSeriesComponents() {
    let { pageViewCount, pageLikeCount } = this.state
    let { isComicURL, comicData, currentPageStatus, pathUrl } = this.props
    const { title, description, links } = this.props.comicData

    if (!pageLikeCount) pageLikeCount = '--'
    if (!pageViewCount) pageViewCount = '--'
    if (pageViewCount == 0) pageViewCount = 1

    const pageViewCountSuffix = pageViewCount != 1 ? "Views" : "View"
    const pageLikeCountSuffix = pageLikeCount != 1 ? isComicURL ? "Likes" : "Followers" : isComicURL ? "Like" : "Follower"

    const panelItems = { ...comicData, ...currentPageStatus }

    return (
      <View>
        <View style={[styles.topTextView, styles.homeTopView]}>
          <SelectableText textValue={title} textStyle={[this.context.h1, { flex: 1, marginTop: scale(4) }]} multiline={true} />
          {this.renderFollowButton()}
        </View>
        {this.renderViewsAndFollowers(pageViewCountSuffix, pageViewCount, pageLikeCountSuffix, pageLikeCount)}
        <View style={styles.htmlContainer(description)}>
          <HTML contentWidth={dimensions.width} textSelectable={true} allowFontScaling baseStyle={this.context.p} source={{ html: description ? description : " " }} imagesMaxWidth={dimensions.width} tagsStyles={{ "b": this.context.pBold, "p": { paddingTop: 0, marginBottom: 0, marginTop: 0 } }} />
        </View>
        {Utils.isHomeURL(pathUrl) &&
          <TouchableOpacity
            style={[styles.shareButtonView, { borderColor: this.context.colors.separators }]}
            onPress={() => { this.props.onIconPress(Constants.REPOST, panelItems) }}>
            <FastImage
              style={[styles.shareButtonIcon]}
              source={require('../../assets/share_icon.png')} />
            <Text style={[this.context.p, { color: this.context.colors.textBold }]}>{Constants.SHARE}</Text>
          </TouchableOpacity>}
        {(links && links.website) && this.renderWebLinkButton(links.website, require('../../assets/web_link_icon.png'))}
        {links && (links.instagram || links.facebook || links.twitter) &&
          <View style={styles.socialIconView}>
            {links.instagram && this.renderSocialIcon(links.instagram, require('../../assets/instagram_icon.png'))}
            {links.facebook && this.renderSocialIcon(links.facebook, require('../../assets/facebook_icon.png'))}
            {links.twitter && this.renderSocialIcon(links.twitter, require('../../assets/white_x_icon.png'))}
          </View>
        }
      </View>
    )
  }

  renderComicConfigDesc() {
    const { pathUrl, comicData } = this.props
    const { 'show-to': freemium, rating } = comicData;
    let isComicFree = !freemium || freemium === Constants.EVERYONE
    let isComicforEveryone = !rating || rating === Constants.ALL_AGES

    if (!this.isComicLocked && (Utils.isEmptyObject(this.comicConfig) || (isComicFree && isComicforEveryone))) {
      return null;
    }

    const freemiumConfig = this.comicConfig['show-to'][Constants.SUBSCRIBERS_ONLY]
    const ageRatingConfig = this.comicConfig.rating[rating]
    const premiumComicIconUrl = Utils.resolvePath(pathUrl, freemiumConfig.icon)

    return (
      <View style={[comicConfigContainer, styles.comicBadgeView]}>
        {(!isComicFree || this.isComicLocked) &&
          <View style={[comicConfigBadgeView, styles.comicConfigView, { marginRight: 12, backgroundColor: this.context.colors.bannerBackgroundError }]}>
            <FastImage
              style={comicConfigIcon}
              source={{ uri: premiumComicIconUrl }} />
            <Text style={[this.context.bodyMini, styles.freemiumTextView, { color: this.context.colors.textBold }]}>{freemiumConfig.label}</Text>
          </View>
        }
        {!isComicforEveryone &&
          <View style={[comicConfigBadgeView, styles.comicConfigView, { backgroundColor: this.context.colors.bannerBackgroundError }]}>
            <Text style={[this.context.bodyMini, { color: this.context.colors.textBold }]}>{ageRatingConfig.label}</Text>
          </View>
        }
      </View>
    )
  }

  renderCreditsSection() {
    const { pathUrl } = this.props
    const seriesCredits = SessionManager.instance.getSeriesCredits(this.seriesName)
    if (!Utils.isComicPage(pathUrl) || !seriesCredits) {
      return null
    }

    return (
      seriesCredits.map((item, index) => {
        const [key, value] = Object.entries(item)[0];
        return (
          <View style={styles.comicCreditsView}>
            <Text style={[this.context.comments, { color: this.context.textBold }]}>{key}:
              <Text style={this.context.h2}> {value}</Text>
            </Text>
          </View>
        );
      })
    )
  }

  renderComicComponents() {
    const { pathUrl } = this.props
    const { title, comments, datetime } = this.props.comicData

    const isComicPage = Utils.isComicPage(pathUrl)
    const isLiked = this.props.currentPageStatus ? this.props.currentPageStatus.isLiked : false

    return (
      <View>
        <View style={styles.topTextView}>
          {this.renderSeriesHome()}
          {this.renderCreditsSection()}
          {isComicPage && this.renderComicConfigDesc()}
        </View>
        <View style={styles.comicTitleView}>
          <SelectableText textValue={title} textStyle={[this.context.h2, { paddingTop: 0 }]} multiline={true} />
        </View>
        {this.renderDateTime(datetime)}
        <View style={styles.htmlContainer(comments)}>
          <HTML contentWidth={dimensions.width} textSelectable={true} allowFontScaling baseStyle={this.context.comments} source={{ html: comments ? comments : " " }} imagesMaxWidth={dimensions.width} tagsStyles={{ "b": styles.HTMLBoldContainerStyle, "p": { paddingTop: 0, marginBottom: 0, marginTop: 0 } }} systemFonts={[...defaultSystemFonts, this.context.comments.fontFamily]} />
        </View>
        {!isComicPage &&
          <View>
            {this.renderSeparator()}
            <View style={styles.comicReactions}>
              <ReactionComponent shoudHideCommentButton={!this.pagesRequiredComments} onCountPress={this.props.onCountPress} currentPageStatus={this.props.currentPageStatus} isComicURL={this.props.isComicURL} isFollowing={isLiked} onIconPress={this.props.onIconPress} onShowCommentPress={this.props.onShowCommentPress} panelItems={this.props.comicData} storyID={this.state.storyID} onCommentPress={this.onCommentPress} onLikePress={this.props.onLikePress} isLiked={isLiked} hideReactionBar={isComicPage} />
            </View>
          </View>
        }
      </View>
    )
  }

  renderManageAlertMsg() {
    const { isSubscribed } = this.props
    let isAnyAlertsEnabled = UserSession.instance.isAnyUserAlertsEnabled()
    if (isAnyAlertsEnabled || !isSubscribed) {
      return;
    }

    let message = Constants.MANAGE_ALERTS_ACTIONSHEET_TITLE;
    return (
      <View>
        <View style={[bannerMsgContainer, styles.notificationMsgContainer, { backgroundColor: this.context.colors.bannerBackgroundError }]}>
          <View
            style={bannerTextView}>
            <Text style={[this.context.bodyMini, { color: this.context.colors.bannerTextError }]} adjustsFontSizeToFit>{message}</Text>
          </View>
          {this.renderButton(Constants.MANAGE_ALERTS)}
          <TouchableOpacity
            style={bannerCrossIconView}
            onPress={() => this.hidePushNotificationMsg()}>
            <FastImage
              style={bannerCrossIcon}
              tintColor={this.context.colors.bannerTextError}
              source={require('../../assets/close_window.png')}
            />
          </TouchableOpacity>
        </View>
      </View>
    )
  }

  renderUpgradeSubsMsg() {
    const { isSubscribed, pathUrl } = this.props
    let isAnyAlertsEnabled = UserSession.instance.isAnyUserAlertsEnabled()
    if (this.hasAnySubscription || Utils.isSubscriptionURL(pathUrl) || !isAnyAlertsEnabled || !isSubscribed) {
      return;
    }

    let message = Constants.UPGRADE_SUBSCRIPTION_TITLE_MESSAGE;
    return (
      <View>
        <View style={[bannerMsgContainer, styles.notificationMsgContainer, { backgroundColor: this.context.colors.bannerBackgroundError }]}>
          <View
            style={bannerTextView}>
            <Text style={[this.context.bodyMini, { color: this.context.colors.bannerTextError }]} adjustsFontSizeToFit>{message}</Text>
          </View>
          {this.renderButton(Constants.UPGRADE)}
          <TouchableOpacity
            style={bannerCrossIconView}
            onPress={() => this.hidePushNotificationMsg()}>
            <FastImage
              style={bannerCrossIcon}
              tintColor={this.context.colors.bannerTextError}
              source={require('../../assets/close_window.png')}
            />
          </TouchableOpacity>
        </View>
      </View>
    )
  }

  renderHeader() {
    if ((this.props.errorRes && this.props.error.urlToCheck == this.props.pathUrl) || Utils.isEmptyObject(this.props.comicData)) {
      return null
    }

    const isSeriesUrl = Utils.isChannelURL(this.props.pathUrl)
    const { isComicURL } = this.props
    const { ogImage } = this.props.comicData
    const coverImageUrl = Utils.resolvePath(this.props.pathUrl, ogImage)
    let seriesData = SessionManager.instance.getSeriesData(this.seriesName)
    let profileImageUrl = Utils.resolvePath(this.props.pathUrl, seriesData.profile)

    return (
      <View>
        {this.state.showSubscribeMessage && this.renderSubscribeMsg()}
        {(this.state.showNotificationMessage && !this.isTopMessageShown) && this.renderFollowSeriesMsg()}
        {(this.state.showNotificationMessage && !this.isTopMessageShown) && this.renderManageAlertMsg()}
        {(this.state.showNotificationMessage && !this.isTopMessageShown) && this.renderUpgradeSubsMsg()}
        <View>
          <FastImage
            style={this.imageStyle()}
            resizeMode='contain'
            source={{ uri: coverImageUrl, cache: 'web' }}
          />
          <TouchableOpacity
            onPress={!isSeriesUrl ? this.props.onSeriesHomeClicked : null}
            disabled={isSeriesUrl}
            style={styles.profileImageView}>
            <FastImage
              style={[roundImage, styles.roundImage, { borderColor: this.context.colors.textInverse }]}
              source={{ uri: profileImageUrl, cache: 'web' }} />
          </TouchableOpacity>
        </View>
        <View style={[styles.headerView, { backgroundColor: this.bgColor }]}>
          <View style={[mainHeaderContainer, { backgroundColor: this.context.colors.textInverse }]}>
            {!isComicURL && this.renderHomeSeriesComponents()}
            {isComicURL && this.renderComicComponents()}
          </View>
        </View>
      </View>
    )
  }

  render() {
    return (
      this.renderHeader()
    )
  }

  imageStyle = (w = dimensions.width, h = 210) => {
    return {
      width: w,
      aspectRatio: 40 / 21
    }
  }
}

ComicHeaderView.contextType = ThemeContext;

const styles = StyleSheet.create({
  roundImage: {
    width: scale(68),
    height: scale(68),
    borderRadius: scale(34),
    borderWidth: scale(3),
    overflow: "hidden",
    marginLeft: scale(32),
    marginTop: scale(-20)
  },
  homeButtonContainer: {
    flexDirection: 'row',
    marginTop: 8,
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  container: {
    flexDirection: 'row',
  },
  textContainer: {
    alignSelf: 'center'
  },
  htmlContainer: (comicComments) => {
    return {
      backgroundColor: 'transparent',
      marginTop: 12,
      marginBottom: comicComments ? 20 : 0
    }
  },
  notificationMsgContainer: {
    flex: 1
  },
  webLinkButton: {
    paddingTop: 10,
    paddingBottom: 10,
  },
  followButton: (isComicURL) => {
    return {
      height: isComicURL ? 23 : 40,
      marginLeft: 10
    }
  },
  countText: {
    alignSelf: 'center',
    marginLeft: 4,
  },
  socMediaButton: {
    marginLeft: 12,
    marginRight: 12
  },
  separatorStyle: {
    height: 1,
    width: '100%'
  },
  topTextView: {
    marginTop: scale(20)
  },
  homeTopView: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  socialIconView: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 20,
    marginBottom: 23
  },
  viewsFollowersStyle: {
    flexDirection: 'row',
    marginTop: 12,
    alignItems: 'center'
  },
  HTMLBoldContainerStyle: {
    color: '#3D3D3D',
  },
  websiteButtonView: {
    flexDirection: 'row',
    justifyContent: 'center'
  },
  urlTextView: {
    marginLeft: 8
  },
  headerView: {
    marginTop: scale(-58),
    zIndex: -1
  },
  comicFollowView: {
    flexDirection: 'row',
    marginTop: 3
  },
  comicReactions: {
    marginBottom: 10
  },
  profileImageView: {
    zIndex: 1,
    marginRight: dimensions.width - scale(100)
  },
  buyButton: {
    height: 25,
    marginLeft: 10,
    marginRight: 8,
    position: 'relative'
  },
  crossIconView: {
    marginLeft: 12
  },
  comicBadgeView: {
    marginTop: 10,
    marginBottom: -10
  },
  comicConfigView: {
    marginTop: 16
  },
  freemiumTextView: {
    marginLeft: 4
  },
  followButtonView: {
    height: 32,
    marginLeft: 10,
    paddingLeft: 8,
    paddingRight: 8,
    borderRadius: 6,
    borderWidth: 1,
    flexDirection: 'row',
    alignSelf: 'flex-end',
    alignItems: 'center'
  },
  followButtonIcon: (isEpisButton) => {
    return {
      height: isEpisButton ? 16 : 12,
      width: isEpisButton ? 16 : 12,
      marginRight: 4
    }
  },
  shareButtonView: {
    borderWidth: 1,
    borderRadius: 8,
    paddingTop: 10,
    paddingBottom: 10,
    marginBottom: 20,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row'
  },
  shareButtonIcon: {
    height: 20,
    width: 20,
    marginRight: 8
  },
  comicTitleView: {
    marginTop: 16,
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  comicCreditsView: {
    marginTop: 8
  }
})
