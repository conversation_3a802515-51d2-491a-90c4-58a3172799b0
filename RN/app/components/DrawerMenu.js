import React from "react"
import PropTypes from "prop-types"
import { Platform, FlatList, Linking, Image, StyleSheet, Alert, BackHandler, AppState, TouchableOpacity, TouchableWithoutFeedback } from "react-native"
import { Text, View, HStack } from "native-base"
import { scale } from "react-native-size-matters"
import { settings } from "../config/settings"
import { Constants } from "../config/Constants"
import FirebaseManager from "../config/FirebaseManager"
import { SharedPreferences } from "../config/SharedPreferences"
import IAPManager from "../config/IAPManager"
import DeepLinkManager from "../config/DeepLinkManager"
import { connect } from "react-redux"
import { restorePurchase, openWebViewAction, signOutUser, hasNotificationPermission, getUserDetails, deleteAccount, validateLastSubscription, getStripePortalLink, getFriendList, getUnlockComicURLs, shareInviteLink, addInfluencePoint, getUserNotification, setNotificationAlerts } from "../redux/actions/actions"
import { Utils } from "../config/Utils"
import { Color } from "../config/Color"
import { StackActions } from "@react-navigation/native"
import FileCache from "../config/FileCache"
import { navigationStyle } from "../config/styles"
import FastImage from 'react-native-fast-image'
import SessionManager from "../config/SessionManager"
import BadgesView from "./BadgesView"
import UserSession from "../config/UserSession"
import NavigationService from "../config/NavigationService"
import { ThemeContext } from "../Contexts"
import { Ajax } from "../api/ajax"
import ShareBottomSheet from "./ShareBottomSheet"
import ActionSheet from "./actionSheet/ActionSheetCustom"
import NetworkUtils from "../config/NetworkUtils"
import { getDrawerStatusFromState } from "@react-navigation/drawer"
import { SafeAreaView } from "react-native-safe-area-context"
import moment from "moment";

class DrawerMenu extends React.Component {
  constructor(props) {
    super(props)
    this.isAnonymous = true;
    this.configBottomSheet = ''
    this.getLastPurchasedSubsStore = null
    this.manageSubscriptionKey = null
    this.manageSubscriptionIcon = null
    this.connections = {
      key: Constants.FRIENDS,
      icon: require("../../assets/friends_icon_menu.png"),
      routeName: "",
      subMenus: [
        { key: Constants.INVITE_FRIENDS, componentToRender: Constants.INVITE_FRIENDS, routeName: "FriendsComponent", icon: require("../../assets/invite_friends_icon.png") },
        { key: Constants.SEE_FRIENDS, componentToRender: Constants.FRIENDS, routeName: "FriendsComponent", icon: require("../../assets/friends_icon_menu.png") },
        { key: Constants.REQUESTS, componentToRender: Constants.REQUESTS, routeName: "FriendsComponent", icon: require("../../assets/alert_icon.png") },
      ],
    },

      this.signout = {
        key: Constants.SIGN_OUT,
        routeName: "",
        icon: require("../../assets/signout_icon.png")
      },

      this.influencePoints = {
        key: Constants.INFLUENCE_POINTS,
        routeName: "Influence",
        icon: require("../../assets/tinyview_influence_points_icon.png"),
      }

    this.settings = {
      key: Constants.SETTINGS,
      icon: require("../../assets/settings.png"),
      routeName: "",
      subMenus: [
        { key: Constants.MANAGE_ALERTS, routeName: '', icon: require('../../assets/notifications_icon.png') },
        { key: Constants.CLEAR_CACHED_IMAGES, routeName: "", icon: require("../../assets/clear_cache.png") }
      ],
    }


    this.updateManageSubsDetails()
    this.state = {
      profilePicURL: null,
      lastFourMobileNoDigit: null,
      showSubMenus: false,
      clickedItem: null,
      showInviteBottomSheet: false,
      sheetMessage: Constants.INVITE_FRIENDS_ACTION_SHEET_MESSAGE,
      sheetOptions: [Constants.SIGN_IN, Constants.CANCEL],
      cancelOptionIndex: 1,
      userUnreadNotifCount: UserSession.instance.getUnreadNotifications(),
      isUpdateAlertActive: false,
      navigationItems: [
        {
          key: Constants.ACCOUNT,
          icon: require("../../assets/account.png"),
          routeName: "",
          isAccountMenu: true,
          subMenus: [
            { key: Constants.MY_PROFILE, routeName: Constants.USER_PROFILE_SCREEN, icon: require("../../assets/account.png") },
            { key: Constants.MANAGE_YOUR_PLAN, routeName: 'Home', icon: SessionManager.instance.hasAnySubscriptionPurchase() ? require('../../assets/premium_plan_icon.png') : require('../../assets/free_plan_icon.png'), link: settings.getSubscribeURL() },
            { key: this.manageSubscriptionKey, routeName: '', icon: this.manageSubscriptionIcon },
            { key: Constants.RESTORE_PURCHASE, routeName: '', icon: require("../../assets/restore_iap.png") },
          ],
        },
        this.influencePoints,
        this.connections,
        this.settings,
        {
          key: Constants.LEGAL,
          icon: require("../../assets/legal_icon.png"),
          routeName: "",
          subMenus: [
            { key: Constants.TERMSOFUSE, routeName: "", icon: require("../../assets/terms_and_conditions.png") },
            { key: Constants.PRIVACYPOLICY, routeName: "", icon: require("../../assets/privacy_policy.png") },
          ],
        },
        { key: Constants.CONTACT_US, routeName: "", icon: require("../../assets/mail_icon.png") },
        this.signout,
      ],
    }

    this.fetchUserInfoRetryCount = 0
    this.isDrawerOpen = false
    this.friendsNavPath = null

    this.isSubMenuParentTapped = false
    this.checkIsItemShownInSubMenus = this.checkIsItemShownInSubMenus.bind(this)
    this.updateUserData = this.updateUserData.bind(this)
    this.onProfileImageTap = this.onProfileImageTap.bind(this)
    this.checkIsItemShown = this.checkIsItemShown.bind(this)
    this.signOut = this.signOut.bind(this)
    this.fetchUserInfoForNewUser = this.fetchUserInfoForNewUser.bind(this)
    this.handleBackButtonClick = this.handleBackButtonClick.bind(this)
    this.onAppStateChange = this.onAppStateChange.bind(this)
    this.onListItemSelected = this.onListItemSelected.bind(this)
    this.createEmail = this.createEmail.bind(this)
    this.openManageSubscriptionInBrowser = this.openManageSubscriptionInBrowser.bind(this)
    this.navigateToAppropriateScreen = this.navigateToAppropriateScreen.bind(this)
    this.switchSendboxMode = this.switchSendboxMode.bind(this)
    this.getCurrentAppVersion = this.getCurrentAppVersion.bind(this)
    this.onClearCache = this.onClearCache.bind(this)
    this.checkUserSubscription = this.checkUserSubscription.bind(this)
    this.showAlertToSubscriber = this.showAlertToSubscriber.bind(this)
    this.deleteAccountAlert = this.deleteAccountAlert.bind(this)
    this.getCancelSubsLink = this.getCancelSubsLink.bind(this)
    this.onDeleteAccountTap = this.onDeleteAccountTap.bind(this)
    this.showHideSubMenus = this.showHideSubMenus.bind(this)
    this.renderInviteActionSheet = this.renderInviteActionSheet.bind(this)
    this.closeBottomSheet = this.closeBottomSheet.bind(this)
    this.onActionSheetPress = this.onActionSheetPress.bind(this)
    this.onTitleTap = this.onTitleTap.bind(this)
    this.updateNotificationAlerts = this.updateNotificationAlerts.bind(this)
    this.notifyAppUpdateAlert = this.notifyAppUpdateAlert.bind(this)
    this.checkPurchasedStoreAndOpen = this.checkPurchasedStoreAndOpen.bind(this)
    this.checkIfNeedToReloadHomePage = this.checkIfNeedToReloadHomePage.bind(this)
  }

  UNSAFE_componentWillMount() {
    BackHandler.addEventListener("hardwareBackPress", this.handleBackButtonClick)
  }

  componentDidMount() {
    this.appListener = AppState.addEventListener("change", this.onAppStateChange)
    this.drawerListener = this.props.navigation.addListener('state', () => {
      this.isDrawerOpen = getDrawerStatusFromState(this.props.navigation.getState()) === 'open'
    });
    Utils.updateInstalledAppList()

    Ajax.fetchAndSaveComic(settings.DIRECTORY_DETAILS_URL, false)
      .then((res) => {
        if (res && !Utils.isEmptyObject(res)) {
          for (const iterator in res) {
            delete res[iterator].description
            delete res[iterator].action
          }

          SessionManager.instance.updateSeriesDetails(res)
        }
      })

    if (UserSession.instance.isLoggedInUser) {
      this.props.getFriendList(null, true)
    }

    this.props.getUnlockedComics()
    this.props.getUserNotification({ countOnly: 1 })
    this.updateNotificationAlerts()
    this.notifyAppUpdateAlert()
  }

  updateManageSubsDetails() {
    this.getLastPurchasedSubsStore = SessionManager.instance.getLastPurchasedData() ? SessionManager.instance.getLastPurchasedData().store : null
    if (this.getLastPurchasedSubsStore) {
      this.manageSubscriptionKey = this.getLastPurchasedSubsStore === Constants.STRIPE ? Constants.STRIPE_SUBSCRIPTIONS : (this.getLastPurchasedSubsStore === Constants.APPLE) ? Constants.APPLE_SUBSCRIPTIONS : Constants.GOOGLE_SUBSCRIPTIONS
      this.manageSubscriptionIcon = this.getLastPurchasedSubsStore === Constants.STRIPE ? require('../../assets/stripe_icon.png') : (this.getLastPurchasedSubsStore === Constants.APPLE) ? require('../../assets/apple_icon.png') : require('../../assets/google_icon.png')
    } else {
      this.manageSubscriptionKey = Platform.OS === 'ios' ? Constants.APPLE_SUBSCRIPTIONS : Constants.GOOGLE_SUBSCRIPTIONS
      this.manageSubscriptionIcon = Platform.OS === 'ios' ? require('../../assets/apple_icon.png') : require('../../assets/google_icon.png')
    }
  }

  notifyAppUpdateAlert() {
    try {
      if (this.state.isUpdateAlertActive) {
        return
      }

      const appVersionsUrl = Utils.resolvePath(settings.apiBaseURL, settings.getAppVersionsURL());
      Ajax.getJSONFile(appVersionsUrl, false).then(async (appVersionsData) => {
        if (Utils.isEmptyObject(appVersionsData)) {
          return
        }

        const versions = appVersionsData.latestAppInfo && appVersionsData.latestAppInfo.versions
        let currVersion = parseFloat(settings.appVersion).toFixed(2)

        const currVersionIndex = versions.findIndex(
          (versionData) => {
            const platformVersion = Platform.OS === 'ios' ? versionData.iosVersion : versionData.androidVersion;
            return parseFloat(platformVersion) === parseFloat(currVersion);
          }
        );

        if (currVersionIndex < 1) {
          return
        }

        this.setState({ isUpdateAlertActive: true })
        let showForceUpdate = false
        for (const key in versions) {
          if (parseInt(key) === currVersionIndex) {
            break
          }

          if (versions[key].isForceUpdate) {
            showForceUpdate = true
            break
          }
        }

        const alertTitle = showForceUpdate ? Constants.MANDATORY_UPDATE_ALERT_TITLE : Constants.OPTIONAL_UPDATE_ALERT_TITLE
        const alertButtons = showForceUpdate ? [{ text: Constants.UPDATE_THE_APP, onPress: () => { this.setState({ isUpdateAlertActive: false }); Utils.openStore() } }]
          : [{ text: Constants.UPDATE_THE_APP, onPress: () => { this.setState({ isUpdateAlertActive: false }); Utils.openStore() } }, { text: Constants.NOT_NOW, onPress: () => { this.setState({ isUpdateAlertActive: false }) } }];
        const alertMessage = (versions[0] && versions[0].message) ? versions[0].message : ""

        if (!showForceUpdate) {
          const showAlert = await Utils.shouldShowOptionalAlert()
          if (!showAlert) {
            return;
          }
        }

        Alert.alert(alertTitle, alertMessage, alertButtons, { cancelable: false })
      }).catch((error) => {
        console.log("Error in fetching app versions JSON " + error)
      })
    } catch (error) {
      console.log("App Update error " + error)
    }
  }

  isDrawerOpen = () => {
    useDrawerStatus() === 'open';
  }

  componentDidUpdate() {
    if (!this.isDrawerOpen && this.state.showSubMenus && !this.isSubMenuParentTapped) {
      this.showHideSubMenus(true)
    }

    let firebaseInstance = FirebaseManager.instance
    this.isAnonymous = firebaseInstance.isUserAnonymous()

    const lastPurchase = SessionManager.instance.getLastPurchasedData() ? SessionManager.instance.getLastPurchasedData().store : null
    const isPurchaseStoreChanged = this.getLastPurchasedSubsStore !== lastPurchase
    if (isPurchaseStoreChanged) {
      this.updateManageSubsDetails()
    }

    if (this.isAnonymous != null && this.isAnonymous) {

      this.state.navigationItems.splice(0, 1, { ...this.state.navigationItems[0], key: Constants.ACCOUNT })

      if (!this.checkIsItemShown(2, Constants.FRIENDS))
        this.state.navigationItems.splice(2, 0, this.connections)

      if (this.checkIsItemShown(1, Constants.ACCOUNT))
        this.state.navigationItems.splice(1, 1, this.influencePoints)

      if (SessionManager.instance.hasAnySubscriptionPurchase()) {
        if (this.checkIsItemShown(1, Constants.INFLUENCE_POINTS)) {
          this.state.navigationItems.splice(1, 1)
        }
        if (this.checkIsItemShown(2, Constants.FRIENDS)) {
          this.state.navigationItems.splice(2, 1)
        }
      }

      if (!SessionManager.instance.hasAnySubscriptionPurchase()) {
        if (!this.checkIsItemShown(1, Constants.INFLUENCE_POINTS)) {
          this.state.navigationItems.splice(1, 1, this.influencePoints)
        }
      }

      if (isPurchaseStoreChanged) {
        this.state.navigationItems[0].subMenus.splice(1, 1, { key: this.manageSubscriptionKey, routeName: '', icon: this.manageSubscriptionIcon })
      }

      const menuLength = this.state.navigationItems.length
      if (this.checkIsItemShown(menuLength - 1, Constants.SIGN_OUT))
        this.state.navigationItems.splice(menuLength - 1, 1)

      if (this.checkIsItemShownInSubMenus(0, 0, Constants.MANAGE_YOUR_PLAN)) this.state.navigationItems[0].subMenus.splice(0, 1, { key: Constants.MANAGE_YOUR_PLAN, routeName: 'Home', icon: SessionManager.instance.hasAnySubscriptionPurchase() ? require('../../assets/premium_plan_icon.png') : require('../../assets/free_plan_icon.png'), link: settings.getSubscribeURL() },)

      if (this.checkIsItemShownInSubMenus(0, 1, Constants.EDIT_PROFILE)) this.state.navigationItems[0].subMenus.splice(1, 1)

      if (this.checkIsItemShownInSubMenus(0, 1, this.manageSubscriptionKey))
        this.state.navigationItems[0].subMenus.splice(1, 1)

      let subMenusLength = this.state.navigationItems[0].subMenus.length
      if (this.checkIsItemShownInSubMenus(0, subMenusLength - 1, Constants.ACCOUNT_DELETE)) this.state.navigationItems[0].subMenus.splice(subMenusLength - 1, 1)

    } else {

      if (this.checkIsItemShown(0, Constants.SIGN_IN))
        this.state.navigationItems.splice(0, 1)


      if (!this.checkIsItemShown(2, Constants.FRIENDS)) this.state.navigationItems.splice(2, 0, this.connections)
      this.state.navigationItems.splice(0, 1, { ...this.state.navigationItems[0], key: `${Constants.ACCOUNT}` })

      if (SessionManager.instance.hasAnySubscriptionPurchase()) {
        if (this.checkIsItemShown(1, Constants.INFLUENCE_POINTS)) {
          this.state.navigationItems.splice(1, 1)
        }
        if (this.checkIsItemShown(2, Constants.FRIENDS)) {
          this.state.navigationItems.splice(2, 1)
        }
        if (!this.checkIsItemShown(1, Constants.FRIENDS)) {
          this.state.navigationItems.splice(1, 0, this.connections)
        }
      }

      if (!this.checkIsItemShownInSubMenus(0, 1, Constants.EDIT_PROFILE))
        this.state.navigationItems[0].subMenus.splice(1, 0, { key: Constants.EDIT_PROFILE, routeName: Constants.DOB_SCREEN, icon: require("../../assets/edit_profile_icon.png") })

      let subMenusLength = this.state.navigationItems[0].subMenus.length
      if (!this.checkIsItemShownInSubMenus(0, subMenusLength - 1, Constants.ACCOUNT_DELETE))
        this.state.navigationItems[0].subMenus.push({ key: `${Constants.ACCOUNT_DELETE} ${UserSession.instance.getUserLastFourMobileNoDigit()}`, routeName: "", icon: require("../../assets/delete_account.png") })

      let menuLengths = this.state.navigationItems.length
      if (!this.checkIsItemShown(menuLengths - 1, Constants.SIGN_OUT))
        this.state.navigationItems.splice(menuLengths, 0, this.signout)

      if (this.checkIsItemShownInSubMenus(0, 2, Constants.MANAGE_YOUR_PLAN))
        this.state.navigationItems[0].subMenus.splice(2, 1, { key: Constants.MANAGE_YOUR_PLAN, routeName: 'Home', icon: SessionManager.instance.hasAnySubscriptionPurchase() ? require('../../assets/premium_plan_icon.png') : require('../../assets/free_plan_icon.png'), link: settings.getSubscribeURL() },)

      if (this.checkIsItemShownInSubMenus(0, 2, this.manageSubscriptionKey))
        this.state.navigationItems[0].subMenus.splice(2, 1)

      if (isPurchaseStoreChanged) {
        this.state.navigationItems[0].subMenus.splice(3, 1, { key: this.manageSubscriptionKey, routeName: '', icon: this.manageSubscriptionIcon })
      }
    }
  }

  checkIsItemShown(index, keyValue) {
    return this.state.navigationItems[index].key.includes(keyValue)
  }

  checkIsItemShownInSubMenus(navIndex, subMenuIndex, keyValue) {
    return this.state.navigationItems[navIndex].subMenus[subMenuIndex].key.includes(keyValue)
  }

  signOut(pathUrl) {
    this.props.signOutUser(pathUrl, (isSuccess) => {
      if (isSuccess) {
        this.fetchUserInfoForNewUser()
      }
    })
  }

  fetchUserInfoForNewUser() {
    let params = { comicHome: settings.getComicHomeURL(), hasUserInfo: true }
    NavigationService.resetStackAndNavigate(Constants.HOME, params)
    this.addAppInstallInfluencePoint()
  }

  async addAppInstallInfluencePoint() {
    let isInfluencePointsGiven = await SharedPreferences.getData(settings.INFLUENCE_POINTS_SP_KEY)
    if (isInfluencePointsGiven != "true") {
      this.props.addInfluencePoint({ action: settings.INFLUENCE_POINTS_ACTIONS.INSTALL_APP }, (isSuccess) => {
        if (isSuccess) {
          this.props.getUserDetails(null)
        }
      })
    }
  }

  onProfileImageTap() {
    this.navigateToAppropriateScreen()
    setTimeout(() => {
      this.props.navigation.closeDrawer()
    }, 1000);
  }

  componentWillUnmount() {
    BackHandler.removeEventListener("hardwareBackPress", this.handleBackButtonClick)
    if (this.appListener) {
      this.appListener.remove()
    }
  }

  handleBackButtonClick() {
    if (this.isDrawerOpen) {
      this.props.navigation.closeDrawer()
      this.showHideSubMenus(true)
      return true
    }
    return false
  }

  onAppStateChange(nextAppState) {
    if (nextAppState === "background") {
      DeepLinkManager.instance.appLastState = nextAppState
      SessionManager.instance.updateBGStayTime()
    }
    if (nextAppState === "background" && this.isDrawerOpen) {
      try {
        this.props.navigation.closeDrawer()
        this.showHideSubMenus(true)
      } catch (error) {
        Utils.log("error in " + error)
      }
    } else if (nextAppState === "active") {
      if (FirebaseManager.instance.isUserAnonymous()) {
        try {
          FirebaseManager.instance.currentUser().reload()
        } catch (error) { }
      }

      this.checkIfNeedToReloadHomePage()
      this.updateNotificationAlerts()
      this.notifyAppUpdateAlert()
    }
  }

  checkIfNeedToReloadHomePage() {
    try {
      const lastBGTime = SessionManager.instance.getBGStayTime()
      if (moment().diff(lastBGTime, "m") >= settings.feedResetTime) { //Take user to the home page and refresh the feed.
        let currentRoute = NavigationService.getCurrentRoute()
        if (currentRoute && currentRoute == Constants.HOME) {
          this.props.navigation.navigate('Home', { forceReload: true })
        }
      }
    } catch (error) {

    }

  }

  async updateNotificationAlerts() {
    let currentRoute = NavigationService.getCurrentRoute()
    if (currentRoute == Constants.MANAGE_ALERTS_SCREEN) {
      return;
    }

    let hasDeterminedPermission = true
    if (Platform.OS == "ios") {
      hasDeterminedPermission = await FirebaseManager.instance.determinedNotificationStatus()
    } else {
      let hasShownPermissionAlert = await SharedPreferences.getData(settings.SHOW_NOTIFICATION_PERMISSION_ALERT_KEY)
      hasDeterminedPermission = hasShownPermissionAlert == 'true'
    }

    let hasLocalPermission = await FirebaseManager.instance.hasNotificationPermission()
    var notifAlerts = JSON.parse(JSON.stringify(UserSession.instance.getCurrentUserNotifAlerts()))
    if (hasDeterminedPermission && !hasLocalPermission && notifAlerts && notifAlerts['push'] && notifAlerts['push'].enabled == true) {
      notifAlerts['push'].enabled = false;
      this.props.setNotificationAlerts({ "notificationSettings": notifAlerts })
    }
  }

  onListItemSelected(item) {
    this.props.navigation.closeDrawer()
    this.showHideSubMenus(true)
    const self = this
    setTimeout(() => {
      if (item.item.key == Constants.CONTACT_US) {
        this.createEmail()
      } else if (item.item.key == this.manageSubscriptionKey) {
        this.checkPurchasedStoreAndOpen(this.getLastPurchasedSubsStore)
      } else if (item.item.key == Constants.RESTORE_PURCHASE) {
        this.props.restorePurchase(false)
      } else if (item.item.key == Constants.TERMSOFUSE) {
        this.props.openWebView(settings.termsAndConditionUrl)
      } else if (item.item.key == Constants.SIGN_OUT) {
        this.signOut(self.props.pathUrl)
      } else if (item.item.key == Constants.PRIVACYPOLICY) {
        this.props.openWebView(settings.privacyPolicyUrl)
      } else if (item.item.key.includes(Constants.ACCOUNT_DELETE)) {
        let hasSubs = SessionManager.instance.hasAnySubscriptionPurchase()
        if (hasSubs) {
          this.checkUserSubscription()
        } else {
          this.deleteAccountAlert()
        }
      } else if (item.item.key == Constants.INFLUENCE_POINTS) {
        this.navigateToInfluenceScreen()
      }
      else if (item.item.key == Constants.CLEAR_CACHED_IMAGES) {
        this.onClearCache()
      } else if (item.item.key == Constants.INVITE_FRIENDS) {
        let isSignedIn = UserSession.instance.isLoggedInUser()
        if (isSignedIn) {
          this.configBottomSheet = Constants.INVITE_FRIENDS
          this.setState({ showInviteBottomSheet: true })
        } else {
          this.friendsNavPath = Constants.INVITE_FRIEND
          this.setState({ sheetMessage: Constants.INVITE_FRIENDS_ACTION_SHEET_MESSAGE }, () => {
            this.actionSheet.show()
          })
        }
      } else if (item.item.key == Constants.ACCOUNT || item.item.key == Constants.USER_CONTACT || item.item.key == Constants.DateOfBirth || item.item.key == Constants.SIGN_IN || item.item.key == Constants.SIGN_UP) {
        let params = {}
        if (item.item.key == Constants.SIGN_IN || item.item.key == Constants.SIGN_UP) {
          params = { isForLoginProcess: item.item.key == Constants.SIGN_IN ? true : false }
        }

        Utils.navigateToDrawerLoginRoute(this.props, item.item.routeName, params)

      } else if (item.item.key == Constants.SEE_FRIENDS || item.item.key == Constants.REQUESTS) {
        let params = { componentToRender: item.item.componentToRender }
        this.props.navigation.push(Constants.FRIENDS_COMPONENT, { ...params })
      } else if (item.item.key == Constants.MY_PROFILE) {
        this.navigateToAppropriateScreen()
      } else if (item.item.key == Constants.EDIT_PROFILE) {
        const isConnected = NetworkUtils.instance.isAvailable()
        if (!isConnected) {
          return Utils.showError(Constants.INTERNET_ERROR)
        }
        Utils.navigateToDrawerLoginRoute(this.props, Constants.EDIT_PROFILE_SCREEN, { isupdatingProfile: true })
      } else if (item.item.key == Constants.MANAGE_ALERTS) {
        Utils.navigateToManageAlertsPage(this.props)
      } else {
        let urlParam = null
        if (item.item.link) {
          urlParam = Utils.resolvePath(settings.apiBaseURL, item.item.link)
        }

        if (item.item.key == Constants.HOME) {
          this.props.navigation.dispatch(StackActions.popToTop())
        } else if (self.props.pathUrl != item.item.link) {
          self.props.navigation.push(item.item.routeName, { comicHome: item.item.link, hasUserInfo: true })
        }
      }
    }, 100)
  }

  updateUserData(data) {
    if (data) {
      this.setState({
        profilePicURL: data.photoURL,
        displayName: data.displayName
      })
    }
  }

  closeBottomSheet() {
    this.friendsNavPath = null
    this.setState({ showInviteBottomSheet: false })
  }

  renderInviteActionSheet() {
    const valueProps = { closeBottomSheet: this.closeBottomSheet, userDetails: this.props.userDetails, shareInviteLink: this.props.shareInviteLink }
    return (
      <ShareBottomSheet {...valueProps} configShareSheetFor={this.configBottomSheet} />
    )
  }

  async createEmail() {
    const toEmail = settings.supportEmail
    const firbaseInstance = FirebaseManager.instance
    const user = firbaseInstance.currentUser()
    const userToken = firbaseInstance.getFirebaseToken()
    const signedInUserData = await SharedPreferences.getData(settings.SIGNED_IN_USER_DATA)
    let data = { uid: user.uid, token: userToken, platform: Platform.OS, appVersion: this.getCurrentAppVersion() }
    if (signedInUserData) {
      data = { ...data, signedInUserData: JSON.parse(signedInUserData) }
    }

    let branchParams = DeepLinkManager.instance.branchParams
    if (branchParams == null) {
      branchParams = {}
    }

    let des = " \n\n\n\n\n -- Do not write below this line -- \n\n " + JSON.stringify(data) + "\n\n" + JSON.stringify(branchParams)
    des = des.replace("=", "")
    if (Platform.OS == "ios") {
      Linking.openURL(`mailto:${toEmail}?subject=${encodeURI("[App Contact Us]")}&body=${encodeURI(des)}`)
    } else {
      Linking.openURL("mailto:" + toEmail + "?subject=[App Contact Us] &body=" + des)
    }
  }

  openManageSubscriptionInBrowser() {
    Linking.openURL(settings.manageSubscriptionsUrl).catch((err) => Utils.error("Couldn't load page", err))
  }

  navigateToInfluenceScreen = async () => {
    let currentRoute = NavigationService.getCurrentRoute()
    if (currentRoute && currentRoute != Constants.MY_INFLUENCE_COMPONENT) {
      let routeName = Constants.MY_INFLUENCE_COMPONENT
      this.props.navigation.push(routeName)
    } else if (!currentRoute) {
      FirebaseManager.instance.recordError(null, new Error("currentRoute value is null in Drawer Menu"), "Method : navigateToInfluenceScreen")
    }
  }

  async navigateToAppropriateScreen() {
    const isConnected = NetworkUtils.instance.isAvailable()
    if (!isConnected) {
      return Utils.showError(Constants.INTERNET_ERROR)
    }

    this.props.navigation.closeDrawer()

    let isLoggedInUser = UserSession.instance.isLoggedInUser()
    let routeName = Constants.USER_PROFILE_SCREEN
    let params = { isupdatingProfile: true }
    let data = this.props.userDetails

    if (!data.displayName) {
      routeName = Constants.EDIT_PROFILE_SCREEN
    }

    if (!isLoggedInUser || routeName === Constants.USER_PROFILE_SCREEN) {
      const currentRoute = NavigationService.getCurrentRoute();
      const isUserProfileScreen = currentRoute === Constants.USER_PROFILE_SCREEN;

      if (!isUserProfileScreen) {
        this.props.navigation.push(Constants.USER_PROFILE_SCREEN, { ...params });
        return;
      }

      const clickedUserID = NavigationService.getCurrentNavigator()?.params?.clickedUserID;
      if (clickedUserID) {
        this.props.navigation.push(Constants.USER_PROFILE_SCREEN, { ...params });
      }
    } else {
      Utils.navigateToDrawerLoginRoute(this.props, routeName, params);
    }
  }

  async switchSendboxMode() {
    await SharedPreferences.saveData(settings.IAP_PURCHASE_MODE_KEY, "" + !IAPManager.default.getSendboxMode())
    const iapProductionMode = await IAPManager.default.updateSendboxMode()

    if (!iapProductionMode) {
      Alert.alert("", "Sendbox mode Activated")
    } else {
      Alert.alert("", "Sendbox mode Deactivated")
    }
  }

  getCurrentAppVersion() {
    return settings.appVersion + " (" + settings.buildVersion + ")"
  }

  onClearCache() {
    Alert.alert("", "Do you want to clear all cached images?", [
      ,
      {
        text: "No",
      },
      {
        text: "Yes",
        onPress: () => {
          FileCache.default
            .delete()
            .then(() => {
              Alert.alert("", "Cached images have been cleared")
            })
            .catch((error) => {
              Alert.alert("", "No cached images to clear")
            })
        },
        style: "default",
      },
    ])
  }

  checkUserSubscription() {
    this.props.validateLastSubscription((hasValidSubscription, purchasedStore = null) => {
      if (hasValidSubscription) {
        this.showAlertToSubscriber(purchasedStore)
      } else {
        this.deleteAccountAlert()
      }
    })
  }

  showAlertToSubscriber(subsPurchasedStore) {
    const getLastSubscription = SessionManager.instance.getLatestSubscription()
    const Message = `You are a premium user - ${getLastSubscription} level. Please cancel your subscription first.`

    Alert.alert("", Message, [
      {
        text: "Cancel",
      },
      {
        text: this.manageSubscriptionKey,
        onPress: () => {
          this.checkPurchasedStoreAndOpen(subsPurchasedStore)
        },
        style: "default",
      },
    ])
  }

  deleteAccountAlert() {
    Alert.alert("", Constants.DELETE_ACCOUNT_ALERT, [
      {
        text: "Cancel",
      },
      {
        text: "Delete Account",
        onPress: () => {
          this.onDeleteAccountTap()
        },
        style: "default"
      },
    ])
  }

  checkPurchasedStoreAndOpen(subsPurchasedStore) {
    if (subsPurchasedStore && subsPurchasedStore.toLowerCase() == Constants.PAYMENT_MODE_STRIPE) {
      this.props.getStripePortalLink(this.getCancelSubsLink)
    } else {
      this.openManageSubscriptionInBrowser()
    }
  }

  getCancelSubsLink(pathUrl) {
    if (pathUrl) {
      Linking.openURL(pathUrl)
    }
  }

  onDeleteAccountTap() {
    this.props.deleteAccount((isDeleted) => {
      if (isDeleted) {
        Utils.navigateToDeleteAccountRoute(this.props, "AccountDeletedScreen")
      }
    })
  }

  onActionSheetPress(index) {
    if (index == 0) {
      if (this.state.sheetOptions[0] == Constants.SIGN_IN) {
        const params = { isForLoginProcess: true, postSignInFriendsPath: this.friendsNavPath }
        this.closeBottomSheet()
        Utils.navigateToDrawerLoginRoute(this.props, Constants.LOGIN_SCREEN, params)
      }
    } else if (index == 1) {
    }
  }

  showHideSubMenus(value, item = null) {
    this.isSubMenuParentTapped = true
    this.setState({ showSubMenus: !value, clickedItem: item })

    setTimeout(() => {
      this.isSubMenuParentTapped = false
    }, 2000)
  }

  onTitleTap() {
    if (!UserSession.instance.isLoggedInUser()) {
      this.props.navigation.closeDrawer()
      const params = { isForLoginProcess: true }
      Utils.navigateToDrawerLoginRoute(this.props, Constants.LOGIN_SCREEN, params)
    }
  }

  renderMenuItems(item) {
    let isLoggedInUserDetailsEmpty = UserSession.instance.isLoggedInUser() && UserSession.instance.isUserDetailsEmpty()
    return (
      <TouchableOpacity style={{ flex: 1, flexDirection: 'row', justifyContent: 'flex-start', marginBottom: scale(20) }} onPress={() => (item.item.subMenus ? this.showHideSubMenus(this.state.showSubMenus, item) : this.onListItemSelected(item))}>
        <Image style={styles.navigationIcons} source={item.item.icon} />
        <View style={{ flex: 0.8, flexDirection: 'row', alignSelf: 'center' }}>
          <Text style={[this.context.p, styles.label]}>{item.item.key}</Text>
          {isLoggedInUserDetailsEmpty && (item.item.isAccountMenu || item.item.key == Constants.MY_PROFILE) && <Image style={styles.emptyProfileIcon} source={require('../../assets/filled_exclamation_inside_circle_icon.png')} />}
        </View>
        {(item.item.subMenus || item.item.key == Constants.INFLUENCE_POINTS) && (
          <Image style={styles.navigationIcons} source={require("../../assets/white_right_arrow.png")} />
        )}
      </TouchableOpacity>
    )
  }

  render() {
    const { navigationItems, clickedItem } = this.state
    const { photoURL, displayName, badges, phoneNumber } = this.props.userDetails
    let lastFourMobileNoDigit = UserSession.instance.getUserLastFourMobileNoDigit()
    let userEmailAddress = UserSession.instance.getUserEmailAddress()
    let isLoggedInUserDetailsEmpty = UserSession.instance.isLoggedInUser() && UserSession.instance.isUserDetailsEmpty()
    let isLoggedInUser = UserSession.instance.isLoggedInUser()
    let userDisplayNameValue = isLoggedInUser ? displayName ? displayName.trim() : isLoggedInUserDetailsEmpty ? lastFourMobileNoDigit : Constants.NON_SIGNED_IN_USER : Constants.NON_SIGNED_IN_USER

    return (
      <SafeAreaView style={{ flex: 1, marginLeft: scale(20), }} >
        <View style={{ marginTop: navigationStyle.navHeight }}>
          <TouchableOpacity
            onPress={() => this.props.navigation.closeDrawer()}>
            <FastImage style={styles.closeIconView} source={require('../../assets/close_window.png')} />
          </TouchableOpacity>
          <TouchableWithoutFeedback
            onPress={this.onProfileImageTap}
            onLongPress={this.switchSendboxMode}
            delayLongPress={settings.SWITCH_SENDBOX_MODE_TIME}>
            <Image style={[styles.avatar, { backgroundColor: this.context.colors.textInverse, borderColor: photoURL ? this.context.colors.chatBubbles : 'transparent' }]} source={photoURL ? { uri: photoURL } : require('../../assets/black_outline_user_icon.png')} />
          </TouchableWithoutFeedback>
          <View style={{ marginRight: scale(30) }}>
            <TouchableOpacity
              style={{ flexDirection: 'row' }}
              onPress={this.onProfileImageTap}>
              <Text numberOfLines={1} style={this.context.h2}>{userDisplayNameValue}</Text>
              {<BadgesView badges={badges} />}
            </TouchableOpacity>
          </View>
          {isLoggedInUser && !isLoggedInUserDetailsEmpty &&
            <View>
              {Utils.checkData(phoneNumber) && <Text style={this.context.p}>{phoneNumber}</Text>}
              {Utils.checkData(userEmailAddress) && <Text style={[this.context.p, { marginTop: Utils.checkData(phoneNumber) ? 2 : 0 }]}>{userEmailAddress}</Text>}
            </View>
          }
          {isLoggedInUserDetailsEmpty &&
            <TouchableOpacity
              style={{ marginTop: 10 }}
              onPress={() => { this.navigateToAppropriateScreen() }}>
              <Text style={[this.context.p, { textDecorationLine: 'underline', color: this.context.colors.logoRed }]}>{Constants.COMPLETE_PROFILE}</Text>
            </TouchableOpacity>
          }
          {!isLoggedInUser &&
            <>
              <View style={{ marginTop: 12 }}>
                <TouchableOpacity
                  disabled={UserSession.instance.isLoggedInUser()}
                  onPress={this.onTitleTap}>
                  <Text numberOfLines={1} style={this.context.h2}>{Constants.SIGN_IN}</Text>
                </TouchableOpacity>
              </View>
            </>
          }
        </View>
        <View style={styles.bodyContainer}>
          {this.state.showSubMenus &&
            <TouchableOpacity
              style={styles.backContainer}
              onPress={() => this.showHideSubMenus(this.state.showSubMenus)} >
              <Image style={styles.back_icon} source={require("../../assets/back_left_arrow.png")} />
              <Text style={[this.context.p, styles.backLabel]}>Back</Text>
            </TouchableOpacity>
          }
          <FlatList
            data={this.state.showSubMenus && clickedItem.item ? clickedItem.item.subMenus : navigationItems}
            renderItem={(item) => this.renderMenuItems(item)}
            keyExtractor={(item, index) => index.toString()}
          />
        </View>
        <HStack justifyContent="center">
          <Text style={this.context.bodyMini}>v{this.getCurrentAppVersion()}</Text>
        </HStack>
        <ActionSheet
          ref={o => this.actionSheet = o}
          message={this.state.sheetMessage}
          options={this.state.sheetOptions}
          cancelButtonIndex={this.state.cancelOptionIndex}
          tintColor={this.context.colors.logoRed}
          onPress={(index) => { this.onActionSheetPress(index) }}
          useNativeDriver={true}
          styles={{ titleText: this.context.h2, messageText: this.context.p, messageBox: { paddingTop: 20 } }}
        />
        {this.state.showInviteBottomSheet && this.renderInviteActionSheet()}
      </SafeAreaView >
    )
  }
}

const mapStateToProps = (state) => {
  return {
    isAnonymous: state.loginInfo.isAnonymous,
    userDetails: state.loginInfo.userDetails,
    pathUrl: state.readComic.pathUrl,
    hasNotifPermission: state.readComic.hasNotificationPermission
  }
}

const mapDispatchToProps = (dispatch) => {
  return {
    restorePurchase(checkCurrentPageIAPs) {
      dispatch(restorePurchase(checkCurrentPageIAPs))
    },
    openWebView(url) {
      dispatch(openWebViewAction(url))
    },
    signOutUser(pathUrl, callback) {
      dispatch(signOutUser(pathUrl, callback))
    },
    deleteAccount(callback) {
      dispatch(deleteAccount(callback))
    },
    validateLastSubscription(callback) {
      dispatch(validateLastSubscription(callback))
    },
    getUserDetails(callback) {
      dispatch(
        getUserDetails(callback)
      )
    },
    hasNotificationPermission() {
      dispatch(
        hasNotificationPermission()
      )
    },
    getStripePortalLink(callback) {
      dispatch(
        getStripePortalLink(callback)
      )
    },
    getFriendList(callback, forceLoad) {
      dispatch(
        getFriendList(callback, forceLoad)
      )
    },
    getUnlockedComics() {
      dispatch(
        getUnlockComicURLs()
      )
    },
    shareInviteLink(requestedData, callback) {
      dispatch(
        shareInviteLink(requestedData, callback)
      )
    },
    addInfluencePoint(requestedData, callback) {
      dispatch(
        addInfluencePoint(requestedData, callback)
      )
    },
    getUserNotification(requestedData) {
      dispatch(
        getUserNotification(requestedData)
      )
    },
    setNotificationAlerts(data) {
      dispatch(
        setNotificationAlerts(data)
      )
    }
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(DrawerMenu)

DrawerMenu.contextType = ThemeContext

const styles = StyleSheet.create({
  navigationIcons: {
    width: scale(22),
    height: scale(22),
    tintColor: Color.DRAWER_MENU_ICON_TINT_COLOR,
  },
  bodyContainer: {
    marginTop: 50,
    flex: 1
  },
  backContainer: {
    marginBottom: scale(15),
    justifyContent: "center",
    alignItems: "center",
    alignSelf: "flex-start",
    flexDirection: "row"
  },
  back_icon: {
    width: scale(25),
    height: scale(25),
    tintColor: Color.DRAWER_MENU_ICON_TINT_COLOR,
  },
  backLabel: {
    marginLeft: scale(10),
  },
  avatar: {
    width: scale(80),
    height: scale(80),
    borderRadius: scale(40),
    borderWidth: 0.5
  },
  label: {
    marginLeft: scale(10)
  },
  rightLabel: {
    color: "#000000",
  },
  footerContainer: {
    elevation: 0,
    borderColor: "transparent",
    alignItems: "center",
    backgroundColor: "transparent",
    height: scale(40),
  },
  closeIconView: {
    height: 20,
    width: 20,
    alignSelf: 'flex-end',
    marginRight: 20,
    marginBottom: 5
  },
  emptyProfileIcon: {
    height: 16,
    width: 16,
    marginLeft: 8,
    alignSelf: 'center'
  }
})

DrawerMenu.propTypes = {
  navigationItems: PropTypes.arrayOf(
    PropTypes.shape({
      key: PropTypes.string,
      routeName: PropTypes.string,
      icon: PropTypes.string,
    })
  ),
}
