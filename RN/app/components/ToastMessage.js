import { ThemeContext } from "../Contexts"
import { Text, View } from "native-base";
import React, { useContext } from 'react'
import { StyleSheet } from "react-native";

export default ToastMessage = (props) => {
    const context = useContext(ThemeContext)

    return (
        <View style={[styles.toastView, { backgroundColor: context.colors.toastBackground }]}>
            <View style={styles.toastTextView}>
                {props.title &&
                    <Text style={[context.p, { textAlign: 'center', color: context.colors.toastText }]}>{props.title}</Text>}
                <Text style={[context.p, { textAlign: 'center', color: context.colors.toastText }]}>{props.message}</Text>
            </View>
        </View>
    )
}

const styles = StyleSheet.create({
    toastView: {
        alignContent: 'center',
        justifyContent: 'center',
        borderRadius: 8,
        marginBottom: 35,
        marginLeft: 5,
        marginRight: 5
    },
    toastTextView: {
        paddingBottom: 10,
        paddingTop: 10,
        paddingRight: 14,
        paddingLeft: 14
    }
})

