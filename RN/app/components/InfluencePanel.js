import React, { useContext } from 'react'
import { View, StyleSheet, Dimensions } from 'react-native'
import { Text, Button } from 'native-base';
import { navigationStyle } from '../config/styles'
import { Color } from '../config/Color';
import { scale } from 'react-native-size-matters';
import { connect } from 'react-redux';
import { Constants } from '../config/Constants';
import UserSession from '../config/UserSession';
import FastImage from 'react-native-fast-image'
import { ThemeContext } from '../Contexts';
import HTML from 'react-native-render-html';
import UserSession from '../config/UserSession';

let dimensions = Dimensions.get('window');
const InfluencePanel = (props) => {

	const context = useContext(ThemeContext);
	const influencePoints = UserSession.instance.getUserInfluencePoints();

	const navigateToScreens = (navigationScreen) => {
		if (navigationScreen == Constants.SUBSCRIBE) {
			props.navigateToSubscribePage();
		} else if (navigationScreen == Constants.EARN_INFLUENCE_POINTS) {
			props.navigateToInfluenceScreen();
		} else if (navigationScreen == Constants.INVITE_FRIENDS) {
			props.navigateToInviteBottomSheet();
		} else {
			purchasingComic()
		}
	};

	const purchasingComic = () => {
		props.redeemInfluencePoint();
	};

	const { item } = props.item;
	if (item.isUseInfluencePointsPanel && (influencePoints.balance < props.comicInfluencePoints)) {
		return null;
	};

	if ((item.action == Constants.INVITE_FRIENDS) && (UserSession.instance.getFriendsCountForFreeAccess() == -1 || UserSession.instance.getMonthlyMaxComicReadQuota() < 0)) {
		return null;
	};

	const isSubscribePanel = (item.action == Constants.SUBSCRIBE);
	const isInviteFriendsPanel = (item.action == Constants.INVITE_FRIENDS);

	return (
		<View style={[styles.containerView, styles.influencePanelView(1600, navigationStyle.panelLeftRightMargin), { borderColor: context.colors.chatBubbles, backgroundColor: context.colors.textInverse }]}>
			<View style={styles.productView}>
				<View style={{ flex: 0.20 }}>
					<View style={[styles.iconView]}>
						<FastImage source={isSubscribePanel ? require("../../assets/coffee_icon.png") : isInviteFriendsPanel ? require("../../assets/invite_friends_icon.png") : require("../../assets/tinyview_influence_points_icon.png")} style={styles.panelImage} />
					</View>
				</View>
				<View style={{ flex: 0.80 }}>
					<Text style={[context.pBold]}>
						{item.title}
					</Text>
					{!isSubscribePanel && !isInviteFriendsPanel &&
						<Text style={[context.pBold, { marginTop: 3 }]}>
							{influencePoints.balance} <Text style={[context.p, { color: context.colors.textBold }]}>{Constants.CURRENT_BALANCE}</Text>
						</Text>
					}
					<View style={{ marginTop: 8 }}>
						<HTML contentWidth={dimensions.width} textSelectable={true} allowFontScaling baseStyle={context.p} source={{ html: item.descriptions }} tagsStyles={{ "b": { color: context.colors.textBold }, "p": { paddingTop: 0, marginBottom: 0, marginTop: 0 } }} />
					</View>
					{item.actionType &&
						<View style={styles.subsButtonContainer}>
							<Button variant='solid' style={[styles.bottomStyle, { backgroundColor: context.colors.logoRed, borderColor: context.colors.logoRed }]}
								onPress={() => {
									navigateToScreens(item.action)
								}}>
								<Text style={[context.p, { color: context.colors.textInverse }]}>
									{item.action}
								</Text>
							</Button>
						</View>
					}
				</View>
			</View>
		</View>
	);
};

const mapStateToProps = (state) => {
	return {
		pathUrl: state.readComic.pathUrl
	};
};

const mapDispatchToProps = (dispatch) => {
	return {
	};
};

const styles = StyleSheet.create({
	containerView: {
		shadowColor: "#000",
		marginBottom: navigationStyle.panelsMargin,
		marginLeft: navigationStyle.panelLeftRightMargin,
		marginRight: navigationStyle.panelLeftRightMargin
	},
	influencePanelView: (w, margin = 2) => {
		let aspectRatio = (dimensions.width - margin * 2) / w

		return {
			width: w * aspectRatio,
			marginRight: margin,
			resizeMode: 'contain',
			borderWidth: 1,
			borderRadius: 8,
			shadowColor: Color.BLACK_COLOR,
			shadowOpacity: 0.1,
			shadowRadius: 12,
			elevation: 12
		}
	},
	productView: {
		flexDirection: 'row',
		margin: scale(20),
	},
	bottomStyle: {
		borderWidth: 1,
		height: 40,
		borderRadius: 6,
		position: 'absolute',
		elevation: 0,
		justifyContent: 'center',
		paddingTop: 0,
		paddingBottom: 0,
	},
	subsButtonContainer: {
		height: 35,
		marginTop: 12,
	},
	iconView: {
		alignSelf: 'flex-start',
		paddingTop: 0,
	},
	panelImage: {
		alignSelf: 'flex-start',
		width: 32,
		height: 32
	}
});

export default connect(mapStateToProps, mapDispatchToProps)(InfluencePanel);