import React, { useContext } from "react";
import { StyleSheet, Text, View } from "react-native";
import { ThemeContext } from "../Contexts";
import FastImage from "react-native-fast-image";

export default ErrorTextMsg = (props) => {
    const context = useContext(ThemeContext)

    return (
        <View style={[styles.errorView, { flexDirection: 'row' }]}>
            <FastImage
                style={styles.exclamationMarkView}
                tintColor={context.colors.logoRed}
                source={require('../../assets/exclamation_inside_circle_icon.png')} />
            <Text style={[context.bodyMini, styles.errorTextStyle, { color: context.colors.logoRed }]}>{props.errorText}</Text>
        </View>
    )
}

const styles = StyleSheet.create({
    errorView: {
        marginTop: 4,
        flexDirection: 'row'
    },
    exclamationMarkView: {
        height: 16,
        width: 16,
        alignSelf: 'center'
    },
    errorTextStyle: {
        marginLeft: 6
    }
})