import React from "react";
import { View, StyleSheet, Image } from 'react-native'
import FastImage from 'react-native-fast-image'

export default BadgesView = (props) => {

    var badgesToShow = props.badges
    const getPrevStyle = props.badgesStyle
    if (!badgesToShow || !Array.isArray(badgesToShow) || badgesToShow.length == 0) {
        return
    }

    if (props.badgeType == "subscriber") {
        badgesToShow = badgesToShow.filter((item) => {
            return item.includes("subscriber")
        })
        badgesToShow = [badgesToShow[0]]
    } else {
        badgesToShow = [badgesToShow[0]]
    }

    return (
        <View style={{ flexDirection: 'row' }}>
            {badgesToShow.map((subItem) => {
                if (subItem == "tinyview") {
                    return (
                        <FastImage key={subItem} style={[styles.badgeView, getPrevStyle]}
                            source={require('../../assets/tinyview_badge.png')}
                        />
                    )
                } else if (subItem == "creator") {
                    return (
                        <FastImage key={subItem} style={[styles.badgeView, getPrevStyle]}
                            source={require('../../assets/creator_badge.png')}
                        />
                    )
                } else if (subItem == "subscriber-artsupplies") {
                    return (
                        <FastImage key={subItem} style={[styles.badgeView, getPrevStyle]}
                            source={require('../../assets/artsupplies_badge.png')}
                        />
                    )
                } else if (subItem == "subscriber-coffee") {
                    return (
                        <FastImage key={subItem} style={[styles.badgeView, getPrevStyle]}
                            source={require('../../assets/coffee_badge.png')}
                        />
                    )
                } else if (subItem == "subscriber-cookie") {
                    return (
                        <FastImage key={subItem} style={[styles.badgeView, getPrevStyle]}
                            source={require('../../assets/cookie_badge.png')}
                        />
                    )
                } else if (subItem == "virtual-gift") {
                    return (
                        <FastImage key={subItem} style={[styles.badgeView, styles.giftBadgeView, getPrevStyle]}
                            source={require('../../assets/gift_badge.png')}
                        />
                    )
                } else if (subItem == "subscriber-bagel") {
                    return (
                        <FastImage key={subItem} style={[styles.badgeView, getPrevStyle]}
                            source={require('../../assets/bagel_icon.png')}
                        />
                    )
                } else if (subItem == "subscriber-pizza") {
                    return (
                        <FastImage key={subItem} style={[styles.badgeView, getPrevStyle]}
                            source={require('../../assets/pizza_icon.png')}
                        />
                    )
                }
            })}
        </View>
    )
}

const styles = StyleSheet.create({
    badgeView: {
        height: 16,
        width: 16,
        marginLeft: 8,
        alignSelf: 'center',
    },
    giftBadgeView: {
        height: 18,
        width: 18
    }
})