import { View, Text, StyleSheet, Image, Dimensions, SectionList, RefreshControl, TouchableOpacity, BackHandler, Animated } from "react-native";
import React, { Component } from "react";
import { connect } from 'react-redux';
import BottomTabBar from "../BottomTabBar";
import { Constants } from "../../config/Constants";
import SelectableText from "../../config/SelectableText";
import { ThemeContext } from "../../Contexts";
import { SharedPreferences } from "../../config/SharedPreferences";
import { settings } from "../../config/settings";
import { Utils } from "../../config/Utils";
import { StackActions, CommonActions } from '@react-navigation/native';
import SessionManager from "../../config/SessionManager";
import { updateShowAllComicsSettings, getUserNotification, shareInviteLink, sendNotificationsLastSeen, updateAlertSubscription, openWebViewAction } from '../../redux/actions/actions';
import { navigationStyle, buyButton, bannerMsgContainer, bannerTextView, bannerCrossIconView, bannerCrossIcon, loadingViewStyle } from "../../config/styles";
import FastImage from "react-native-fast-image";
import { Color } from "../../config/Color";
import { Button } from "native-base";
import moment from 'moment'
import FirebaseManager from "../../config/FirebaseManager";
import UserSession from "../../config/UserSession";
import ActionSheet from "../actionSheet/ActionSheetCustom";
import ShareBottomSheet from '../ShareBottomSheet'
import NetworkUtils from "../../config/NetworkUtils";
import { LoadingIndicator } from "../LoadingIndicator";
import NotificationPanel from "./NotificationPanel";
import { SafeAreaView } from "react-native-safe-area-context";

let dimensions = Dimensions.get('window')

class NotificationsScreen extends Component {

    constructor(props) {
        super(props)

        this.listData = []
        this.lastVisited = {}
        this.isListEmpty = false
        this.todayNotifications = []
        this.ydayNotifications = []
        this.earlierNotifications = []
        this.lastNotificationID = ""
        this.isAPIInProgress = false
        this.hasMoreData = true
        this.configBottomSheet = ''

        this.state = {
            showRefresh: true,
            showNotificationMessage: SessionManager.instance.needsToShowAlertMessage(),
            sheetTitle: '',
            sheetMessage: Constants.INVITE_FRIENDS_ACTION_SHEET_MESSAGE,
            sheetOptions: [Constants.SIGN_IN, Constants.CANCEL],
            cancelOptionIndex: 1,
            refreshUI: true,
            showInviteBottomSheet: false,
            userUnreadNotifCount: UserSession.instance.getUnreadNotifications(),
            hideBottomBar: false
        }

        this.renderMainBottomBar = this.renderMainBottomBar.bind(this)
        this.onShowAllComicSwitchTap = this.onShowAllComicSwitchTap.bind(this)
        this.openChapter = this.openChapter.bind(this)
        this.scrollToTopOrReload = this.scrollToTopOrReload.bind(this)
        this.reloadPage = this.reloadPage.bind(this)
        this.renderButton = this.renderButton.bind(this)
        this.renderEmptyNotification = this.renderEmptyNotification.bind(this)
        this.renderNotificationPanel = this.renderNotificationPanel.bind(this)
        this.onNotificationFetched = this.onNotificationFetched.bind(this)
        this.renderListTitle = this.renderListTitle.bind(this)
        this.getTimeDate = this.getTimeDate.bind(this)
        this.navigateToInviteFriends = this.navigateToInviteFriends.bind(this)
        this.renderFollowSeriesMsg = this.renderFollowSeriesMsg.bind(this)
        this.hideNotificationMsg = this.hideNotificationMsg.bind(this)
        this.navigateToScreens = this.navigateToScreens.bind(this)
        this.onActionSheetPress = this.onActionSheetPress.bind(this)
        this.navigateToUserProfile = this.navigateToUserProfile.bind(this)
        this._onEndReached = this._onEndReached.bind(this)
        this.fetchNotifications = this.fetchNotifications.bind(this)
        this.onBackPress = this.onBackPress.bind(this)
        this._onViewDidFocus = this._onViewDidFocus.bind(this)
        this.renderListHeader = this.renderListHeader.bind(this)
        this._footerPlaceHolderComponent = this._footerPlaceHolderComponent.bind(this)
        this._listEmptyComponent = this._listEmptyComponent.bind(this)
        this.renderInviteActionSheet = this.renderInviteActionSheet.bind(this)
        this.closeBottomSheet = this.closeBottomSheet.bind(this)
        this.onSendNotifLastSeen = this.onSendNotifLastSeen.bind(this)
        this.onNotifCountFetched = this.onNotifCountFetched.bind(this)
        this.onFollowPress = this.onFollowPress.bind(this)
        this.changeActionSheetTitleAndButton = this.changeActionSheetTitleAndButton.bind(this)
        this.isSubscribedToAlert = this.isSubscribedToAlert.bind(this)
        this.subscribeToAlert = this.subscribeToAlert.bind(this)
        this.renderBannerButton = this.renderBannerButton.bind(this)
        this.renderManageAlertMsg = this.renderManageAlertMsg.bind(this)
        this.navigateToStackTop = this.navigateToStackTop.bind(this)
        this.openWebView = this.openWebView.bind(this)
        this.renderUpgradeSubsMsg = this.renderUpgradeSubsMsg.bind(this)

        this.onViewDidMount = false
        this.isFollowingTinyview = this.isSubscribedToAlert(settings.getWhatsTinyviewURL())
        this.lastScrollOffset = 0;
        this.scrollY = new Animated.Value(0)

        this.aspectRatio = dimensions.width / Constants.LOADING_GIF_DIMEN.width
    }

    componentDidMount() {

        this.props.navigation.setParams({
            onShowAllComicSwitchTap: this.onShowAllComicSwitchTap,
            openChapter: this.openChapter,
            scrollToTopOrReload: this.scrollToTopOrReload
        })

        this._focusUnsubscribe = this.props.navigation.addListener('focus', () => {
            this._onViewDidFocus()
        });

        this.getTimeDate()
        this.reloadPage()
        let notifLastVisited = moment().unix() * 1000
        this.props.sendNotificationsLastSeen({ notificationLastSeenAt: notifLastVisited }, this.onSendNotifLastSeen)
    }

    UNSAFE_componentWillReceiveProps(nextProps) {
        if (nextProps) {
            if (this.props.alerts != nextProps.alerts) {
                let alertName = Utils.getChannelName(settings.getWhatsTinyviewURL())
                this.isFollowingTinyview = nextProps.alerts && nextProps.alerts[alertName] == true ? true : false
            }
        }
    }

    onSendNotifLastSeen(data) {
        if (data) {
            this.props.getUserNotification({ countOnly: 1 }, this.onNotifCountFetched)
        }
    }

    onNotifCountFetched(data) {
        if (data || data == 0) {
            this.setState({ userUnreadNotifCount: data })
        }
    }

    UNSAFE_componentWillMount() {
        BackHandler.addEventListener('hardwareBackPress', this.onBackPress);
    }

    componentWillUnmount() {
        BackHandler.removeEventListener('hardwareBackPress', this.onBackPress);
        if (this._focusUnsubscribe) {
            this._focusUnsubscribe()
        }
    }

    onBackPress() {
        this.props.navigation.dispatch(CommonActions.goBack())
        return true;
    }

    fetchNotifications() {
        this.isAPIInProgress = true
        this.props.getUserNotification({ startAfter: this.lastNotificationID, records: Constants.NOTIFICATIONS_PAGINATION_COUNT }, this.onNotificationFetched)
    }

    reloadPage() {
        this.listData = []
        this.todayNotifications = []
        this.ydayNotifications = []
        this.earlierNotifications = []
        this.hasMoreData = true
        this.lastNotificationID = ""

        if (!this.state.showRefresh) {
            this.setState({ showRefresh: true })
        }

        this.fetchNotifications()
    }

    navigateToStackTop() {
        this.props.navigation.dispatch(StackActions.popToTop());
    }

    openWebView(url) {
        this.props.openWebView(url)
    }

    async getTimeDate() {
        this.lastVisited = await SharedPreferences.getData(settings.LAST_VISITED_NOTIFICATION)
    }

    onNotificationFetched(data) {
        if (data && data.length < Constants.NOTIFICATIONS_PAGINATION_COUNT) {
            this.hasMoreData = false
        }

        if (data) {
            for (const iterator of data) {
                let notificationDate = moment(iterator.createdAt)

                if (iterator.user && iterator.user.image) {
                    iterator.user.image = settings.IMAGE_BASE_URL + "/" + iterator.user.image
                }

                if (notificationDate.isSame(moment(), 'day')) {
                    this.todayNotifications.push(iterator)
                } else if (notificationDate.isSame(moment().subtract(1, 'd'), 'day')) {
                    this.ydayNotifications.push(iterator)
                } else {
                    this.earlierNotifications.push(iterator)
                }
            }
        }

        if (data && data[data.length - 1]) {
            this.lastNotificationID = data[data.length - 1].notificationID
        }

        let isNotifListEmpty = this.todayNotifications.length == 0 && this.ydayNotifications.length == 0 && this.earlierNotifications.length == 0
        this.isListEmpty = isNotifListEmpty

        this.listData = [
            {
                title: this.todayNotifications.length > 0 ? Constants.TODAY : null,
                data: this.todayNotifications,
                index: 0
            },
            {
                title: this.ydayNotifications.length > 0 ? Constants.YESTERDAY : null,
                data: this.ydayNotifications,
                index: 1
            },
            {
                title: this.earlierNotifications.length > 0 ? Constants.EARLIER : null,
                data: this.earlierNotifications,
                index: 2
            }
        ]

        SharedPreferences.saveData(settings.LAST_VISITED_NOTIFICATION, (moment().unix() * 1000).toString())
        this.isAPIInProgress = false
        this.setState({ showRefresh: false })
    }

    renderMainBottomBar() {
        return (
            <BottomTabBar hideBottomBar={this.state.hideBottomBar} navigation={this.props.navigation} openWebView={this.openWebView} navigateToStackTop={this.navigateToStackTop} onBackPress={this.onBackPress} openChapter={this.openChapter} onShowAllComicSwitchTap={this.onShowAllComicSwitchTap} scrollToTopOrReload={this.scrollToTopOrReload} topNavbar={this.topNavbar} userUnreadNotifCount={this.state.userUnreadNotifCount} userDetails={this.props.userDetails} />
        )
    }

    onShowAllComicSwitchTap(value) {
        SessionManager.instance.showAllComics = value
        this.props.updateShowAllComicsSettings(value)
        SharedPreferences.setShowAllComicsValue(value)

        this.props.navigation.dispatch(StackActions.popToTop());
    }

    openChapter(pathURL, item = null) {
        const redirectPath = Utils.resolvePath(settings.apiBaseURL, pathURL)
        this.props.navigation.push('Home', { comicHome: redirectPath, hasUserInfo: true })
    }

    scrollToTopOrReload() {
        this.reloadPage()
    }

    renderButton(item) {
        let isFriendButton = (item == Constants.INVITE_FRIENDS)

        return (
            <View style={styles.buttonView(isFriendButton)}>
                <Button
                    variant='solid'
                    style={[styles.navbuttonView, { backgroundColor: this.context.colors.logoRed, borderColor: this.context.colors.logoRed }]}
                    onPress={() => { !isFriendButton ? this.openChapter(settings.DIRECTORY_COMIC_URL) : this.navigateToInviteFriends() }}>
                    <Text style={[this.context.p, { color: this.context.colors.textInverse }]}>{item}</Text>
                </Button>
            </View>
        )
    }

    closeBottomSheet() {
        this.setState({ showInviteBottomSheet: false })
    }

    renderInviteActionSheet() {
        const valueProps = { closeBottomSheet: this.closeBottomSheet, userDetails: this.props.userDetails, shareInviteLink: this.props.shareInviteLink }
        return (
            <ShareBottomSheet {...valueProps} configShareSheetFor={this.configBottomSheet} />
        )
    }

    changeActionSheetTitleAndButton(sheetTitle, sheetOptions, sheetMessage = '') {
        this.setState({ sheetTitle: sheetTitle, sheetMessage: sheetMessage, sheetOptions: sheetOptions }, () => {
            this.actionSheet.show()
        })
    }

    navigateToInviteFriends() {
        if (!UserSession.instance.isLoggedInUser()) {
            let sheetTitle = ''
            let sheetMessage = Constants.INVITE_FRIENDS_ACTION_SHEET_MESSAGE
            let sheetOptions = [Constants.SIGN_IN, Constants.CANCEL]
            this.changeActionSheetTitleAndButton(sheetTitle, sheetOptions, sheetMessage)
            return;
        }
        this.configBottomSheet = Constants.INVITE_FRIENDS
        this.setState({ showInviteBottomSheet: true })
    }

    _onEndReached() {
        if (!this.hasMoreData || this.isAPIInProgress) {
            return
        }

        this.fetchNotifications()
    }

    renderEmptyNotification({ section }) {
        if (!this.isListEmpty || section.index != 0) {
            return null
        }

        return (
            <View style={{ marginLeft: navigationStyle.panelLeftRightMargin, marginRight: navigationStyle.panelLeftRightMargin }}>
                <FastImage style={styles.emptyNotifImageView}
                    source={require('../../../assets/no_notification_icon.png')}
                />
                <Text style={[this.context.h2, styles.empNotifHeadingView]}>No notifications yet!</Text>
                <Text style={[this.context.p, styles.empNotifTextView]}>Follow your favorite creators or invite friends to get updates from them.</Text>
                <View style={{ flexDirection: 'row', justifyContent: 'center' }}>
                    {this.renderButton(Constants.ALL_SERIES)}
                    {this.renderButton(Constants.INVITE_FRIENDS)}
                </View>
            </View>
        )
    }

    navigateToScreens(item) {
        if (item != null) {
            FirebaseManager.instance.handleNotification(item, false)
        }
    }

    navigateToUserProfile(item) {
        if (item == null) {
            return;
        }

        let userId = item.user && item.user.userID
        if (userId) {
            var subRoutesParams = { clickedUserID: userId }
            this.props.navigation.push(Constants.USER_PROFILE_SCREEN, { ...subRoutesParams })
        } else if (item.topic) {
            let seriesUrl = Utils.getChannelURL(item.topic)
            if (seriesUrl) {
                this.openChapter(seriesUrl)
            }
        }
    }

    renderNotificationPanel({ item }) {
        return (
            <NotificationPanel item={item} navigateToUserProfile={this.navigateToUserProfile} navigateToScreens={this.navigateToScreens} lastVisited={this.lastVisited} />
        )
    }

    renderListTitle(title) {
        if (title == null) {
            return;
        }

        let isTitleToday = title == Constants.TODAY
        return (
            <Text style={[this.context.p, styles.titleView(isTitleToday), { color: this.context.colors.textBold }]}> {title} </Text>
        )
    }

    onFollowPress(pathUrl, isFollowing) {
        const isConnected = NetworkUtils.instance.isAvailable()
        if (!isConnected) {
            SessionManager.instance.showErrorAlert("No Internet Connection", "An error occurred while trying to access the server. Please check your Internet connection and try again.")
            return;
        }

        if (!isFollowing && !UserSession.instance.isAnyUserAlertsEnabled()) {
            const channelName = Utils.getUserVisibleChannelName(pathUrl)
            let sheetTitle = Constants.MANAGE_ALERTS_ACTIONSHEET_TITLE
            let sheetMessage = `You want to follow ${channelName} but we have no way of reaching you. Manage alerts and let us know how to send you updates from ${channelName}.`
            let sheetOptions = [Constants.MANAGE_ALERTS, Constants.NOT_NOW]
            this.changeActionSheetTitleAndButton(sheetTitle, sheetOptions, sheetMessage)
        } else {
            let isSubscribed = this.isSubscribedToAlert(pathUrl)
            if (isFollowing) {
                if (isSubscribed) this.subscribeToAlert(false, pathUrl)
            } else if (!isSubscribed) this.subscribeToAlert(true, pathUrl)
        }
    }

    isSubscribedToAlert(seriesURL = null) {
        const alertName = Utils.getChannelName(seriesURL)
        return this.props.alerts && this.props.alerts[alertName] == true ? true : false
    }

    subscribeToAlert(isSubscribe, seriesURL = null) {
        this.props.updateAlertSubscription(isSubscribe, seriesURL)
    }

    hideNotificationMsg() {
        SessionManager.instance.shownAlertMessage = false;
        this.setState({ showNotificationMessage: false })
    }

    renderFollowSeriesMsg() {
        if (this.isFollowingTinyview) {
            return;
        }

        let message = `Never miss an update from ${Constants.LETTER_CASE_TINYVIEW}!`;

        return (
            <View style={[bannerMsgContainer, { backgroundColor: this.context.colors.bannerBackgroundError }]}>
                <View style={bannerTextView}>
                    <Text style={[this.context.bodyMini, { color: this.context.colors.bannerTextError }]} numberOfLines={1} adjustsFontSizeToFit>{message}</Text>
                </View>
                {this.renderBannerButton(Constants.FOLLOW)}
                <TouchableOpacity
                    style={bannerCrossIconView}
                    onPress={() => this.hideNotificationMsg()}>
                    <FastImage
                        style={bannerCrossIcon}
                        tintColor={this.context.colors.bannerTextError}
                        source={require('../../../assets/close_window.png')}
                    />
                </TouchableOpacity>
            </View>
        )
    }

    renderBannerButton(item) {
        const isFollowButton = (item === Constants.FOLLOW)
        const isUpgradeButton = (item === Constants.UPGRADE)

        return (
            <TouchableOpacity
                style={[buyButton, styles.buyButton, { backgroundColor: this.context.colors.logoRed, borderColor: this.context.colors.logoRed }]}
                onPress={() => { isUpgradeButton ? this.openChapter(settings.getSubscribeURL()) : isFollowButton ? this.onFollowPress(settings.getWhatsTinyviewURL(), this.isFollowingTinyview) : Utils.navigateToManageAlertsPage() }}>
                <Text style={[this.context.bodyMini, { color: this.context.colors.textInverse }]}>{item}</Text>
            </TouchableOpacity>
        )
    }

    renderManageAlertMsg() {
        let isAnyAlertsEnabled = UserSession.instance.isAnyUserAlertsEnabled()
        if (isAnyAlertsEnabled || !this.isFollowingTinyview) {
            return;
        }

        let message = Constants.MANAGE_ALERTS_ACTIONSHEET_TITLE;
        return (
            <View style={[bannerMsgContainer, { backgroundColor: this.context.colors.bannerBackgroundError }]}>
                <View style={bannerTextView}>
                    <Text style={[this.context.bodyMini, { color: this.context.colors.bannerTextError }]} numberOfLines={1} adjustsFontSizeToFit>{message}</Text>
                </View>
                {this.renderBannerButton(Constants.MANAGE_ALERTS)}
                <TouchableOpacity
                    style={bannerCrossIconView}
                    onPress={() => this.hideNotificationMsg()}>
                    <FastImage
                        style={bannerCrossIcon}
                        tintColor={this.context.colors.bannerTextError}
                        source={require('../../../assets/close_window.png')}
                    />
                </TouchableOpacity>
            </View>
        )
    }

    renderUpgradeSubsMsg() {
        let hasAnySubscription = SessionManager.instance.hasAnySubscriptionPurchase()
        let isAnyAlertsEnabled = UserSession.instance.isAnyUserAlertsEnabled()
        if (hasAnySubscription || !isAnyAlertsEnabled || !this.isFollowingTinyview) {
            return;
        }

        let message = Constants.UPGRADE_SUBSCRIPTION_TITLE_MESSAGE;
        return (
            <View>
                <View style={[bannerMsgContainer, { backgroundColor: this.context.colors.bannerBackgroundError }]}>
                    <View style={bannerTextView}>
                        <Text style={[this.context.bodyMini, { color: this.context.colors.bannerTextError }]} numberOfLines={1} adjustsFontSizeToFit>{message}</Text>
                    </View>
                    {this.renderBannerButton(Constants.UPGRADE)}
                    <TouchableOpacity
                        style={bannerCrossIconView}
                        onPress={() => this.hideNotificationMsg()}>
                        <FastImage
                            style={bannerCrossIcon}
                            tintColor={this.context.colors.bannerTextError}
                            source={require('../../../assets/close_window.png')}
                        />
                    </TouchableOpacity>
                </View>
            </View>
        )
    }

    renderListHeader() {
        return (
            <View style={styles.topContainer}>
                <SelectableText textValue={Constants.NOTIFICATION} textStyle={[this.context.h1]} multiline={true} />
                <Text style={[this.context.p, { marginTop: 12 }]}>Notifications from creators and friends you follow</Text>
            </View>
        )
    }

    onActionSheetPress(index) {
        if (index == 0) {
            if (this.state.sheetOptions[0] == Constants.SIGN_IN) {
                const params = { isForLoginProcess: true }
                Utils.navigateToDrawerLoginRoute(this.props, Constants.LOGIN_SCREEN, params)
            } else if (this.state.sheetOptions[0] == Constants.MANAGE_ALERTS) {
                Utils.navigateToManageAlertsPage(this.props, { configFromFollowTab: true, seriesToFollowData: { title: Constants.LETTER_CASE_TINYVIEW, action: "/tinyview/index.json" } })
            }
        }
    }

    _listEmptyComponent() {
        if (!this.listData) {
            return
        }

        return (
            <View >
                <Image style={loadingViewStyle(this.aspectRatio)} source={require('../../../assets/notification_loading_view.gif')} />
            </View>
        )
    }

    _footerPlaceHolderComponent() {
        if (!this.hasMoreData) {
            return
        }

        return (
            <View >
                <Image style={loadingViewStyle(this.aspectRatio)} source={require('../../../assets/notification_loading_view.gif')} />
            </View>
        )
    }

    _onViewDidFocus() {
        setTimeout(() => {
            if (this.onViewDidMount) {
                this.setState({ refreshUI: !this.state.refreshUI, hideBottomBar: false })
            }
            this.onViewDidMount = true
        }, 200);
    }

    render() {
        const { hideBottomBar } = this.state

        return (
            <SafeAreaView style={{ flex: 1 }}>
                {this.state.showNotificationMessage && this.renderFollowSeriesMsg()}
                {this.state.showNotificationMessage && this.renderManageAlertMsg()}
                {this.state.showNotificationMessage && this.renderUpgradeSubsMsg()}
                <View style={styles.mainContainer}>
                    <SectionList
                        onScroll={Animated.event([{ nativeEvent: { contentOffset: { y: this.scrollY } } }],
                            {
                                useNativeDriver: false,
                                listener: (event) => {
                                    const { contentOffset: { y: currentOffset }, layoutMeasurement, contentSize } = event.nativeEvent
                                    const isNearBottom = currentOffset + layoutMeasurement.height >= contentSize.height - 20

                                    // Should not trigger on overscroll at the top or bottom of the page.
                                    if (currentOffset < 0 || isNearBottom) {
                                        if (isNearBottom) {
                                            // Update the last scroll position
                                            this.lastScrollOffset = currentOffset
                                        }
                                        this.setState({ hideBottomBar: false })
                                        return
                                    }

                                    // Initialize offset tracking on first scroll event
                                    if (this.lastScrollOffset == null) {
                                        this.lastScrollOffset = currentOffset
                                    } else {
                                        if (Math.abs(currentOffset - this.lastScrollOffset) < Constants.SCROLL_THRESHOLD) {
                                            return;
                                        }

                                        const scrollDirection = currentOffset > this.lastScrollOffset ? Constants.DOWN : Constants.UP

                                        // Hide tab bar immediately when scrolling down
                                        if (scrollDirection === Constants.DOWN && !hideBottomBar) {
                                            this.setState({ hideBottomBar: true })
                                        }

                                        // Show tab bar immediately when scrolling up
                                        if (scrollDirection === Constants.UP && hideBottomBar) {
                                            this.setState({ hideBottomBar: false })
                                        }

                                        // Update the last scroll position
                                        this.lastScrollOffset = currentOffset
                                    }
                                }
                            })}
                        refreshControl={
                            <RefreshControl
                                refreshing={this.state.showRefresh}
                                onRefresh={() => {
                                    this.reloadPage()
                                }}
                                progressViewOffset={settings.PROGRESS_VIEW_OFFSET}
                                tintColor={Color.CIRCLE_PROGRESS_BAR_COLOR}
                            />
                        }
                        ListHeaderComponent={this.renderListHeader}
                        sections={this.listData}
                        keyExtractor={(item, index) => item.notificationID + index}
                        renderItem={this.renderNotificationPanel}
                        renderSectionHeader={({ section: { title } }) => this.renderListTitle(title)}
                        ListFooterComponent={this._footerPlaceHolderComponent()}
                        renderSectionFooter={this.renderEmptyNotification}
                        ListEmptyComponent={this._listEmptyComponent}
                        contentContainerStyle={{
                            paddingBottom: navigationStyle.panelsMargin + navigationStyle.navHeight
                        }}
                        onEndReached={this._onEndReached}
                        onEndReachedThreshold={0.7}
                        stickySectionHeadersEnabled={false}
                    />
                </View>
                <ActionSheet
                    ref={o => this.actionSheet = o}
                    title={this.state.sheetTitle}
                    message={this.state.sheetMessage}
                    options={this.state.sheetOptions}
                    cancelButtonIndex={this.state.cancelOptionIndex}
                    tintColor={this.context.colors.logoRed}
                    onPress={(index) => { this.onActionSheetPress(index) }}
                    useNativeDriver={true}
                    styles={{ titleText: this.context.h2, messageText: this.context.p, messageBox: { paddingTop: this.state.sheetTitle == "" ? 20 : 0 } }}
                />
                {this.state.showInviteBottomSheet && this.renderInviteActionSheet()}
                {this.props.isLogInProcess && <LoadingIndicator />}
                {this.renderMainBottomBar()}
            </SafeAreaView>
        )
    }
}

const mapStateToProps = (state) => {
    return {
        pathUrl: state.readComic.pathUrl,
        userDetails: state.loginInfo.userDetails,
        alerts: state.userInfo.alerts,
        isLogInProcess: state.loginInfo.isLogInProcess
    }
}

const mapDispatchToProps = (dispatch) => {
    return {
        updateShowAllComicsSettings(value) {
            dispatch(
                updateShowAllComicsSettings(value)
            )
        },
        getUserNotification(value, callBack) {
            dispatch(
                getUserNotification(value, callBack)
            )
        },
        shareInviteLink(requestedData, callback) {
            dispatch(
                shareInviteLink(requestedData, callback)
            )
        },
        sendNotificationsLastSeen(requestedData, callback) {
            dispatch(
                sendNotificationsLastSeen(requestedData, callback)
            )
        },
        updateAlertSubscription(subscribe, seriesURL) {
            dispatch(
                updateAlertSubscription(subscribe, seriesURL)
            )
        },
        openWebView(url) {
            dispatch(
                openWebViewAction(url)
            )
        },
    }
}

export default (connect)(mapStateToProps, mapDispatchToProps)(NotificationsScreen);

NotificationsScreen.contextType = ThemeContext

const styles = StyleSheet.create({
    mainContainer: {
        flex: 1
    },
    topContainer: {
        marginTop: 10,
        marginLeft: navigationStyle.panelLeftRightMargin,
        marginRight: navigationStyle.panelLeftRightMargin,
    },
    emptyNotifImageView: {
        height: 120,
        width: 120,
        marginTop: 60,
        marginBottom: 40,
        alignSelf: 'center'
    },
    empNotifHeadingView: {
        alignSelf: 'center',
    },
    empNotifTextView: {
        marginTop: 12,
        textAlign: 'center'
    },
    buttonView: (isFriendButton) => {
        return {
            marginTop: navigationStyle.panelsMargin,
            marginLeft: isFriendButton ? 12 : 0
        }
    },
    titleView: (isTitleToday) => {
        return {
            marginTop: !isTitleToday ? 40 : 22,
            marginBottom: 8,
            marginLeft: navigationStyle.panelLeftRightMargin,
        }
    },
    navbuttonView: {
        borderRadius: 6,
        height: 40,
        justifyContent: 'center'
    },
    buyButton: {
        height: 25,
        paddingLeft: 8,
        paddingRight: 8,
        position: 'relative'
    }
})