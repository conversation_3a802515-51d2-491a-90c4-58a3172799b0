import React from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { Animated, Alert, BackHandler, InteractionManager, StyleSheet, Platform, Dimensions, StatusBar, PermissionsAndroid } from 'react-native'
import SafeArea from 'react-native-safe-area'
import {
  readComicAction,
  getUserDetails,
  updateAlertSubscription,
  requestPurchase,
  restorePurchase,
  shareApp,
  openWebViewAction,
  hasNotificationPermission,
  clearComics,
  getPageStatus,
  clearComicPageInfo,
  onComicChange,
  updateShowAllComicsSettings,
  recordPageView,
  getUserFeeds,
  getSeriesComics,
  likeDislikeFeed,
  likeDislikeComment,
  postCommentOnFeed,
  getFeedComments,
  getFeedLikes,
  editComment,
  shareImage,
  deleteRepostedComic,
  deleteSentPost,
  deleteComment,
  getMultiplePanelInfo,
  getStoryDetails,
  getAllComics,
  fetchAppConfigList,
  flagUserComment,
  blockUserAction,
  getSubscribeProgressAction,
  addInfluencePoint,
  getNavigationComics,
  redeemInfluencePoint,
  shareInviteLink,
  getFeedEpisodes,
  setNotificationAlerts,
  getSeriesCarousel,
  addNewTransaction
} from '../../redux/actions/actions'
import ReaderView from '../ReaderView';
import { navigationStyle } from '../../config/styles'
import { View, Spinner, Text } from 'native-base';
import { Utils } from '../../config/Utils';
import { SharedPreferences } from '../../config/SharedPreferences';
import { settings } from '../../config/settings';
import SessionManager from '../../config/SessionManager';
import NetworkUtils from '../../config/NetworkUtils';
import { CommonActions, StackActions } from '@react-navigation/native';
import FirebaseManager from '../../config/FirebaseManager';
import ImageDownloader from '../ImageDownloader';
import { Constants } from '../../config/Constants';
import { LoadingIndicator } from "../LoadingIndicator"
import UserSession from '../../config/UserSession';
import ActionSheet from '../actionSheet/ActionSheetCustom';
import FeedComponent from '../feed/FeedComponent';
import Types from '../../redux/actions/types';
import GiftBottomSheet from '../GiftUI/GiftBottomSheet';
import ShareBottomSheet from '../ShareBottomSheet';
import BottomTabBar from '../BottomTabBar';
import { ThemeContext } from '../../Contexts';
import DeepLinkManager from '../../config/DeepLinkManager';
import ComicBottomBar from '../ComicBottomBar';
import SeriesBottomBar from '../SeriesBottomBar';
import IAPManager from '../../config/IAPManager';
import { SafeAreaView } from 'react-native-safe-area-context';

const dimensions = Dimensions.get('window');
class Reader extends FeedComponent {

  constructor(props) {
    super(props)

    this.state = {
      containerInsets: { top: 0, bottom: 0, left: 0, right: 0 },
      comicData: {},
      profilePicURL: null,
      seriesHomeName: null,
      sheetTitle: '',
      sheetMessage: Constants.SIGN_IN_ACTION_SHEET_MESSAGE,
      sheetOptions: [Constants.SIGN_IN, Constants.CANCEL],
      cancelOptionIndex: 1,
      showFooterPlaceHolder: false,
      showBottomSheet: false,
      showShareBottomSheet: false,
      showInviteBottomSheet: false,
      comicFeedEpisodes: {},
      continueReadingComic: {},
      continueReadPanelEpisodes: null,
      isEpisodsAPIResponded: false,
      loadingProductId: null,
      hideBottomBar: false
    }

    this.scrollY = new Animated.Value(0)
    this.initialScrollY = new Animated.Value(0)
    this.navOpacity = new Animated.Value(1)
    this.topNavbar = new Animated.Value(0)
    this.isNavigationHidden = false
    this.clickedStoryID = null
    this.clickedPanelItems = null
    this.isAtTop = true
    this.flatlist = null
    this.updatedHeader = false;
    this.isViewPushed = false;
    this.seriesHomeProfile = null;
    this.isNavigationInProgress = false;
    this.isFirstTimeUser = false;
    this.bottomSheetModalRef = null
    this.isAlreadyPurchased = false;
    this.isAlreadyPurchasedFromInfluence = false;
    this.tappedComicData = null
    this.productAlreadyPurchased = Utils.checkObject(this.props.isAlreadyPurchased) ? JSON.parse(JSON.stringify(this.props.isAlreadyPurchased)) : false
    this.tappedGiftedItems = {}
    this.isComicUnlockedCurrently = false
    this.configBottomSheet = ''
    this.seriesToFollowData = null
    this.subscribedAlerts = {}
    this.isPreviousViewedToastShown = false
    this.isAnyAlertEnabled = UserSession.instance.isAnyUserAlertsEnabled()
    this.productReqToPuchase = {}
    this.unlockPanelType = null

    this.valueProps = {
      postUserComment: this.postUserComment, likeDislikeFeed: this.likeDislikeFeed,
      likeDislikeComment: this.likeDislikeComment, onCountPress: this.onCountPress, onCommentLikeCountPress: this.onCommentLikeCountPress,
      onShowCommentPress: this.onShowCommentPress, onIconPress: this.onIconPress, getIsStoryLiked: this.getIsStoryLiked, deleteRepost: this.deleteRepost,
      editRepost: this.editRepost, onReplyPress: this.onReplyPress, deleteComment: this.deleteComment, onFlagCommentTap: this.onFlagCommentTap, onBlockUserTap: this.onBlockUserTap,
      editComment: this.editComment, navigateToUserProfile: this.navigateToUserProfile, onEndReached: this.getUserFeeds, getStoryDetails: this.getStoryDetails,
      isHideReactionComponentIcons: this.isHideReactionComponentIcons
    }

    this.props.navigation.setParams({
      openHomePage: this.openHomePage,
      onIconPress: this.onIconPress,
      onBackPress: this.onBackPress,
      onShowAllComicSwitchTap: this.onShowAllComicSwitchTap,
      openChapter: this.openChapter,
      scrollToTopOrReload: this.scrollToTopOrReload
    })

    this.handleBackButton = this.handleBackButton.bind(this)
    this.onShowCommentPress = this.onShowCommentPress.bind(this)
    this.onCountPress = this.onCountPress.bind(this)
    this.onActionSheetButtonPress = this.onActionSheetButtonPress.bind(this)
    this.changeActionSheetTitleAndButton = this.changeActionSheetTitleAndButton.bind(this)
    this.updateComicDataItem = this.updateComicDataItem.bind(this)
    this.getStoryComments = super.getStoryComments.bind(this)
    this.loadComic = this.loadComic.bind(this)
    this.onComicLoad = this.onComicLoad.bind(this)
    this.onUserInfoUpdated = this.onUserInfoUpdated.bind(this)
    this.downloadSeriesHomeProfile = this.downloadSeriesHomeProfile.bind(this)
    this.isUserDetailsEmpty = this.isUserDetailsEmpty.bind(this)
    this.getStoryDetails = this.getStoryDetails.bind(this)
    this.resetActionSheet = this.resetActionSheet.bind(this)
    this.signInSheet = this.signInSheet.bind(this)
    this.copyLink = this.copyLink.bind(this)
    this.navigateToAppropriateScreen = this.navigateToAppropriateScreen.bind(this)
    this.openSeriesHomePage = this.openSeriesHomePage.bind(this)
    this.navigateToSignUp = this.navigateToSignUp.bind(this)
    this.navigateToMyProfile = this.navigateToMyProfile.bind(this)
    this.navigateToSubscribePage = this.navigateToSubscribePage.bind(this)
    this.navigateToInfluenceScreen = this.navigateToInfluenceScreen.bind(this)
    this.updateFlatListProps = this.updateFlatListProps.bind(this)
    this.onFollowPress = this.onFollowPress.bind(this)
    this.closeBottomSheet = this.closeBottomSheet.bind(this)
    this.onPageStatusFetch = this.onPageStatusFetch.bind(this)
    this.requestPurchase = this.requestPurchase.bind(this)
    this.resolvePath = this.resolvePath.bind(this)
    this.isHideReactionComponentIcons = this.isHideReactionComponentIcons.bind(this)
    this.showFollowSeriesAlert = this.showFollowSeriesAlert.bind(this)
    this.subscribeToAlert = this.subscribeToAlert.bind(this)
    this.openWebView = this.openWebView.bind(this)
    this.showAlert = this.showAlert.bind(this)
    this.isSubscribedToAlert = this.isSubscribedToAlert.bind(this)
    this.isSubscribedWithAlert = this.isSubscribedWithAlert.bind(this)
    this.onActionSheetPress = this.onActionSheetPress.bind(this)
    this.getAlertChannelName = this.getAlertChannelName.bind(this)
    this.onSeriesHomeClicked = this.onSeriesHomeClicked.bind(this)
    this.isComicDataEmpty = this.isComicDataEmpty.bind(this)
    this._onViewWillFocus = this._onViewWillFocus.bind(this)
    this._onViewDidFocus = this._onViewDidFocus.bind(this)
    this.addAppInstallInfluencePoint = this.addAppInstallInfluencePoint.bind(this)
    this.renderMainBottomBar = this.renderMainBottomBar.bind(this)
    this.onBackPress = this.onBackPress.bind(this)
    this.onComicUnlocked = this.onComicUnlocked.bind(this)
    this.redeemInfluencePoint = this.redeemInfluencePoint.bind(this)
    this.navigateToInviteBottomSheet = this.navigateToInviteBottomSheet.bind(this)
    this.onChangeSeriesTab = this.onChangeSeriesTab.bind(this)
    this.onShowAllComicSwitchTap = this.onShowAllComicSwitchTap.bind(this)
    this.getFeedPanelEpisodes = this.getFeedPanelEpisodes.bind(this)
    this.onFeedEpisodesFetched = this.onFeedEpisodesFetched.bind(this)
    this.showNotificationPermissionAlert = this.showNotificationPermissionAlert.bind(this)
    this.onUserDetailsFetched = this.onUserDetailsFetched.bind(this)
    this.getUnlimitedComicAccessSheet = this.getUnlimitedComicAccessSheet.bind(this)
    this.navigateToSignIn = this.navigateToSignIn.bind(this)
    this.getLockedComicActionSheet = this.getLockedComicActionSheet.bind(this)
    this.renderComicBottomBar = this.renderComicBottomBar.bind(this)
    this.openHomePage = this.openHomePage.bind(this)
    this.getContinueReadingComic = this.getContinueReadingComic.bind(this)
    this.onContinueReadingComicFetched = this.onContinueReadingComicFetched.bind(this)
    this.onFetchedContinueReading = this.onFetchedContinueReading.bind(this)
    this.navigateToStackTop = this.navigateToStackTop.bind(this)
    this.fetchIAPProducts = this.fetchIAPProducts.bind(this)
    this.getContinueReadingEpisodes = this.getContinueReadingEpisodes.bind(this)
    this.onSubscriptionSuccess = this.onSubscriptionSuccess.bind(this)
    this.updateHidingBottomBar = this.updateHidingBottomBar.bind(this)
  }

  shouldComponentUpdate(nextProps, nextState) {
    //Utils.log("shouldComponentUpdate : " + " comicURL " + this.comicURL + " nextProps.pathUrl " + nextProps.pathUrl)
    if (this.comicURL && nextProps.pathUrl != this.comicURL) {
      //Utils.log("shouldComponentUpdate : Reader " + "false")
      return false
    }

    //Utils.log("shouldComponentUpdate : Reader " + "true")
    return true
  }

  UNSAFE_componentWillReceiveProps(nextProps) {

    if (!this.isRefreshingInProgress && nextProps.refreshPageStatus.type == Types.REFRESH_PAGE && nextProps.refreshPageStatus.urlToRefresh == this.comicURL && !nextProps.refreshPageStatus.isRefreshed) {
      this.isRefreshingInProgress = true
      this.setState({ comicData: null })
    }

    if (!this.isRefreshingInProgress && nextProps.refreshComicPage.type == Types.REFRESH_COMIC_PAGE && nextProps.refreshComicPage.urlToCheck == this.comicURL) {
      this.isRefreshingInProgress = true
      this.setState({ comicData: null })
      this.reloadChapter()
      return
    }

    if (this.props.showAllComics != nextProps.showAllComics && !this.isCompletePageLoadInProgress && Utils.isHomeURL(this.comicURL)) {
      this.isCompletePageLoadInProgress = true
      this.hasMoreData = true

      try {
        var comicData = JSON.parse(JSON.stringify(this.state.comicData))
        comicData = null
        this.setState({ comicData }, () => {
          this.loadComic(this.comicURL, true)
        })
      } catch (error) {
        this.loadComic(this.comicURL, true)
      }

      this.props.navigation.setParams()
    }

    if ((nextProps.lastAction.type == Types.ADD_LIKE || nextProps.lastAction.type == Types.REMOVE_LIKE || nextProps.lastAction.type == Types.ADD_GIFT || nextProps.lastAction.type == Types.REMOVE_GIFT)) {
      if (nextProps.lastAction.payload) {
        if (Utils.resolvePath(this.props.pathUrl, nextProps.lastAction.payload) == this.comicURL) {
          if (nextProps.lastAction.type == Types.ADD_LIKE) {
            this.currentPageStatus = JSON.parse(JSON.stringify(this.currentPageStatus))
            this.currentPageStatus.isLikeChanged = true
            this.currentPageStatus.isLiked = true
            this.currentPageStatus.likeCount++
            if (this.currentPageStatus.giftsCount) {
              if (!this.currentPageStatus.giftsCount[Constants.LIKE.toUpperCase()]) {
                this.currentPageStatus.giftsCount[Constants.LIKE.toUpperCase()] = 0
              }

              this.currentPageStatus.giftsCount[Constants.LIKE.toUpperCase()]++
            }

          } else if (nextProps.lastAction.type == Types.ADD_GIFT || nextProps.lastAction.type == Types.REMOVE_GIFT) {
            this.updateGiftsData(nextProps.lastAction.reactionName, nextProps.lastAction.payload, nextProps.lastAction.storyID, nextProps.lastAction.type)
          } else if (nextProps.lastAction.type == Types.REMOVE_LIKE) {
            this.currentPageStatus = JSON.parse(JSON.stringify(this.currentPageStatus))
            this.currentPageStatus.isLikeChanged = false
            this.currentPageStatus.isLiked = false
            if (this.currentPageStatus.likeCount > 0) this.currentPageStatus.likeCount--

            if (this.currentPageStatus.giftsCount) {
              if (Utils.checkData(this.currentPageStatus.giftsCount[Constants.LIKE.toUpperCase()])) {
                this.currentPageStatus.giftsCount[Constants.LIKE.toUpperCase()]--
              } else {
                this.currentPageStatus.giftsCount[Constants.LIKE.toUpperCase()] = 0
              }
            }
          }
        }
      }

      if (nextProps.lastAction.payload && !nextProps.lastAction.storyID) {
        if (nextProps.lastAction.type == Types.ADD_LIKE) {
          this.addLikeInStory(nextProps.lastAction.payload)
        } else if (nextProps.lastAction.type == Types.ADD_GIFT || nextProps.lastAction.type == Types.REMOVE_GIFT) {
          this.updateGiftsData(nextProps.lastAction.reactionName, nextProps.lastAction.payload, nextProps.lastAction.storyID, nextProps.lastAction.type)
        } else if (nextProps.lastAction.type == Types.REMOVE_LIKE) {
          this.removeLikeInStory(nextProps.lastAction.payload)
        }
      } else if (nextProps.lastAction.storyID) {
        if (nextProps.lastAction.type == Types.ADD_LIKE) {
          this.addLikeInStory(null, nextProps.lastAction.storyID)
        } else if (nextProps.lastAction.type == Types.ADD_GIFT || nextProps.lastAction.type == Types.REMOVE_GIFT) {
          this.updateGiftsData(nextProps.lastAction.reactionName, nextProps.lastAction.payload, nextProps.lastAction.storyID, nextProps.lastAction.type)
        } else if (nextProps.lastAction.type == Types.REMOVE_LIKE) {
          this.removeLikeInStory(null, nextProps.lastAction.storyID)
        }
      }
    }

    if (nextProps.lastAction.type == Types.LIKE_COMMENT) {
      if (nextProps.lastAction.storyID && nextProps.lastAction.commentID) {
        this.addLikeInComment(nextProps.lastAction.storyID, nextProps.lastAction.commentID, nextProps.lastAction.subCommentID)
      }
    }

    if (nextProps.lastAction.type == Types.DISLIKE_COMMENT) {
      if (nextProps.lastAction.storyID && nextProps.lastAction.commentID) {
        this.removeLikeInComment(nextProps.lastAction.storyID, nextProps.lastAction.commentID, nextProps.lastAction.subCommentID)
      }
    }

    if ((nextProps.lastAction.type == Types.SENT_POST)) {
      if (nextProps.lastAction.storyID) {
        this.addSentPostInStory(nextProps.lastAction.storyID)
      }
    }

    if ((nextProps.lastAction.type == Types.ADD_COMMENT)) {
      if (nextProps.lastAction.storyID) {
        this.addCommentInStory(nextProps.lastAction.storyID)
      }
    }

    if (this.comicURL && this.comicURL == nextProps.pathUrl) {
      this.isAlreadyPurchased = nextProps.isAlreadyPurchased;
      this.isAlreadyPurchasedFromInfluence = nextProps.isAlreadyPurchasedFromInfluence;
    }

    if (nextProps.route.params.forcePush && !this.isNavigationInProgress) { // For Push notification and Branch link if user is on another 
      this.isNavigationInProgress = true;
      this.props.navigation.setParams({ forcePush: false })
      this.openChapter(nextProps.route.params.comicHome)
      setTimeout(() => {
        this.isNavigationInProgress = false
      }, 2000);
    }

    if (nextProps.route.params.forceReload && !this.isCompletePageLoadInProgress) {
      this.isCompletePageLoadInProgress = true
      this.props.navigation.setParams({ forceReload: false })
      if (nextProps.route.params.isLoginSuccess) {
        this.props.navigation.setParams({ isLoginSuccess: false })
        setTimeout(() => {
          if (nextProps.route.params.showInviteSheet) {
            this.props.navigation.setParams({ showInviteFriend: false })
            this.navigateToInviteBottomSheet()
          }
          Utils.showToast("You’ve successfully signed in.", "bottom", null, 3000)
        }, 2000)
      }
      this.reloadChapter()
      Utils.log("forceReload--->", nextProps.route.params.pathUrl)
    }

    if (nextProps.route.params.isAuthCompletedForSubs && this.productReqToPuchase && Utils.isSubscriptionURL(this.comicURL)) {
      this.props.navigation.setParams({ isAuthCompletedForSubs: false })
      Utils.showToast("You have signed in successfully", "top", null, 3000)
      setTimeout(() => {
        if (!SessionManager.instance.hasAnySubscriptionPurchase()) { //Show only those user who has not purchased any subscription          
          if (settings.isAndroidDevice) {
            if (!this.productReqToPuchase || this.productReqToPuchase.productId == null) {
              this.productReqToPuchase = { productId: nextProps.route.params.productId }
            }
            this.fetchIAPProducts([this.productReqToPuchase.productId]).then((products) => {
              if (products && products.length > 0) {
                this.requestPurchase(products[0])
              }
            })
          } else {
            if (this.productReqToPuchase && this.productReqToPuchase.productId) {
              this.requestPurchase(this.productReqToPuchase)
            }
          }
          this.productReqToPuchase = null
        } else {
          this.reloadChapter()
        }
      }, 2000)
    }
  }

  loadComic(url, ignoreCache) {
    const isSharedByPremiumUser = SessionManager.instance.isLastGiftedComicLink(url)
    this.props.readComic(url, ignoreCache, isSharedByPremiumUser, this.onComicLoad)
  }

  async onComicLoad(response, pageStatus) {
    const params = this.props.route.params
    this.currentPageStatus = JSON.parse(JSON.stringify(pageStatus))
    var comicData = { ...response, giftedItems: this.currentPageStatus.giftedItems, aboveStory: params.aboveStoryPanel, belowStory: params.belowStoryPanel, isNavFromSeriesPage: params.isNavFromSeriesPage, myFeedID: params.myFeedID, isNavFromNotification: params.isNavFromNotification }
    let allComicsSwitchValue = SessionManager.instance.getShowAllComics()
    if (comicData.isUserAlreadyReadComic || comicData.isComicUnlocked) {
      if (this.isComicUnlockedCurrently) {
        this.isComicUnlockedCurrently = false
      } else if (UserSession.instance.getMonthlyMaxComicReadQuota() >= 0 && !Utils.isPremiumComic(comicData) && !this.isPreviousViewedToastShown) {
        let message = `Previously viewed comic. Doesn't affect your comic limit as a guest.`
        Utils.showToast(message, "bottom", "viewedComicMsg", 5000)
        this.isPreviousViewedToastShown = true
      }
    }
    if (!Utils.isComicURL(this.comicURL)) {
      comicData = JSON.parse(JSON.stringify(response))
      comicData.panels = []
      this.initialPanels = response ? JSON.parse(JSON.stringify(response.panels)) : []
    }

    const purchasePanels = response.panels.filter((panel) => panel.action === Constants.IN_APP_PURCHASE);
    const subsProducts = purchasePanels.map((panel) => panel.inAppPurchase);
    if (subsProducts.length > 0) {
      await this.fetchIAPProducts(subsProducts).then((products) => {
        for (let index = 0; index < response.panels.length; index++) {
          const panel = response.panels[index];
          const inAppProduct = products.find(
            (product) => panel.inAppPurchase === product.productId
          );
          if (inAppProduct) {
            response.panels[index] = { ...inAppProduct, ...panel }
          } else if (panel.inAppPurchase && !inAppProduct) {
            response.panels.splice(index, 1);
            index--;
          }
        }
      })
    }

    this.getFeedPanelEpisodes()
    this.setState({ comicData }, () => {
      if (!Utils.isComicURL(this.comicURL)) { // Fetching Feed and Stories
        let reqData = null
        if (response && response.panels && response.panels.length > 0) {
          for (const iterator of response.panels) {
            if (iterator) {
              if (iterator.template == "newsfeed" || iterator.template == Constants.TEMPLATE_TYPE_STORIES) {
                reqData = iterator
                break
              }
            }
          }
        }
        if (reqData) {
          this.reqData = reqData
          this.getUserFeeds(allComicsSwitchValue, true)
        }
      } else {
        FirebaseManager.instance.stopTrace(this.performanceTrace)
        this.isRefreshingInProgress = false
        this.isCompletePageLoadInProgress = false
      }
    })
  }

  async fetchIAPProducts(finalItems) {
    try {
      const products = await IAPManager.default.getProducts(finalItems)
      return products;
    } catch (error) {
      Utils.log('Getting Product info error ' + error)
      Alert.alert('', 'Failed to load Products')
      return null;
    }
  }

  redeemInfluencePoint() {
    let influencePoints = UserSession.instance.getUserInfluencePoints()
    let comicInfluencePoints = this.state.comicData && this.state.comicData.influencePoints
    let hasSufficientBalance = influencePoints.balance >= comicInfluencePoints
    if (hasSufficientBalance) {
      let comicUrl = Utils.getMeaningFullURL(this.props.pathUrl)
      let data = { point: comicInfluencePoints, url: comicUrl }
      this.props.redeemInfluencePoint(data, this.onComicUnlocked)
    } else {
      let prefixMsg = (this.unlockPanelType == Constants.UNLOCK_BONUS_PANEL) ? "unlock the bonus panel." : "unlock this comic."
      Utils.showToast(`You don’t have enough influence points to ${prefixMsg}`)
      this.unlockPanelType = null
    }
  }

  onComicUnlocked(data) {
    let comicUrl = Utils.getMeaningFullURL(this.props.pathUrl)
    let comicInfluencePoints = this.state.comicData && this.state.comicData.influencePoints
    if (data) {
      this.isComicUnlockedCurrently = true
      UserSession.instance.addNewUnlockedComic(comicUrl)
      this.reloadChapter()
      setTimeout(() => {
        let prefixMsg = (this.unlockPanelType == Constants.UNLOCK_BONUS_PANEL) ? "unlock the bonus panel." : "unlock this comic."
        let multiplePointsComic = comicInfluencePoints == 1 ? "point" : "points"
        Utils.showToast(`You've used ${comicInfluencePoints} influence ${multiplePointsComic} to ${prefixMsg}`)
        this.unlockPanelType = null
      }, 2000)
    }
  }

  componentDidUpdate() {
    InteractionManager.runAfterInteractions(() => {
      if (this.props.route.params && this.props.route.params.reload) {
        this.props.navigation.setParams({ reload: false })
        this.loadComic(this.props.route.params.comicHome, true)
      }
    })
  }

  async componentDidMount() {
    InteractionManager.runAfterInteractions(() => {
      BackHandler.addEventListener('hardwareBackPress', this.handleBackButton);
      this._componentDidMount();
    })
  }

  _componentDidMount = async () => {
    Utils.log("Styling ", this.context)

    this._focusUnsubscribe = this.props.navigation.addListener('focus', () => {
      this._onViewWillFocus()
      this._onViewDidFocus()
    });

    this.subscribedAlerts = JSON.parse(JSON.stringify(this.props.alerts))

    const params = this.props.route.params
    this.tappedGiftedItems = params && params.tappedGiftedItems ? params.tappedGiftedItems : {}
    if (params && params.comicHome) {
      this.comicURL = params.comicHome
      this.loadComic(this.comicURL, true)
    } else {
      this.comicURL = settings.getComicHomeURL()
      const isNewUser = await SharedPreferences.getData(settings.INITIAL_INTRO_LOAD_KEY)
      this.isFirstTimeUser = !isNewUser || isNewUser !== "true";
      if (!this.isFirstTimeUser) {
        this.loadComic(this.comicURL, true)
      }

      if (this.isFirstTimeUser) {
        this.isFirstTimeUser = true;
        SharedPreferences.saveData(settings.INITIAL_INTRO_LOAD_KEY, "true")
        this.openChapter(settings.getComicHomeURL())
      }

      this.addAppInstallInfluencePoint()
    }

    FirebaseManager.instance.startTrace(null, this.comicURL).then((value) => {
      this.performanceTrace = value
    })

    this.props.navigation.setParams({
      pathUrl: this.comicURL,
      navOpacity: this.navOpacity,
      topNavbar: this.topNavbar,
    })

    if (Platform.OS == 'ios') {
      try {
        const result = await SafeArea.getSafeAreaInsetsForRootView()
        SessionManager.instance.updateContainerInsets(result.safeAreaInsets)
        this.setState({ containerInsets: result.safeAreaInsets })
      } catch (error) {
        Utils.log(error.status + error)
      }
    }

    const channelName = Utils.getChannelName(this.comicURL)
    const isComicURL = Utils.isComicURL(this.comicURL)
    let seriesHomeName = SessionManager.instance.getSeriesName(channelName)
    if (isComicURL) {
      this.setState({ seriesHomeName })
    }

    this.downloadSeriesHomeProfile(channelName).then((imageLocalPath) => {
      if (imageLocalPath) {
        this.setState({ profilePicURL: imageLocalPath, seriesHomeName: seriesHomeName })
      }
    })

    this.getContinueReadingComic()

    if (params && params.hasUserInfo) {
      return;
    }

    this.showNotificationPermissionAlert()
    this.props.fetchAppConfigList()

    FirebaseManager.instance.addInitialNotificationListener();

    this.props.getUserDetails(this.onUserInfoUpdated)
    FirebaseManager.instance.hasNotificationPermission()
  }

  getFeedPanelEpisodes() {
    if ((Utils.isComicPage(this.comicURL) || Utils.isSubscriptionURL(this.comicURL)) && !Utils.isEmptyObject(this.currentPageStatus)) {
      let requestedData = {
        action: Utils.getMeaningFullURL(this.props.pathUrl),
        series: Utils.getChannelName(this.comicURL),
        episodeID: this.currentPageStatus.storyID
      }
      this.props.getFeedEpisodes(requestedData, this.onFeedEpisodesFetched)
    }
  }

  getContinueReadingComic() {
    if (Utils.isChannelURL(this.comicURL)) {
      const channelName = Utils.getChannelName(this.comicURL)
      let requestedData = { series: channelName, isContinueReading: true }
      this.props.getNavigationComics(requestedData, this.onContinueReadingComicFetched)
    }
  }

  onContinueReadingComicFetched(response) {
    if (response) {
      if (response.image) {
        response.image = Utils.resolvePath(this.props.pathUrl, response.image)
      }
      this.setState({ continueReadingComic: response })
    }
  }

  onFeedEpisodesFetched(response) {
    this.setState({ comicFeedEpisodes: response ? response : {}, isEpisodsAPIResponded: true })
  }

  onUserInfoUpdated(userData, userID, purchases) {
    if (SessionManager.instance.hasAnySubscriptionPurchase() || (this.props.alerts && Object.keys(this.props.alerts).length > 1)) {
      Utils.showInAppReview();
    }

    this.props.hasNotificationPermission()
  }

  async showNotificationPermissionAlert() {
    let hasShownPermissionAlert = await SharedPreferences.getData(settings.SHOW_NOTIFICATION_PERMISSION_ALERT_KEY)
    if (hasShownPermissionAlert != "true") {
      let permissionStatus = null;
      if (Platform.OS == 'android' && Platform.Version >= 33) {
        try {
          permissionStatus = await PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS);
          if (!permissionStatus) {
            permissionStatus = await PermissionsAndroid.request(
              PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
              {
                title: 'Notification Permission',
                message: 'App would like to access your notification permission.',
                buttonNegative: 'Cancel',
                buttonPositive: 'OK',
              }
            );

            if (permissionStatus === "granted") {
              let notifAlerts = JSON.parse(JSON.stringify(UserSession.instance.getCurrentUserNotifAlerts()))
              if (notifAlerts && notifAlerts['push'] && notifAlerts['push'].enabled == false) {
                notifAlerts['push'].enabled = true;
                this.props.setNotificationAlerts({ "notificationSettings": notifAlerts })
              }
            }
            SharedPreferences.saveData(settings.SHOW_NOTIFICATION_PERMISSION_ALERT_KEY, "true") // iOS permission dialog is show by PushNotificationIOS library on App launch
          }
        } catch (error) {
          console.log("notificationStatus_Error " + err);
        }
      }
    }
  }

  async addAppInstallInfluencePoint() {
    let isInfluencePointsGiven = await SharedPreferences.getData(settings.INFLUENCE_POINTS_SP_KEY)
    if (isInfluencePointsGiven != "true") {
      this.props.addInfluencePoint({ action: settings.INFLUENCE_POINTS_ACTIONS.INSTALL_APP }, (isSuccess) => {
        if (isSuccess) {
          this.props.getUserDetails(null)
        }
      })
    }
  }

  async downloadSeriesHomeProfile(channelName) {
    try {
      let finalUrl = Utils.getSeriesProfileURL(channelName)
      return await ImageDownloader.downloadPanelImage(finalUrl)
    } catch (error) {
      Utils.log("Downloading failed image channelName " + channelName + " error " + error)
      return error;
    }
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.handleBackButton);
    if (this._focusUnsubscribe) {
      this._focusUnsubscribe()
    }
  }

  handleBackButton() {
    if (Utils.isHomeURL(this.comicURL)) {
      BackHandler.exitApp();
      return true
    }

    this.props.navigation.goBack()
    return true;
  }

  isUserDetailsEmpty(showActionSheet = true) {
    if (UserSession.instance.isUserDetailsEmpty()) {
      let sheetTitle = ''
      let sheetMessage = 'You need to complete your profile first.'
      let sheetOptions = [Constants.EDIT_PROFILE, Constants.CANCEL]
      this.changeActionSheetTitleAndButton(sheetTitle, sheetOptions, showActionSheet, sheetMessage)
      return true
    }
    return false
  }

  onShowCommentPress(actionName, panelItem, selectedIndex, commentItem, storyData = null, showComments = true) {
    if (!showComments && !this.canUserComment()) {
      return
    }

    if (storyData) {
      const { storyID = null, action = null, series = null } = storyData

      let storyReqData = null
      if (Utils.checkData(storyID)) {
        storyReqData = { storyID: storyID, onStoryUpdated: this.onStoryUpdated }
      } else if (Utils.checkData(action)) {
        storyReqData = { action: action, onStoryUpdated: this.onStoryUpdated, series }
      } else {
        return null
      }

      return this.props.navigation.push(Constants.POST_COMMENTS_SCREEN, { storyData: storyReqData })
    }

    super.onShowCommentPress(actionName, panelItem, selectedIndex, commentItem) //calling FeedComponent method
  }

  onCountPress(actionName, storyID, panelItems) {
    Utils.log(actionName, "---", storyID)
    if (!Utils.checkData(storyID)) {
      return null
    }

    let subRoute = actionName == Constants.FEED_LIKE ? Constants.STORY_REACTIONS_LIST_SCREEN : Constants.USER_LIST_SCREEN
    let navTitle = actionName === Constants.REPOSTED ? actionName : Constants.REACTIONS
    let subParams = { configFor: actionName, storyID: storyID, props: this.valueProps, panelItems: panelItems }
    this.props.navigation.push(subRoute, { ...subParams })
  }

  async onIconPress(actionName, panelItems, storyID) {
    if (actionName == Constants.REPOST) {
      this.clickedStoryID = panelItems != null ? panelItems.storyID : storyID
      this.clickedPanelItems = panelItems != null ? panelItems : null
      this.isUserDetailsEmpty(false)

      if (Utils.isComicPage(this.comicURL)) {
        if (this.clickedPanelItems.actionType == "website") {
          const shareComicURL = await DeepLinkManager.instance.getShareBranchURL(this.clickedPanelItems)
          this.clickedPanelItems.action = shareComicURL
        }
      }

      this.configBottomSheet = Constants.SHARE
      this.setState({ showShareBottomSheet: true })
    } else if (actionName === Constants.SHARE) {
      let getCurrentPageData = SessionManager.instance.getCurrectComicPageData()
      let relativeUrl = Utils.getMeaningFullURL(this.props.pathUrl)
      this.clickedStoryID = panelItems.storyID ? panelItems.storyID : getCurrentPageData.storyID
      this.clickedPanelItems = { action: relativeUrl, storyID: this.clickedStoryID }
      this.configBottomSheet = Constants.SHARE
      this.setState({ showShareBottomSheet: true })
    }
  }

  navigateToInviteBottomSheet() {
    let isSignedIn = UserSession.instance.isLoggedInUser()
    if (isSignedIn) {
      this.configBottomSheet = Constants.INVITE_FRIENDS
      this.setState({ showInviteBottomSheet: true })
    } else {
      let sheetTitle = ''
      let sheetMessage = 'You must be signed in to invite friends'
      let sheetOptions = [Constants.SIGN_IN, Constants.CANCEL]
      this.changeActionSheetTitleAndButton(sheetTitle, sheetOptions, true, sheetMessage)
    }
  }

  async getStoryDetails(data) {
    await this.props.getStoryDetails(data, this.updateComicDataItem)
  }

  updateComicDataItem(storyDetails, data) {
    let panelIndex = -1
    if (Utils.isEmptyObject(this.state.comicData) || !data) {
      return
    }

    for (const index in this.state.comicData.panels) {
      let jsonPanel = this.state.comicData.panels[index]
      let actionURL = jsonPanel.action ? jsonPanel.action : null
      let isFeedJSONPanel = jsonPanel.template == "story" || jsonPanel.template == "toc";
      const isFeedTemp = storyDetails.template == "story" || storyDetails.template == "toc";
      if (data.data.action == actionURL && isFeedTemp && isFeedJSONPanel) {
        this.nonStoryPanelsMap.set(actionURL, { ...jsonPanel, ...storyDetails, template: jsonPanel.template, isSeriesTopExtraPanel: true })
        panelIndex = index
        break
      }
    }

    let comicData = JSON.parse(JSON.stringify(this.state.comicData))

    if (panelIndex >= 0) {
      comicData.panels[panelIndex] = this.nonStoryPanelsMap.get(data.data.action)
      this.setState({ comicData: comicData })
      this.nonStoryPanelsMap.delete(data.data.action)
    }
  }

  resetActionSheet() {
    let sheetTitle = ''
    let sheetMessage = Constants.SIGN_IN_ACTION_SHEET_MESSAGE
    let sheetOptions = [Constants.SIGN_IN, Constants.CANCEL]
    this.changeActionSheetTitleAndButton(sheetTitle, sheetOptions, false, sheetMessage)
  }

  changeActionSheetTitleAndButton(sheetTitle, sheetOptions, showActionSheet = false, sheetMessage = '', configFor) {
    this.setState({ sheetTitle: sheetTitle, sheetMessage: sheetMessage, sheetOptions: sheetOptions, showShareBottomSheet: false }, () => {
      if (showActionSheet) {
        this.actionSheet.configurationFor(configFor)
        this.actionSheet.show()
      }
    })
  }

  signInSheet(message, cancenButtonText, configFor) {
    let sheetTitle = ''
    let sheetMessage = message ? message : Constants.SIGN_IN_ACTION_SHEET_MESSAGE
    let cancelButton = cancenButtonText ? cancenButtonText : Constants.CANCEL
    let sheetOptions = [Constants.SIGN_IN, cancelButton]
    this.changeActionSheetTitleAndButton(sheetTitle, sheetOptions, true, sheetMessage, configFor)
  }

  onActionSheetButtonPress(index, configFor) {
    if (index == 0) {
      if (this.state.sheetOptions[0] == Constants.SIGN_IN) {
        this.navigateToSignIn(configFor)
      } else if (this.state.sheetOptions[0] == Constants.SIGN_UP) {
        this.navigateToSignUp()
      } else if (this.state.sheetOptions[0] == Constants.UPGRADE_TO_A_PREMIUM_PLAN) {
        this.props.navigation.push('Home', { comicHome: settings.getSubscribeURL(), hasUserInfo: true })
      } else if (this.state.sheetOptions[0] == Constants.ALL_FRIENDS) {
        const params = {
          respotType: this.state.sheetOptions[0], sendToAllFriend: true,
          clickedStoryID: this.clickedStoryID, onStoryUpdated: this.onStoryUpdated,
          clickedPanelItems: this.clickedPanelItems
        }
        Utils.navigateToSubRouteWithParams(Constants.FEED_SCREEN, Constants.SEND_POST_SCREEN, this.props, params)
      } else if (this.state.sheetOptions[0] == Constants.EDIT_PROFILE) {
        this.navigateToAppropriateScreen()
      } else if (this.state.sheetOptions[0] == Constants.COPY_LINK) {
        this.copyLink()
      } else if (this.state.sheetOptions[0] == Constants.MANAGE_ALERTS) {
        const { comicData } = this.state
        let shouldRefreshOnFollow = comicData && comicData.autoRefreshOnFollow
        this.isAnyAlertEnabled = UserSession.instance.isAnyUserAlertsEnabled()
        Utils.navigateToManageAlertsPage(this.props, { configFromFollowTab: true, seriesToFollowData: this.seriesToFollowData, autoRefreshOnFollow: shouldRefreshOnFollow })
      } else if (this.state.sheetOptions[0].includes("Follow")) {
        setTimeout(() => {
          this.onFollowPress(this.props.pathUrl, false, false, false)
        }, settings.isAndroidDevice ? 0 : 500);
      }
    } else if (index == 1) {
      this.resetActionSheet()
    } else if (index == 2) {
      if (this.state.sheetOptions[2] == Constants.SELECT_FRIENDS) {
        const params = {
          respotType: this.state.sheetOptions[2], sendToAllFriend: false,
          clickedStoryID: this.clickedStoryID, onStoryUpdated: this.onStoryUpdated,
          clickedPanelItems: this.clickedPanelItems
        }
        Utils.navigateToSubRouteWithParams(Constants.FEED_SCREEN, Constants.SEND_POST_SCREEN, this.props, params)
      } else if (this.state.sheetOptions[2] == Constants.SHARE_VIA) {
        this.props.share(this.clickedPanelItems)
      } else if (this.state.sheetOptions[2] == Constants.UPGRADE_TO_A_PREMIUM_PLAN) {
        this.navigateToSubscribePage()
      } else if (this.state.sheetOptions[2] == Constants.COPY_LINK) {
        this.copyLink()
      } else if (this.state.sheetOptions[2] == Constants.SIGN_IN) {
        this.navigateToSignIn()
      } else if (this.state.sheetOptions[2] && this.state.sheetOptions[2].props && this.state.sheetOptions[2].props.children && this.state.sheetOptions[2].props.children.includes("Use") && this.state.sheetOptions[2].props.children.includes("Influence")) {
        this.redeemInfluencePoint()
      } else if (this.state.sheetOptions[2] == Constants.EARN_INFLUENCE_POINTS) {
        this.navigateToInfluenceScreen()
      }
    } else if (index == 3) {
      if (this.state.sheetOptions[3] == Constants.SHARE_VIA) {
        this.props.share(this.clickedPanelItems)
      } else if (this.state.sheetOptions[3] == Constants.COPY_LINK) {
        this.copyLink()
      } else if (this.state.sheetOptions[3] == Constants.EARN_INFLUENCE_POINTS) {
        this.navigateToInfluenceScreen()
      } else if (this.state.sheetOptions[3] == Constants.RESTORE_PURCHASE) {
        this.props.restorePurchase(false)
      } else if (this.state.sheetOptions[3] && this.state.sheetOptions[3].props && this.state.sheetOptions[3].props.children && this.state.sheetOptions[3].props.children.includes("Use") && this.state.sheetOptions[3].props.children.includes("Influence")) {
        this.redeemInfluencePoint()
      } else if (this.state.sheetOptions[3] == Constants.I_AM_A_PREMIUM_USER) {
        this.navigateToSignIn()
      }
    } else if (index == 4) {
      if (this.state.sheetOptions[4] == Constants.RESTORE_PURCHASE) {
        this.props.restorePurchase(false)
      } else if (this.state.sheetOptions[4] == Constants.I_AM_A_PREMIUM_USER) {
        this.navigateToSignIn()
      } else if (this.state.sheetOptions[4] == Constants.EARN_INFLUENCE_POINTS) {
        this.navigateToInfluenceScreen()
      } else {
        this.props.share(this.clickedPanelItems)
      }
    } else if (index == 5) {
      if (this.state.sheetOptions[5] == Constants.RESTORE_PURCHASE) {
        this.props.restorePurchase(false)
      } else if (this.state.sheetOptions[5] == Constants.I_AM_A_PREMIUM_USER) {
        this.navigateToSignIn()
      } else {
        this.props.share(this.clickedPanelItems)
      }
    } else if (index == 6) {
      if (this.state.sheetOptions[6] == Constants.RESTORE_PURCHASE) {
        this.props.restorePurchase(false)
      }
    }
  }

  copyLink() {
    let actionURL = this.clickedPanelItems && this.clickedPanelItems.action
    if (!Utils.checkData(actionURL)) {
      actionURL = settings.webBaseURL + Utils.getWebURL(Utils.getMeaningFullURL(this.comicURL))
    }

    let completeURL = actionURL
    if (!actionURL.startsWith(settings.webBaseURL) && this.clickedPanelItems && this.clickedPanelItems.actionType != "website") {
      completeURL = settings.webBaseURL + Utils.getWebURL(actionURL)
    }

    Utils.copyToClipboard(completeURL, "Link Copied")
  }

  async navigateToAppropriateScreen() {
    let routeName = Constants.USER_PROFILE_SCREEN
    let params = { isupdatingProfile: true }
    let data = this.props.userDetails
    let currentUserId = Utils.getCurrentUserId()

    if (!data.displayName || !data.gender) {
      routeName = Constants.EDIT_PROFILE_SCREEN
    } else if (!data.aboutMe || data.aboutMe == '') {
      routeName = Constants.EDIT_SOCIAL_LINKS
    }

    if (routeName == Constants.USER_PROFILE_SCREEN) {
      this.props.navigation.push(routeName, { ...params })
    } else {
      Utils.navigateToDrawerLoginRoute(this.props, routeName, params)
    }
  }

  navigateToStackTop() {
    this.props.navigation.dispatch(StackActions.popToTop());
  }

  openHomePage() {
    if (Utils.isHomeURL(this.comicURL)) {
      this.scrollToTopOrReload()
      return
    }

    const parent = this.props.navigation.getParent()
    let index = parent.getState().index
    if (index > 0) {
      this.props.navigation.dispatch(StackActions.popToTop());
    } else {
      this.openChapter(settings.getComicHomeURL())
    }
  }

  openSeriesHomePage(pathURL) {
    const channelId = Utils.getChannelName(this.props.pathUrl)
    let seriesURL = null
    if (channelId == settings.TINYVIEW_CHANNEL_NAME) {
      seriesURL = settings.DIRECTORY_COMIC_URL
    } else {
      seriesURL = Utils.getComicSeriesURL(this.props.pathUrl);
    }

    this.openChapter(seriesURL)
  }

  navigateToSignUp() {
    var params = { isForLoginProcess: true }
    Utils.navigateToDrawerLoginRoute(this.props, Constants.LOGIN_SCREEN, params)
  }

  navigateToSignIn(configFor = null) {
    let params = { isForLoginProcess: true }
    if (configFor == Constants.ACTION_SHEET_FOR_UPGRADE_PLAN) {
      params = { ...params, forAuthOnly: true, metadata: { productId: this.productReqToPuchase.productId } }
    }
    Utils.navigateToDrawerLoginRoute(this.props, Constants.LOGIN_SCREEN, params)
  }

  navigateToMyProfile() {
    this.props.navigation.push(Constants.USER_PROFILE_SCREEN)
  }

  navigateToSubscribePage() {
    this.openChapter(settings.getSubscribeURL())
  }

  navigateToInfluenceScreen() {
    let routeName = Constants.MY_INFLUENCE_COMPONENT
    let params = { isupdatingProfile: true }
    this.props.navigation.push(routeName, { ...params })
  }

  scrollToTopOrReload = () => {
    if (!this.isAtTop && this.flatlist) {
      this.flatlist.scrollToOffset({ animated: true, offset: - navigationStyle.navHeight });
      this.isAtTop = true
    } else {
      this.reloadChapter(true)
    }
  }

  updateFlatListProps(value, flatlist) {
    this.isAtTop = value
    this.flatlist = flatlist
  }

  onShowAllComicSwitchTap(value) {
    if (this.isCompletePageLoadInProgress) {
      return;
    }
    SharedPreferences.setShowAllComicsValue(value)
    this.props.updateShowAllComicsSettings(value)
    this.hasMoreData = true
    this.isEmptyFeedDisplayed = false
    this.props.navigation.dispatch(StackActions.popToTop());
  }

  onFollowPress(pathUrl, isFollowing, forceFollowSeries = false, forSync = false, showActionSheet = true) {
    const isConnected = NetworkUtils.instance.isAvailable()
    if (!isConnected) {
      SessionManager.instance.showErrorAlert("No Internet Connection", "An error occurred while trying to access the server. Please check your Internet connection and try again.")
      return;
    }

    if (!forSync && (Utils.isHomeURL(pathUrl) || Utils.isTVSeriesURL(pathUrl))) { // Syncing Home and What's Tinyview comic follow/unfollow
      if (Utils.isHomeURL(pathUrl)) {
        this.onFollowPress(settings.getWhatsTinyviewURL(), isFollowing, forceFollowSeries, true, true)
      } else {
        this.onFollowPress(settings.getComicHomeURL(), isFollowing, forceFollowSeries, true, true)
      }
    } else {
      if (showActionSheet && !isFollowing && !UserSession.instance.isAnyUserAlertsEnabled()) {
        const channelName = Utils.getUserVisibleChannelName(pathUrl)
        this.seriesToFollowData = { title: channelName, action: Utils.getMeaningFullURL(pathUrl) }
        let isTinyviewChannel = channelName == Constants.LETTER_CASE_TINYVIEW
        let sheetTitle = Constants.MANAGE_ALERTS_ACTIONSHEET_TITLE
        let sheetMessage = isTinyviewChannel ? `You want to follow ${channelName} but we have no way of reaching you. Manage alerts and let us know how to send you updates from ${channelName}.` : `You want to follow ${channelName} but we have no way of reaching you. Manage alerts and know when ${channelName} publishes a new comic.`
        let sheetOptions = [Constants.MANAGE_ALERTS, Constants.NOT_NOW]
        this.changeActionSheetTitleAndButton(sheetTitle, sheetOptions, true, sheetMessage)
      } else {
        let isSubscribed = this.isSubscribedToAlert(pathUrl)
        if (isFollowing) {
          if (isSubscribed) this.subscribeToAlert(false, pathUrl)
        } else {
          if (!isSubscribed) {
            return this.subscribeToAlert(true, pathUrl)
          }
          if (this.props.alerts) {
            const seriesURL = Utils.resolvePath(this.props.pathUrl, Utils.getComicSeriesURL(pathUrl));
            const isFollowingSeries = this.isSubscribedToAlert(seriesURL) //this.props.getLikesData.includes(Utils.getMeaningFullURL(seriesURL))
            if (!isFollowingSeries || !isSubscribed) {
              if (forceFollowSeries) {
                this.subscribeToAlert(true, pathUrl)
              } else {
                this.showFollowSeriesAlert(seriesURL)
              }
            }
          }
        }
      }
    }
  }

  closeBottomSheet(fromGiftTap = false, item, feedData) {
    this.setState({ showBottomSheet: false, showShareBottomSheet: false, showInviteBottomSheet: false }, () => {
      if (fromGiftTap) {
        this.requestPurchase(item, feedData)
      }
    })
  }

  onLikePress = (selComic) => {
    const isConnected = NetworkUtils.instance.isAvailable()
    if (!isConnected) {
      SessionManager.instance.showErrorAlert("No Internet Connection", "An error occurred while trying to access the server. Please check your Internet connection and try again.")
      return;
    }
    this.setState({ showBottomSheet: true })
    this.tappedComicData = selComic
  }

  openChapter = (path, item = null, isSeriesNavigation = null, myFeedID = null, isNotificationNav) => {
    if (this.isViewPushed) {
      return
    }
    this.isViewPushed = true
    let isCurrSeriesPage = Utils.isChannelURL(this.props.pathUrl) || isSeriesNavigation
    let comicFeedID = myFeedID

    if (Utils.isHomeURL(this.props.pathUrl) && Utils.isComicURL(path)) {
      let homePanels = this.state.comicData && this.state.comicData.panels
      for (var panelsKey in homePanels) {
        if (homePanels[panelsKey].action == path) {
          if (!isCurrSeriesPage && !isNotificationNav && !SessionManager.instance.getShowAllComics() && !comicFeedID) {
            comicFeedID = homePanels[panelsKey].myFeedID
          }
          break;
        }
      }
    }

    if (path == settings.getInfluencePageURL()) {
      Utils.navigateToInfluencePage(this.props)
    } else if (!Utils.isHomeURL(path) && !Utils.isChannelURL(path) && !Utils.isComicURL(path) && item && item.item.user && item.item.user.userId) {
      this.navigateToUserProfile(item)
    } else {
      let redirectPath = this.resolvePath(path)
      let tappedGiftedItems = item && item.item && item.item.giftedItems ? item.item.giftedItems : {}
      this.props.navigation.push('Home', { comicHome: redirectPath, hasUserInfo: true, tappedGiftedItems: tappedGiftedItems, isNavFromSeriesPage: isCurrSeriesPage, myFeedID: comicFeedID, isNavFromNotification: isNotificationNav })
    }

    setTimeout(() => {
      this.isViewPushed = false;
    }, 500);
  }

  onPageStatusFetch(data) {
    this.currentPageStatus = data
    SessionManager.instance.setCurrectComicPageData({ storyID: data.storyID, pageURL: data.pageURL })
  }

  reloadChapter = () => {
    const isConnected = NetworkUtils.instance.isAvailable()
    if (!isConnected) {
      SessionManager.instance.showErrorAlert("No Internet Connection", "An error occurred while trying to access the server. Please check your Internet connection and try again.")
      return;
    }
    this.hasMoreData = true
    this.isEmptyFeedDisplayed = false
    this.isPreviousViewedToastShown = true
    this.getContinueReadingEpisodes()
    if (!SessionManager.instance.getIsGeneratingEmailLink()) {
      this.props.getUserDetails()
      this.props.clearComicPageInfo()
      this.loadComic(this.comicURL, true)
      this.getContinueReadingComic()
    } else {
      this.props.getUserDetails(this.onUserDetailsFetched)
    }
  }

  onUserDetailsFetched(data) {
    if (data != "MERGED") {
      this.props.clearComicPageInfo()
      this.loadComic(this.comicURL, true)
    }
  }

  requestPurchase(product, feedData) {
    if (Utils.isSubsProduct(product.productId)) { //Show action sheet to Sign in
      if (!UserSession.instance.isLoggedInUser()) {
        this.productReqToPuchase = product
        this.signInSheet(Constants.UPGRADE_PLAN_SIGN_IN_ACTION_SHEET_MSG, Constants.NO_THANKS, Constants.ACTION_SHEET_FOR_UPGRADE_PLAN)
      } else {
        this.setState({ loadingProductId: product.productId }, () => {
          this.props.requestPurchase(product, null, this.onSubscriptionSuccess)
        })
      }
    } else {
      const userBalance = UserSession.instance.getUserReferralBalance()
      const referralBalance = parseFloat(userBalance.toFixed(2))
      const productCost = product?.cost ? parseFloat(product.cost.toFixed(2)) : null
      if (productCost && referralBalance >= productCost) {
        const storyID = feedData?.storyID
        const transaction = { productId: product?.productId }
        const reqData = { data: { storyID, platform: Constants.INTERNAL_PAYMENT_PLATFORM, productInfo: product, transaction } }
        this.props.addNewTransaction(reqData, feedData, this.onGiftItemSent)
      } else {
        this.props.requestPurchase(product, feedData, this.onGiftItemSent)
      }
    }
  }

  onSubscriptionSuccess() {
    this.setState({ loadingProductId: null })
  }

  resolvePath(pageURL) {
    return Utils.resolvePath(this.props.pathUrl, pageURL)
  }

  isHideReactionComponentIcons() {
    const pathUrl = this.comicURL
    const isAtWhatsTinyview = pathUrl == settings.getWhatsTinyviewURL()
    if (isAtWhatsTinyview) {
      return true
    }
    return false
  }

  async showFollowSeriesAlert(seriesURL) {
    const buttons = [
      {
        text: "No, Thanks"
      },
      {
        text: "OK", onPress: () => {
          this.onFollowPress(seriesURL, false, true, false)
        }
      }
    ];

    var channelName = Utils.getUserVisibleChannelName(seriesURL)
    let message = "Would you like to follow " + '"' + channelName + '"? We will show you their comics on the homepage.'
    if (channelName && channelName.toLowerCase() == settings.TINYVIEW_CHANNEL_NAME) {
      message = "Get alerts for new comic series and app features"
    }
    this.showAlert("", message, buttons);
  }

  subscribeToAlert(isSubscribe, seriesURL = null) {
    const { comicData, seriesHomeName } = this.state
    let shouldRefreshOnFollow = comicData && comicData.autoRefreshOnFollow
    let showToastMsg = SessionManager.instance.hasAnySubscriptionPurchase() || !(comicData && comicData.autoRefreshOnFollow)
    this.props.updateAlertSubscription(isSubscribe, seriesURL, showToastMsg);

    if (Utils.isComicPage(seriesURL)) {
      const channelName = Utils.getChannelName(seriesURL)
      if (isSubscribe) {
        this.subscribedAlerts = {
          ...this.subscribedAlerts,
          [channelName]: true,
        };
      } else {
        delete this.subscribedAlerts[channelName];
      }
    }

    if (shouldRefreshOnFollow && !SessionManager.instance.hasAnySubscriptionPurchase()) {
      this.props.clearComicPageInfo()
      this.loadComic(this.comicURL, true)

      if (isSubscribe) {
        Utils.showToast(`Thanks for following ${seriesHomeName}!`, "bottom", "seriesFollowed", 5000)
      }
    }
  }

  openWebView(url) {
    this.props.openWebView(url)
  }

  showAlert(title, message, buttons) {
    Alert.alert(title, message, buttons);
  }

  isSubscribedToAlert(seriesURL = null) {
    const alertName = this.getAlertChannelName(seriesURL);
    return this.props.alerts && this.props.alerts[alertName] == true ? true : false
  }

  isSubscribedWithAlert(alerts = null, seriesURL = null) {
    const alertName = this.getAlertChannelName(seriesURL);
    return alerts && alerts[alertName] == true ? true : false
  }

  onActionSheetPress(index, configFor = null) {
    this.onActionSheetButtonPress(index, configFor)
  }

  onChangeSeriesTab(showComics) {
    const { comicData } = this.state
    let headerPanels = []
    if (comicData && comicData.panels && comicData.panels.length > 0) {
      for (const index in comicData.panels) {
        let panelData = comicData.panels[index]
        if (panelData.template === Constants.TEMPLATE_TYPE_STORIES) {
          headerPanels.push(panelData)
          break;
        }
        headerPanels.push(panelData)
      }
    }
    this.state.comicData.panels = headerPanels
    SessionManager.instance.setActiveSeriesTab(showComics)
    this.hasMoreData = true
    this.getUserFeeds(false, true)
  }

  getAlertChannelName(seriesURL = null) {
    let alertName = Utils.getChannelName(this.comicURL)
    if (seriesURL) {
      alertName = Utils.getChannelName(Utils.resolvePath(this.props.pathUrl, seriesURL))
    }

    return alertName;
  }

  onSeriesHomeClicked() {
    const redirectPath = this.resolvePath(Utils.getComicSeriesURL(this.comicURL))
    this.props.navigation.push('Home', { comicHome: redirectPath, hasUserInfo: true, })
  }

  isComicDataEmpty() {
    if (Utils.isEmptyObject(this.state.comicData)) {
      return true
    }

    if (this.state.comicData.panels && (!this.state.comicData.panels.length > 0)) {
      return true
    }
    return false
  }

  getUnlimitedComicAccessSheet() {
    const isSubscriber = SessionManager.instance.hasAnySubscriptionPurchase()
    let sheetTitle, sheetMessage
    if (isSubscriber) {
      sheetTitle = Constants.WHY_SIGNUP
      sheetMessage = `Sign Up or Sign In using email or phone so we can remember you as a premium user. This also allows you to use premium benefits on any device or the Web.`
    } else {
      let diffActionsInfluencePoints = SessionManager.instance.getInfluencePointsValuesForActions()
      sheetTitle = Constants.GET_UNLIMITED_ACCESS
      sheetMessage = `Sign Up or Sign In using email or phone for unlimited access. It's free. Also, earn ${diffActionsInfluencePoints.SIGNUP} influence points when you Sign Up and use them to unlock bonus panels.`
    }
    let sheetOptions = [Constants.SIGN_UP, Constants.NOT_NOW, Constants.SIGN_IN]
    this.changeActionSheetTitleAndButton(sheetTitle, sheetOptions, true, sheetMessage)
  }

  getLockedComicActionSheet(actionSheetTitle) {
    this.unlockPanelType = actionSheetTitle
    let influencePoints = UserSession.instance.getUserInfluencePoints()
    let comicInfluencePoints = this.state.comicData && this.state.comicData.influencePoints
    let hasSufficientBalance = influencePoints.balance >= comicInfluencePoints
    let multiplePointsUser = influencePoints.balance === 1 ? "point" : "points"
    let multiplePointsComic = comicInfluencePoints === 1 ? "Point" : "Points"
    let sheetTitle = actionSheetTitle
    let sheetMessage = `You have ${influencePoints.balance} influence ${multiplePointsUser}.`
    let sheetOptions = [Constants.UPGRADE_TO_A_PREMIUM_PLAN, Constants.NO_THANKS, <Text style={{ fontSize: 20, color: !hasSufficientBalance ? this.context.colors.separators : this.context.colors.logoRed }}>{`Use ${comicInfluencePoints} Influence ${multiplePointsComic}`}</Text>, Constants.RESTORE_PURCHASE]

    if (FirebaseManager.instance.isUserAnonymous()) {
      sheetOptions.splice(3, 0, Constants.I_AM_A_PREMIUM_USER)
    }

    if (!hasSufficientBalance) {
      sheetOptions.splice(3, 0, Constants.EARN_INFLUENCE_POINTS)
    }

    this.changeActionSheetTitleAndButton(sheetTitle, sheetOptions, true, sheetMessage)
  }

  onBackPress() {
    this.props.navigation.dispatch(CommonActions.goBack())
    return true;
  }

  _onViewWillFocus() {
    Utils.log("On view focus called " + this.comicURL);
    if (this.comicURL) {
      this.props.onComicChange(this.comicURL, this.state.comicData)

      this.props.navigation.setParams({ // To refresh the home bar
        pathUrl: this.comicURL,
      })

      if (Utils.isHomeURL(this.comicURL) && this.isComicDataEmpty() && !this.isCompletePageLoadInProgress) {
        this.reloadChapter()
      }

      this.getContinueReadingEpisodes()
      this.getContinueReadingComic()

      const endPointURL = Utils.getMeaningFullURL(this.comicURL);
      this.props.getPageStatus(endPointURL, this.onPageStatusFetch)
      if (Utils.isComicURL(this.comicURL) && !Utils.isTinyviewComicsPage(this.comicURL) && this.currentPageStatus && this.currentPageStatus.storyID) {
        let data = { data: { storyIDs: [this.currentPageStatus.storyID] } }
        this.getStoryComments(data)
      }
    }
  }

  getContinueReadingEpisodes() {
    const { comicData } = this.state
    if (Utils.isHomeURL(this.comicURL)) {
      if (comicData && comicData.panels && comicData.panels.length > 0) {
        const hasContinueReading = comicData.panels.some(
          (panel) => panel.carouselType === Constants.CONTINUE_READING
        );

        if (hasContinueReading) {
          const requestedData = {};
          this.props.getSeriesCarousel(requestedData, this.onFetchedContinueReading);
        }
      }
    }
  }

  _onViewDidFocus() {
    setTimeout(() => {
      this.isNavigationHidden = true

      if (!Utils.isTinyviewComicsPage(this.comicURL) && Utils.isComicURL(this.comicURL)) {
        if (this.props.isAlreadyPurchased && this.props.isAlreadyPurchased != this.productAlreadyPurchased) {
          this.productAlreadyPurchased = this.props.isAlreadyPurchased
          Utils.log("Refreshing comic to update subscription by purchase")
          this.props.clearComicPageInfo()
          this.loadComic(this.comicURL, true)
        }
      }
    }, 500);
    this.updateHidingBottomBar(false)
  }

  onFetchedContinueReading(response) {
    if (response) {
      this.setState({ continueReadPanelEpisodes: response })
    }
  }

  renderBottomSheet() {
    const valueProps = { likeDislikeFeed: this.likeDislikeFeed, closeBottomSheet: this.closeBottomSheet, requestPurchase: this.requestPurchase, tappedComicData: this.tappedComicData, showBottomSheet: this.state.showBottomSheet }
    return (
      <GiftBottomSheet {...valueProps} />
    )
  }

  renderShareActionSheet() {
    const valueProps = { closeBottomSheet: this.closeBottomSheet, showShareBottomSheet: this.state.showShareBottomSheet, share: this.props.share, clickedPanelItems: this.clickedPanelItems, clickedStoryID: this.clickedStoryID, onStoryUpdated: this.onStoryUpdated, navigation: this.props.navigation, signInSheet: this.signInSheet, shareInviteLink: this.props.shareInviteLink, userDetails: this.props.userDetails, pathUrl: this.comicURL }
    return (
      <ShareBottomSheet {...valueProps} configShareSheetFor={this.configBottomSheet} />
    )
  }

  renderSeriesBottomBar() {
    if (!Utils.isChannelURL(this.comicURL) || Utils.isEmptyObject(this.state.comicData)) {
      return null
    }

    const currComicData = { ...this.state.comicData, ...this.currentPageStatus }
    return (
      <SeriesBottomBar hideBottomBar={this.state.hideBottomBar} navigation={this.props.navigation} navigateToStackTop={this.navigateToStackTop} onShowCommentPress={this.onShowCommentPress} continueReadingComic={this.state.continueReadingComic} onIconPress={this.onIconPress} comicData={currComicData} onBackPress={this.onBackPress} isSubscribed={this.isSubscribedToAlert()} onFollowPress={this.onFollowPress} pathUrl={this.comicURL} openChapter={this.openChapter} recordPageView={this.props.recordPageView} />
    )
  }

  renderMainBottomBar() {
    if (this.comicURL == null || (Utils.isComicURL(this.comicURL) && !Utils.isDirectoryPageURL(this.comicURL)) || Utils.isChannelURL(this.comicURL)) {
      return null
    }

    return (
      <BottomTabBar hideBottomBar={this.state.hideBottomBar} navigation={this.props.navigation} openWebView={this.openWebView} navigateToStackTop={this.navigateToStackTop} onBackPress={this.onBackPress} openChapter={this.openChapter} onShowAllComicSwitchTap={this.onShowAllComicSwitchTap} pathUrl={this.props.pathUrl} scrollToTopOrReload={this.scrollToTopOrReload} navigateToSubscribePage={this.navigateToSubscribePage} navigateToSignIn={this.navigateToSignIn} userDetails={this.props.userDetails} />
    )
  }

  renderComicBottomBar() {
    if (!Utils.isComicURL(this.comicURL) || Utils.isDirectoryPageURL(this.comicURL) || Utils.isEmptyObject(this.state.comicData) || (Utils.isEmptyObject(this.state.comicFeedEpisodes) && !this.state.isEpisodsAPIResponded)) {
      return null
    }

    const isLiked = this.currentPageStatus ? this.currentPageStatus.isLiked : false
    return (
      <ComicBottomBar hideBottomBar={this.state.hideBottomBar} navigation={this.props.navigation} currentPageStatus={this.currentPageStatus} navigateToStackTop={this.navigateToStackTop} onIconPress={this.onIconPress} onShowCommentPress={this.onShowCommentPress} onLikePress={this.onLikePress} comicData={this.state.comicData} isLiked={isLiked} onBackPress={this.onBackPress} isSubscribed={this.isSubscribedToAlert()} onFollowPress={this.onFollowPress} onSeriesHomeClicked={this.onSeriesHomeClicked} comicFeedEpisodes={this.state.comicFeedEpisodes} pathUrl={this.comicURL} openChapter={this.openChapter} recordPageView={this.props.recordPageView} />
    )
  }

  updateHidingBottomBar(value) {
    this.setState({ hideBottomBar: value })
  }

  render() {
    Utils.log("Rendering reader view")
    return (
      <SafeAreaView style={{ flex: 1 }}>
        <StatusBar backgroundColor={this.context.colors.textInverse} barStyle="dark-content" />
        {
          this.props.isPurchaseInProgress
            ? <View style={styles.spinnerContainer}>
              <Spinner color='white' size="lg" />
            </View>
            : <View />
        }
        <ReaderView
          hasMoreData={this.hasMoreData}
          scrollY={this.scrollY}
          editRepost={this.editRepost}
          deleteRepost={this.deleteRepost}
          getStoryDetails={this.getStoryDetails}
          getPageStatus={this.props.getPageStatus}
          onFlagCommentTap={this.onFlagCommentTap}
          shareImage={this.props.shareImage}
          deleteComment={this.deleteComment}
          onReplyPress={this.onReplyPress}
          getIsStoryLiked={this.getIsStoryLiked}
          alerts={this.props.alerts}
          showFooterPlaceHolder={this.state.showFooterPlaceHolder}
          isLoading={this.props.isLoading}
          navigateToUserProfile={this.navigateToUserProfile}
          restorePurchase={this.props.restorePurchase}
          likeDislikeFeed={this.likeDislikeFeed}
          tappedGiftedItems={this.tappedGiftedItems}
          requestPurchase={this.requestPurchase}
          likeDislikeComment={this.likeDislikeComment}
          recordPageView={this.props.recordPageView}
          getMultiplePanelInfo={this.props.getMultiplePanelInfo}
          getSubscribersProgress={this.props.getSubscribersProgress}
          multiplePanelStatus={this.props.multiplePanelStatus}
          onEndReached={this.getUserFeeds}
          userDetails={this.props.userDetails}
          isHideReactionComponentIcons={this.isHideReactionComponentIcons}
          containerInsets={this.state.containerInsets}
          initialScrollY={this.initialScrollY}
          isSubscribed={this.isSubscribedToAlert}
          openWebView={this.openWebView}
          navigateToSignUp={this.navigateToSignUp}
          navigateToSubscribePage={this.navigateToSubscribePage}
          openHomePage={this.openHomePage}
          openSeriesHomePage={this.openSeriesHomePage}
          updateFlatListProps={this.updateFlatListProps}
          comicData={this.state.comicData}
          pathUrl={this.comicURL}
          onSeriesHomeClicked={this.onSeriesHomeClicked}
          seriesHomeName={this.state.seriesHomeName}
          seriesHomeProfile={this.state.profilePicURL}
          openChapter={this.openChapter}
          isAlreadyPurchased={this.isAlreadyPurchased}
          isAlreadyPurchasedFromInfluence={this.isAlreadyPurchasedFromInfluence}
          onShowAllComicSwitchTap={this.onShowAllComicSwitchTap}
          onBackPress={this.onBackPress}
          share={this.props.share}
          onCountPress={this.onCountPress}
          onCommentLikeCountPress={this.onCommentLikeCountPress}
          onIconPress={this.onIconPress}
          onShowCommentPress={this.onShowCommentPress}
          reloadChapter={this.reloadChapter}
          currentPageStatus={this.currentPageStatus}
          onLikePress={this.onLikePress}
          onFollowPress={this.onFollowPress}
          getStoryComments={this.getStoryComments}
          navigateToInfluenceScreen={this.navigateToInfluenceScreen}
          getNavigationComics={this.props.getNavigationComics}
          redeemInfluencePoint={this.redeemInfluencePoint}
          navigateToInviteBottomSheet={this.navigateToInviteBottomSheet}
          getFeedEpisodes={this.props.getFeedEpisodes}
          onChangeSeriesTab={this.onChangeSeriesTab}
          templateViewType={this.templateViewType}
          comicFeedEpisodes={this.state.comicFeedEpisodes}
          getUnlimitedComicAccessSheet={this.getUnlimitedComicAccessSheet}
          getLockedComicActionSheet={this.getLockedComicActionSheet}
          onBlockUserTap={this.onBlockUserTap}
          getSeriesCarousel={this.props.getSeriesCarousel}
          getContinueReadingComic={this.getContinueReadingComic}
          continueReadingComic={this.state.continueReadingComic}
          continueReadPanelEpisodes={this.state.continueReadPanelEpisodes}
          loadingProductId={this.state.loadingProductId}
          updateHidingBottomBar={this.updateHidingBottomBar}
          hideBottomBar={this.state.hideBottomBar}
        />
        {this.state.showBottomSheet && this.renderBottomSheet()}
        {(this.state.showShareBottomSheet || this.state.showInviteBottomSheet) && this.renderShareActionSheet()}
        <ActionSheet
          ref={o => this.actionSheet = o}
          title={this.state.sheetTitle}
          message={this.state.sheetMessage}
          options={this.state.sheetOptions}
          cancelButtonIndex={this.state.cancelOptionIndex}
          tintColor={this.context.colors.logoRed}
          onPress={this.onActionSheetPress}
          useNativeDriver={true}
          styles={{ titleText: this.context.h2, messageText: this.context.p, messageBox: { paddingTop: this.state.sheetTitle == "" ? 20 : 0 } }}
        />
        {this.renderMainBottomBar()}
        {this.renderComicBottomBar()}
        {this.renderSeriesBottomBar()}
        {(this.props.isLogInProcess || this.state.showPageRefresh || this.props.friendsActivityInProgress) && <LoadingIndicator />}
      </SafeAreaView >
    );
  }
}

const styles = StyleSheet.create({
  spinnerContainer: {
    position: 'absolute',
    zIndex: 20,
    height: dimensions.height,
    width: '100%',
    backgroundColor: '#00000090',
    justifyContent: 'center',
    alignItems: 'center'
  }
})

Reader.contextType = ThemeContext

Reader.propTypes = {
  navigation: PropTypes.object,
  styles: PropTypes.object,
  updateCurrentChapter: PropTypes.func,
  actions: PropTypes.objectOf(
    PropTypes.func
  ),
};

const mapStateToProps = (state) => {
  return {
    isLoading: state.readComic.isLoading,
    pathUrl: state.readComic.pathUrl,
    showAllComics: state.readComic.showAllComics,
    refreshComicPage: state.readComic.refreshComicPage,
    alerts: state.userInfo.alerts,
    isAlreadyPurchased: state.purchaseIndicators.productAlreadyPurchased,
    isAlreadyPurchasedFromInfluence: state.purchaseIndicators.productAlreadyPurchasedFromInfluence,
    isPurchaseInProgress: state.purchaseIndicators.isPurchaseInProgress,
    refreshPageStatus: state.comicPageStatus.refreshPageInfo,
    multiplePanelStatus: state.comicPageStatus.multiplePanelStatus,
    lastAction: state.getLikesStatus.performAction,

    isLogInProcess: state.loginInfo.isLogInProcess,
    isAnonymous: state.loginInfo.isAnonymous,
    userDetails: state.loginInfo.userDetails,
    friendsActivityInProgress: state.friendActivityIndicator.friendsActivityInProgress,

    errorRes: state.readComic.errorRes.payload,
    error: state.readComic.errorRes,
  }
}

const mapDispatchToProps = (dispatch) => {
  return {
    readComic(url, ignoreCache, isSharedByPremiumUser, callback) {
      dispatch(
        readComicAction(url, ignoreCache, isSharedByPremiumUser, callback)
      )
    },
    getUserFeeds(data, callBack) {
      dispatch(
        getUserFeeds(data, callBack)
      )
    },
    getAllComics(data, callBack) {
      dispatch(
        getAllComics(data, callBack)
      )
    },
    fetchAppConfigList() {
      dispatch(
        fetchAppConfigList()
      )
    },
    getSeriesComics(data, callBack) {
      dispatch(
        getSeriesComics(data, callBack)
      )
    },
    getFeedComments(data, callBack) {
      dispatch(
        getFeedComments(data, callBack)
      )
    },
    getFeedLikes(data, callBack) {
      dispatch(
        getFeedLikes(data, callBack)
      )
    },
    likeDislikeFeed(data, isLike, callback) {
      dispatch(
        likeDislikeFeed(data, isLike, callback)
      )
    },
    likeDislikeComment(data, isLike, callback) {
      dispatch(
        likeDislikeComment(data, isLike, callback)
      )
    },
    editComment(data) {
      dispatch(
        editComment(data)
      )
    },
    deleteComment(data) {
      dispatch(
        deleteComment(data)
      )
    },
    flagUserComment(data, callback) {
      dispatch(
        flagUserComment(data, callback)
      )
    },
    blockUserAction(data, callback) {
      dispatch(
        blockUserAction(data, callback)
      )
    },
    postCommentOnFeed(data, callback) {
      dispatch(
        postCommentOnFeed(data, callback)
      )
    },
    share(panelItems, actionType, callback) {
      dispatch(
        shareApp(panelItems, actionType, callback)
      )
    },
    deleteRepostedComic(data, callBack) {
      dispatch(
        deleteRepostedComic(data, callBack)
      )
    },
    deleteSentPost(data, callBack) {
      dispatch(
        deleteSentPost(data, callBack)
      )
    },
    onComicChange(url) {
      dispatch(
        onComicChange(url)
      )
    },
    getPageStatus(logURL, callback) {
      dispatch(
        getPageStatus(logURL, callback)
      )
    },
    getMultiplePanelInfo(pages) {
      dispatch(
        getMultiplePanelInfo(pages)
      )
    },
    clearComics() {
      dispatch(
        clearComics()
      )
    },
    openWebView(url) {
      dispatch(
        openWebViewAction(url)
      )
    },
    getUserDetails(callback) {
      dispatch(
        getUserDetails(callback)
      )
    },
    updateAlertSubscription(subscribe, seriesURL, hideFollowMsg) {
      dispatch(
        updateAlertSubscription(subscribe, seriesURL, hideFollowMsg)
      )
    },
    requestPurchase(product, feedData, callback) {
      dispatch(
        requestPurchase(product, feedData, callback)
      )
    },
    restorePurchase(checkCurrentPageIAPs) {
      dispatch(
        restorePurchase(checkCurrentPageIAPs)
      )
    },
    shareImage(item) {
      dispatch(
        shareImage(item)
      )
    },
    hasNotificationPermission() {
      dispatch(
        hasNotificationPermission()
      )
    },
    clearComicPageInfo() {
      dispatch(
        clearComicPageInfo()
      )
    },
    updateShowAllComicsSettings(value) {
      dispatch(
        updateShowAllComicsSettings(value)
      )
    },
    recordPageView(url, isRead, storyID) {
      dispatch(
        recordPageView(url, isRead, storyID)
      )
    },
    getStoryDetails(data, callBack) {
      dispatch(
        getStoryDetails(data, callBack)
      )
    },
    getSubscribersProgress(data, callback) {
      dispatch(
        getSubscribeProgressAction(data, callback)
      )
    },
    addInfluencePoint(requestedData, callback) {
      dispatch(
        addInfluencePoint(requestedData, callback)
      )
    },
    getNavigationComics(requestedData, callback) {
      dispatch(
        getNavigationComics(requestedData, callback)
      )
    },
    redeemInfluencePoint(requestedData, callback) {
      dispatch(
        redeemInfluencePoint(requestedData, callback)
      )
    },
    shareInviteLink(requestedData, callback) {
      dispatch(
        shareInviteLink(requestedData, callback)
      )
    },
    getFeedEpisodes(requestedData, callback) {
      dispatch(
        getFeedEpisodes(requestedData, callback)
      )
    },
    setNotificationAlerts(data) {
      dispatch(
        setNotificationAlerts(data)
      )
    },
    getSeriesCarousel(data, callback) {
      dispatch(
        getSeriesCarousel(data, callback)
      )
    },
    addNewTransaction(requestedData, feedData, callback) {
      dispatch(
        addNewTransaction(requestedData, feedData, callback)
      )
    }
  }
}
export default connect(mapStateToProps, mapDispatchToProps)(Reader)