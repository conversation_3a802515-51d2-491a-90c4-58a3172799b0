import React, { Component } from 'react'
import {  Text } from 'native-base'
import { Constants } from '../../config/Constants'
import { settings } from '../../config/settings'
import { BackHandler, StyleSheet, View } from 'react-native'
import { scale } from 'react-native-size-matters'
import { Color } from '../../config/Color';
import { SafeAreaView } from 'react-native-safe-area-context'

class AccountDeletedScreen extends Component {	

	constructor(props) {
		super(props)		
	}	

	UNSAFE_componentWillMount() {
		BackHandler.addEventListener('hardwareBackPress', this.handleHardwareBackButton);
	  }
	
	componentWillUnmount() {
		BackHandler.removeEventListener('hardwareBackPress', this.handleHardwareBackButton);
	}
	
	handleHardwareBackButton = () => {		
		return true
	}

	render() {
		return (
			<SafeAreaView style={style.mainContainer} >
				<View style={style.container}>
					<Text style={style.message}>{Constants.ACCOUNT_DELETED_MESSAGE} {settings.domainName}</Text>
				</View>
			</SafeAreaView>
		)
	}
}

const style = StyleSheet.create({
	mainContainer: {
		flex: 1,
		justifyContent: 'center',
	},
	container: {	
		margin:50,	
		padding:30,		
		borderWidth: 2,
		borderRadius: scale(5),
		borderColor: Color.ROUNDED_BORDER_COLOR,
		backgroundColor: Color.ROUNDED_BORDER_BACKGROUND_COLOR,			
	},
	message: {
		textAlign:"center",
		fontSize: scale(15)
	}	
})

export default AccountDeletedScreen
