import React, { useState, useRef, useEffect, useContext } from 'react'
import {
  Image,
  StyleSheet,
  Text,
  Dimensions,
  View,
  ScrollView,
  RefreshControl,
  BackHandler,
  TouchableOpacity
} from 'react-native'
import { Button } from 'native-base'
import { scale } from 'react-native-size-matters'
import { connect } from 'react-redux';
import { Utils } from '../../config/Utils';
import { settings } from '../../config/settings';
import { Color } from '../../config/Color';
import { navigationStyle, loadingViewStyle } from '../../config/styles';
import SelectableText from '../../config/SelectableText';
import SessionManager from '../../config/SessionManager';
import { SharedPreferences } from '../../config/SharedPreferences';
import { updateShowAllComicsSettings, getPageStatus, recordPageView, shareInviteLink, getFeedEpisodes, shareApp, getUserDetails, updateAlertSubscription, likeDislikeFeed, requestPurchase, addNewTransaction } from '../../redux/actions/actions';
import UserSession from '../../config/UserSession';
import { Constants } from '../../config/Constants'
import ShareBottomSheet from '../ShareBottomSheet';
import ActionSheet from '../actionSheet/ActionSheetCustom';
import { CommonActions, StackActions } from '@react-navigation/native'
import { ThemeContext } from '../../Contexts';
import FastImage from 'react-native-fast-image'
import { Ajax } from '../../api/ajax';
import { LoadingIndicator } from '../LoadingIndicator';
import HTML from 'react-native-render-html';
import ComicBottomBar from '../ComicBottomBar';
import GiftBottomSheet from '../GiftUI/GiftBottomSheet';
import NetworkUtils from '../../config/NetworkUtils';
import { SafeAreaView } from 'react-native-safe-area-context';

const dimensions = Dimensions.get('window');
const MyInfluenceComponent = (props) => {

  const { navigation, userDetails, pathUrl, isLogInProcess, isPurchaseInProgress } = props;

  const context = useContext(ThemeContext);

  const [showRefresh, setShowRefresh] = useState(true);
  const [showShareBottomSheet, setShowShareBottomSheet] = useState(false);
  const [showInviteBottomSheet, setShowInviteBottomSheet] = useState(false);
  const [sheetData, setSheetData] = useState({ sheetTitle: '', sheetMessage: 'You need to Sign In first', sheetOptions: [Constants.SIGN_IN, Constants.CANCEL] });
  const [cancelOptionIndex, setCancelOptionIndex] = useState(1);
  const [influenceImagePanel, setInfluenceImagePanel] = useState(Utils.getInfluenceImagePanel());
  const [headerPanelData, setHeaderPanelData] = useState('');
  const [comicFeedEpisodes, setComicFeedEpisodes] = useState({});
  const [showBottomSheet, setShowBottomSheet] = useState(false);
  const [clickedPanelItems, setClickedPanelItems] = useState({});
  const [isEpisodsAPIResponded, setIsEpisodsAPIResponded] = useState(false);
  const [userPurchaseRequest, setUserPurchaseRequest] = useState(null);

  const seriesToFollowData = useRef(null);
  const tappedComicData = useRef(null);
  const configBottomSheet = useRef('');
  const clickedStoryID = useRef(null);
  const actionSheetRef = useRef(null);

  const influencePoints = UserSession.instance.getUserInfluencePoints();
  const diffActionsInfluencePoints = SessionManager.instance.getInfluencePointsValuesForActions();
  const bgColor = Utils.getSeriesSpecificStyle(settings.TINYVIEW_CHANNEL_NAME).backgroundColor;
  const inviteFriendsText = `Know when a friend likes or comments on a comic. Earn ${diffActionsInfluencePoints.FRIEND_REQUEST_SENDER} influence points for every invite accepted by friends.`;
  const comicConfig = SessionManager.instance.getComicsConfigList();
  const aspectRatio = dimensions.width / Constants.LOADING_GIF_DIMEN.width;
  const isLoggedInUser = UserSession.instance.isLoggedInUser();

  useEffect(() => {
    navigation.setParams({
      onShowAllComicSwitchTap,
      openChapter,
    });

    refreshPage();
    props.getPageStatus(settings.getInfluencePageURL(), onPageInfoFetched);
    props.recordPageView(settings.getInfluencePageURL());

    BackHandler.addEventListener('hardwareBackPress', onBackPress);

    return () => {
      BackHandler.removeEventListener('hardwareBackPress', onBackPress);
    };
  }, []);

  const onPageInfoFetched = (data) => {
    clickedStoryID.current = data.storyID;
    SessionManager.instance.setCurrectComicPageData({ storyID: data.storyID, pageURL: data.pageURL });
    let pageStatus = { ...data, ...{ action: data.pageURL, views: data.viewCount } };
    setClickedPanelItems(pageStatus);

    getFeedPanelEpisodes();
  };

  const onBackPress = () => {
    navigation.dispatch(CommonActions.goBack());
    return true;
  };

  const getFeedPanelEpisodes = () => {
    let requestedData = {
      action: Utils.getMeaningFullURL(pathUrl),
      series: Utils.getChannelName(pathUrl),
      episodeID: clickedStoryID.current
    };
    props.getFeedEpisodes(requestedData, onFeedEpisodesFetched);
  };

  const onFeedEpisodesFetched = (response) => {
    setComicFeedEpisodes(response ? response : {});
    setIsEpisodsAPIResponded(true);
  };

  const openChapter = (pathURL, item = null) => {
    const redirectPath = Utils.resolvePath(settings.apiBaseURL, pathURL);
    navigation.push('Home', { comicHome: redirectPath, hasUserInfo: true });
  };

  const onShowAllComicSwitchTap = (value) => {
    SessionManager.instance.showAllComics = value;
    props.updateShowAllComicsSettings(value);
    SharedPreferences.setShowAllComicsValue(value);

    navigation.dispatch(StackActions.popToTop());
  };

  const navigateToScreens = (navigationScreen) => {
    if (navigationScreen === Constants.LOGIN_SCREEN) {
      var params = { isForLoginProcess: true };
      Utils.navigateToDrawerLoginRoute(props, Constants.LOGIN_SCREEN, params);
    }
  };

  const closeBottomSheet = (fromGiftTap = false, item, feedData) => {
    setShowBottomSheet(false);
    setShowShareBottomSheet(false);
    setShowInviteBottomSheet(false);

    if (fromGiftTap) {
      setUserPurchaseRequest({ item, feedData });
    }
  };

  useEffect(() => {
    if (userPurchaseRequest && !showBottomSheet && !showShareBottomSheet && !showInviteBottomSheet) {
      requestPurchase(userPurchaseRequest.item, userPurchaseRequest.feedData);
      setUserPurchaseRequest(null);
    }
  }, [showBottomSheet, showShareBottomSheet, showInviteBottomSheet, userPurchaseRequest]);

  const signInSheet = () => {
    setSheetData({ sheetTitle: '', sheetMessage: 'You need to Sign In first', sheetOptions: [Constants.SIGN_IN, Constants.CANCEL] });
    setShowShareBottomSheet(false);
  };

  const renderShareActionSheet = () => {
    const valueProps = { closeBottomSheet: closeBottomSheet, showShareBottomSheet: showShareBottomSheet, share: props.share, clickedPanelItems: clickedPanelItems, signInSheet: signInSheet, clickedStoryID: clickedStoryID.current, navigation: navigation, userDetails: userDetails, shareInviteLink: props.shareInviteLink }
    return (
      <ShareBottomSheet {...valueProps} configShareSheetFor={configBottomSheet.current} />
    )
  };

  const balanceView = () => {
    const total = Utils.checkData(influencePoints) ? influencePoints.total : 0
    const balance = Utils.checkData(influencePoints) ? influencePoints.balance : 0

    return (
      <View style={styles.influencePointsView}>
        <View style={{ flexDirection: 'row' }}>
          <Text style={[context.pBold, styles.totalEarnedPoints]}>{total}</Text>
          <Text style={[context.p, styles.pointsTextView]}>{Constants.INFLUENCE}</Text>
        </View>
        <View style={{ flexDirection: 'row', marginTop: 8 }}>
          <Text style={[context.pBold, styles.totalEarnedPoints]}>{balance}</Text>
          <Text style={[context.p, styles.pointsTextView]}>{Constants.CURRENT_BALANCE}</Text>
        </View>
        <View>
        </View>
      </View>
    )
  };

  const unlockBenefitsView = () => {
    if (!Utils.isEmptyObject(comicConfig)) {
      const freemiumConfig = comicConfig['show-to'][Constants.SUBSCRIBERS_ONLY];
      premiumComicIcon = Utils.resolvePath(pathUrl, freemiumConfig.icon);
    }

    return (
      <View>
        <SelectableText textValue={"How to Use"} textStyle={[context.h1, styles.paddingTopBottom]} multiline={true} />
        <View style={[styles.productView, styles.paddingTopBottom, styles.paddingLeftRight, styles.topBottomPanelView, { marginLeft: 0, backgroundColor: context.colors.textInverse }]}>
          <View style={styles.iconView}>
            <Image source={require("../../../assets/eye_icon_one.png")} style={styles.benefitsImagesView} />
          </View>
          <View style={styles.contentView}>
            <Text style={context.pBold}>Read bonus panels</Text>
            <Text style={[context.p, { marginTop: 8 }]}>{`You can use ${diffActionsInfluencePoints.READ_BONUS_PANEL} influence point to unlock and read a bonus panel.`}</Text>
          </View>
        </View>
        <View style={[styles.productView, styles.paddingTopBottom, styles.paddingLeftRight, styles.topBottomPanelView, { marginLeft: 0, backgroundColor: context.colors.textInverse }]}>
          <View style={styles.iconView}>
            <Image source={require("../../../assets/premium_plan_icon.png")} style={styles.benefitsImagesView} />
          </View>
          <View style={styles.contentView}>
            <Text style={context.pBold}>Read premium comics</Text>
            <Text style={[context.p, { marginTop: 8 }]}>{`You can use ${diffActionsInfluencePoints.READ_PREMIUM_COMIC} influence points to unlock and read a premium comic.`}</Text>
          </View>
        </View>
      </View>
    );
  };

  const signUpPanel = () => {
    return (
      <View style={[styles.productView, styles.influencePanel(1600, navigationStyle.panelLeftRightMargin), { backgroundColor: context.colors.textInverse }]}>
        <View style={styles.iconView}>
          <Image source={require("../../../assets/signin-red-100.png")} style={styles.panelImageView} />
        </View>
        <View style={styles.contentView}>
          <Text style={context.pBold}>{Constants.SIGN_UP}</Text>
          <Text style={[context.p, styles.descTextView]}>{`Sign up so you can access your account from any device or Web. Earn ${diffActionsInfluencePoints.SIGNUP} influence points each when you sign up using email and phone.`}</Text>
          <View style={styles.subsButtonContainer}>
            <Button
              variant='solid'
              style={styles.buttonStyle}
              onPress={() => { navigateToScreens(Constants.LOGIN_SCREEN) }}>
              <Text style={[context.p, { color: context.colors.textInverse }]}>{Constants.SIGN_UP}</Text>
            </Button>
          </View>
        </View>
      </View>
    );
  };

  const sharePanel = () => {
    return (
      <View style={[styles.productView, styles.influencePanel(1600, navigationStyle.panelLeftRightMargin), { backgroundColor: context.colors.textInverse }]}>
        <View style={styles.iconView}>
          <Image source={require("../../../assets/share_icon.png")} style={styles.panelImageView} />
        </View>
        <View style={styles.contentView}>
          <Text style={context.pBold}>Share Comics</Text>
          <Text style={[context.p, styles.descTextView]}>{`Share comics with friends or on social media. You earn ${diffActionsInfluencePoints.REFERRER} influence point for each click.`}</Text>
          <View style={styles.subsButtonContainer}>
            <Button variant='solid' style={styles.buttonStyle}
              onPress={() => {
                configBottomSheet.current = Constants.SHARE;
                setShowShareBottomSheet(true);
              }}>
              <Text style={[context.p, { color: context.colors.textInverse }]}>{Constants.SHARE}</Text>
            </Button>
          </View>
        </View>
      </View>
    );
  };

  const inviteFriendsPanel = () => {
    return (
      <View style={[styles.productView, styles.influencePanel(1600, navigationStyle.panelLeftRightMargin), { backgroundColor: context.colors.textInverse }]}>
        <View style={styles.iconView}>
          <Image source={require("../../../assets/icons8-add-user-group-man-man-100.png")} style={styles.panelImageView} />
        </View>
        <View style={styles.contentView}>
          <Text style={context.pBold}>Invite Friends</Text>
          <Text style={[context.p, styles.descTextView]}>{inviteFriendsText}</Text>
          <View style={styles.subsButtonContainer}>
            <Button
              variant='solid'
              style={styles.buttonStyle}
              onPress={() => {
                let isSignedIn = UserSession.instance.isLoggedInUser()
                if (isSignedIn) {
                  configBottomSheet.current = Constants.INVITE_FRIENDS
                  setShowInviteBottomSheet(true)
                } else {
                  setSheetData({ sheetTitle: '', sheetMessage: Constants.INVITE_FRIENDS_ACTION_SHEET_MESSAGE, sheetOptions: [Constants.SIGN_IN, Constants.CANCEL] });
                }
              }}>
              <Text style={[context.p, { color: context.colors.textInverse }]}>Invite Friends</Text>
            </Button>
          </View>
        </View>
      </View>
    );
  };

  const renderHeader = () => {
    return (
      <View>
        <FastImage
          style={styles.coverImageView}
          resizeMode='contain'
          source={{ uri: Utils.resolvePath(pathUrl, headerPanelData.ogImage), cache: 'web' }}
        />
        <TouchableOpacity
          style={styles.influenceImageView}
          onPress={() => openChapter("/tinyview/index.json")}>
          <FastImage
            style={[styles.roundImage, { borderColor: context.colors.textInverse }]}
            source={require("../../../assets/tinyview-profile.jpg")} />
        </TouchableOpacity>
        <View style={[styles.paddingLeftRight, { marginTop: scale(-30) }]}>
          <View style={[styles.paddingTopBottom, styles.paddingLeftRight, styles.topBottomPanelView, { backgroundColor: context.colors.textInverse }]}>
            <SelectableText textValue={headerPanelData.title} textStyle={[context.h1, { marginTop: 5 }]} multiline={true} />
            {balanceView()}
            <HTML contentWidth={dimensions.width} textSelectable={true} allowFontScaling baseStyle={context.p} source={{ html: headerPanelData.description ? headerPanelData.description : " " }} tagsStyles={{ "b": context.pBold, "p": { paddingTop: 0, marginBottom: 0, marginTop: 0 } }} />
          </View>
        </View>
        <View style={styles.paddingTopBottom}>
          {renderImage()}
        </View>
      </View>
    );
  };

  const renderImage = () => {
    const panelImageStyle = imageStyle(influenceImagePanel.width, influenceImagePanel.height, navigationStyle.panelLeftRightMargin, influenceImagePanel)
    return (
      <View style={styles.imageContainerStyle(influenceImagePanel)}>
        <Image
          style={{ ...panelImageStyle }}
          source={{ uri: Utils.resolvePath(settings.apiBaseURL, influenceImagePanel.image) }} />
      </View>
    );
  };

  const imageStyle = (w, h, margin = 2, item) => {
    let rightBorder = 0;
    if (item.border && item.border["border-left"] && item.border["border-right"]) {
      rightBorder = item.border["border-width"];
    }

    let aspectRatio = (dimensions.width - ((margin + rightBorder) * 2)) / w

    return {
      width: w * aspectRatio,
      height: h * aspectRatio,
      resizeMode: 'contain'
    }
  };

  const onActionSheetPress = (index) => {
    const { sheetOptions } = sheetData

    if (index == 0) {
      if (sheetOptions[0] == Constants.SIGN_IN) {
        const params = { isForLoginProcess: true }
        closeBottomSheet()
        Utils.navigateToDrawerLoginRoute(props, Constants.LOGIN_SCREEN, params)
      } else if (sheetOptions[0] == Constants.MANAGE_ALERTS) {
        Utils.navigateToManageAlertsPage(props, { configFromFollowTab: true, seriesToFollowData: seriesToFollowData.current })
      }
    }
  };

  const refreshPage = () => {
    if (!showRefresh) {
      setShowRefresh(true);
    }

    fetchHeaderData()
    props.getUserDetails((isSuccess) => {
      setShowRefresh(false);
    });
  };

  const fetchHeaderData = async () => {
    const influencePageURL = Utils.resolvePath(settings.apiBaseURL, settings.getInfluencePageURL());
    const headerData = await Ajax.getJSONFile(influencePageURL, true)
    if (headerData) {
      let comicData = { title: headerData.title, description: headerData.description, ogImage: headerData.ogImage }
      if (headerData.panels && headerData.panels.length != 0) {
        setHeaderPanelData(comicData);
        setInfluenceImagePanel(headerData.panels[0]);
      } else {
        setHeaderPanelData(comicData);
      }
    }
  };

  const onGiftItemSent = () => {
    props.getPageStatus(settings.getInfluencePageURL(), onPageInfoFetched);
  };

  const requestPurchase = (product, feedData) => {
    const userBalance = UserSession.instance.getUserReferralBalance()
    const referralBalance = parseFloat(userBalance.toFixed(2))
    const productCost = product?.cost ? parseFloat(product.cost.toFixed(2)) : null;
    if (productCost && referralBalance >= productCost) {
      const storyID = feedData?.storyID
      const transaction = { productId: product?.productId }
      const reqData = { data: { storyID, platform: Constants.INTERNAL_PAYMENT_PLATFORM, productInfo: product, transaction } }
      props.addNewTransaction(reqData, feedData, onGiftItemSent);
    } else {
      props.requestPurchase(product, feedData, onGiftItemSent);
    }
  };

  const likeDislikeFeed = (isLiked, item, index) => {
    const isConnected = NetworkUtils.instance.isAvailable()
    if (!isConnected) {
      return Utils.showError(Constants.INTERNET_ERROR)
    }

    if (!isLiked) {
      Utils.vibrateDevice()
    }

    var panelItems = JSON.parse(JSON.stringify(clickedPanelItems))
    panelItems.isLiked = !panelItems.isLiked
    panelItems.isLikeChanged = panelItems.isLiked
    if (panelItems.isLiked) {
      panelItems.likeCount++
      panelItems.isLikeChanged = true
      if (!panelItems.giftsCount) {
        panelItems.giftsCount = {}
      }
      if (!Utils.checkData(panelItems.giftsCount[Constants.LIKE.toUpperCase()])) {
        panelItems.giftsCount[Constants.LIKE.toUpperCase()] = 0
      }
      panelItems.giftsCount[Constants.LIKE.toUpperCase()]++
    } else {
      panelItems.likeCount--
      panelItems.isLikeChanged = false
      if (!panelItems.giftsCount) {
        panelItems.giftsCount = {}
      }
      if (Utils.checkData(panelItems.giftsCount[Constants.LIKE.toUpperCase()])) {
        panelItems.giftsCount[Constants.LIKE.toUpperCase()]--
      } else {
        panelItems.giftsCount[Constants.LIKE.toUpperCase()] = 0
      }
    }
    setClickedPanelItems(panelItems);

    let data = { "data": { storyID: panelItems.storyID, action: panelItems.action, reactionType: 'LIKE', refType: panelItems.refType } }
    props.likeDislikeFeed(data, isLiked);
  };

  const renderBottomSheet = () => {
    const valueProps = { likeDislikeFeed: likeDislikeFeed, closeBottomSheet: closeBottomSheet, requestPurchase: requestPurchase, tappedComicData: tappedComicData }
    return (
      <GiftBottomSheet {...valueProps} />
    );
  };

  const onLikePress = (selComic) => {
    setShowBottomSheet(true);
    tappedComicData.current = selComic;
  };

  const onIconPress = (actionName, panelItems, storyID) => {
    if (actionName == Constants.REPOST || actionName === Constants.SHARE) {
      configBottomSheet.current = Constants.SHARE
      setShowShareBottomSheet(true);
    }
  };

  const isSubscribedToAlert = (seriesURL = null) => {
    let alertName = Utils.getChannelName(pathUrl, seriesURL)
    return props.alerts && props.alerts[alertName] == true ? true : false
  };

  const subscribeToAlert = (isSubscribe, seriesURL = null) => {
    props.updateAlertSubscription(isSubscribe, seriesURL)
  };

  const onFollowPress = (pathUrl, isFollowing) => {
    if (!isFollowing && !UserSession.instance.isAnyUserAlertsEnabled()) {
      const channelName = Utils.getUserVisibleChannelName(pathUrl)
      seriesToFollowData.current = { title: channelName, action: Utils.getMeaningFullURL(pathUrl) }
      let sheetTitle = Constants.MANAGE_ALERTS_ACTIONSHEET_TITLE
      let sheetMessage = `You want to follow ${channelName} but we have no way of reaching you. Manage alerts and let us know how to send you updates from ${channelName}.`
      let sheetOptions = [Constants.MANAGE_ALERTS, Constants.NOT_NOW]
      setSheetData({ sheetTitle: sheetTitle, sheetMessage: sheetMessage, sheetOptions: sheetOptions })
    } else {
      let isSubscribed = isSubscribedToAlert(pathUrl)
      if (isFollowing) {
        if (isSubscribed) subscribeToAlert(false, pathUrl)
      } else if (!isSubscribed) subscribeToAlert(true, pathUrl)
    }
  };

  useEffect(() => {
    if (actionSheetRef.current) {
      actionSheetRef.current.show();
    }
  }, [sheetData]);

  const onSeriesHomeClicked = () => {
    navigation.push('Home', { comicHome: Utils.getComicSeriesURL(pathUrl), hasUserInfo: true })
  };

  const onShowCommentPress = (actionName, panelItem, selectedIndex, commentItem, storyData = null, showComments = true) => {
    let subRoute = Constants.POST_COMMENTS_SCREEN
    if (storyData) {
      const { storyID = null } = storyData
      if (!Utils.checkData(storyID)) {
        return null
      }

      var storyData = { storyID: storyID }
      return navigation.push(subRoute, { storyData })
    }
  };

  const navigateToStackTop = () => {
    navigation.dispatch(StackActions.popToTop());
  };

  const renderComicBottomBar = () => {
    if (Utils.isEmptyObject(clickedPanelItems) || (Utils.isEmptyObject(comicFeedEpisodes) && !isEpisodsAPIResponded)) {
      return null;
    }

    const isLiked = clickedPanelItems ? clickedPanelItems.isLiked : false
    return (
      <ComicBottomBar navigation={navigation} currentPageStatus={clickedPanelItems} isLiked={isLiked} navigateToStackTop={navigateToStackTop} onIconPress={onIconPress} onShowCommentPress={onShowCommentPress} isSubscribed={isSubscribedToAlert()} onLikePress={onLikePress} comicData={clickedPanelItems} onBackPress={onBackPress} onFollowPress={onFollowPress} onSeriesHomeClicked={onSeriesHomeClicked} comicFeedEpisodes={comicFeedEpisodes} pathUrl={pathUrl} openChapter={openChapter} recordPageView={props.recordPageView} />
    )
  };

  const { sheetTitle, sheetMessage, sheetOptions } = sheetData;
  return (
    <SafeAreaView style={{ flex: 1 }}>
      {(showRefresh)
        ?
        <Image style={loadingViewStyle(aspectRatio)} source={require('./../../../assets/top_loading_view.gif')} />
        :
        <ScrollView
          refreshControl={
            <RefreshControl
              refreshing={showRefresh}
              onRefresh={() => {
                refreshPage()
              }}
              progressViewOffset={settings.PROGRESS_VIEW_OFFSET}
              tintColor={Color.CIRCLE_PROGRESS_BAR_COLOR}
            />
          }>
          <View style={[styles.mainContainer(bgColor)]}>
            {renderHeader()}
            <View style={[styles.paddingLeftRight, { paddingBottom: 30 }]}>
              <Text style={[context.h1, styles.paddingTopBottom]}>How to Earn</Text>
              {!isLoggedInUser && signUpPanel()}
              {sharePanel()}
              {inviteFriendsPanel()}
              {unlockBenefitsView()}
              {(showShareBottomSheet || showInviteBottomSheet) && renderShareActionSheet()}
            </View>
          </View>
          {showBottomSheet && renderBottomSheet()}
          <ActionSheet
            ref={actionSheetRef}
            title={sheetTitle}
            message={sheetMessage}
            options={sheetOptions}
            cancelButtonIndex={cancelOptionIndex}
            tintColor={context.colors.logoRed}
            onPress={(index) => { onActionSheetPress(index) }}
            useNativeDriver={true}
            styles={{ titleText: context.h2, messageText: context.p, messageBox: { paddingTop: sheetTitle ? 0 : 20 } }}
          />
        </ScrollView>
      }
      {(isLogInProcess || isPurchaseInProgress) && <LoadingIndicator />}
      {renderComicBottomBar()}
    </SafeAreaView>
  );
};

const mapStateToProps = (state) => {
  return {
    pathUrl: state.readComic.pathUrl,
    userDetails: state.loginInfo.userDetails,
    isLogInProcess: state.loginInfo.isLogInProcess,
    alerts: state.userInfo.alerts,
    isPurchaseInProgress: state.purchaseIndicators.isPurchaseInProgress
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    updateShowAllComicsSettings(value) {
      dispatch(
        updateShowAllComicsSettings(value)
      )
    },
    share(panelItems, actionType, callback) {
      dispatch(
        shareApp(panelItems, actionType, callback)
      )
    },
    getUserDetails(callback) {
      dispatch(
        getUserDetails(callback)
      )
    },
    getPageStatus(pageURL, callback) {
      dispatch(
        getPageStatus(pageURL, callback)
      )
    },
    recordPageView(pageURL) {
      dispatch(
        recordPageView(pageURL)
      )
    },
    shareInviteLink(requestedData, callback) {
      dispatch(
        shareInviteLink(requestedData, callback)
      )
    },
    getFeedEpisodes(requestedData, callback) {
      dispatch(
        getFeedEpisodes(requestedData, callback)
      )
    },
    updateAlertSubscription(subscribe, seriesURL) {
      dispatch(
        updateAlertSubscription(subscribe, seriesURL)
      )
    },
    likeDislikeFeed(data, isLike, callback) {
      dispatch(
        likeDislikeFeed(data, isLike, callback)
      )
    },
    requestPurchase(product, feedData, callback) {
      dispatch(
        requestPurchase(product, feedData, callback)
      )
    },
    addNewTransaction(requestedData, feedData, callback) {
      dispatch(
        addNewTransaction(requestedData, feedData, callback)
      )
    }
  };
};

const styles = StyleSheet.create({
  mainContainer: (bgColor) => {
    return {
      paddingBottom: scale(70),
      backgroundColor: bgColor
    }
  },
  imageContainerStyle: (item) => {
    if (!item.border) {
      return
    }

    const width = item.border["border-width"];
    const color = item.border["border-color"];
    const hasTopBorder = item.border["border-top"];
    const hasLeftBorder = item.border["border-left"];
    const hasRightBorder = item.border["border-right"];
    const hasBottomBorder = item.border["border-bottom"];

    return {
      borderColor: color,
      borderTopWidth: hasTopBorder ? width : 0,
      borderLeftWidth: hasLeftBorder ? width : 0,
      borderRightWidth: hasRightBorder ? width : 0,
      borderBottomWidth: hasBottomBorder ? width : 0,
      marginLeft: navigationStyle.panelLeftRightMargin,
      marginRight: navigationStyle.panelLeftRightMargin,
    }
  },
  influencePanel: (w, margin = 2) => {
    let dimensions = Dimensions.get('window')
    let aspectRatio = (dimensions.width - margin * 2) / w

    return {
      width: w * aspectRatio,
      marginRight: margin,
      resizeMode: 'contain',
      borderRadius: 8,
      marginLeft: 0,
      padding: navigationStyle.panelsMargin
    }
  },
  paddingLeftRight: {
    paddingLeft: navigationStyle.panelLeftRightMargin,
    paddingRight: navigationStyle.panelLeftRightMargin
  },
  paddingTopBottom: {
    paddingTop: navigationStyle.panelsMargin,
    paddingBottom: navigationStyle.panelsMargin
  },
  buttonStyle: {
    backgroundColor: Color.IN_APP_PANEL_BUTTON_COLOR,
    borderColor: Color.RED_TEXT_COLOR,
    borderWidth: 1,
    flex: 1,
    height: 40,
    borderRadius: 6,
    elevation: 0,
    paddingTop: 0,
    paddingBottom: 0,
    position: 'absolute',
  },
  productView: {
    flexDirection: 'row',
    alignItems: 'center',
    alignContent: 'center',
    marginBottom: navigationStyle.panelsMargin
  },
  subsButtonContainer: {
    height: 35,
    marginTop: 20,
  },
  iconView: {
    alignSelf: 'flex-start',
    flex: 0.15,
  },
  totalEarnedPoints: {
    alignSelf: 'center'
  },
  panelImageView: {
    tintColor: 'black',
    alignSelf: 'flex-start',
    width: scale(24),
    height: scale(24)
  },
  contentView: {
    flex: 0.85
  },
  influencePointsView: {
    marginTop: 12,
    marginBottom: 16
  },
  benefitsImagesView: {
    alignSelf: 'flex-start',
    width: scale(24),
    height: scale(24),
    resizeMode: "contain"
  },
  roundImage: {
    width: scale(68),
    height: scale(68),
    borderWidth: scale(3),
    borderRadius: scale(34),
    overflow: "hidden",
    marginLeft: scale(32),
    marginTop: scale(-20)
  },
  descTextView: {
    marginTop: 5
  },
  topBottomPanelView: {
    borderRadius: 8,
    overflow: 'hidden'
  },
  coverImageView: {
    width: dimensions.width,
    aspectRatio: 800 / 420
  },
  pointsTextView: {
    marginLeft: 5
  },
  influenceImageView: {
    zIndex: 1,
    marginRight: dimensions.width - scale(100)
  }
});

export default connect(mapStateToProps, mapDispatchToProps)(MyInfluenceComponent);