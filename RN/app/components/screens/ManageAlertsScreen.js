import React, { useEffect, useRef, useState } from "react";
import { connect } from "react-redux";
import { Constants } from "../../config/Constants";
import { View } from "native-base";
import { BackHandler, StyleSheet, ScrollView, AppState, RefreshControl } from "react-native";
import { navigationStyle } from "../../config/styles";
import { CommonActions } from "@react-navigation/native";
import { Utils } from "../../config/Utils";
import { getUserDetails, setNotificationAlerts, askForNotificationPermission, updateAlertSubscription } from "../../redux/actions/actions";
import ToggleAlerts from "./ToggleAlerts";
import { LoadingIndicator } from "../LoadingIndicator";
import FirebaseManager from "../../config/FirebaseManager";
import UserSession from "../../config/UserSession";
import { settings } from "../../config/settings";
import { Color } from "../../config/Color";
import SessionManager from "../../config/SessionManager";

const ManageAlertsScreen = (props) => {

    const getNotificationDataForUI = (data, hasSystemNotifPermis, switchAlerts = true) => {
        let notificationAlerts = [];
        if (data) {
            UserSession.instance.updateCurrentUserNotifAlerts(data);
            let pushAlertValues = data && data.push;
            if (pushAlertValues) {
                let isPushNotifEnabled = pushAlertValues.enabled;
                let pushNotifEvents = pushAlertValues.events;

                if (!hasSystemNotifPermis && isPushNotifEnabled && switchAlerts) {
                    onAlertSwitched("push", false, "push");
                }

                let isUserACreator = UserSession.instance.isUserAComicCreator();

                notificationAlerts = [{ channel: 'push', item: 'push', title: 'Push Notifications', toggleValue: isPushNotifEnabled && hasSystemNotifPermis, isHeaderAlert: true }];
                if (isPushNotifEnabled && hasSystemNotifPermis) {
                    let pushNotifSubAlerts = [
                        { channel: 'push', item: 'publishComic', title: 'New content from series you follow', toggleValue: pushNotifEvents.publishComic },
                        { channel: 'push', item: 'giftUser', title: 'A friend gifts a creator', toggleValue: pushNotifEvents.giftUser },
                        { channel: 'push', item: 'likeStory', title: 'A friend likes a post', toggleValue: pushNotifEvents.likeStory },
                        { channel: 'push', item: 'likeComment', title: 'Some user likes your comment', toggleValue: pushNotifEvents.likeComment },
                        { channel: 'push', item: 'commentOnStory', title: 'A friend comments on a post', toggleValue: pushNotifEvents.commentOnStory },
                        { channel: 'push', item: 'friendActivity', title: 'Friend network', toggleValue: pushNotifEvents.friendActivity },
                        { channel: 'push', item: 'friendRequest', title: 'Friend requests', toggleValue: pushNotifEvents.friendRequest },
                        { channel: 'push', item: 'influencePoints', title: 'Influence points activity', toggleValue: pushNotifEvents.influencePoints },
                        { channel: 'push', item: 'accountActivity', title: 'Account activity', toggleValue: pushNotifEvents.accountActivity }
                    ]

                    if (isUserACreator) {
                        pushNotifSubAlerts.push({ channel: 'push', item: 'commentOnStoryCreator', title: '[Creators] A user comments on your post', toggleValue: pushNotifEvents.commentOnStoryCreator })
                        pushNotifSubAlerts.push({ channel: 'push', item: 'giftCreator', title: '[Creators] A user gifts you', toggleValue: pushNotifEvents.giftCreator })
                        pushNotifSubAlerts.push({ channel: 'push', item: 'flagComment', title: '[Creators] A user reports a comment', toggleValue: pushNotifEvents.flagComment })
                    }
                    notificationAlerts = [...notificationAlerts, ...pushNotifSubAlerts];
                }

                let emailAlertValues = data && data.email;
                let isEmailNotifEnabled = emailAlertValues.enabled;
                let emailNotifEvents = emailAlertValues.events;
                notificationAlerts = [...notificationAlerts, ...[{ channel: 'email', item: 'email', title: 'Email', toggleValue: isEmailNotifEnabled, isHeaderAlert: true }]];
                if (isEmailNotifEnabled) {
                    let emailNotifSubAlerts = [
                        { channel: 'email', item: 'publishComic', title: 'New content from series you follow', toggleValue: emailNotifEvents.publishComic },
                        { channel: 'email', item: 'friendActivity', title: 'Friend network', toggleValue: emailNotifEvents.friendActivity },
                        { channel: 'email', item: 'friendRequest', title: 'Friend requests', toggleValue: emailNotifEvents.friendRequest },
                        { channel: 'email', item: 'accountActivity', title: 'Account activity', toggleValue: emailNotifEvents.accountActivity }
                    ];

                    if (isUserACreator) {
                        emailNotifSubAlerts.push({ channel: 'email', item: 'giftCreator', title: '[Creators] A user gifts you', toggleValue: emailNotifEvents.giftCreator });
                        emailNotifSubAlerts.push({ channel: 'email', item: 'flagComment', title: '[Creators] A user reports a comment', toggleValue: emailNotifEvents.flagComment });
                    }

                    notificationAlerts = [...notificationAlerts, ...emailNotifSubAlerts];
                }
            }
        }

        return notificationAlerts;
    };

    const { navigation, route, hasNotifPermission, alerts, getUserDetails, setNotificationAlerts, askForNotificationPermission, updateAlertSubscription } = props;

    const params = route.params || {};
    const navigatedWhileFollowing = params.configFromFollowTab;
    const seriesToFollowData = params.seriesToFollowData;
    const autoRefreshOnFollow = params.autoRefreshOnFollow;

    const currentNotificationData = UserSession.instance.getCurrentUserNotifAlerts();
    const currentAlerts = getNotificationDataForUI(currentNotificationData, hasNotifPermission, false);

    const [userNotifAlerts, setUserNotifAlerts] = useState(currentAlerts);
    const [showLoadingIndicator, setShowLoadingIndicator] = useState(true);
    const [notifAlertsCallback, setNotifAlertsCallback] = useState(null)

    const scrollViewRef = useRef(null);
    const appListenerRef = useRef(null);
    const delayTimerRef = useRef(null);
    const fromEmailVerification = useRef(false);
    const currentNotifSettings = useRef(currentNotificationData);

    useEffect(() => {
        navigation.setParams({
            leftButtonText: "Back",
            onBackPress: onBackPress,
            title: Constants.MANAGE_ALERTS
        });

        BackHandler.addEventListener('hardwareBackPress', onBackPress);

        appListenerRef.current = AppState.addEventListener("change", onAppStateChange);

        if (!params.emailVerified) {
            updateAlerts(0);
        }

        return () => {
            BackHandler.removeEventListener('hardwareBackPress', onBackPress);
            if (appListenerRef.current) {
                appListenerRef.current.remove();
            }
            if (delayTimerRef.current) {
                clearTimeout(delayTimerRef.current);
            }
        };
    }, []);

    useEffect(() => {
        if (route.params) {
            if (route.params.emailVerified) {
                const seriesFollowed = route.params.seriesFollowed;
                fromEmailVerification.current = true;

                if (seriesFollowed) {
                    Utils.showToast(`Your email address is verified. You're following ${seriesFollowed} now.`, "bottom", "EmailSeriesVerified", 5000);
                } else {
                    Utils.showToast("Your email address is verified.", "bottom", "emailValid", 5000);
                }

                navigation.setParams({
                    leftButtonText: "Back",
                    onBackPress: onBackPress,
                    title: Constants.MANAGE_ALERTS,
                    emailVerified: false
                });

                onAlertSwitched("email", true, "email", () => {
                    updateAlerts(0);
                });
            } else if (route.params.shouldRefreshProps) {
                navigation.setParams({
                    leftButtonText: "Back",
                    onBackPress: onBackPress,
                    title: Constants.MANAGE_ALERTS,
                    emailVerified: false,
                    shouldRefreshProps: false
                });
            }
        }
    }, [route.params]);

    const onAppStateChange = async (nextAppState) => {
        if (nextAppState === "active") {
            let prevHasLocalPermission = hasNotifPermission;
            let hasLocalPermission = await FirebaseManager.instance.hasNotificationPermission();

            if (prevHasLocalPermission != hasLocalPermission) {
                onAlertSwitched("push", hasLocalPermission, "push");
            }
        }
    };

    const updateAlerts = (delay = 0) => {
        Utils.log("Delay in get User Details API in Manage Alert page " + delay);
        setTimeout(() => {
            getUserDetails(onUserDetailsUpdated);
        }, delay);
    };

    const refreshPage = () => {
        setShowLoadingIndicator(true);
        updateAlerts();
    };

    const subscribeToAlert = (isSubscribe, seriesURL = null) => {
        const seriesName = seriesToFollowData?.title;
        let showToastMsg = seriesName && !SessionManager.instance.hasAnySubscriptionPurchase() && autoRefreshOnFollow;
        updateAlertSubscription(isSubscribe, seriesURL, !showToastMsg && !fromEmailVerification.current);
        if (showToastMsg && !fromEmailVerification.current) {
            Utils.showToast(`Thanks for following ${seriesName}!`, "bottom", "seriesFollowed", 5000);
        }
    };

    const onUserDetailsUpdated = (data) => {
        if (data && data.notificationSettings) {
            updateNotifAlerts(data.notificationSettings);
        }
    };

    const updateNotifAlerts = async (data, callback) => {
        const hasSystemNotifPermis = await FirebaseManager.instance.hasNotificationPermission();
        let notificationAlerts = getNotificationDataForUI(data, hasSystemNotifPermis);
        setShowLoadingIndicator(false);
        setUserNotifAlerts(notificationAlerts);
        if (callback) {
            setNotifAlertsCallback(() => callback)
        }
    };

    useEffect(() => {
        if (notifAlertsCallback) {
            notifAlertsCallback();
            setNotifAlertsCallback(null);
        }
    }, [userNotifAlerts, notifAlertsCallback]);

    const onBackPress = async () => {
        navigation.dispatch(CommonActions.goBack());
        return true;
    };

    const callAPIToUpdateNotifications = (notificationSettings, callback = null) => {
        clearTimeout(delayTimerRef.current);
        delayTimerRef.current = setTimeout(() => {
            setNotificationAlerts({ "notificationSettings": notificationSettings }, callback);
        }, 1000);
    };

    const onAlertSwitched = (key, value, channel, callback = null) => {
        let isUserHasEmail = FirebaseManager.instance.currentUser() && FirebaseManager.instance.currentUser().email;
        if (!isUserHasEmail && key == 'email' && value) {
            let params = { forEmailAlert: true, isForEmailVerification: true, seriesToFollowData: seriesToFollowData };
            Utils.navigateToDrawerLoginRoute(props, Constants.LOGIN_SCREEN, params);
        } else {
            let isPushNotificationAlerts = channel == "push" && value;
            if (isPushNotificationAlerts && !hasNotifPermission) {
                askForNotificationPermission();
                return;
            }

            Utils.vibrateDevice();

            const isScrollingNeeded = key == "email" && channel == "email" && value;
            const updatedAlerts = [...userNotifAlerts];
            for (const index in updatedAlerts) {
                if (key == updatedAlerts[index].item && channel == updatedAlerts[index].channel) {
                    updatedAlerts[index].toggleValue = value;
                    break;
                }
            }

            var notificationSettings = JSON.parse(JSON.stringify(currentNotifSettings.current));
            for (const alert of updatedAlerts) {
                const alertKey = alert.channel;
                const item = alert.item;
                const toggleValue = alert.toggleValue;

                if (item === alertKey) {
                    notificationSettings[alertKey].enabled = toggleValue;
                } else {
                    const subAlerts = notificationSettings[alertKey].events;
                    subAlerts[item] = toggleValue;
                }
            }

            currentNotifSettings.current = notificationSettings;
            updateNotifAlerts(notificationSettings, () => {
                if (isScrollingNeeded) {
                    setTimeout(() => {
                        if (scrollViewRef.current) {
                            scrollViewRef.current.scrollToEnd({ animated: true });
                        }
                    }, 500);
                }

                if (navigatedWhileFollowing) {
                    let seriesEndPoint = seriesToFollowData?.action;
                    let seriesUrl = Utils.resolvePath(settings.apiBaseURL, seriesEndPoint);

                    let isSubscribed = isSubscribedToAlert(seriesUrl);
                    if (UserSession.instance.isAnyUserAlertsEnabled() && !isSubscribed) {
                        subscribeToAlert(true, seriesUrl);
                    }
                }

                callAPIToUpdateNotifications(notificationSettings, callback);
            });
        }
    };

    const isSubscribedToAlert = (seriesURL = null) => {
        const alertName = Utils.getChannelName(seriesURL);
        return alerts && alerts[alertName] == true ? true : false;
    };

    const renderNotifAlerts = () => {
        return userNotifAlerts && userNotifAlerts.map((value, index) => {
            return (
                <ToggleAlerts
                    key={value.title + index}
                    {...value}
                    onAlertSwitched={onAlertSwitched}
                    hasNotifPermission={hasNotifPermission}
                />
            );
        });
    };

    return (
        <View style={styles.viewStyle}>
            {(showLoadingIndicator)
                ?
                <LoadingIndicator />
                :
                <ScrollView
                    refreshControl={
                        <RefreshControl
                            refreshing={showLoadingIndicator}
                            onRefresh={() => {
                                refreshPage();
                            }}
                            progressViewOffset={settings.PROGRESS_VIEW_OFFSET}
                            tintColor={Color.CIRCLE_PROGRESS_BAR_COLOR}
                        />
                    }
                    ref={scrollViewRef}>
                    <View style={styles.mainContainer}>
                        {renderNotifAlerts()}
                    </View>
                </ScrollView>
            }
        </View>
    );
};

const styles = StyleSheet.create({
    viewStyle: {
        flex: 1
    },
    mainContainer: {
        flex: 1,
        marginLeft: navigationStyle.panelLeftRightMargin,
        marginRight: navigationStyle.panelLeftRightMargin
    }
});

const mapStateToProps = (state) => {
    return {
        hasNotifPermission: state.readComic.hasNotificationPermission,
        alerts: state.userInfo.alerts
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        getUserDetails(data, callback) {
            dispatch(
                getUserDetails(data, callback)
            )
        },
        setNotificationAlerts(data, callback) {
            dispatch(
                setNotificationAlerts(data, callback)
            )
        },
        askForNotificationPermission() {
            dispatch(
                askForNotificationPermission()
            )
        },
        updateAlertSubscription(subscribe, seriesURL, hideFollowMsg) {
            dispatch(
                updateAlertSubscription(subscribe, seriesURL, hideFollowMsg)
            )
        },
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(ManageAlertsScreen);