import React, { useContext } from "react";
import { StyleSheet, Platform, Linking } from 'react-native'
import { View, Text } from "native-base";
import { Switch } from 'react-native-switch';
import { ThemeContext } from "../../Contexts";
import FastImage from "react-native-fast-image";
import UserSession from "../../config/UserSession";

export default ToggleAlerts = (props) => {
    const context = useContext(ThemeContext)
    const title = props.title
    const toggleValue = props.toggleValue
    const isHeaderAlert = props.isHeaderAlert
    const key = props.item
    const userEmailAddress = UserSession.instance.getUserEmailAddress()

    let isPushNotificationAlert = props.isHeaderAlert && key == 'push'
    let isEmailNotificationAlert = props.isHeaderAlert && key == 'email'
    let isSmsNotificationAlert = props.isHeaderAlert && key == 'sms'

    const onSettingsPress = () => {
        Platform.OS == 'ios' ? Linking.openURL('app-settings://notification/${com.newput.tinyview}') : Linking.openSettings()
    }

    return (
        <View style={styles.componentView(isPushNotificationAlert, (isEmailNotificationAlert || isSmsNotificationAlert))}>
            <View style={styles.mainContainer}>
                <View style={styles.titleTextView}>
                    <Text style={[isHeaderAlert ? context.h2 : context.p, { color: context.colors.textBold }]}>{title}</Text>
                </View>
                <Switch
                    value={toggleValue}
                    onValueChange={(value) => { props.onAlertSwitched(key, value, props.channel) }}
                    circleSize={28}
                    circleBorderWidth={2}
                    backgroundActive={context.colors.green}
                    backgroundInactive={context.colors.chatBubbles}
                    circleBorderActiveColor={context.colors.green}
                    circleBorderInactiveColor={context.colors.chatBubbles}
                    changeValueImmediately={true}
                    innerCircleStyle={styles.switchCircleView}
                    renderActiveText={false}
                    renderInActiveText={false}
                />
            </View>
            {isPushNotificationAlert && !props.hasNotifPermission &&
                <View style={styles.verifiedTextView}>
                    <Text style={context.bodyMini}>Visit your device <Text onPress={() => { onSettingsPress() }} style={[context.bodyMini, { color: context.colors.logoRed, textDecorationLine: 'underline' }]}>Settings</Text>
                        <Text style={context.bodyMini}> to enable notifications.</Text>
                    </Text>
                </View>
            }
            {isEmailNotificationAlert && userEmailAddress &&
                <View style={styles.verifiedTextView}>
                    <Text style={context.bodyMini}>{userEmailAddress}</Text>
                    <FastImage style={styles.checkmarkView} source={require('../../../assets/green_checkmark.png')} />
                </View>
            }
        </View >
    )
}

const styles = StyleSheet.create({
    mainContainer: {
        paddingTop: 12,
        paddingBottom: 12,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center'
    },
    alertsView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center'
    },
    switchCircleView: {
        alignItems: "center",
        justifyContent: "center"
    },
    checkmarkView: {
        height: 20,
        width: 20,
        marginLeft: 12
    },
    verifiedTextView: {
        flexDirection: 'row'
    },
    componentView: (isPushAlert, isEmailOrSmsAlert) => {
        return {
            marginTop: isPushAlert || isEmailOrSmsAlert ? 20 : 0
        }
    },
    titleTextView: {
        flex: 1,
        marginRight: 20
    }
})