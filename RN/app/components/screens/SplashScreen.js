import React, { Component } from 'react'
import { Alert, ActivityIndicator, Platform, BackHandler } from 'react-native'
import FirebaseManager from '../../config/FirebaseManager'
import { connect } from 'react-redux'
import { createUser, getUserDetails } from '../../redux/actions/actions'
import { SharedPreferences } from '../../config/SharedPreferences'
import { Color } from '../../config/Color'
import SessionManager from '../../config/SessionManager'
import Orientation from 'react-native-orientation'
import PushNotification from 'react-native-push-notification';
import { settings } from '../../config/settings'
import { StackActions } from "@react-navigation/native"
import { Utils } from '../../config/Utils'
import { SafeAreaView } from 'react-native-safe-area-context'
const directory = require('../../../directory.json');

class SplashScreen extends Component {

	constructor(props) {
		super(props)

		this.checkUser = this.checkUser.bind(this)
		this.transistUser = this.transistUser.bind(this)
		this.onUserLikeInfoFetched = this.onUserLikeInfoFetched.bind(this)
		this.getInfoRetryCount = 0;

		if (Platform.OS == 'ios') {
			Orientation.lockToPortrait()
		}
	}

	UNSAFE_componentWillMount() {
		PushNotification.createChannel({
			channelId: settings.notifChannelId,
			channelName: settings.appName,
			vibrate: true,
			playSound: true
		})

		this.checkUser()
	}

	showErrorAlert() {
		Alert.alert("Error", "Something went wrong. Please restart the app and try again", [
			{
				text: "OK", onPress: () => {
					try {
						BackHandler.exitApp();
					} catch (error) {}
				}
			}
		])
	}

	async checkUser() {
		if (!FirebaseManager.instance.isUserAvailable()) {
			const data = await FirebaseManager.instance.signInAnonymously()
			if (data) {				
				this.transistUser(data)
			} else {
				this.showErrorAlert()
			}
		} else {
			try {
				await FirebaseManager.instance.currentUser().reload()	
			} catch (error) {}
			Utils.log("Current User " + JSON.stringify(FirebaseManager.instance.currentUser()))
			this.props.getUserDetails(this.transistUser)
		}
	}		

	async transistUser(data) {
		if (data) {
			const cachedPurchases = await SharedPreferences.getStorePurchasesProducts()
			SessionManager.instance.updatePurchaseItems(cachedPurchases)
			await SharedPreferences.getShowAllComicsValue()
			SessionManager.instance.updateSeriesDetails(directory)

			this.onUserLikeInfoFetched()
		} else {
			const newData = await FirebaseManager.instance.createAndGetUser()
			if (newData) {
				this.transistUser(newData)
			} else {
				this.showErrorAlert()
			}
		}
	}

	async onUserLikeInfoFetched() {
		await FirebaseManager.instance.fetchRemoteConfigurations()
		FirebaseManager.instance.updateUserData()
		this.props.navigation.dispatch(StackActions.replace('DrawerMenu'))
	}

	render() {
		return (
			<SafeAreaView style={{ flex: 1, justifyContent: 'center', }} >
				<ActivityIndicator size="large" color={Color.BLACK_COLOR} />
			</SafeAreaView>
		)
	}
}

const mapStateToProps = (state) => {
	return {

	}
}

const mapDispatchToProps = (dispatch) => {
	return {
		getUserDetails(callback) {
			dispatch(
				getUserDetails(callback)
			)
		},
		createUser(callback) {
			dispatch(
				createUser(callback)
			)
		}
	}
}

export default connect(mapStateToProps, mapDispatchToProps)(SplashScreen)
