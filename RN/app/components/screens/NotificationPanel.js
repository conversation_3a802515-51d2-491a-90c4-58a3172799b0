import React, { Component } from "react";
import { Utils } from "../../config/Utils";
import { Constants } from "../../config/Constants";
import { StyleSheet, TouchableOpacity, Dimensions } from "react-native";
import { ThemeContext } from "../../Contexts";
import { View, Text } from "native-base";
import FastImage from "react-native-fast-image";
import { notificationRoundImage } from "../../config/styles";
import ImagePlaceHolder from "../ImagePlaceHolder";
import { Color } from "../../config/Color";
import { settings } from "../../config/settings";
import HTML from 'react-native-render-html';
import SessionManager from "../../config/SessionManager";

let dimensions = Dimensions.get('window')
export default class NotificationPanel extends Component {
    constructor(props) {
        super(props)

        this.state = {
            errorInImageLoading: false
        }

        this.bgColor = Utils.getSeriesSpecificStyle(settings.TINYVIEW_CHANNEL_NAME).notificationColor
        this.onImageError = this.onImageError.bind(this)
    }

    onImageError() {
        this.setState({ errorInImageLoading: true })
    }

    render() {
        const { item, lastVisited, navFromProfilePage, profileData, isAnotherUserProfile, sectionType, isFirstItem, applyBottomRadius } = this.props
        const { errorInImageLoading } = this.state
        const { user, story, imageUrl, subtitle, body, title, commentText, createdAt, isReply, reaction } = item
        let notificationTime = Utils.convertTimeSpanIntoTimeAgo(createdAt)
        if (notificationTime != Constants.NOW) {
            notificationTime = notificationTime + " ago"
        }

        let showBackground = false
        if (createdAt >= lastVisited) {
            showBackground = true
        }

        const storyTitle = (story && story.title) ? story.title : ""
        const imageURL = (user && user.image) ? user.image : imageUrl
        const displayName = (user && user.name) ? user.name : Constants.NON_SIGNED_IN_USER
        const notifSubtitle = subtitle ? `[${subtitle}] ` : storyTitle ? `[${storyTitle}]` : ""
        const notifBody = body ? body : commentText ? ` "${commentText}"` : ""
        const notifText = (notifSubtitle + notifBody) ? (notifSubtitle + notifBody) : ""
        let notifTitle = title
        let giftIcon = ""

        if (navFromProfilePage && profileData) {
            if (sectionType == Constants.RECENT_COMMENTS) {
                if (isReply) {
                    notifTitle = isAnotherUserProfile ? `${profileData.displayName} replied` : "You replied"
                } else {
                    notifTitle = isAnotherUserProfile ? `${profileData.displayName} commented` : "You commented"
                }
            } else if (sectionType == Constants.GIFTS_SENT) {
                let giftDetails = SessionManager.instance.getGiftItemDetails(reaction)
                if (giftDetails) {
                    const { title, image } = giftDetails
                    notifTitle = isAnotherUserProfile ? `${profileData.displayName} gifted ${title}` : `You gifted ${title}`
                    giftIcon = image
                }
            }
        }

        const showGiftIcon = Utils.checkData(giftIcon)

        return (
            <View>
                <TouchableOpacity
                    style={[styles.container(isFirstItem, applyBottomRadius), { backgroundColor: showBackground ? this.bgColor : this.context.colors.textInverse }]}
                    disabled={!navFromProfilePage}
                    onPress={() => { this.props.navigateToScreens(item) }}>
                    <TouchableOpacity
                        disabled={navFromProfilePage}
                        onPress={() => this.props.navigateToUserProfile(item)}>
                        {(!errorInImageLoading) && (Utils.checkData(imageURL))
                            ?
                            <FastImage
                                style={notificationRoundImage}
                                source={{ uri: imageURL }}
                                onError={() => { this.onImageError() }} />
                            :
                            <ImagePlaceHolder
                                backgroundColor={Color.PROFILE_PLACE_HOLDER_BG}
                                showCircularBorder={true}
                                textColor={Color.COMMENT_TEXT_COLOR}
                                size={32}
                                type={'circle'}>{displayName}</ImagePlaceHolder>
                        }
                    </TouchableOpacity>
                    <View style={styles.notifTextInfoView}>
                        <TouchableOpacity
                            disabled={navFromProfilePage}
                            onPress={() => { this.props.navigateToScreens(item) }}>
                            <View style={styles.textContainer}>
                                <View style={styles.titleView(showGiftIcon)}>
                                    <Text style={[this.context.pBold, styles.notfTitleView]}>{notifTitle}</Text>
                                    {showGiftIcon && <FastImage source={{ uri: giftIcon }} style={styles.giftIconView} />}
                                </View>
                                <Text style={[this.context.p, styles.timeView]}>{notificationTime}</Text>
                            </View>
                            <View style={styles.htmlTextView}>
                                <HTML contentWidth={dimensions.width} textSelectable={true} allowFontScaling baseStyle={this.context.p} source={{ html: notifText }} tagsStyles={{ "b": this.context.pBold, "p": { paddingTop: 0, marginBottom: 0, marginTop: 0 } }} />
                            </View>
                        </TouchableOpacity>
                    </View>
                </TouchableOpacity>
                <View style={[styles.separatorStyle, { navFromProfilePage: this.context.colors.chatBubbles, backgroundColor: showBackground ? this.context.colors.textInverse : this.context.colors.borders }]} />
            </View>
        )
    }
}

NotificationPanel.contextType = ThemeContext

const styles = StyleSheet.create({
    container: (isFirstItem, applyBottomRadius) => {
        return {
            flexDirection: 'row',
            paddingTop: 12,
            paddingBottom: 12,
            paddingLeft: 16,
            paddingRight: 16,
            borderTopLeftRadius: isFirstItem ? 12 : 0,
            borderTopRightRadius: isFirstItem ? 12 : 0,
            borderBottomLeftRadius: applyBottomRadius ? 12 : 0,
            borderBottomRightRadius: applyBottomRadius ? 12 : 0
        }
    },
    textContainer: {
        flex: 1,
        marginStart: 12,
        flexDirection: 'row',
        justifyContent: 'space-between'
    },
    titleView: (showGiftIcon) => {
        return {
            flex: 1,
            flexDirection: 'row',
            marginRight: showGiftIcon ? 25 : 0
        }
    },
    timeView: {
        marginLeft: 5
    },
    giftIconView: {
        height: 20,
        width: 20,
        marginLeft: 8
    },
    separatorStyle: {
        height: 1,
        width: '100%'
    },
    notifTextInfoView: {
        flex: 1,
        flexDirection: 'column'
    },
    htmlTextView: {
        marginStart: 12,
        marginTop: 4
    },
    notfTitleView: {
        flexWrap: 'nowrap'
    }
})