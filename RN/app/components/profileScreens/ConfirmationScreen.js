import React, { Component } from 'react'
import { StyleSheet, BackHandler, Image, Dimensions, SafeAreaView } from 'react-native'
import { View, Button, Text } from 'native-base'
import { connect } from "react-redux"
import { Color } from '../../config/Color'
import { SystemFont } from '../../config/Typography'
import { scale } from 'react-native-size-matters'
import { Constants } from '../../config/Constants'
import ShareBottomSheet from '../ShareBottomSheet'
import { shareInviteLink } from '../../redux/actions/actions'
import { Utils } from '../../config/Utils'
import SigningHeaderBar from '../friendsPackage/shared/SigningHeaderBar'
import { ThemeContext } from '../../Contexts'
import UserSession from '../../config/UserSession'

class ConfirmationScreen extends Component {

  static navigationOptions = ({ navigation }) => {
    return {
      header: (props) => <SigningHeaderBar {...props} />
    }
  }

  constructor(props) {
    super(props)
    this.state = {
      isForLoginProcess: true,
      showInviteBottomSheet: false
    }

    const params = this.props.route.params
    this.fromSignUp = params && params.fromSignUp

    this.configBottomSheet = ''

    this.onBackPress = this.onBackPress.bind(this)
    this.navigateToInviteBottomSheet = this.navigateToInviteBottomSheet.bind(this)
    this.imageStyle = this.imageStyle.bind(this)
    this.closeBottomSheet = this.closeBottomSheet.bind(this)
    this.renderInviteActionSheet = this.renderInviteActionSheet.bind(this)
    this.renderButton = this.renderButton.bind(this)
    this.navigateToAppropriatePage = this.navigateToAppropriatePage.bind(this)
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.navigation && nextProps.route.params) {
      if (this.state.isForLoginProcess != nextProps.route.params.isForLoginProcess) {
        this.props.navigation.setParams({
          title: this.fromSignUp ? Constants.SIGN_UP_SUCCESSFUL : Constants.SIGN_IN_SUCCESSFUL
        })
        this.setState({ isForLoginProcess: nextProps.route.params.isForLoginProcess })
      }
    }
  }

  componentDidMount() {
    this.props.navigation.setParams({
      title: this.fromSignUp ? Constants.SIGN_UP_SUCCESSFUL : Constants.SIGN_IN_SUCCESSFUL
    })

    if (UserSession.instance.isMonthlyComicLimitApplied()) {
      Utils.showToast("As a signed in user, you have unlimited access now.", "bottom", "userSignedIn", 5000)
    }    

    if (this.fromSignUp) {
      setTimeout(() => {
        this.navigateToAppropriatePage()
      }, 10000)
    }
  }

  onBackPress() {
    this.navigateToAppropriatePage()
    return true
  }

  UNSAFE_componentWillMount() {
    BackHandler.addEventListener('hardwareBackPress', this.onBackPress);
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.onBackPress);
  }

  navigateToInviteBottomSheet() {
    this.configBottomSheet = Constants.INVITE_FRIENDS
    this.setState({ showInviteBottomSheet: true })
  }

  closeBottomSheet() {
    this.setState({ showInviteBottomSheet: false })
  }

  renderInviteActionSheet() {
    const valueProps = { closeBottomSheet: this.closeBottomSheet, userDetails: this.props.userDetails, shareInviteLink: this.props.shareInviteLink }
    return (
      <ShareBottomSheet {...valueProps} configShareSheetFor={this.configBottomSheet} />
    )
  }

  navigateToAppropriatePage(item = null) {
    if (item == Constants.INVITE_FRIENDS) {
      this.navigateToInviteBottomSheet()
    } else {
      this.props.navigation.navigate('Home', { 'forceReload': true })
    }
  }

  renderButton(item) {
    let isLaterButton = item == Constants.DO_IT_LATER
    let isHomePageButton = item == Constants.DONE

    return (
      <View style={isHomePageButton ? styles.buttonStyle : [styles.buttonStyle, { width: '46%' }]}>
        <Button
          variant='solid'
          style={[styles.homeButtonView, { borderColor: isLaterButton ? this.context.colors.separators : this.context.colors.logoRed, backgroundColor: isLaterButton ? this.context.colors.textInverse : this.context.colors.logoRed }]}
          onPress={() => this.navigateToAppropriatePage(item)}>
          <Text style={[this.context.pBold, { color: isLaterButton ? this.context.colors.text : this.context.colors.textInverse }]}>{item}</Text>
        </Button>
      </View>
    )
  }

  imageStyle(w = 800, h = 600, margin = 15) {
    let dimensions = Dimensions.get('window')
    let aspectRatio = (dimensions.width - ((margin) * 2)) / w

    return {
      width: w * aspectRatio,
      height: h * aspectRatio,
      resizeMode: 'contain'
    }
  }

  render() {
    return (
      <SafeAreaView style={styles.mainContainer}>
        <View style={styles.viewStyle}>
          <Image
            style={this.imageStyle()}
            source={this.fromSignUp ? require("../../../assets/signup_confirmation.jpg") : { uri: Utils.getPanelURL(Constants.WHY_INVITE_FRIEND_IMAGE) }}
          />
          {!this.fromSignUp &&
            <View style={{ flexDirection: 'row', justifyContent: 'space-around' }}>
              {this.renderButton(Constants.DO_IT_LATER)}
              {this.renderButton(Constants.INVITE_FRIENDS)}
            </View>}
          {this.fromSignUp && this.renderButton(Constants.DONE)}
        </View>
        {this.state.showInviteBottomSheet && this.renderInviteActionSheet()}
      </SafeAreaView>
    )
  }
}

ConfirmationScreen.contextType = ThemeContext;

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: 'white',
    height: '100%'
  },
  viewStyle: {
    marginTop: 48,
    marginBottom: 40,
    alignSelf: 'center'
  },
  otpText: {
    color: Color.BLACK_COLOR,
    fontFamily: SystemFont.SELECTED_FONT,
    fontWeight: 'bold',
    fontSize: scale(22),
    marginBottom: scale(12),
    marginTop: scale(15),
  },
  confirmText: {
    color: Color.DARK_GREY,
    fontFamily: SystemFont.SELECTED_FONT,
    fontSize: scale(18),
    marginTop: scale(15)
  },
  homeButtonView: {
    borderWidth: 1,
    borderRadius: 6,
    paddingTop: 12,
    paddingBottom: 12
  },
  buttonStyle: {
    marginTop: 32
  }
});

const mapStateToProps = (state) => {
  return {
    isAnonymous: state.loginInfo.isAnonymous,
    pathUrl: state.readComic.pathUrl,
    userDetails: state.loginInfo.userDetails
  }
}

const mapDispatchToProps = (dispatch) => {
  return {
    shareInviteLink(requestedData, callback) {
      dispatch(
        shareInviteLink(requestedData, callback)
      )
    }
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(ConfirmationScreen)
