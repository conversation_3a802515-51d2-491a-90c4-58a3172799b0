import React, { useContext } from "react";
import { View, StyleSheet, Text } from "react-native";
import { ThemeContext } from "../../Contexts";

export default SignUpPageInfo = (props) => {
    const context = useContext(ThemeContext)
    let isThirdSignupStep = props.signUpStep == 3
    let isSecondSignupStep = (props.signUpStep == 2 || isThirdSignupStep)
    let isFirstSignupStep = (props.signUpStep == 1 || isSecondSignupStep || isThirdSignupStep)
    
    return (
        <View style={styles.mainContainer}>
            <View style={[styles.circleContainer, { backgroundColor: isFirstSignupStep ? context.colors.logoRed : context.colors.textInverse, borderColor: isFirstSignupStep ? context.colors.logoRed : context.colors.separators }]}>
                <Text style={[styles.circleTextView, { color: isFirstSignupStep ? context.colors.textInverse : context.colors.textBold }]}>1</Text>
            </View>
            <View style={[styles.separatorView, { borderColor: isSecondSignupStep ? context.colors.logoRed : context.colors.separators }]}></View>
            <View style={[styles.circleContainer, { backgroundColor: isSecondSignupStep ? context.colors.logoRed : context.colors.textInverse, borderColor: isSecondSignupStep ? context.colors.logoRed : context.colors.separators }]}>
                <Text style={[styles.circleTextView, { color: isSecondSignupStep ? context.colors.textInverse : context.colors.textBold }]}>2</Text>
            </View>
            <View style={[styles.separatorView, { borderColor: isThirdSignupStep ? context.colors.logoRed : context.colors.separators }]}></View>
            <View style={[styles.circleContainer, { backgroundColor: isThirdSignupStep ? context.colors.logoRed : context.colors.textInverse, borderColor: isThirdSignupStep ? context.colors.logoRed : context.colors.separators }]}>
                <Text style={[styles.circleTextView, { color: isThirdSignupStep ? context.colors.textInverse : context.colors.textBold }]}>3</Text>
            </View>
        </View>
    )
}

const styles = StyleSheet.create({
    mainContainer: {
        marginTop: 20,
        marginBottom: 32,
        flexDirection: 'row',
        alignSelf: 'center'
    },
    circleContainer: {
        height: 40,
        width: 40,
        borderWidth: 1,
        borderRadius: 100,
        justifyContent: 'center'
    },
    separatorView: {
        width: 30,
        borderWidth: 1,
        alignSelf: 'center',
        marginLeft: 12,
        marginRight: 12
    },
    circleTextView: {
        alignSelf: 'center'
    }
})