import React, { Component } from 'react'
import { Dimensions, Image, StyleSheet, BackHandler, RefreshControl, TouchableOpacity, Animated, SectionList } from 'react-native'
import { View, Text, Button } from 'native-base'
import { connect } from 'react-redux'
import {
  getUserDetails,
  openWebViewAction,
  sendFriendRequest,
  unfriend,
  updateFriendRequest,
  shareApp,
  getFriendList,
  getUserFriendshipStatus,
  getFriendRequest,
  resendFriendRequest,
  shareInviteLink,
  getUserRecentComments,
  getUserGiftsSent,
  updateShowAllComicsSettings
} from '../../redux/actions/actions'
import { Color } from '../../config/Color'
import { StackActions } from '@react-navigation/native'
import { settings } from '../../config/settings'
import { Utils } from '../../config/Utils'
import FirebaseManager from '../../config/FirebaseManager'
import { navigationStyle, loadingViewStyle } from '../../config/styles'
import UserProfileHeaderView from '../profileScreens/UserProfileHeaderView'
import { Constants } from '../../config/Constants'
import NetworkUtils from '../../config/NetworkUtils';
import ShareBottomSheet from '../../components/ShareBottomSheet'
import BottomTabBar from '../BottomTabBar'
import { ThemeContext } from '../../Contexts'
import NotificationPanel from '../screens/NotificationPanel'
import FriendsPanel from '../friendsPackage/panels/FriendsPanel'
import SessionManager from '../../config/SessionManager'
import { SharedPreferences } from '../../config/SharedPreferences'
import { LoadingIndicator } from '../LoadingIndicator'
import DeepLinkManager from '../../config/DeepLinkManager'
import ActionSheet from '../actionSheet/ActionSheetCustom'
import { SafeAreaView } from 'react-native-safe-area-context'

let dimensions = Dimensions.get('window')
class UserProfileScreen extends Component {

  constructor(props) {
    super(props)

    let currentUser = FirebaseManager.instance.currentUser()
    const params = props.route.params
    this.contactToSendRequest = params && params.contactToSendRequest
    this.clickedUserID = params && params.clickedUserID
    this.UID = currentUser.uid
    this.profileUserID = this.clickedUserID ? this.clickedUserID : this.UID
    this.isAnotherUserProfile = this.UID != this.profileUserID
    this.currentLoggedInUserMob = currentUser._user.phoneNumber
    this.isGuestUserProfile = false

    this._unsubscribe = null
    this.bgColor = Utils.getSeriesSpecificStyle(settings.TINYVIEW_CHANNEL_NAME).backgroundColor
    this.lastScrollOffset = 0;
    this.scrollY = new Animated.Value(0)

    this.totalRecentComments = null
    this.totalGiftsSent = null
    this.lastRecentCommentID = ""
    this.lastGiftCommentID = ""
    this.recentGiftsSentList = []
    this.recentCommentsList = []
    this.userFriendsList = []
    this.friendSentRequests = []
    this.friendReceivedRequests = []
    this.hasMoreInviteReceives = true
    this.hasMoreInviteSent = true
    this.hasMoreFriends = true
    this.hasMoreRecentComments = true
    this.hasMoreGiftsSent = true
    this._hasMounted = false
    this.initialSectionList = [{ key: Constants.INVITES_RECEIVED, title: Constants.INVITES_RECEIVED, data: this.friendReceivedRequests },
    { key: Constants.INVITES_SENT, title: Constants.INVITES_SENT, data: this.friendSentRequests },
    { key: Constants.FRIENDS, title: Constants.FRIENDS, data: this.userFriendsList },
    { key: Constants.GIFTS_SENT, title: Constants.GIFTS_SENT, data: this.recentGiftsSentList },
    { key: Constants.RECENT_COMMENTS, title: Constants.RECENT_COMMENTS, data: this.recentCommentsList }]

    if (this.isAnotherUserProfile) {
      this.initialSectionList = [{ key: Constants.GIFTS_SENT, title: Constants.GIFTS_SENT, data: this.recentGiftsSentList },
      { key: Constants.RECENT_COMMENTS, title: Constants.RECENT_COMMENTS, data: this.recentCommentsList },
      { key: Constants.FRIENDS, title: Constants.FRIENDS, data: this.userFriendsList }]
    }

    this.state = {
      showRefresh: false,
      profileData: null,
      profileButtonName: null,
      socialLinks: [
        {
          icon: require('../../../assets/icons_instagram.png'), link: null, key: Constants.INSTAGRAM_LINK, webDomain: Constants.INSTAGRAM_WEB
        },
        {
          icon: require('../../../assets/icons_twitter.png'), link: null, key: Constants.TWITTER_LINK, webDomain: Constants.TWITTER_WEB
        },
        {
          icon: require('../../../assets/icons_facebook.png'), link: null, key: Constants.FACEBOOK_LINK, webDomain: Constants.FACEBOOK_WEB
        },
        {
          icon: null, link: null, key: Constants.WEBSITE_LINK
        },
      ],
      showInviteBottomSheet: false,
      friendshipData: null,
      friendshipStatus: null,
      isLoadingRecentComments: false,
      isLoadingInvitesSent: false,
      isLoadingInvitesReceived: false,
      isLoadingFriends: false,
      hideBottomBar: false,
      sectionListData: this.initialSectionList,
      isLoadingGiftsSent: false,
      sheetTitle: '',
      sheetMessage: Constants.SIGN_IN_ACTION_SHEET_MESSAGE,
      sheetOptions: [Constants.SIGN_IN, Constants.CANCEL],
      cancelOptionIndex: 1
    }

    this.updateProfileDetails = this.updateProfileDetails.bind(this)
    this.navigateToUserProfile = this.navigateToUserProfile.bind(this)
    this.handleHardwareBackButton = this.handleHardwareBackButton.bind(this)
    this.onBackPress = this.onBackPress.bind(this)
    this.reloadChapter = this.reloadChapter.bind(this)
    this.getUserDetails = this.getUserDetails.bind(this)
    this.openWebView = this.openWebView.bind(this)
    this.updateFriendRequest = this.updateFriendRequest.bind(this)
    this.unfriendUser = this.unfriendUser.bind(this)
    this.onSuccess = this.onSuccess.bind(this)
    this.sendTextToContacts = this.sendTextToContacts.bind(this)
    this.getContactsToSendRequest = this.getContactsToSendRequest.bind(this)
    this.openChapter = this.openChapter.bind(this)
    this.renderHeader = this.renderHeader.bind(this)
    this.sectionLoaderView = this.sectionLoaderView.bind(this)
    this.renderMainBottomBar = this.renderMainBottomBar.bind(this)
    this.navigateToStackTop = this.navigateToStackTop.bind(this)
    this.renderInvitesFriendsList = this.renderInvitesFriendsList.bind(this)
    this.renderFooter = this.renderFooter.bind(this)
    this.onPressFooter = this.onPressFooter.bind(this)
    this.onInviteFriendButtonTap = this.onInviteFriendButtonTap.bind(this)
    this.closeBottomSheet = this.closeBottomSheet.bind(this)
    this.fetchFriendsSection = this.fetchFriendsSection.bind(this)
    this.fetchUserComments = this.fetchUserComments.bind(this)
    this.updateRecentActivityLists = this.updateRecentActivityLists.bind(this)
    this.fetchGiftsSentComments = this.fetchGiftsSentComments.bind(this)
    this.onUpdatedFriendRequest = this.onUpdatedFriendRequest.bind(this)
    this.getFriendshipData = this.getFriendshipData.bind(this)
    this.updatedFriendshipData = this.updatedFriendshipData.bind(this)
    this.onPressHeaderButton = this.onPressHeaderButton.bind(this)
    this.renderListTitle = this.renderListTitle.bind(this)
    this._listEmptyComponent = this._listEmptyComponent.bind(this)
    this.navigateToScreens = this.navigateToScreens.bind(this)
    this.emptyAllLists = this.emptyAllLists.bind(this)
    this.updateInvitesAndFriends = this.updateInvitesAndFriends.bind(this)
    this.renderShareActionSheet = this.renderShareActionSheet.bind(this)
    this.renderSeparator = this.renderSeparator.bind(this)
    this.navigateToSubscribePage = this.navigateToSubscribePage.bind(this)
    this.onShowAllComicSwitchTap = this.onShowAllComicSwitchTap.bind(this)
    this.navigateToLoginPage = this.navigateToLoginPage.bind(this)
    this.updateSectionListData = this.updateSectionListData.bind(this)
    this._onViewDidFocus = this._onViewDidFocus.bind(this)
    this.navigateToInvitesSent = this.navigateToInvitesSent.bind(this)
    this.onActionSheetPress = this.onActionSheetPress.bind(this)
    this.signInSheet = this.signInSheet.bind(this)
    this.changeActionSheetTitleAndButton = this.changeActionSheetTitleAndButton.bind(this)

    this.aspectRatio = dimensions.width / Constants.LOADING_GIF_DIMEN.width
  }

  UNSAFE_componentWillMount() {
    BackHandler.addEventListener('hardwareBackPress', this.handleHardwareBackButton);
  }

  componentWillUnmount() {
    if (this._unsubscribe) {
      this._unsubscribe()
    }
    BackHandler.removeEventListener('hardwareBackPress', this.handleHardwareBackButton);
  }

  handleHardwareBackButton() {
    this.onBackPress()
    return true
  }

  onBackPress() {
    this.props.navigation.goBack()
    return true;
  }

  componentDidMount() {
    this.reloadChapter()
    this._unsubscribe = this.props.navigation.addListener("focus", async () => {
      if (this._hasMounted) {
        this._onViewDidFocus();
      } else {
        this._hasMounted = true;
      }
    });
  }

  _onViewDidFocus() {
    if (!this.isAnotherUserProfile) {
      this.setState({ hideBottomBar: false })
      this.reloadChapter()
    } else {
      this.userFriendsList = []
      let sectionListData = this.updateSectionListData()
      this.setState({ sectionListData, isLoadingFriends: true, hideBottomBar: false })
      this.fetchFriendsSection().then(() => {
        let updatedSections = this.updateSectionListData()
        this.setState({ sectionListData: updatedSections, isLoadingFriends: false })
      })
    }
  }

  reloadChapter() {
    this.setState({ sectionListData: this.initialSectionList, showRefresh: true, isLoadingGiftsSent: true, isLoadingRecentComments: true, isLoadingFriends: true, isLoadingInvitesReceived: true, isLoadingInvitesSent: true })

    this.emptyAllLists()
    this.getUserDetails(() => {
      Promise.allSettled([
        this.fetchFriendsSection(),
        this.fetchGiftsSentComments(),
        this.fetchUserComments()
      ]).then(() => {
        let updatedSections = this.updateSectionListData()
        if (this.isAnotherUserProfile) {
          updatedSections = updatedSections.filter(section => Array.isArray(section.data) && section.data.length > 0)
        }
        this.setState({ sectionListData: updatedSections, isLoadingGiftsSent: false, isLoadingRecentComments: false, isLoadingFriends: false, isLoadingInvitesReceived: false, isLoadingInvitesSent: false })
      })
    })
  }

  updateSectionListData() {
    const updatedSectionList = this.state.sectionListData.map(section => {
      switch (section.key) {
        case Constants.INVITES_RECEIVED:
          return { ...section, data: this.friendReceivedRequests }
        case Constants.INVITES_SENT:
          return { ...section, data: this.friendSentRequests }
        case Constants.FRIENDS:
          return { ...section, data: this.userFriendsList }
        case Constants.GIFTS_SENT:
          return { ...section, data: this.recentGiftsSentList }
        case Constants.RECENT_COMMENTS:
          return { ...section, data: this.recentCommentsList }
        default:
          return section
      }
    })
    return updatedSectionList
  }


  emptyAllLists() {
    this.totalRecentComments = null
    this.totalGiftsSent = null
    this.lastRecentCommentID = ""
    this.lastGiftCommentID = ""
    this.recentGiftsSentList = []
    this.recentCommentsList = []
    this.userFriendsList = []
    this.friendSentRequests = []
    this.friendReceivedRequests = []
    this.hasMoreInviteReceives = true
    this.hasMoreInviteSent = true
    this.hasMoreFriends = true
    this.hasMoreRecentComments = true
    this.hasMoreGiftsSent = true
  }

  fetchGiftsSentComments() {
    return new Promise((resolve) => {
      this.props.getUserGiftsSent({ userID: this.profileUserID, startAfter: this.lastGiftCommentID, records: Constants.USER_PROFILE_PAGE_PAGINATION_COUNT },
        (data) => {
          this.updateRecentActivityLists(data, Constants.GIFTS_SENT)
          resolve()
        }
      )
    })
  }

  fetchUserComments() {
    return new Promise((resolve) => {
      this.props.getUserRecentComments({ userID: this.profileUserID, startAfter: this.lastRecentCommentID, records: Constants.USER_PROFILE_PAGE_PAGINATION_COUNT },
        (data) => {
          this.updateRecentActivityLists(data, Constants.RECENT_COMMENTS)
          resolve()
        }
      )
    })
  }

  updateRecentActivityLists(data, listType) {
    if (!data) {
      return null
    }

    const { comments, total, startAfter, gifts } = data
    let updatedList = []

    if (listType === Constants.GIFTS_SENT) {
      if (this.totalGiftsSent === null) {
        this.totalGiftsSent = total
      }

      updatedList = [...this.recentGiftsSentList, ...gifts];
      if (this.totalGiftsSent <= updatedList.length) {
        this.hasMoreGiftsSent = false
      }

      this.lastGiftCommentID = startAfter
      this.recentGiftsSentList = updatedList
    } else if (listType === Constants.RECENT_COMMENTS) {
      if (this.totalRecentComments === null) {
        this.totalRecentComments = total
      }

      updatedList = [...this.recentCommentsList, ...comments];
      if (this.totalRecentComments <= updatedList.length) {
        this.hasMoreRecentComments = false
      }

      this.lastRecentCommentID = startAfter
      this.recentCommentsList = updatedList
    }
  }


  fetchFriendsSection() {
    if (this.isGuestUserProfile) {
      return Promise.resolve();
    }

    return new Promise((resolve) => {
      let promises = []

      if (!this.isAnotherUserProfile) {
        promises.push(
          new Promise((res) => {
            this.props.getFriendRequest({ records: Constants.USER_PROFILE_PAGE_PAGINATION_COUNT, requestType: Constants.SENT.toLowerCase() },
              (data) => {
                this.updateInvitesAndFriends(data, Constants.INVITES_SENT)
                res()
              }
            )
          }),
          new Promise((res) => {
            this.props.getFriendRequest({ records: Constants.USER_PROFILE_PAGE_PAGINATION_COUNT, requestType: Constants.RECEIVED.toLowerCase() },
              (data) => {
                this.updateInvitesAndFriends(data, Constants.INVITES_RECEIVED)
                res()
              }
            )
          })
        )
      }

      promises.push(
        new Promise((res) => {
          this.props.getFriendList(
            (data) => {
              this.updateInvitesAndFriends(data, Constants.FRIENDS)
              res()
            }, true, { userID: this.profileUserID, records: Constants.USER_PROFILE_PAGE_PAGINATION_COUNT }
          )
        })
      )

      Promise.allSettled(promises).then(() => resolve())
    })
  }

  updateInvitesAndFriends(data, listType) {
    if (!data) {
      return null
    }

    const paginationLimit = Constants.USER_PROFILE_PAGE_PAGINATION_COUNT;

    if (listType === Constants.INVITES_RECEIVED) {
      this.friendReceivedRequests = data
      if (data.length < paginationLimit) {
        this.hasMoreInviteReceives = false
      }
    } else if (listType === Constants.INVITES_SENT) {
      this.friendSentRequests = data
      if (data.length < paginationLimit) {
        this.hasMoreInviteSent = false
      }
    } else if (listType === Constants.FRIENDS) {
      this.userFriendsList = data
      if (data.length < paginationLimit) {
        this.hasMoreFriends = false
      }
    }
  }

  getFriendshipData() {
    if (this.isGuestUserProfile) {
      this.setState({ friendshipStatus: Constants.ADD_FRIEND_TITLE_CASE })
      return
    }

    this.props.getUserFriendshipStatus({ userID: this.profileUserID }, this.updatedFriendshipData)
  }

  updatedFriendshipData(data) {
    if (data) {
      let friendStatus = ""
      if (data.friendshipStatus) {
        const { status } = data.friendshipStatus
        if (!status) {
          friendStatus = Constants.ADD_FRIEND_TITLE_CASE
        } else if (status && status === 'sent') {
          friendStatus = Constants.CANCEL_REQUEST
        } else if (status && status === 'received') {
          friendStatus = Constants.ACCEPT_REQUEST
        } else if (status && status === 'friends') {
          friendStatus = Constants.FRIEND
        }
      }
      this.setState({ friendshipData: data, friendshipStatus: friendStatus })
    }
  }

  onPressHeaderButton(status) {
    const { friendshipData } = this.state
    const requestedID = friendshipData && friendshipData.friendshipStatus && friendshipData.friendshipStatus.requestDocID

    if (status === Constants.ADD_FRIEND_TITLE_CASE) {
      this.sendTextToContacts()
    } else if (status === Constants.EDIT_PROFILE) {
      Utils.navigateToDrawerLoginRoute(null, Constants.EDIT_PROFILE_SCREEN, { isupdatingProfile: true })
    } else if (status === Constants.CANCEL_REQUEST) {
      let data = { requestID: requestedID, status: Constants.DELETE }
      this.updateFriendRequest(data, true)
    } else if (status === Constants.ACCEPT_REQUEST) {
      let data = { requestID: requestedID, status: Constants.ACCEPTED }
      this.updateFriendRequest(data, true)
    } else if (status === Constants.FRIEND) {
      let data = { friendUID: this.clickedUserID }
      this.unfriendUser(data)
    }
  }

  async getUserDetails(callback = null) {
    const isConnected = NetworkUtils.instance.isAvailable()
    if (!isConnected) {
      return Utils.showError(Constants.INTERNET_ERROR)
    }

    this.props.getUserDetails((data, userID, error) => {
      this.updateProfileDetails(data, userID, error)
      if (callback) {
        callback()
      }
    }, this.profileUserID)
  }

  async updateProfileDetails(userData, userID, error = null) {
    let data = null
    if (userData) {
      data = { ...userData }
    }

    if (data) {
      for (const index in this.state.socialLinks) {
        const item = this.state.socialLinks[index]
        let webDomain = item.webDomain

        item.link = null

        if (data && data.socialLinks && data.socialLinks.length > 0) { //checking for social links
          for (const key in data.socialLinks) {
            if (data.socialLinks[key] && data.socialLinks[key].includes(webDomain)) {
              item.link = data.socialLinks[key]
            }
            if (!webDomain && key == data.socialLinks.length - 1) {
              item.link = data.socialLinks[key]
            }
          }
        }
      }

      this.isGuestUserProfile = Utils.isGuestUserProfile(data)
      this.setState({ profileData: data, showRefresh: false })
      { this.isAnotherUserProfile && this.getFriendshipData() }
    } else {
      Utils.showToast(error)
      this.onBackPress()
      return
    }
  }

  openWebView(url) {
    this.props.openWebView(url)
  }

  updateFriendRequest(data, sameUser = false) {
    this.props.updateFriendRequest(data, (success, status) => this.onUpdatedFriendRequest(success, status, data.requestID, sameUser))
  }

  onUpdatedFriendRequest(success = false, status = null, requestID = null, sameUser = false) {
    if (!success || !status) {
      return
    }

    if (this.isAnotherUserProfile && !sameUser) {
      this.userFriendsList = []
      let sectionListData = this.updateSectionListData()
      this.setState({ sectionListData, isLoadingFriends: true })
      this.fetchFriendsSection().then(() => {
        let updatedSections = this.updateSectionListData()
        this.setState({ sectionListData: updatedSections, isLoadingFriends: false })
      })
      return
    }

    const { profileData } = this.state
    let sectionListData = [...this.state.sectionListData]
    const lowerStatus = status.toLowerCase()
    const isDeleteAction = lowerStatus === Constants.DELETE.toLowerCase()
    const isAcceptAction = status === Constants.ACCEPTED
    const isRejectOrAcceptAction = status === Constants.REJECTED || isAcceptAction
    let userFriendsCount = profileData.friendCount
    let acceptedRequestedUser = null


    if (isDeleteAction) {
      if (sameUser) {
        this.setState({ friendshipStatus: Constants.ADD_FRIEND_TITLE_CASE })
      } else {
        const index = this.friendSentRequests.findIndex(item => item.docId === requestID)
        if (index !== -1) {
          this.friendSentRequests.splice(index, 1)
          sectionListData = this.updateSectionListData()
        }
      }
    } else if (isRejectOrAcceptAction) {
      if (sameUser) {
        this.setState({ friendshipStatus: Constants.FRIEND })
      } else {
        const index = this.friendReceivedRequests.findIndex(item => item.docId === requestID)
        if (index !== -1) {
          if (isAcceptAction) {
            const acceptedUser = this.friendReceivedRequests[index];
            acceptedRequestedUser = acceptedUser?.fullName
            userFriendsCount = profileData.friendCount + 1
            this.userFriendsList.push(acceptedUser)
            sectionListData = this.updateSectionListData()
          }

          this.friendReceivedRequests.splice(index, 1);
          sectionListData = this.updateSectionListData()
        }
      }
    }

    if (isAcceptAction) {
      if (acceptedRequestedUser) {
        Utils.showToast(`You are friends with ${acceptedRequestedUser} now.`)
      }
      this.setState({ sectionListData, profileData: { ...profileData, friendCount: userFriendsCount } })
    } else {
      this.setState({ sectionListData })
    }
  }

  unfriendUser(data, unfriendUser = false) {
    this.props.unfriend(data, (success, status) => this.onSuccess(success, status, data.documentId, unfriendUser))
  }

  onSuccess(isSuccess = false, status = null, requestID = null, isFriendListUser = false) {
    if (!isSuccess) {
      return
    }

    if (this.isAnotherUserProfile && isFriendListUser) {
      this.userFriendsList = []
      let sectionListData = this.updateSectionListData()
      this.setState({ sectionListData, isLoadingFriends: true })
      this.fetchFriendsSection().then(() => {
        let updatedSections = this.updateSectionListData()
        this.setState({ sectionListData: updatedSections, isLoadingFriends: false })
      })
    } else if (status == Constants.UNFRIEND.toLowerCase()) {
      const { profileData } = this.state
      let userFriendsCount = profileData.friendCount
      let sectionListData = [...this.state.sectionListData];
      const index = this.userFriendsList.findIndex(item => item.docId === requestID);
      if (index !== -1) {
        userFriendsCount = profileData.friendCount - 1
        this.userFriendsList.splice(index, 1)
        sectionListData = this.updateSectionListData()
      }

      this.setState({ friendshipStatus: Constants.ADD_FRIEND_TITLE_CASE, sectionListData, profileData: { ...profileData, friendCount: userFriendsCount } });
    } else if (status == Constants.SEND_FRIEND_REQUEST) {
      this.getFriendshipData()
    }
  }

  async sendTextToContacts(receiverData = null) {
    let extraData = {}
    let data = this.props.userDetails
    let sendTextToContactsParams = null
    if (!this.contactToSendRequest) {
      this.contactToSendRequest = this.state.profileData && this.state.profileData.phoneNumber
    }

    if (this.contactToSendRequest) {
      const deepLinkURL = await DeepLinkManager.instance.getBranchLinkURL(Constants.ADD_FRIEND, null, this.contactToSendRequest)
      extraData[this.contactToSendRequest] = deepLinkURL

      let displayName = data.displayName ? data.displayName : Constants.TINYVIEW_USER
      let fullMessageText = `${Constants.inviteFriendTemplateStart} ${displayName} ${Constants.inviteFriendTemplateEnd}`


      sendTextToContactsParams = {
        body: fullMessageText,
        invitedContacts: [`${this.contactToSendRequest}`],
        extraMessage: extraData, // will add contact is appContact or non-appContact
      }
    }

    let senderName = (data && data.displayName) ? data.displayName : ''
    let senderProfilePic = (data && data.photoURL) ? data.photoURL : ''
    let sendFriendRequestParams = {
      data: {
        requests: this.getContactsToSendRequest(receiverData),
        senderDetails: {
          senderName: senderName,
          senderPhoneNumber: this.currentLoggedInUserMob,
          senderProfilePic: senderProfilePic,
          senderUID: this.UID
        },
      },
    }

    const isFriendListUser = receiverData ? true : false
    this.props.sendFriendRequest(sendFriendRequestParams.data, sendTextToContactsParams, (isSuccess, status) => { this.onSuccess(isSuccess, status, null, isFriendListUser) })
  }

  getContactsToSendRequest(receiverData = null) {
    let requests = []
    let data = {}
    if (receiverData) {
      data = {
        receiverUID: receiverData.uid,
        receiverName: receiverData.fullName,
        receiverPhoneNumber: receiverData.userPhoneNumber,
        receiverProfilePic: receiverData.userProfilePic,
        status: "pending"
      }
    } else {
      data = {
        receiverUID: this.clickedUserID,
        receiverName: this.state.profileData && this.state.profileData.displayName,  // for this condition profile data is having receiver's data
        receiverPhoneNumber: this.state.profileData && this.state.profileData.phoneNumber,
        receiverProfilePic: this.state.profileData && this.state.profileData.photoURL,
        status: "pending"
      }
    }
    requests.push(data)
    return requests
  }

  openChapter(path, item) {
    if (path == settings.getInfluencePageURL()) {
      Utils.navigateToInfluencePage(this.props)
    } else if (!Utils.isHomeURL(path) && !Utils.isChannelURL(path) && !Utils.isComicURL(path) && item && item.item && item.item.user && item.item.user.userId) {
      this.navigateToUserProfile(item)
    } else {
      const redirectPath = Utils.resolvePath(settings.apiBaseURL, path)
      this.props.navigation.push('Home', { comicHome: redirectPath, hasUserInfo: true })
    }
  }

  onInviteFriendButtonTap() {
    this.setState({ showInviteBottomSheet: true })
  }

  closeBottomSheet() {
    this.setState({ showBottomSheet: false, showShareBottomSheet: false, showInviteBottomSheet: false })
  }

  navigateToUserProfile(item) {
    let userId = item.uid ? item.uid : item.userId ? item.userId : item.item ? item.item.user.userId : item.user.userId ? item.user.userId : null
    if (userId) {
      var subRoutesParams = { clickedUserID: userId }
      this.props.navigation.push(Constants.USER_PROFILE_SCREEN, { ...subRoutesParams })
    }
  }

  navigateToScreens(item) {
    const { story } = item
    const { storyID = null, action = null } = story
    let subRoute = Constants.POST_COMMENTS_SCREEN
    let storyData = { storyID: storyID, action: action }
    return this.props.navigation.push(subRoute, { storyData })
  }

  navigateToStackTop() {
    this.props.navigation.dispatch(StackActions.popToTop());
  }

  renderInvitesFriendsList(item, index, section) {
    if (!item) {
      return null;
    }

    const { key } = section
    const isFirstItem = index === 0
    const isLastItem = (key == Constants.FRIENDS && index == this.userFriendsList.length - 1) || (key == Constants.INVITES_SENT && index == this.friendSentRequests.length - 1) || (key == Constants.INVITES_RECEIVED && index == this.friendReceivedRequests.length - 1) || (key == Constants.RECENT_COMMENTS && index == this.recentCommentsList.length - 1) || (key == Constants.GIFTS_SENT && index == this.recentGiftsSentList.length - 1)
    const hasMoreData = (key == Constants.FRIENDS && this.hasMoreFriends) || (key == Constants.INVITES_SENT && this.hasMoreInviteSent) || (key == Constants.INVITES_RECEIVED && this.hasMoreInviteReceives) || (key == Constants.RECENT_COMMENTS && this.hasMoreRecentComments) || (key == Constants.GIFTS_SENT && this.hasMoreGiftsSent)
    const applyBottomRadius = isLastItem && !hasMoreData
    const isCommentsList = key == Constants.RECENT_COMMENTS || key == Constants.GIFTS_SENT

    let listType = key == Constants.FRIENDS ? Constants.FRIENDS : key == Constants.INVITES_SENT ? Constants.SENT : Constants.RECEIVED;
    if (this.UID == item.uid) {
      listType = null
    } else if (this.isAnotherUserProfile && listType == Constants.FRIENDS) {
      const { friendshipStatus } = item
      listType = friendshipStatus.status ? friendshipStatus.status : Constants.ADD_FRIEND
    }

    return (
      <View style={styles.sectionListView}>
        <View style={[styles.friendsPanelInnerView(isFirstItem, applyBottomRadius), { backgroundColor: this.context.colors.textInverse }]}>
          {!isFirstItem && !isCommentsList && this.renderSeparator()}
          {isCommentsList && <NotificationPanel item={item} navigateToUserProfile={this.navigateToUserProfile} navigateToScreens={this.navigateToScreens} navFromProfilePage={true} profileData={this.state.profileData} isAnotherUserProfile={this.isAnotherUserProfile} sectionType={key} isFirstItem={isFirstItem} applyBottomRadius={applyBottomRadius} />}
          {!isCommentsList &&
            <FriendsPanel
              item={item}
              updateFriendRequest={this.updateFriendRequest}
              navigateToUserProfile={this.navigateToUserProfile}
              unfriendUser={this.unfriendUser}
              selectedTab={listType}
              resendFriendRequest={this.props.resendFriendRequest}
              userDetails={this.props.userDetails}
              sendTextToContacts={this.sendTextToContacts}
              isAnotherUserProfile={this.isAnotherUserProfile}
              navigateToInvitesSent={this.navigateToInvitesSent}
              signInSheet={this.signInSheet}
            />
          }
        </View>
      </View>
    )
  }


  renderSeparator() {
    return (
      <View style={[styles.separatorStyle, { backgroundColor: this.context.colors.chatBubbles }]} />
    )
  }

  navigateToInvitesSent() {
    let params = { selectedTab: Constants.SENT }
    Utils.navigateToFriendRequests(null, params)
  }

  onPressFooter(listType) {
    if (listType === Constants.INVITES_RECEIVED) {
      Utils.navigateToFriendRequests(null);
    } else if (listType === Constants.INVITES_SENT) {
      this.navigateToInvitesSent()
    } else if (listType === Constants.FRIENDS) {
      const { profileData } = this.state
      let params = { componentToRender: Constants.FRIENDS, profileUserData: { profileUserID: this.profileUserID, profileUserName: profileData.displayName } }
      this.props.navigation.push(Constants.FRIENDS_COMPONENT, { ...params })
    } else if (listType === Constants.RECENT_COMMENTS) {
      this.setState({ isLoadingRecentComments: true })
      this.fetchUserComments().then(() => {
        let updatedSections = this.updateSectionListData()
        this.setState({ sectionListData: updatedSections, isLoadingRecentComments: false })
      })
    } else if (listType === Constants.GIFTS_SENT) {
      this.setState({ isLoadingGiftsSent: true })
      this.fetchGiftsSentComments().then(() => {
        let updatedSections = this.updateSectionListData()
        this.setState({ sectionListData: updatedSections, isLoadingGiftsSent: false })
      })
    }
  }

  navigateToSubscribePage() {
    this.openChapter(settings.getSubscribeURL())
  }

  renderFooter(section) {
    const { isLoadingRecentComments, isLoadingGiftsSent, isLoadingFriends, isLoadingInvitesReceived, isLoadingInvitesSent } = this.state
    const { key, data } = section
    const emptyText = key == Constants.FRIENDS ? this.isGuestUserProfile ? "Sign in to see or invite friends" : "No friends yet" :
      key == Constants.INVITES_RECEIVED ? this.isGuestUserProfile ? "Sign in to see invites received" : "You haven't received any invites" :
        key == Constants.INVITES_SENT ? this.isGuestUserProfile ? "Sign in to invite friends" : "You haven’t invited any friends" :
          key == Constants.GIFTS_SENT ? "You haven't sent any gifts" :
            key == Constants.RECENT_COMMENTS ? "Comment and participate in the discussion" : null
    const showInviteButton = key == Constants.INVITES_SENT || key == Constants.FRIENDS
    const showUpgradeButton = key == Constants.RECENT_COMMENTS && !SessionManager.instance.hasAnySubscriptionPurchase()
    const showCommentsLoader = (key == Constants.RECENT_COMMENTS && isLoadingRecentComments) || (key == Constants.GIFTS_SENT && isLoadingGiftsSent)
    const showFriendsLoader = (key == Constants.INVITES_SENT && isLoadingInvitesSent) || (key == Constants.INVITES_RECEIVED && isLoadingInvitesReceived) || (key == Constants.FRIENDS && isLoadingFriends)
    const showSignInButton = this.isGuestUserProfile && (key == Constants.INVITES_SENT || key == Constants.INVITES_RECEIVED || key == Constants.FRIENDS)
    const isCommentsLoadingMore = (key == Constants.RECENT_COMMENTS && isLoadingRecentComments && this.recentCommentsList.length > 0) || (key == Constants.GIFTS_SENT && isLoadingGiftsSent && this.recentGiftsSentList.length > 0)

    if (showCommentsLoader || showFriendsLoader) {
      return (
        <View style={[styles.sectionListView, { marginTop: isCommentsLoadingMore ? -8 : 20 }]}>
          <Image style={this.sectionLoaderView(showFriendsLoader)} source={showFriendsLoader ? require('./../../../assets/friends_loader_view.gif') : require('./../../../assets/notification_loading_view.gif')} />
        </View>
      )
    } else if (!Utils.checkData(data)) {
      return (
        <View style={styles.sectionListView}>
          <Text style={[this.context.p, { marginTop: 12, color: this.context.colors.text }]}>{emptyText}</Text>
          {(showInviteButton || showUpgradeButton || showSignInButton) &&
            <Button
              variant='solid'
              onPress={() => { showSignInButton ? this.navigateToLoginPage() : showUpgradeButton ? this.navigateToSubscribePage() : this.onInviteFriendButtonTap() }}
              style={[styles.emptyListButton, { borderColor: this.context.colors.logoRed, backgroundColor: this.context.colors.logoRed }]}>
              <Text style={[this.context.p, { color: this.context.colors.textInverse }]}>{showSignInButton ? Constants.SIGN_IN : showUpgradeButton ? Constants.UPGRADE : Constants.INVITE_FRIENDS}</Text>
            </Button>
          }
        </View>
      )
    }

    const hasMoreData = (key == Constants.FRIENDS && this.hasMoreFriends) || (key == Constants.INVITES_SENT && this.hasMoreInviteSent) || (key == Constants.INVITES_RECEIVED && this.hasMoreInviteReceives) || (key == Constants.RECENT_COMMENTS && this.hasMoreRecentComments) || (key == Constants.GIFTS_SENT && this.hasMoreGiftsSent)
    if (!hasMoreData) {
      return null
    }

    const isCommentsList = key == Constants.RECENT_COMMENTS || key == Constants.GIFTS_SENT


    return (
      <View>
        <TouchableOpacity
          style={[styles.sectionListView, styles.viewMoreTextView, { backgroundColor: this.context.colors.textInverse }]}
          onPress={() => { this.onPressFooter(key) }}
          activeOpacity={1}>
          {!isCommentsList && this.renderSeparator()}
          {<Text style={[this.context.p, styles.friendsFooterText, { color: this.context.colors.textBold }]}>View more</Text>}
        </TouchableOpacity>
      </View >
    );
  }

  renderListTitle(section) {
    const { title } = section
    if (!Utils.checkData(title)) {
      return;
    }

    return (
      <Text style={[this.context.h1, styles.panelTitleView, { color: this.context.colors.textBold }]}>{title}</Text>
    )
  }

  _listEmptyComponent() {
    if (!this.isAnotherUserProfile) {
      return;
    }

    return (
      <Text style={[this.context.p, styles.emptyFriendsProfileText]}>No other activity</Text>
    )
  }

  onShowAllComicSwitchTap(value) {
    SessionManager.instance.showAllComics = value
    this.props.updateShowAllComicsSettings(value)
    SharedPreferences.setShowAllComicsValue(value)

    this.props.navigation.dispatch(StackActions.popToTop());
  }

  signInSheet() {
    let sheetTitle = ''
    let sheetMessage = Constants.SIGN_IN_ACTION_SHEET_MESSAGE
    let sheetOptions = [Constants.SIGN_IN, Constants.CANCEL]
    this.changeActionSheetTitleAndButton(sheetTitle, sheetOptions, sheetMessage)
  }

  changeActionSheetTitleAndButton(sheetTitle, sheetOptions, sheetMessage = '') {
    this.setState({ sheetTitle: sheetTitle, sheetMessage: sheetMessage, sheetOptions: sheetOptions }, () => {
      this.actionSheet.show()
    })
  }

  navigateToLoginPage() {
    let params = { isForLoginProcess: true }
    Utils.navigateToDrawerLoginRoute(this.props, Constants.LOGIN_SCREEN, params)
  }

  onActionSheetPress(index) {
    if (index == 0) {
      if (this.state.sheetOptions[0] == Constants.SIGN_IN) {
        this.closeBottomSheet()
        this.navigateToLoginPage()
      }
    } else if (index == 1) {
    }
  }

  renderShareActionSheet() {
    const valueProps = { closeBottomSheet: this.closeBottomSheet, share: this.props.share, navigation: this.props.navigation, tinyviewShareForAnonymousUser: this.tinyviewShareForAnonymousUser, signInSheet: this.signInSheet, userDetails: this.props.userDetails, shareInviteLink: this.props.shareInviteLink }
    return (
      <ShareBottomSheet {...valueProps} configShareSheetFor={Constants.INVITE_FRIENDS} />
    )
  }

  renderHeader() {
    const { profileData, socialLinks, friendshipData, friendshipStatus } = this.state
    const valueProps = { profileData: profileData, socialLinks: socialLinks, connections: this.connections, openWebView: this.openWebView, profileButtonName: this.state.profileButtonName, userDetails: this.props.userDetails, friendshipData: friendshipData, onInviteFriendButtonTap: this.onInviteFriendButtonTap, isAnotherUserProfile: this.isAnotherUserProfile, friendshipStatus: friendshipStatus, sendTextToContacts: this.sendTextToContacts, currentUserID: this.clickedUserID, onPressHeaderButton: this.onPressHeaderButton, isGuestUserProfile: this.isGuestUserProfile, navigateToLoginPage: this.navigateToLoginPage, totalGiftsSent: this.totalGiftsSent, totalRecentComments: this.totalRecentComments, signInSheet: this.signInSheet }

    return (<UserProfileHeaderView {...valueProps} />)
  }

  renderMainBottomBar() {
    return (
      <BottomTabBar hideBottomBar={this.state.hideBottomBar} navigation={this.props.navigation} openWebView={this.openWebView} navigateToStackTop={this.navigateToStackTop} onBackPress={this.onBackPress} openChapter={this.openChapter} onShowAllComicSwitchTap={this.onShowAllComicSwitchTap} userDetails={this.props.userDetails} />
    )
  }

  render() {
    const { showRefresh, hideBottomBar, sheetTitle, sheetMessage, sheetOptions, cancelOptionIndex } = this.state

    return (
      <SafeAreaView style={{ flex: 1 }}>
        {(showRefresh)
          ?
          <Image style={loadingViewStyle(this.aspectRatio)} source={require('./../../../assets/top_loading_view.gif')} />
          :
          <SectionList
            onScroll={Animated.event([{ nativeEvent: { contentOffset: { y: this.scrollY } } }],
              {
                useNativeDriver: false,
                listener: (event) => {
                  const { contentOffset: { y: currentOffset }, layoutMeasurement, contentSize } = event.nativeEvent
                  const isNearBottom = currentOffset + layoutMeasurement.height >= contentSize.height - 20

                  // Should not trigger on overscroll at the top or bottom of the page.
                  if (currentOffset < 0 || isNearBottom) {
                    if (isNearBottom) {
                      // Update the last scroll position
                      this.lastScrollOffset = currentOffset
                    }
                    this.setState({ hideBottomBar: false })
                    return
                  }

                  // Initialize offset tracking on first scroll event
                  if (this.lastScrollOffset == null) {
                    this.lastScrollOffset = currentOffset
                  } else {
                    if (Math.abs(currentOffset - this.lastScrollOffset) < Constants.SCROLL_THRESHOLD) {
                      return;
                    }

                    const scrollDirection = currentOffset > this.lastScrollOffset ? Constants.DOWN : Constants.UP

                    // Hide tab bar immediately when scrolling down
                    if (scrollDirection === Constants.DOWN && !hideBottomBar) {
                      this.setState({ hideBottomBar: true })
                    }

                    // Show tab bar immediately when scrolling up
                    if (scrollDirection === Constants.UP && hideBottomBar) {
                      this.setState({ hideBottomBar: false })
                    }

                    // Update the last scroll position
                    this.lastScrollOffset = currentOffset
                  }
                }
              })}
            refreshControl={
              <RefreshControl
                refreshing={showRefresh}
                onRefresh={() => {
                  this.reloadChapter()
                }}
                progressViewOffset={settings.PROGRESS_VIEW_OFFSET}
                tintColor={Color.CIRCLE_PROGRESS_BAR_COLOR} />
            }
            ListHeaderComponent={this.renderHeader()}
            sections={this.state.sectionListData}
            keyExtractor={(item, index) => `${index}`}
            renderSectionHeader={({ section }) => this.renderListTitle(section)}
            renderItem={({ item, index, section }) => this.renderInvitesFriendsList(item, index, section)}
            renderSectionFooter={({ section }) => this.renderFooter(section)}
            ListEmptyComponent={this._listEmptyComponent}
            contentContainerStyle={{
              paddingBottom: navigationStyle.panelsMargin + navigationStyle.navHeight,
              backgroundColor: this.bgColor
            }}
            onEndReachedThreshold={0.7}
            stickySectionHeadersEnabled={false}
            style={{ backgroundColor: this.bgColor }} />
        }
        <ActionSheet
          ref={o => this.actionSheet = o}
          title={sheetTitle}
          message={sheetMessage}
          options={sheetOptions}
          cancelButtonIndex={cancelOptionIndex}
          tintColor={this.context.colors.logoRed}
          onPress={(index) => { this.onActionSheetPress(index) }}
          useNativeDriver={true}
          styles={{ titleText: this.context.h2, messageText: this.context.p, messageBox: { paddingTop: this.state.sheetTitle ? 0 : 20 } }}
        />
        {(this.state.showInviteBottomSheet) && this.renderShareActionSheet()}
        {(this.props.friendsActivityInProgress || this.props.isLogInProcess) && <LoadingIndicator />}
        {this.renderMainBottomBar()}
      </SafeAreaView >
    )
  }

  sectionLoaderView(showFriendsLoader = false) {
    let width = dimensions.width - 40
    let aspectRatio = (width / Constants.LOADING_GIF_DIMEN.width)

    return {
      width: width,
      height: showFriendsLoader ? 450 * aspectRatio : Constants.LOADING_GIF_DIMEN.height * aspectRatio,
      resizeMode: 'contain',
      overflow: 'hidden'
    }
  }
}

UserProfileScreen.contextType = ThemeContext

const styles = StyleSheet.create({
  separatorStyle: {
    height: 1,
    width: '100%',
  },
  friendsFooterText: {
    marginTop: 12,
    marginBottom: 12,
    textAlign: 'center'
  },
  panelTitleView: {
    marginTop: 40,
    marginLeft: 20
  },
  emptyListButton: {
    height: 32,
    borderRadius: 4,
    elevation: 0,
    borderWidth: 1,
    paddingTop: 4,
    paddingBottom: 4,
    paddingLeft: 8,
    paddingRight: 8,
    marginTop: 12,
    alignSelf: 'flex-start'
  },
  emptyFriendsProfileText: {
    paddingTop: 100,
    paddingBottom: 100,
    textAlign: 'center'
  },
  sectionListView: {
    marginLeft: 20,
    marginRight: 20
  },
  friendsPanelInnerView: (isFirstItem, applyBottomRadius) => {
    return {
      marginTop: isFirstItem ? 20 : 0,
      borderTopLeftRadius: isFirstItem ? 12 : 0,
      borderTopRightRadius: isFirstItem ? 12 : 0,
      borderBottomRightRadius: applyBottomRadius ? 12 : 0,
      borderBottomLeftRadius: applyBottomRadius ? 12 : 0
    }
  },
  viewMoreTextView: {
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
  }
});

const mapStateToProps = (state) => {
  return {
    userDetails: state.loginInfo.userDetails,
    isLogInProcess: state.loginInfo.isLogInProcess,
    friendsActivityInProgress: state.friendActivityIndicator.friendsActivityInProgress
  }
}

const mapDispatchToProps = (dispatch) => {
  return {
    getUserDetails(callback, userID) {
      dispatch(
        getUserDetails(callback, userID)
      )
    },
    openWebView(url) {
      dispatch(
        openWebViewAction(url)
      )
    },
    sendFriendRequest(sendFriendRequestParams, sendTextToContactsParams, callback) {
      dispatch(
        sendFriendRequest(sendFriendRequestParams, sendTextToContactsParams, callback)
      )
    },
    unfriend(data, callback) {
      dispatch(
        unfriend(data, callback)
      )
    },
    updateFriendRequest(data, callback) {
      dispatch(
        updateFriendRequest(data, callback)
      )
    },
    share(panelItems, actionType, callback) {
      dispatch(
        shareApp(panelItems, actionType, callback)
      )
    },
    getFriendList(callback, forceLoad, data) {
      dispatch(
        getFriendList(callback, forceLoad, data)
      )
    },
    getUserFriendshipStatus(value, callback) {
      dispatch(
        getUserFriendshipStatus(value, callback)
      )
    },
    getFriendRequest(requstType, callback) {
      dispatch(
        getFriendRequest(requstType, callback)
      )
    },
    resendFriendRequest(data) {
      dispatch(
        resendFriendRequest(data)
      )
    },
    shareInviteLink(requestedData, callback) {
      dispatch(
        shareInviteLink(requestedData, callback)
      )
    },
    getUserRecentComments(requestedData, callback) {
      dispatch(
        getUserRecentComments(requestedData, callback)
      )
    },
    getUserGiftsSent(requestedData, callback) {
      dispatch(
        getUserGiftsSent(requestedData, callback)
      )
    },
    updateShowAllComicsSettings(value) {
      dispatch(
        updateShowAllComicsSettings(value)
      )
    }
  }
}
export default connect(mapStateToProps, mapDispatchToProps)(UserProfileScreen)