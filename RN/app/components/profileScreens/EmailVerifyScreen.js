import React, { Component } from "react";
import { connect } from 'react-redux'
import SigningHeaderBar from "../friendsPackage/shared/SigningHeaderBar";
import { Constants } from "../../config/Constants";
import { View, Text } from "native-base";
import { StyleSheet, TouchableOpacity, BackHandler, SafeAreaView } from "react-native";
import { ThemeContext } from "../../Contexts";
import { CommonActions } from '@react-navigation/native';
import { Utils } from "../../config/Utils";
import SignUpPageInfo from "./SignUpPageInfo";
import FirebaseManager from "../../config/FirebaseManager";
import { LoadingIndicator } from "../LoadingIndicator";

class EmailVerifyScreen extends Component {

    static navigationOptions = {
        header: (props) => <SigningHeaderBar {...props} />
    }

    constructor(props) {
        super(props)

        const params = this.props.route.params
        let isVerificationFailed = params && params.isVerifFailed
        let emailAddress = params && params.userEmailAddress
        let fromSignUp = params && params.fromSignUp
        let forEmailAlert = params && params.forEmailAlert
        let forAuthOnly = params && params.forAuthOnly
        this.metadata = params && params.metadata

        this.state = {
            showLoadingIndicator: false,
            isVerifFailed: isVerificationFailed,
            userEmailAddress: emailAddress,
            fromSignUp: fromSignUp,
            forEmailAlert: forEmailAlert,
            forAuthOnly: forAuthOnly
        }

        this.resendVerifyEmailLink = this.resendVerifyEmailLink.bind(this)
        this.onBackPress = this.onBackPress.bind(this)
        this.onPressChangeEmail = this.onPressChangeEmail.bind(this)

    }

    componentDidMount() {
        this.props.navigation.setParams({
            leftButtonText: "Back",
            onBackPress: this.onBackPress,
            title: this.state.fromSignUp ? Constants.SIGN_UP : (!this.state.fromSignUp && this.state.isVerifFailed) ? 'Sign In Unsuccessful' : Constants.CHECK_YOUR_EMAIL
        })
    }

    UNSAFE_componentWillReceiveProps(nextProps) {
        if (nextProps.navigation && nextProps.route.params) {
            if (this.state.isVerifFailed != nextProps.route.params.isVerifFailed || nextProps.route.params.shouldRefreshProps) {
                this.setState({ isVerifFailed: nextProps.route.params.isVerifFailed }, () => {
                    this.props.navigation.setParams({
                        leftButtonText: "Back",
                        onBackPress: this.onBackPress,
                        title: this.state.fromSignUp ? Constants.SIGN_UP : (!this.state.fromSignUp && this.state.isVerifFailed) ? 'Sign In Unsuccessful' : Constants.CHECK_YOUR_EMAIL,
                        shouldRefreshProps: false
                    })
                })
            }

            if (nextProps.route.params.isEmailVerfied && nextProps.route.params.shouldRefreshProps) {
                this.props.navigation.setParams({
                    isEmailVerfied: false
                })
                if (this.props.navigation && this.props.navigation.getParent()) {
                    this.props.navigation.getParent().goBack();
                } else {
                    this.props.navigation.pop(2)
                }
            }

            if (this.state.userEmailAddress != nextProps.route.params.userEmailAddress) {
                this.setState({ userEmailAddress: nextProps.route.params.userEmailAddress })
            }

            if (this.state.fromSignUp != nextProps.route.params.fromSignUp) {
                this.setState({ fromSignUp: nextProps.route.params.fromSignUp })
            }

            if (this.state.forEmailAlert != nextProps.route.params.forEmailAlert) {
                this.setState({ forEmailAlert: nextProps.route.params.forEmailAlert })
            }

            if (this.state.forAuthOnly != nextProps.route.params.forAuthOnly) {
                this.setState({ forAuthOnly: nextProps.route.params.forAuthOnly })
            }
        }
    }

    UNSAFE_componentWillMount() {
        BackHandler.addEventListener('hardwareBackPress', this.onBackPress);
    }

    componentWillUnmount() {
        BackHandler.removeEventListener('hardwareBackPress', this.onBackPress);
    }

    onBackPress() {
        this.props.navigation.dispatch(CommonActions.goBack())
        return true
    }

    async resendVerifyEmailLink() {
        const { flowDataID } = this.props.route.params
        if (!flowDataID || !this.state.userEmailAddress) {
            return
        }

        this.setState({ showLoadingIndicator: true })
        let forLoginOrAlert = this.state.forAuthOnly ? 'subs_sign_in' : this.state.forEmailAlert ? 'alerts' : this.state.fromSignUp ? 'signup' : 'signin'
        await FirebaseManager.instance.sendEmailAuthLink(flowDataID, this.state.userEmailAddress, forLoginOrAlert, this.metadata)
        this.setState({ showLoadingIndicator: false }, () => {
            Utils.showToast("Verification link has been sent.")
        })
    }

    onPressChangeEmail() {
        let params = { fromSignUp: this.state.fromSignUp, shouldRefreshProps: true, isForEmailVerification: true, isForLoginProcess: !this.state.fromSignUp && !this.state.forEmailAlert, forEmailAlert: this.state.forEmailAlert, forAuthOnly: this.state.forAuthOnly, metadata: this.metadata }
        Utils.navigateToDrawerLoginRoute(this.props, Constants.LOGIN_SCREEN, params)
    }

    render() {
        const { showLoadingIndicator, isVerifFailed, userEmailAddress, fromSignUp } = this.state
        const { flowDataID } = this.props.route.params

        return (
            <SafeAreaView style={styles.mainContainer}>
                <View style={styles.viewStyle(fromSignUp)}>
                    {fromSignUp &&
                        <View>
                            <SignUpPageInfo signUpStep={2} />
                            <Text style={[this.context.h2, { marginBottom: 20 }]}>{isVerifFailed ? 'Email Verification Unsuccessful' : Constants.CHECK_YOUR_EMAIL}</Text>
                        </View>
                    }
                    {!isVerifFailed && <Text style={this.context.p}>Link sent to
                        <Text style={[this.context.pBold, { color: this.context.colors.text }]}> {userEmailAddress}</Text>. Please click on the link to sign in.
                    </Text>
                    }
                    {isVerifFailed &&
                        <Text style={this.context.p}>Your email address could not be verified.</Text>
                    }
                    <View style={styles.resendEmailTextView}>
                        <Text style={this.context.bodyMini}>{isVerifFailed ? 'Try Again? ' : 'Didn’t receive the email? '}</Text>
                        <View style={{ marginTop: 8, flexDirection: 'row' }}>
                            {(flowDataID && this.state.userEmailAddress) &&
                                <>
                                    <TouchableOpacity
                                        onPress={() => { this.resendVerifyEmailLink() }}>
                                        <Text style={[this.context.bodyMini, styles.underLineTextView, { color: this.context.colors.logoRed }]}>{isVerifFailed ? 'Resend link' : 'Resend'}</Text>
                                    </TouchableOpacity>
                                    <Text style={[this.context.bodyMini, { color: this.context.colors.separators }]}> | </Text>
                                </>
                            }
                            <TouchableOpacity
                                onPress={() => { this.onPressChangeEmail() }}>
                                <Text style={[this.context.bodyMini, styles.underLineTextView, { color: this.context.colors.logoRed }]}>Change email address</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
                {(this.props.isLogInProcess || showLoadingIndicator) && <LoadingIndicator />}
            </SafeAreaView>
        )
    }
}

EmailVerifyScreen.contextType = ThemeContext

const styles = StyleSheet.create({
    mainContainer: {
        flex: 1
    },
    viewStyle: (fromSignUp) => {
        return {
            marginTop: fromSignUp ? 0 : 30,
            marginLeft: 20,
            marginRight: 20
        }
    },
    resendEmailTextView: {
        marginTop: 20
    },
    underLineTextView: {
        textDecorationLine: 'underline'
    }
});

const mapStateToProps = (state) => {
    return {
        isLogInProcess: state.loginInfo.isLogInProcess
    }
}

const mapDispatchToProps = (dispatch) => {
    return {

    }
}

export default connect(mapStateToProps, mapDispatchToProps)(EmailVerifyScreen)