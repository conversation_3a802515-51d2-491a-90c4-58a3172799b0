import React, { Component } from 'react'
import { StyleSheet, BackHandler, Image, Dimensions } from 'react-native'
import { View } from 'native-base'
import { Color } from '../../config/Color'
import { SystemFont } from '../../config/Typography'
import { scale } from 'react-native-size-matters'
import FirebaseManager from '../../config/FirebaseManager'
import FriendsHeaderBar from '../friendsPackage/shared/FriendsHeaderBar'
import { Constants } from '../../config/Constants'
import { Utils } from '../../config/Utils'
import { connect } from 'react-redux'
import { SafeAreaView } from 'react-native-safe-area-context'

class WhySignUpScreen extends Component {
  
  static navigationOptions = ({ navigation }) => {
    return {
      header:(props) => <FriendsHeaderBar {...props}/>      
    }
  }

  constructor(props) {
    super(props)
    this.UID = FirebaseManager.instance.currentUser().uid
    this.anonymousUserIdToken = null
    this.shouldResendOTP = false;
 
    this.onBackPress = this.onBackPress.bind(this)
    this.close = this.close.bind(this)
    this.navigateToAppropriateScreen = this.navigateToAppropriateScreen.bind(this)
    this.imageStyle = this.imageStyle.bind(this)
  }

  componentDidMount() {
    this.props.navigation.setParams({
      leftButtonText: "Back",
      onBackPress: this.onBackPress,
      title: Constants.WHY_SIGNIN,
      rightButtonText: "Next",
      onRightClick: this.navigateToAppropriateScreen,
    })
  }

  onBackPress() {
    this.close()
    return true
  }

  UNSAFE_componentWillMount() {
    BackHandler.addEventListener('hardwareBackPress', this.onBackPress);
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.onBackPress);
  }

  close() {
    if (this.props.navigation && this.props.navigation.getParent()) {
      this.props.navigation.getParent().goBack();
    }
  }

  async navigateToAppropriateScreen() {
    let routeName = Constants.LOGIN_SCREEN
    let params = { isForLoginProcess: true, isupdatingProfile: false }
    Utils.navigateToDrawerLoginRoute(this.props, routeName, params)
  }

  imageStyle(w = 800, h = 600, margin = 15) {
    let dimensions = Dimensions.get('window')
    let aspectRatio = (dimensions.width - ((margin) * 2)) / w

    return {
      marginTop: 20,
      width: w * aspectRatio,
      height: h * aspectRatio,
      resizeMode: 'contain'
    }
  }

  render() {
    return (
      <SafeAreaView style={{ flex: 1, }}>
        <View style={styles.viewStyle}>
          <Image
            style={this.imageStyle()}
            source={require("../../../assets/tinyview_why_signup_1.jpg")}
          />
          <Image
            style={this.imageStyle()}
            source={{ uri: Utils.getPanelURL(Constants.WHY_INVITE_FRIEND_IMAGE) }}
          />
        </View>
      </SafeAreaView>
    )
  }
}

const styles = StyleSheet.create({
  viewStyle: {
    marginStart: scale(12),
    marginEnd: scale(10),
  },
  otpText: {
    color: Color.DARK_GREY,
    fontFamily: SystemFont.SELECTED_FONT,
    fontWeight: 'bold',
    fontSize: scale(20),
    marginBottom: scale(10),
    marginTop: scale(20),
    textAlign: 'left',
  },
  confirmText: {
    color: Color.DARK_GREY,
    fontFamily: SystemFont.SELECTED_FONT,
    fontSize: scale(18),
    marginTop: scale(25),
    textAlign: 'left'
  },
});

const mapStateToProps = (state) => {
  return {
    isAnonymous: state.loginInfo.isAnonymous,
    userDetails: state.loginInfo.userDetails,
    pathUrl: state.readComic.pathUrl,    
  }
}

const mapDispatchToProps = (dispatch) => {
  return {
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(WhySignUpScreen)
