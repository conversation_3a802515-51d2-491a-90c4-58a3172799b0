import React, { Component } from 'react'
import { StyleSheet, BackHandler, Alert, TouchableOpacity, KeyboardAvoidingView, Platform } from 'react-native'
import { View, Text, Button, Toast } from 'native-base'
import { LoadingIndicator } from '../LoadingIndicator'
import { connect } from 'react-redux'
import { updateUserProfile } from '../../redux/actions/actions'
import { Utils } from '../../config/Utils'
import FirebaseManager from '../../config/FirebaseManager'
import { CommonActions } from "@react-navigation/native"
import TextInputComponent from '../../config/TextInputComponent'
import { Constants } from '../../config/Constants'
import moment from 'moment'
import SessionManager from '../../config/SessionManager'
import { ThemeContext } from '../../Contexts'
import SigningHeaderBar from '../friendsPackage/shared/SigningHeaderBar'
import SignUpPageInfo from './SignUpPageInfo'
import ErrorTextMsg from '../ErrorTextMsg'
import NavigationService from '../../config/NavigationService'
import { ScrollView } from 'react-native-gesture-handler'
import { SafeAreaView } from 'react-native-safe-area-context'

class DOBScreen extends Component {

  static navigationOptions = ({ navigation }) => {
    return {
      header: (props) => <SigningHeaderBar {...props} />
    }
  }

  constructor(props) {
    super(props)
    this.state = {
      isNextDisabled: true,
      dateOfBirthValue: null,
      showLoadingIndicator: false,
      birthDate: '',
      birthMonth: '',
      birthYear: '',
      birthDayFocus: 'false',
      birthMonthFocus: 'false',
      birthYearFocus: 'false',
      isupdatingProfile: false,
      showMonthField: false,
      showDateField: false,
      showError: false,
      showYearError: false,
      showMonthError: false,
      showDateError: false
    }

    const params = this.props.route.params
    this.fromSignUp = params && params.fromSignUp
    this.completeMobileNo = params && params.phoneNumber
    this.userEmailAddress = params && params.userEmailAddress
    this.forEmailVerification = params && params.isForEmailVerification

    this.UID = FirebaseManager.instance.currentUser().uid
    this.userAge = null

    this.navigateToProfile = this.navigateToProfile.bind(this)
    this.updateProfileDetails = this.updateProfileDetails.bind(this)
    this.navigateToHome = this.navigateToHome.bind(this)
    this.onBackPress = this.onBackPress.bind(this)
    this.onChange = this.onChange.bind(this)
    this.validYear = this.validYear.bind(this)
    this.validMonth = this.validMonth.bind(this)
    this.validDate = this.validDate.bind(this)
    this.onInputBlur = this.onInputBlur.bind(this)
    this.isLegalAge = this.isLegalAge.bind(this)
    this.setLoaderVisibility = this.setLoaderVisibility.bind(this)
    this.saveUserProfile = this.saveUserProfile.bind(this)
    this.isEdited = this.isEdited.bind(this)
    this.saveInSharedPref = this.saveInSharedPref.bind(this)
    this.renderVerifyButton = this.renderVerifyButton.bind(this)
    this.navigateToLoginPage = this.navigateToLoginPage.bind(this)
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps) {
      if (nextProps.navigation && nextProps.route.params) {
        if (this.state.isupdatingProfile != nextProps.route.params.isupdatingProfile) {
          this.props.navigation.setParams({
            title: nextProps.route.params.isupdatingProfile ? Constants.EDIT_PROFILE : Constants.SIGN_UP
          })
          this.setState({ isupdatingProfile: nextProps.route.params.isupdatingProfile })
        }
      }
    }
  }

  async componentDidMount() {
    this.props.navigation.setParams({
      leftButtonText: "Back",
      onBackPress: this.onBackPress,
      rightButtonText: "Next",
      isRightDisabled: this.state.isNextDisabled,
      isLoading: this.state.showLoadingIndicator,
      onRightClick: this.saveUserProfile,
      title: this.state.isupdatingProfile ? Constants.EDIT_PROFILE : Constants.SIGN_UP
    })

    if (this.props.userDetails) {
      this.updateProfileDetails(this.props.userDetails)
    }
  }

  UNSAFE_componentWillMount() {
    BackHandler.addEventListener('hardwareBackPress', this.onBackPress);
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.onBackPress);
  }

  onBackPress() {
    if (SessionManager.instance.getIsInviteLinkLogin()) {
      SessionManager.instance.setIsInviteLinkLogin(false)
    }

    if (this.fromSignUp) {
      this.props.navigation.dispatch(CommonActions.goBack())
    } else if (!this.state.isupdatingProfile && this.props.navigation && this.props.navigation.getParent()) {
      this.props.navigation.getParent().goBack();
    } else {
      this.props.navigation.dispatch(CommonActions.goBack())
    }
    return true;
  }

  async updateProfileDetails(userData) {
    let data = userData
    if (data && data.dob) {
      let dobDate = {
        birthDate: data.dob.day ? data.dob.day : '',
        birthMonth: data.dob.month ? data.dob.month : '',
        birthYear: data.dob.year ? data.dob.year : '',
      }

      var showMonthField = Utils.checkData(data.dob.month)
      var showDateField = Utils.checkData(data.dob.day)

      this.setState({ ...dobDate, showMonthField, showDateField })

      if (dobDate.birthYear) {
        this.props.navigation.setParams({
          isRightDisabled: false
        })
        this.setState({ isNextDisabled: false })
      }
    }
  }

  onChange(key, value) {
    this.setState({ [key]: value.trim() }, () => {

      if (key == 'birthMonth' && this.state[key].length == 2) {
        this.birthDateRef && this.birthDateRef.focus()
      } else if (key == 'birthYear' && this.state[key].length == 4) {
        this.birthMonthRef && this.birthMonthRef.focus()
      }

      var isDateValid = true
      if (key == 'birthYear') {
        if (this.state[key].length < 4) {
          isDateValid = false
          if (this.state.showMonthField || this.state.showDateField) {
            this.setState({ showMonthField: false, showDateField: false, birthMonth: '', birthDate: '' })
          }
          if (Utils.notANumericExpr(this.state[key])) {
            this.setState({ showYearError: true })
          } else if (this.state.showYearError) {
            this.setState({ showYearError: false })
          }
        } else {
          isDateValid = this.validYear().isValid && !Utils.notANumericExpr(this.state[key])
          this.setState({ showYearError: !isDateValid })
        }
      } else if (key == 'birthMonth') {
        if (this.state[key].length <= 0) {
          isDateValid = false
          if (this.state.showDateField) {
            this.setState({ showDateField: false, birthDate: '' })
          }
          if (Utils.notANumericExpr(this.state[key])) {
            this.setState({ showMonthError: true })
          } else if (this.state.showMonthError) {
            this.setState({ showMonthError: false })
          }
        } else {
          isDateValid = this.validMonth().isValid && !Utils.notANumericExpr(this.state[key])
          this.setState({ showMonthError: !isDateValid })
        }
      } else if (key == 'birthDate') {
        if (this.state[key].length <= 0) {
          isDateValid = false
          if (Utils.notANumericExpr(this.state[key])) {
            this.setState({ showDateError: true })
          } else if (this.state.showDateError) {
            this.setState({ showDateError: false })
          }
        } else {
          isDateValid = this.validDate().isValid && !Utils.notANumericExpr(this.state[key])
          this.setState({ showDateError: !isDateValid })
        }
      }
      if (!isDateValid) {
        this.props.navigation.setParams({
          isRightDisabled: !isDateValid
        })
        this.setState({ isNextDisabled: !isDateValid, showError: true });
      } else {
        this.onInputBlur(key)
      }
    })
  }

  validYear() {
    let intYear = this.state.birthYear
    let intMonth = this.state.birthMonth ? this.state.birthMonth : "12"
    var intDate = this.state.birthDate ? this.state.birthDate : moment(intMonth + "-" + intYear, "MM-YYYY").daysInMonth();
    let dobDateString = intMonth + "-" + intDate + "-" + intYear
    let dobObject = moment(dobDateString, "MM-DD-YYYY")

    return { isValid: dobObject.isValid(), dateObj: dobObject }
  }

  validMonth() {
    let intYear = this.state.birthYear
    let intMonth = this.state.birthMonth
    var intDate = this.state.birthDate ? this.state.birthDate : "01"
    var dobDateString1 = intMonth + "-" + intDate + "-" + intYear
    let tempDate1 = moment(dobDateString1, "MM-DD-YYYY")

    const correctLastDate = tempDate1.endOf('month').format('DD');

    intDate = correctLastDate

    let dobDateString2 = intMonth + "-" + intDate + "-" + intYear
    let tempDate2 = moment(dobDateString2, "MM-DD-YYYY")

    return { isValid: tempDate2.isValid(), dateObj: tempDate2 }
  }

  validDate() {
    let intYear = this.state.birthYear
    let intMonth = this.state.birthMonth
    let intDate = this.state.birthDate
    let dobDateString = intMonth + "-" + intDate + "-" + intYear

    let tempDate = moment(dobDateString, "MM-DD-YYYY")

    return { isValid: tempDate.isValid(), dateObj: tempDate }
  }

  onInputBlur(key) {
    var isLegalAge = true
    if (key == 'birthYear') {
      let intYear = this.state.birthYear
      let intMonth = "12"
      let intDate = "31"
      let dobDateString = intMonth + "-" + intDate + "-" + intYear

      let tempDate = moment(dobDateString, "MM-DD-YYYY")

      isLegalAge = this.isLegalAge(tempDate)
    } else if (key == 'birthMonth') {
      const tempDate = this.validMonth().dateObj
      isLegalAge = this.isLegalAge(tempDate)
    } else if (key == 'birthDate') {
      const tempDate = this.validDate().dateObj
      isLegalAge = this.isLegalAge(tempDate)
    }

    this.props.navigation.setParams({
      isRightDisabled: !isLegalAge
    })

    let showToastError = true
    let errorMessage = `${Constants.MUST_BE_ABOVE_13_ERROR}`
    let yearDiff = moment().diff(this.state.birthYear, "year")
    if (yearDiff > 125 || yearDiff < 0) {
      showToastError = false
      this.setState({ showError: true, showYearError: true })
    }
    if (!isLegalAge && showToastError) {
      if (!Toast.isActive("dobError")) {
        Utils.showToast(errorMessage, "top", "dobError", 5000)
      }
    }
    this.setState({ isNextDisabled: !isLegalAge });
  }

  isLegalAge(dobObject) {
    let today = moment();
    let year = today.diff(dobObject, "year")
    let monthDiff = today.diff(dobObject, "month")
    let fromLegalAgeMonthsDiff = monthDiff - 156 //Minus number of months in 13 years = 156
    var isValid = true

    let isSameMonth = false
    if (year == 12) { //For handling some special case for end date in moment library.
      let userBirthMonth = moment(dobObject).format("MM")
      let todayMonth = today.format("MM")
      isSameMonth = userBirthMonth.valueOf() == todayMonth.valueOf()
    }

    if (year >= 13 && year < 125) {
      let yearDiff = today.diff(this.state.birthYear, "year")
      if (yearDiff > 13) {
        if (this.state.showMonthField || this.state.showDateField) {
          this.setState({ showMonthField: false, showDateField: false })
        }
        if (this.state.birthMonth || this.state.birthDate) {
          this.setState({ birthMonth: '', birthDate: '' })
        }
      } else if (yearDiff == 13) {
        if (fromLegalAgeMonthsDiff < 12 && fromLegalAgeMonthsDiff > 0) {
          if (this.state.showDateField) {
            this.setState({ showDateField: false, birthDate: '' })
          }
        }
      }
      if (this.state.showYearError || this.state.showMonthError || this.state.showDateError) {
        this.setState({ showYearError: false, showMonthError: false, showDateError: false })
      }
      isValid = true
    } else if (year == 12) {
      if (!this.state.showMonthField) {
        this.setState({ showMonthField: true }, () => {
          this.onChange("birthMonth", this.state.birthMonth)
        })
      } else if (!this.state.showDateField && fromLegalAgeMonthsDiff == -1 && isSameMonth) {
        this.setState({ showDateField: true }, () => {
          this.onChange("birthDate", this.state.birthDate)
        })
      } else if (this.state.showDateField && fromLegalAgeMonthsDiff == -1 && !isSameMonth) {
        this.setState({ showDateField: false, birthDate: '' }, () => {
          this.onChange("birthMonth", this.state.birthMonth)
        })
      } else if (this.state.showDateField && fromLegalAgeMonthsDiff < -1) {
        this.setState({ showDateField: false, birthDate: '' }, () => {
          this.onChange("birthMonth", this.state.birthMonth)
        })
      } else {
        isValid = false
      }

    } else {
      if (this.state.showMonthField || this.state.showDateField) {
        this.setState({ showMonthField: false, showDateField: false, birthMonth: '', birthDate: '' }, () => {
        })
      }
      isValid = false
    }
    return isValid
  }

  navigateToHome(isSuccess = false) {
    if (isSuccess) {
      this.onBackPress()
    } else {
      Alert.alert(
        "",
        "Unable to process your request, Please try again later.",
        [
          {
            text: "OK",
          },
        ],
        { cancelable: true }
      )
    }
    this.setLoaderVisibility(false)
  }

  setLoaderVisibility(value) {
    this.setState({ showLoadingIndicator: value }, () => {
      this.props.navigation.setParams({
        isLoading: this.state.showLoadingIndicator,
      })
    })
  }

  saveUserProfile() {
    this.setLoaderVisibility(true)
    let data = { "data": { "dob": { "year": this.state.birthYear, "month": this.state.birthMonth, "day": this.state.birthDate } } }
    if (!this.isEdited(data.data)) {
      this.navigateToProfile(data)
      return
    }
    this.props.updateUserProfile(data.data, this.saveInSharedPref, true)
  }

  isEdited(data) {
    if (!Utils.checkData(this.props.userDetails.dob)) {
      return true
    }

    let oldDob = this.props.userDetails.dob
    let dobDate = {
      year: oldDob.year ? oldDob.year : '',
      month: oldDob.month ? oldDob.month : '',
      day: oldDob.day ? oldDob.day : '',
    }
    let oldDobData = JSON.stringify(dobDate)
    let editedDobData = JSON.stringify(data.dob)

    return oldDobData != editedDobData
  }

  async saveInSharedPref(data) {
    if (!data) {
      return this.setLoaderVisibility(false)
    }
    let dob = data && data.dob && data.dob
    let userDetails = SessionManager.instance.getAnonymousUserDetails()
    let updatedData = { "dob": dob, ...userDetails }
    SessionManager.instance.saveAnonymousUserDetails(updatedData)
    this.navigateToProfile(data)
  }

  renderVerifyButton() {
    const { isNextDisabled } = this.state
    return (
      <View style={{ marginTop: 32 }}>
        <Button
          variant='solid'
          disabled={isNextDisabled}
          style={[styles.verifyButton, { backgroundColor: isNextDisabled ? this.context.colors.separators : this.context.colors.logoRed }]}
          onPress={() => this.saveUserProfile()}>
          <Text style={[this.context.pBold, { color: this.context.colors.textInverse }]}>{Constants.NEXT}</Text>
        </Button>
      </View>
    )
  }

  navigateToLoginPage(forSignUp = false) {
    let params = {}
    if (forSignUp) {
      params = { fromSignUp: true, phoneNumber: this.completeMobileNo, userEmailAddress: this.userEmailAddress, isForEmailVerification: this.forEmailVerification }
      Utils.navigateToDrawerLoginRoute(this.props, Constants.LOGIN_SCREEN, params)
    } else {
      params = { isForLoginProcess: true, fromSignUp: false }
      NavigationService.resetStackAndNavigate(Constants.LOGIN_SCREEN, params)
    }
  }

  navigateToProfile() {
    if (this.fromSignUp) {
      this.navigateToLoginPage(true)
    } else {
      let params = { isupdatingProfile: this.state.isupdatingProfile }
      Utils.navigateToDrawerLoginRoute(this.props, Constants.EDIT_PROFILE_SCREEN, params)
    }
    this.setLoaderVisibility(false)
  }

  render() {
    const { birthDayFocus, birthMonthFocus, birthYearFocus, birthYear, birthMonth, birthDate, showYearError, showMonthError, showDateError, showError } = this.state
    return (
      <KeyboardAvoidingView
        behavior={Platform.OS == 'ios' ? "padding" : undefined}
        style={{ flex: 1 }}
        keyboardVerticalOffset={80}>
        <SafeAreaView style={styles.mainContainer}>
          <ScrollView style={{ flex: 1 }}>
            <View style={{ flex: 1, marginLeft: 20, marginRight: 20 }}>
              {this.fromSignUp && <SignUpPageInfo signUpStep={1} />}
              <Text style={[this.context.h2, { marginTop: !this.fromSignUp ? 20 : 0 }]}>Date of Birth</Text>
              <Text style={[this.context.p, { marginTop: 8 }]}>{Constants.WHY_DO_WE_ASK_MSG}</Text>
              <View style={{ marginTop: 20 }}>
                <View style={{ flexDirection: 'row' }}>
                  <TextInputComponent
                    inputRef={(r) => { this.birthYearRef = r }}
                    refState="birthYear"
                    autoFocus={true}
                    maxLength={4}
                    selectionColor={this.context.colors.text}
                    onChange={(refState, text) => {
                      this.onChange(refState, text);
                    }}
                    onBlur={(refState) => {
                      this.setState({ "birthYearFocus": false })
                    }}
                    onFocus={() => this.setState({ "birthYearFocus": true })}
                    value={birthYear}
                    keyboardType="numeric"
                    textColor={this.context.colors.textBold}
                    fontSize={16}
                    placeHolder={"YYYY"}
                    placeholderTextColor={this.context.colors.text}
                    textInputStyle={[this.context.p, styles.messageInput, { borderColor: showYearError ? this.context.colors.logoRed : birthYearFocus ? this.context.colors.text : birthYear ? this.context.colors.separators : this.context.colors.chatBubbles }]}
                    inputTextLabel={<Text style={[this.context.bodyMini, { color: this.context.colors.textBold }]}>Year<Text>*</Text></Text>}
                    inputTextLabelStyle={styles.inputTextLabelStyle}
                    textAlignVertical={"center"}
                  />
                </View>
                {showError && showYearError &&
                  <View style={styles.descView}>
                    <ErrorTextMsg errorText={"Please enter a valid 4-digit year."} />
                  </View>
                }
                {this.state.showMonthField &&
                  <View style={{ marginTop: 20, flexDirection: 'row' }}>
                    <TextInputComponent
                      inputRef={(r) => { this.birthMonthRef = r }}
                      refState="birthMonth"
                      keyboardType="numeric"
                      autoFocus={true}
                      maxLength={2}
                      selectionColor={this.context.colors.text}
                      onChange={(refState, text) => {
                        this.onChange(refState, text);
                      }}
                      onBlur={(refState) => {
                        this.setState({ "birthMonthFocus": false })
                      }}
                      onFocus={() => this.setState({ "birthMonthFocus": true })}
                      value={birthMonth}
                      textColor={this.context.colors.textBold}
                      fontSize={16}
                      placeHolder={"MM"}
                      placeholderTextColor={this.context.colors.text}
                      textInputStyle={[this.context.p, styles.messageInput, { borderColor: showMonthError ? this.context.colors.logoRed : birthMonthFocus ? this.context.colors.text : birthMonth ? this.context.colors.separators : this.context.colors.chatBubbles }]}
                      inputTextLabel={<Text style={[this.context.bodyMini, { color: this.context.colors.textBold }]}>Month<Text>*</Text></Text>}
                      inputTextLabelStyle={styles.inputTextLabelStyle}
                      textAlignVertical={"center"}
                    />
                  </View>
                }
                {showError && showMonthError &&
                  <View style={styles.descView}>
                    <ErrorTextMsg errorText={"Please enter a valid numeric month."} />
                  </View>
                }
                {this.state.showDateField &&
                  <View style={{ marginTop: 20, flexDirection: 'row' }}>
                    <TextInputComponent
                      inputRef={(r) => { this.birthDateRef = r }}
                      refState="birthDate"
                      autoFocus={true}
                      maxLength={2}
                      selectionColor={this.context.colors.text}
                      onChange={(refState, text) => {
                        this.onChange(refState, text);
                      }}
                      onBlur={(refState) =>
                        this.setState({ "birthDayFocus": false })
                      }
                      onFocus={() => this.setState({ "birthDayFocus": true })}
                      value={birthDate}
                      keyboardType="numeric"
                      textColor={this.context.colors.textBold}
                      fontSize={16}
                      placeHolder={"DD"}
                      placeholderTextColor={this.context.colors.text}
                      textInputStyle={[this.context.p, styles.messageInput, { borderColor: showDateError ? this.context.colors.logoRed : birthDayFocus ? this.context.colors.text : birthDate ? this.context.colors.separators : this.context.colors.chatBubbles }]}
                      inputTextLabel={<Text style={[this.context.bodyMini, { color: this.context.colors.textBold }]}>Day<Text>*</Text></Text>}
                      inputTextLabelStyle={styles.inputTextLabelStyle}
                      textAlignVertical={"center"}
                    />
                  </View>
                }
                {showError && showDateError &&
                  <View style={styles.descView}>
                    <ErrorTextMsg errorText={"Please enter a valid numeric day."} />
                  </View>
                }
              </View>
              {this.renderVerifyButton()}
              {this.fromSignUp &&
                <View style={styles.signInTextView}>
                  <Text style={this.context.bodyMini}>Already a user? </Text>
                  <TouchableOpacity
                    onPress={() => this.navigateToLoginPage()}>
                    <Text style={[this.context.bodyMini, { textDecorationLine: 'underline', color: this.context.colors.logoRed }]}>{Constants.SIGN_IN}</Text>
                  </TouchableOpacity>
                </View>}
            </View>
          </ScrollView>
          {this.state.showLoadingIndicator && <LoadingIndicator />}
        </SafeAreaView >
      </KeyboardAvoidingView>
    )
  }
}

DOBScreen.contextType = ThemeContext;

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1
  },
  inputTextLabelStyle: {
    marginBottom: 4,
    marginLeft: 0,
    marginTop: 0
  },
  errorTextStyle: {
    marginStart: 6,
  },
  messageInput: {
    height: 52,
    borderWidth: 1,
    borderRadius: 8,
    paddingStart: 16,
    paddingEnd: 16
  },
  verifyButton: {
    borderRadius: 6,
    paddingTop: 12,
    paddingBottom: 12
  },
  signInTextView: {
    flexDirection: 'row',
    marginTop: 12,
    alignSelf: 'center'
  },
  descView: {
    marginTop: 4,
    flexDirection: 'row'
  }
});

const mapStateToProps = (state) => {
  return {
    pathUrl: state.readComic.pathUrl,
    userDetails: state.loginInfo.userDetails,
  }
}

const mapDispatchToProps = (dispatch) => {
  return {
    updateUserProfile(data, callback, callGetUserDetails) {
      dispatch(
        updateUserProfile(data, callback, callGetUserDetails)
      )
    }
  }
}
export default connect(mapStateToProps, mapDispatchToProps)(DOBScreen)