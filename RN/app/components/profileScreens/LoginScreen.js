import React, { Component } from 'react'
import { StyleSheet, BackHandler, Keyboard, Platform, TouchableOpacity, SafeAreaView } from 'react-native'
import { View, Text, Button } from 'native-base'
import TextInputComponent from '../../config/TextInputComponent'
import { LoadingIndicator } from '../LoadingIndicator'
import { connect } from 'react-redux'
import { resendOTP, signInWithMobile, clearLoginState, getUserProfileByIdentity, storeUserEmailData, signinWithSocialLink } from '../../redux/actions/actions'
import { settings } from '../../config/settings'
import FirebaseManager from '../../config/FirebaseManager'
import { CommonActions } from '@react-navigation/native'
import CountryPicker from "react-native-country-picker-module";
import { SharedPreferences } from '../../config/SharedPreferences'
import SessionManager from '../../config/SessionManager'
import { Utils } from '../../config/Utils'
import { Constants } from '../../config/Constants'
import { ThemeContext } from '../../Contexts'
import FastImage from 'react-native-fast-image'
import SigningHeaderBar from '../friendsPackage/shared/SigningHeaderBar'
import SignUpPageInfo from './SignUpPageInfo'
import ErrorTextMsg from '../ErrorTextMsg'
import '@react-native-firebase/auth'
import UserSession from '../../config/UserSession'
import { bannerMsgContainer, bannerTextView, bannerCrossIconView, bannerCrossIcon, navigationStyle } from '../../config/styles'
import { Color } from '../../config/Color'
import { ThreeDotLoader } from '../ThreeDotLoader'

class LoginScreen extends Component {

	static navigationOptions = {
		headerShown: "false",
		animationEnabled: false,
		header: (props) => <SigningHeaderBar {...props} />
	}

	constructor(props) {
		super(props)

		this.textInput = null
		this.EmailTextInput = null
		this.verificationId = null
		this.completeMobileNo = '';
		this.invitedLinkSenderInfo = null

		let countryCode = 'US';
		let countryCallingCode = '+1';
		let phoneNumber = '';
		let loginDisabled = true
		let emailAddress = ''

		const params = this.props.route.params
		this.postSignInFriendsPath = params && params.postSignInFriendsPath
		this.fromSignUp = params && params.fromSignUp
		this.forEmailVerification = params && params.isForEmailVerification //For the Email Sign In/Up.
		this.forEmailAlert = params && params.forEmailAlert //Coming from Manage Alerts page to complete the Email Sign In/Up.
		this.forAuthOnly = params && params.forAuthOnly // Just to authenticate user. If true sign up extra screen will not be shown.
		this.metadata = params && params.metadata
		this.seriesToFollowData = params && params.seriesToFollowData
		if (params && params.phoneNumber) {
			this.completeMobileNo = params.phoneNumber
			const countryData = Utils.getUserCountryCode(this.completeMobileNo)
			if (countryData) {
				countryCode = countryData.countryCode;
				countryCallingCode = countryData.countryCallingCode;

				phoneNumber = this.completeMobileNo.replace(countryCallingCode, "");
				if (!this.forEmailVerification) {
					loginDisabled = false
				}
			}
		}

		if (params && params.userEmailAddress) {
			emailAddress = params.userEmailAddress
			if (this.forEmailVerification) {
				loginDisabled = false
			}
		}

		if (params && params.senderInfo) {
			SessionManager.instance.setIsInviteLinkLogin(true)
			this.invitedLinkSenderInfo = params.senderInfo
		}

		this.state = {
			mobileNo: phoneNumber,
			loginDisabled: loginDisabled,
			countryCode: countryCode,
			callingCode: countryCallingCode,
			validMobileNoLength: 10,
			isForLoginProcess: true,
			alreadyUserError: null,
			showFriendRequestMsg: true,
			isNewUser: false,
			alreadyAUser: false,
			isPhoneFieldFocused: false,
			showPhoneFieldError: false,
			userEmailAddress: emailAddress,
			isEmailFieldFocused: false,
			showEmailSignInLoading: false,
			showPhoneSignInLoading: false,
			loginErrorMsg: null,
			showGoogleLoading: false,
			showAppleLoading: false
		}

		this.anonymousUserIdToken = null
		this.shouldResendOTP = false;
		if (params) {
			if (params.isAutoSendOTP) {
				let requestedData = { phoneNumber: this.completeMobileNo }
				this.props.getUserProfileByIdentity(requestedData, this.sendOtpOrLink)
			}
		}

		this.handleHardwareBackButton = this.handleHardwareBackButton.bind(this)
		this.onBackPress = this.onBackPress.bind(this)
		this.clearErrorState = this.clearErrorState.bind(this)
		this.onInputBlur = this.onInputBlur.bind(this)
		this.checkIsFormFilled = this.checkIsFormFilled.bind(this)
		this.onChange = this.onChange.bind(this)
		this.checkUserAlreadyRegister = this.checkUserAlreadyRegister.bind(this)
		this.sendOtpOrLink = this.sendOtpOrLink.bind(this)
		this.onOTPSent = this.onOTPSent.bind(this)
		this.onResendOTP = this.onResendOTP.bind(this)
		this.onSelect = this.onSelect.bind(this)
		this.renderFriendRequestmsg = this.renderFriendRequestmsg.bind(this)
		this.hideFriendRequestMsg = this.hideFriendRequestMsg.bind(this)
		this.renderSendVerificationButton = this.renderSendVerificationButton.bind(this)
		this.onArrowButtonClick = this.onArrowButtonClick.bind(this)
		this.onEmailDataStored = this.onEmailDataStored.bind(this)
		this.changeLoginMethod = this.changeLoginMethod.bind(this)
		this.isInvalidEmail = this.isInvalidEmail.bind(this)
		this.validateMobileNo = this.validateMobileNo.bind(this)
		this.renderSocialLoginButton = this.renderSocialLoginButton.bind(this)
		this.onGoogleButtonPress = this.onGoogleButtonPress.bind(this)
		this.onAppleButtonPress = this.onAppleButtonPress.bind(this)
		this.onSignedInCompleted = this.onSignedInCompleted.bind(this)
	}

	UNSAFE_componentWillReceiveProps(nextProps) {
		if (nextProps.navigation && nextProps.route.params) {
			if (this.state.isForLoginProcess != nextProps.route.params.isForLoginProcess) {
				this.props.navigation.setParams({
					title: nextProps.route.params.forEmailAlert ? Constants.EMAIL_ADDRESS : nextProps.route.params.isForLoginProcess ? Constants.SIGN_IN : Constants.SIGN_UP,
				})
				this.fromSignUp = nextProps.route.params.fromSignUp
				this.forEmailAlert = nextProps.route.params.forEmailAlert
				this.setState({ isForLoginProcess: nextProps.route.params.isForLoginProcess }, () => {
					Utils.log("isForLoginProcess", this.state.isForLoginProcess)
				})
			} else if (nextProps.route.params.shouldRefreshProps) {
				this.props.navigation.setParams({
					leftButtonText: "Back",
					title: this.forAuthOnly ? this.forEmailVerification ? Constants.EMAIL_ADDRESS : Constants.PHONE_NUMBER : this.forEmailAlert ? Constants.EMAIL_ADDRESS : this.state.isForLoginProcess ? Constants.SIGN_IN : Constants.SIGN_UP,
					onBackPress: this.onBackPress,
					isLoading: this.props.isLogInProcess,
					shouldRefreshProps: false
				})
			}
		}

		if (nextProps && nextProps.isLogInProcess != this.props.isLogInProcess) {
			this.props.navigation.setParams({
				isLoading: nextProps.isLogInProcess,
			})
		}

		if (nextProps && nextProps.loginScreenError && nextProps.loginScreenError != this.props.loginScreenError) {
			this.setState({ loginDisabled: true })
		}

		this.hasAnySubscription = SessionManager.instance.hasAnySubscriptionPurchase()
	}

	componentDidMount() {
		this.props.navigation.setParams({
			leftButtonText: "Back",
			title: this.forAuthOnly ? this.forEmailVerification ? Constants.EMAIL_ADDRESS : Constants.PHONE_NUMBER : this.forEmailAlert ? Constants.EMAIL_ADDRESS : this.state.isForLoginProcess ? Constants.SIGN_IN : Constants.SIGN_UP,
			onBackPress: this.onBackPress,
			isLoading: this.props.isLogInProcess,
		})

		if (FirebaseManager.instance.currentUser() && FirebaseManager.instance.isUserAnonymous()) {
			FirebaseManager.instance.currentUser().getIdToken().then((token) => {
				SessionManager.instance.anonymousUserIdToken = token;
			}); // Anonymous user ID Token if user already linked
		}
	}

	UNSAFE_componentWillMount() {
		BackHandler.addEventListener('hardwareBackPress', this.handleHardwareBackButton);
	}

	componentWillUnmount() {
		BackHandler.removeEventListener('hardwareBackPress', this.handleHardwareBackButton);
	}

	handleHardwareBackButton() {
		this.onBackPress()
		return true
	}

	onBackPress() {
		if (SessionManager.instance.getIsInviteLinkLogin()) {
			SessionManager.instance.setIsInviteLinkLogin(false)
		}
		this.props.navigation.dispatch(CommonActions.goBack())
		this.clearErrorState()
		this.props.clearLoginState()
	}

	clearErrorState() {
		this.setState({ alreadyUserError: null })
	}

	onInputBlur() {
		const { isNewUser, alreadyAUser, userEmailAddress, loginDisabled } = this.state
		let isDisable = loginDisabled
		if (this.forEmailVerification) {
			isDisable = this.isInvalidEmail(userEmailAddress);
		} else {
			isDisable = this.checkIsFormFilled();
		}

		if (isNewUser || alreadyAUser) {
			this.setState({ isNewUser: false, alreadyAUser: false, loginDisabled: isDisable, loginErrorMsg: null })
		} else {
			this.setState({ loginDisabled: isDisable, loginErrorMsg: null });
		}
	}

	isInvalidEmail() {
		let { userEmailAddress } = this.state

		let reg = /^[\p{L}0-9._%+-]+@[\p{L}0-9.-]+\.[a-zA-Z]{2,}$/u;
		let specialPattern = new RegExp(reg)
		if (userEmailAddress != '' && specialPattern.test(userEmailAddress)) {
			return false;
		} else {
			return true;
		}
	}

	checkIsFormFilled() {
		var isDisable = true;
		const { mobileNo, callingCode } = this.state
		if (mobileNo.trim() != '' && this.validateMobileNo(mobileNo, callingCode)) {
			isDisable = false;
		} else if (Utils.notANumericExpr(mobileNo)) {
			this.setState({ showPhoneFieldError: true })
		} else {
			this.setState({ showPhoneFieldError: false })
		}
		return isDisable;
	}

	validateMobileNo(mobileNo, callingCode) {

		let { validMobileNoLength } = this.state

		let genericPattern = /[1-9]{1}[0-9]{6,14}$/
		var stringPattern = `[1-9]{1}[0-9]{${validMobileNoLength - 1}}$`
		let specialPattern = new RegExp(stringPattern)
		genericPattern = validMobileNoLength ? specialPattern : genericPattern
		if (mobileNo != '' && genericPattern.test(mobileNo)) {
			this.completeMobileNo = callingCode + mobileNo
			return true;
		} else {
			return false;
		}
	}

	onChange(key, value) {
		this.setState({ [key]: value.trim() }, () => {
			this.onInputBlur();
		})
	}

	async checkUserAlreadyRegister() {
		Keyboard.dismiss()
		if (this.forEmailVerification) {
			const { userEmailAddress } = this.state
			if (!userEmailAddress) {
				return;
			}

			this.setState({ showEmailSignInLoading: true }, () => {
				let requestedData = { type: 'email', email: userEmailAddress.toLowerCase() }
				this.props.getUserProfileByIdentity(requestedData, this.sendOtpOrLink)
			})
		} else {
			if (!this.completeMobileNo) {
				return;
			}

			this.setState({ showPhoneSignInLoading: true }, () => {
				let requestedData = { phoneNumber: this.completeMobileNo }
				this.props.getUserProfileByIdentity(requestedData, this.sendOtpOrLink)
			})
		}
	}

	async onEmailDataStored(data) {
		if (data) {
			SessionManager.instance.setIsGeneratingEmailLink(true)
			const { userEmailAddress } = this.state
			let emailType = this.forAuthOnly ? 'subs_sign_in' : this.forEmailAlert ? 'alerts' : this.fromSignUp ? 'signup' : 'signin'
			await FirebaseManager.instance.sendEmailAuthLink(data.ID, userEmailAddress, emailType, this.metadata)
		}

		this.setState({ showEmailSignInLoading: false })
	}

	async sendOtpOrLink(alreadyAUser, userDetails, givesError = false) {
		if (givesError) {
			this.setState({ showEmailSignInLoading: false, showPhoneSignInLoading: false })
			return
		}

		let isMergingUserFlow = this.forEmailAlert && UserSession.instance.isLoggedInUser() && Utils.checkObject(userDetails) && Utils.checkData(userDetails.email)
		if (isMergingUserFlow && Utils.checkData(userDetails.phoneNumber)) {
			if (this.forEmailVerification) {
				this.setState({ loginErrorMsg: "A user with this email address already exists.", loginDisabled: true, showEmailSignInLoading: false })
				return
			}
		}

		// if (!this.forEmailAlert && !this.forAuthOnly) {
		// 	if (this.state.isForLoginProcess && !alreadyAUser) {
		// 		this.setState({ isNewUser: true, showEmailSignInLoading: false })
		// 		return;
		// 	} else if (!this.state.isForLoginProcess && alreadyAUser) {
		// 		this.setState({ alreadyAUser: true, showEmailSignInLoading: false })
		// 		return;
		// 	}
		// }

		this.anonymousUserIdToken = await FirebaseManager.instance.currentUser().getIdToken(); // Anonymous user ID Token if user already linked
		let flowType = isMergingUserFlow ? 'merge' : this.forAuthOnly ? 'subs_sign_in' : this.forEmailAlert ? 'alerts' : this.fromSignUp ? 'signup' : 'signin'
		if (this.forEmailVerification) {
			let requestedData = { email: this.state.userEmailAddress, platform: Constants.MOBILE, userType: UserSession.instance.isLoggedInUser() ? 'active' : 'anonymous', flow: flowType, anonymousIdToken: this.anonymousUserIdToken }
			if (isMergingUserFlow) {
				requestedData.targetUserID = userDetails.id
			}

			if (this.seriesToFollowData) {
				requestedData.series = this.seriesToFollowData
			}

			if (this.forAuthOnly) {
				requestedData["metadata"] = { "productId": this.metadata.productId }
			} else if (SessionManager.instance.getIsInviteLinkLogin()) {
				requestedData["metadata"] = { "redirection_type": Constants.FRIEND_REQUEST }
			} else if (this.postSignInFriendsPath) {
				if (this.postSignInFriendsPath == Constants.SEE_FRIENDS) {
					requestedData["metadata"] = { "redirection_type": Constants.SEE_FRIENDS }
				} else if (this.postSignInFriendsPath == Constants.FRIEND_REQUEST) {
					requestedData["metadata"] = { "redirection_type": Constants.FRIEND_REQUEST }
				} else if (this.postSignInFriendsPath == Constants.INVITE_FRIEND) {
					requestedData["metadata"] = { "redirection_type": Constants.INVITE_FRIEND }
				}
			}
			this.props.storeUserEmailData(requestedData, this.onEmailDataStored)
		} else {
			if (this.shouldResendOTP) {
				this.props.resendOTP(this.completeMobileNo, this.onOTPSent)
				this.shouldResendOTP = false;
			} else {
				this.props.signInWithMobile(this.completeMobileNo, this.onOTPSent)
			}
		}
		this.clearErrorState()
	}

	onOTPSent(verificationData, givesError = false) {
		this.setState({ showPhoneSignInLoading: false })
		if (givesError) {
			return
		}

		this.props.navigation.navigate(Constants.OTP_SCREEN, { mobileNo: this.completeMobileNo, verificationId: verificationData.verificationId, code: verificationData.code, isAutoVerified: verificationData.isAutoVerified, anonyIdToken: this.anonymousUserIdToken, onResendOTP: this.onResendOTP, isForLoginProcess: this.state.isForLoginProcess, fromSignUp: this.fromSignUp, forAuthOnly: this.forAuthOnly, metadata: this.metadata, postSignInFriendsPath: this.postSignInFriendsPath })
	}

	onResendOTP() {
		this.shouldResendOTP = true;
	}

	onArrowButtonClick() {
		if (this.countryPickerRef) {
			this.countryPickerRef.open()
		}
	}

	onSelect(value) {
		const callingCode = (!value.callingCode || value.callingCode.length == 0) ? "" : "+" + value.callingCode;
		const countryCode = (!value.code || value.code.length == 0) ? "" : "+" + value.code;
		const mobileNoLength = value.mobileLength ? value.mobileLength : null
		this.setState({ callingCode: callingCode, countryCode: countryCode, selectedCountry: value, validMobileNoLength: parseInt(mobileNoLength) }, () => {
			this.onInputBlur()
		})
		this.textInput.focus()

		SharedPreferences.saveData(settings.SP_COUNTRY_CODE_KEY, callingCode);
	}


	hideFriendRequestMsg() {
		this.setState({ showFriendRequestMsg: false })
	}

	changeLoginMethod() {
		if (this.forEmailVerification) {
			const params = { metadata: this.metadata, forAuthOnly: this.forAuthOnly, fromSignUp: this.fromSignUp, isForLoginProcess: this.state.isForLoginProcess, phoneNumber: this.completeMobileNo, userEmailAddress: this.state.userEmailAddress, senderInfo: this.invitedLinkSenderInfo, postSignInFriendsPath: this.postSignInFriendsPath }
			this.props.navigation.push(Constants.LOGIN_SCREEN, params)
		} else {
			const params = { metadata: this.metadata, forAuthOnly: this.forAuthOnly, fromSignUp: this.fromSignUp, isForEmailVerification: true, isForLoginProcess: this.state.isForLoginProcess, phoneNumber: this.completeMobileNo, userEmailAddress: this.state.userEmailAddress, senderInfo: this.invitedLinkSenderInfo, postSignInFriendsPath: this.postSignInFriendsPath }
			this.props.navigation.push(Constants.LOGIN_SCREEN, params)
		}
	}

	onGoogleButtonPress() {
		this.setState({ showGoogleLoading: true })
		this.props.signinWithSocialLink('google', this.onSignedInCompleted)
	}

	onAppleButtonPress() {
		this.setState({ showAppleLoading: true })
		this.props.signinWithSocialLink('apple', this.onSignedInCompleted)
	}

	onSignedInCompleted(response) {
		this.setState({ showGoogleLoading: false, showAppleLoading: false })
		if (!response) {
			return null;
		}

		if (this.forEmailAlert) {
			let params = { emailVerified: true }
			if (this.seriesToFollowData && this.seriesToFollowData.title) {
				params.seriesFollowed = this.seriesToFollowData.title
			}
			Utils.navigateToManageAlertsPage(this.props, params)
		} else if (this.forAuthOnly) {
			this.props.navigation.navigate('Home', { comicHome: settings.getSubscribeURL(), hasUserInfo: true, isAuthCompletedForSubs: true, productId: this.metadata })
		} else if (SessionManager.instance.getIsInviteLinkLogin()) {
			SessionManager.instance.setIsInviteLinkLogin(false)
			Utils.navigateToFriendRequests(null, { isLoginFromInviteLink: true });
		} else if (this.postSignInFriendsPath && this.postSignInFriendsPath == Constants.SEE_FRIENDS) {
			Utils.navigateToSeeFriendsPage(null)
		} else if (this.postSignInFriendsPath && this.postSignInFriendsPath == Constants.FRIEND_REQUEST) {
			Utils.navigateToFriendRequests(null);
		} else {
			const showInviteFriendsSheet = this.postSignInFriendsPath && this.postSignInFriendsPath == Constants.INVITE_FRIEND
			this.props.navigation.navigate('Home', { 'forceReload': true, isLoginSuccess: true, showInviteSheet: showInviteFriendsSheet })
		}
	}

	renderFriendRequestmsg() {
		if (!this.invitedLinkSenderInfo) {
			return null;
		}

		let message = `You must sign in to accept friend request from ${this.invitedLinkSenderInfo.senderName}`

		return (
			<View>
				<View style={[bannerMsgContainer, { backgroundColor: this.context.colors.bannerBackgroundError }]}>
					<View
						style={bannerTextView}>
						<Text style={[this.context.bodyMini, { color: this.context.colors.bannerTextError }]} adjustsFontSizeToFit>{message}</Text>
					</View>
					<TouchableOpacity
						style={bannerCrossIconView}
						onPress={() => this.hideFriendRequestMsg()}>
						<FastImage
							style={bannerCrossIcon}
							tintColor={this.context.colors.bannerTextError}
							source={require('../../../assets/close_window.png')}
						/>
					</TouchableOpacity>
				</View>
			</View>
		)
	}

	renderSendVerificationButton() { //Login Button
		const { loginDisabled, isNewUser, alreadyAUser, showEmailSignInLoading, showPhoneSignInLoading } = this.state
		const showLoader = (showEmailSignInLoading && (this.forEmailVerification)) || (showPhoneSignInLoading && !this.forEmailVerification)
		const isButtonDisabled = (loginDisabled || isNewUser || alreadyAUser)

		return (
			<View style={{ marginTop: 32 }}>
				<Button
					variant='solid'
					disabled={isButtonDisabled}
					style={[styles.loginButtonsView, { backgroundColor: isButtonDisabled ? this.context.colors.separators : this.context.colors.logoRed }]}
					onPress={() => this.checkUserAlreadyRegister()}>
					{showLoader && <ThreeDotLoader />}
					{!showLoader && <Text style={[this.context.pBold, { color: this.context.colors.textInverse }]}>{this.forEmailVerification ? Constants.SEND_VERIFICATION_LINK : Constants.SEND_VERIFICATION_CODE}</Text>}
				</Button>
			</View>
		)
	}

	renderSocialLoginButton(provider, label, iconSource, redirectionPath) {
		const isEmailProvider = provider === 'email'
		const showGoogleLoader = provider === 'google' && this.state.showGoogleLoading
		const showAppleLoader = provider === 'apple' && this.state.showAppleLoading

		return (
			<View style={!isEmailProvider ? { marginTop: 20 } : null}>
				<Button
					variant="solid"
					style={[styles.loginButtonsView, { backgroundColor: isEmailProvider ? this.context.colors.logoRed : Color.SOCIAL_LOGIN_BUTTON_COLOR }]}
					onPress={redirectionPath}>
					{(showGoogleLoader || showAppleLoader) && <ThreeDotLoader />}
					{(!showGoogleLoader && !showAppleLoader) &&
						<View style={styles.loginButtonDes}>
							<FastImage
								style={styles.loginButtonImageView}
								source={iconSource} />
							<Text style={[this.context.pBold, { color: this.context.colors.textInverse }]}>{label}</Text>
						</View>
					}
				</Button>
			</View>
		);
	};

	render() {
		const { isLogInProcess, loginScreenError } = this.props
		const { countryCode, mobileNo, validMobileNoLength, isNewUser, alreadyAUser, isPhoneFieldFocused, showPhoneFieldError, isEmailFieldFocused, userEmailAddress, showEmailSignInLoading, loginErrorMsg } = this.state

		const errorMsg = loginScreenError ? loginScreenError : loginErrorMsg

		return (
			<SafeAreaView style={{ flex: 1, backgroundColor: 'white', height: '100%' }}>
				{this.state.showFriendRequestMsg && this.renderFriendRequestmsg()}
				<View style={styles.viewStyle}>
					{this.fromSignUp && <SignUpPageInfo signUpStep={2} />}
					{this.fromSignUp && <Text style={[this.context.h2, { marginBottom: 20 }]}>{this.forEmailVerification ? Constants.EMAIL_ADDRESS : Constants.PHONE_NUMBER}</Text>}
					<View style={{ flexDirection: 'row' }}>
						<Text style={[this.context.bodyMini, styles.phoneNumberText(this.fromSignUp)]}>{this.forEmailVerification ? Constants.EMAIL_ADDRESS : Constants.PHONE_NUMBER}<Text>*</Text></Text>
					</View>
					<View style={[styles.phoneNumberView, { borderColor: isNewUser || loginScreenError || alreadyAUser || Utils.checkData(loginErrorMsg) ? this.context.colors.logoRed : (isPhoneFieldFocused || isEmailFieldFocused) ? this.context.colors.text : mobileNo ? this.context.colors.separators : this.context.colors.chatBubbles }]}>
						<View style={{ flexDirection: 'row' }}>
							{!this.forEmailVerification &&
								<View style={styles.phoneNoCountryView}>
									<CountryPicker
										enable={true}
										darkMode={false}
										countryCode={countryCode}
										onSelectCountry={(data) => {
											this.onSelect(data)
										}}
										containerConfig={{
											showFlag: true,
											showCallingCode: true,
											showCountryName: false,
											showCountryCode: false
										}}
										modalConfig={{
											showFlag: true,
											showCallingCode: true,
											showCountryName: true,
											showCountryCode: false
										}}
										containerStyle={{
											flagStyle: { fontSize: 25 },
											callingCodeStyle: [this.context.p, { color: this.context.colors.textBold, alignSelf: 'center' }],
											countryCodeStyle: this.context.p,
											countryNameStyle: {},
										}}
										modalStyle={{
											...Platform.select({
												android: { container: { marginTop: settings.isAndroid14OrBelowDevice ? 0 : navigationStyle.statusBarHeight } },
											}),
											searchStyle: {},
											tileStyle: {},
											itemStyle: {
												itemContainer: {},
												flagStyle: {},
												countryCodeStyle: {},
												countryNameStyle: {},
												callingNameStyle: {},
											},
										}}
										title={"Select your country"}
										searchPlaceholder={"Search"}
										showCloseButton={true}
										showModalTitle={true}
										countryPickerRef={(r) => { this.countryPickerRef = r }}
									/>
									<TouchableOpacity
										onPress={this.onArrowButtonClick}>
										<FastImage
											style={styles.dropDownImageView}
											tintColor={this.context.colors.textBold}
											source={require('../../../assets/dropdown_arrow_icon.png')}
										/>
									</TouchableOpacity>
									<View style={[styles.vertSeparatorView, { backgroundColor: this.context.colors.separators }]}></View>
									<View style={{ height: 52, width: '100%' }}>
										<TextInputComponent
											inputRef={(r) => { this.textInput = r }}
											refState="mobileNo"
											selectionColor={this.context.colors.textBold}
											maxLength={validMobileNoLength ? validMobileNoLength : 14}
											onChange={(refState, text) => {
												this.onChange(refState, text);
											}}
											onBlur={(refState) => {
												this.setState({ isPhoneFieldFocused: false })
												this.onInputBlur();
											}}
											onFocus={() => {
												this.setState({ isPhoneFieldFocused: true })
											}}
											value={mobileNo}
											placeHolder={"Enter your phone number"}
											placeholderTextColor={this.context.colors.text}
											keyboardType="phone-pad"
											textColor={this.context.colors.textBold}
											textInputStyle={[this.context.p, styles.phoneTextField]}
											fontSize={16}
											textAlignVertical={"center"}
										/>
									</View>
								</View>
							}
							{this.forEmailVerification &&
								<TextInputComponent
									inputRef={(r) => { this.EmailTextInput = r }}
									refState="userEmailAddress"
									selectionColor={this.context.colors.textBold}
									maxLength={350}
									onChange={(refState, text) => {
										this.onChange(refState, text);
									}}
									onBlur={(refState) => {
										this.setState({ isEmailFieldFocused: false })
										this.onInputBlur();
									}}
									onFocus={() => {
										this.setState({ isEmailFieldFocused: true })
									}}
									value={userEmailAddress}
									placeHolder={"Enter your email address"}
									placeholderTextColor={this.context.colors.text}
									keyboardType={"email-address"}
									textColor={this.context.colors.textBold}
									textInputStyle={[this.context.p, styles.phoneTextField, { paddingStart: 16 }]}
									fontSize={16}
									textAlignVertical={"center"}
									autoCapitalize={"none"}
									autoCorrect={false}
								/>
							}
						</View>
					</View>
					{!(isNewUser || alreadyAUser) && !loginScreenError && !showPhoneFieldError && !Utils.checkData(loginErrorMsg) && <Text style={[this.context.bodyMini, styles.descView]}>{this.forEmailVerification ? "We will send verification link to this email address." : "We will send verification code to this number."}</Text>}
					{showPhoneFieldError && <ErrorTextMsg errorText={"Please enter numbers only."} />}
					{(loginScreenError || Utils.checkData(loginErrorMsg)) && <ErrorTextMsg errorText={errorMsg} />}
					{this.renderSendVerificationButton()}
					{(!this.forEmailVerification || this.forEmailAlert) &&
						<View>
							<View style={styles.otherLogin}>
								<View style={[styles.separatorStyle, { backgroundColor: this.context.colors.chatBubbles }]} />
								<Text style={[this.context.p, styles.otherLoginText]}>or</Text>
								<View style={[styles.separatorStyle, { backgroundColor: this.context.colors.chatBubbles }]} />
							</View>

							{!this.forEmailAlert && this.renderSocialLoginButton('email', 'Sign in with Email', require('../../../assets/email_login_icon.png'), this.changeLoginMethod)}
							{this.renderSocialLoginButton('google', 'Sign in with Google', require('../../../assets/google_login_icon.png'), this.onGoogleButtonPress)}
							{(Platform.OS === 'ios') && this.renderSocialLoginButton('apple', 'Sign in with Apple', require('../../../assets/apple_login_icon.png'), this.onAppleButtonPress)}
						</View>
					}
				</View>
				{isLogInProcess && <LoadingIndicator />}
			</SafeAreaView >
		)
	}
}

LoginScreen.contextType = ThemeContext;

const styles = StyleSheet.create({
	viewStyle: {
		flex: 1,
		marginLeft: 20,
		marginRight: 20
	},
	phoneNumberText: (fromSignUp) => {
		return {
			marginTop: fromSignUp ? 0 : 30,
			marginBottom: 4
		}
	},
	phoneTextField: {
		width: "100%",
		height: 52,
		paddingStart: 12,
		paddingEnd: 12,
		paddingBottom: Platform.OS == 'android' ? 10 : 4
	},
	phoneText: {
		alignSelf: 'center'
	},
	dropDownImageView: {
		height: 12,
		width: 12,
		marginLeft: 8,
		alignSelf: 'center',
	},
	vertSeparatorView: {
		height: '60%',
		width: 1,
		marginLeft: 8,
		alignSelf: 'center'
	},
	descView: {
		marginTop: 4
	},
	loginButtonsView: {
		borderRadius: 6,
		paddingTop: 12,
		paddingBottom: 12,
	},
	signupTextView: {
		flexDirection: 'row',
		marginTop: 12,
		alignSelf: 'center'
	},
	phoneNumberView: {
		borderWidth: 1,
		borderRadius: 8
	},
	phoneNoCountryView: {
		flexDirection: 'row',
		alignItems: 'center',
		marginLeft: 16,
		marginRight: 16
	},
	loginButtonImageView: {
		height: 22,
		width: 22,
		marginRight: 10
	},
	separatorStyle: {
		flex: 1,
		height: 1,
		width: '100%'
	},
	otherLogin: {
		flexDirection: 'row',
		alignItems: 'center',
		marginTop: 40,
		marginBottom: 40
	},
	otherLoginText: {
		marginLeft: 10,
		marginRight: 10
	},
	loginButtonDes: {
		flexDirection: 'row',
		alignItems: 'center'
	}
});

const mapStateToProps = (state) => {
	return {
		isLogInProcess: state.loginInfo.isLogInProcess,
		loginScreenError: state.loginInfo.loginScreenError
	}
}

const mapDispatchToProps = (dispatch) => {
	return {
		signInWithMobile(mobileNo, callback) {
			dispatch(
				signInWithMobile(mobileNo, callback)
			)
		},
		signinWithSocialLink(linkType, callback) {
			dispatch(
				signinWithSocialLink(linkType, callback)
			)
		},
		clearLoginState() {
			dispatch(
				clearLoginState()
			)
		},
		resendOTP(mobileNo, callback) {
			dispatch(
				resendOTP("loginScreen", mobileNo, callback)
			)
		},
		getUserProfileByIdentity(data, callback) {
			dispatch(
				getUserProfileByIdentity(data, callback)
			)
		},
		storeUserEmailData(data, callBack) {
			dispatch(
				storeUserEmailData(data, callBack)
			)
		}
	}
}
export default connect(mapStateToProps, mapDispatchToProps)(LoginScreen)