import React, { Component } from 'react'
import { StyleSheet, BackHandler, TouchableOpacity, Keyboard, SafeAreaView } from 'react-native'
import { View, Text, Button } from 'native-base'
import { LoadingIndicator } from '../LoadingIndicator'
import { connect } from "react-redux"
import { verifyOTPAndLinkUser, resendOTP, onUserAutoVerified, mergeUserData, reloadComic, clearLoginState } from '../../redux/actions/actions'
import { CommonActions } from '@react-navigation/native'
import FirebaseManager from '../../config/FirebaseManager'
import { Constants } from '../../config/Constants'
import { Utils } from '../../config/Utils'
import SessionManager from '../../config/SessionManager'
import { ThemeContext } from '../../Contexts'
import OtpInputs from 'react-native-otp-inputs';
import SigningHeaderBar from '../friendsPackage/shared/SigningHeaderBar'
import SignUpPageInfo from './SignUpPageInfo'
import ErrorTextMsg from '../ErrorTextMsg'
import { settings } from '../../config/settings'

class OTPScreen extends Component {

	static navigationOptions = {
		header: (props) => <SigningHeaderBar {...props} />
	}

	constructor(props) {
		super(props)

		this.state = {
			otp: '',
			nextDisabled: true,
			isForLoginProcess: true
		}

		const params = this.props.route.params
		this.fromSignUp = params && params.fromSignUp
		this.forEmailVerification = params && params.isForEmailVerification
		this.forAuthOnly = params && params.forAuthOnly
		this.metadata = params && params.metadata
		this.postSignInFriendsPath = params && params.postSignInFriendsPath

		this.verificationId = this.props.route.params.verificationId
		this.isAutoVerified = false;
		this.onBackPress = this.onBackPress.bind(this)
		this.close = this.close.bind(this)
		this.onPressChangeMobile = this.onPressChangeMobile.bind(this)
		this.onInputBlur = this.onInputBlur.bind(this)
		this.checkIsFormFilled = this.checkIsFormFilled.bind(this)
		this.onLoginPress = this.onLoginPress.bind(this)
		this.navigateToConfirmation = this.navigateToConfirmation.bind(this)
		this.resendOTP = this.resendOTP.bind(this)
		this.onOTPSent = this.onOTPSent.bind(this)
		this.renderVerifyButton = this.renderVerifyButton.bind(this)
	}

	UNSAFE_componentWillReceiveProps(nextProps) {
		const { isAnonymous } = this.props
		if (!nextProps.isAnonymous && nextProps.isAnonymous != isAnonymous) {
			this.setState({ nextDisabled: false })
			this.props.navigation.setParams({
				isAnonymous: nextProps.isAnonymous
			})
		}

		if (nextProps.navigation && nextProps.route.params) {
			if (this.state.isForLoginProcess != nextProps.route.params.isForLoginProcess) {
				this.props.navigation.setParams({
					title: (nextProps.route.params.isForLoginProcess || this.forEmailVerification) ? Constants.SIGN_IN : Constants.SIGN_UP,
				})
				this.setState({ isForLoginProcess: nextProps.route.params.isForLoginProcess })
			}
		}

		if (nextProps && nextProps.isLogInProcess != this.props.isLogInProcess) {
			this.props.navigation.setParams({
				isLoading: nextProps.isLogInProcess,
			})
		}

		if (nextProps && nextProps.otpScreenError && nextProps.otpScreenError != this.props.otpScreenError) {
			this.setState({ nextDisabled: true })
		}
	}

	componentDidMount() {
		this.props.navigation.setParams({
			leftButtonText: "Back",
			onBackPress: this.onBackPress,
			title: this.state.isForLoginProcess ? Constants.SIGN_IN : Constants.SIGN_UP,
		})

		const currentUser = FirebaseManager.instance.currentUser()
		if (!currentUser.isAnonymous) {
			const { anonyIdToken, mobileNo } = this.props.route.params
			this.props.mergeUserData(anonyIdToken, mobileNo);
			this.isAutoVerified = true;
			this.props.onUserAutoVerified(this.navigateToConfirmation)
		}

		const { code, isAutoVerified } = this.props.route.params
		if (this.verificationId && code && isAutoVerified) {
			this.isAutoVerified = true;
			this.onLoginPress(code)
		}
	}

	UNSAFE_componentWillMount() {
		BackHandler.addEventListener('hardwareBackPress', this.onBackPress);
	}

	componentWillUnmount() {
		BackHandler.removeEventListener('hardwareBackPress', this.onBackPress);
	}

	onBackPress() {
		this.close()
		return true
	}

	close() {
		if (this.props.isAnonymous) {
			this.props.route.params.onResendOTP && this.props.route.params.onResendOTP();
			this.props.navigation.dispatch(CommonActions.goBack())
		} else {
			if (this.props.navigation && this.props.navigation.getParent()) {
				this.props.navigation.getParent().goBack();
			}
			this.props.reloadComic(this.props.pathUrl)
		}
	}

	onPressChangeMobile() {
		let params = { isForLoginProcess: this.state.isForLoginProcess, shouldRefreshProps: true, forAuthOnly: this.forAuthOnly, metadata: this.metadata }
		Utils.navigateToDrawerLoginRoute(this.props, Constants.LOGIN_SCREEN, params)
	}

	onInputBlur() {
		const isDisable = this.checkIsFormFilled();
		if (this.state.nextDisabled != isDisable) {
		}

		if (this.props.otpScreenError) {
			this.props.clearLoginState()
		}
		this.setState({ nextDisabled: isDisable });
	}

	checkIsFormFilled() {
		var isDisable = true;
		if (this.state.otp.trim() != '' && this.state.otp.length == 6) {
			isDisable = false;
		}
		return isDisable;
	}

	onLoginPress(otp) {
		const { mobileNo } = this.props.route.params
		let OTP = otp ? otp : this.state.otp
		Keyboard.dismiss()
		this.props.verifyOTPAndLinkUser(mobileNo, this.verificationId, OTP, this.navigateToConfirmation)
	}

	navigateToConfirmation() {
		if (this.forAuthOnly) {
			let params = { isMobileVerfied: true, isAuthCompletedForSubs: true, comicHome: settings.getSubscribeURL(), hasUserInfo: true }
			Utils.navigateToReader(params)
		} else if (!this.fromSignUp && SessionManager.instance.getIsInviteLinkLogin()) {
			SessionManager.instance.setIsInviteLinkLogin(false)
			Utils.navigateToFriendRequests(null)
		} else if (this.forEmailVerification) {
			this.props.navigation.navigate(Constants.MANAGE_ALERTS_SCREEN)
		} else if (this.postSignInFriendsPath && this.postSignInFriendsPath == Constants.SEE_FRIENDS) {
			Utils.navigateToSeeFriendsPage(null)
		} else if (this.postSignInFriendsPath && this.postSignInFriendsPath == Constants.FRIEND_REQUEST) {
			Utils.navigateToFriendRequests(null);
		} else {
			const showInviteFriendsSheet = this.postSignInFriendsPath && this.postSignInFriendsPath == Constants.INVITE_FRIEND
			this.props.navigation.navigate('Home', { 'forceReload': true, isLoginSuccess: true, showInviteSheet: showInviteFriendsSheet })
		}
	}

	resendOTP(mobileNo) {
		this.props.resendOTP(mobileNo, this.onOTPSent)
		this.setState({ otp: '', nextDisabled: true })
	}

	onOTPSent(verificationData) {
		if (verificationData) {
			if (verificationData.verificationId) {
				this.verificationId = verificationData.verificationId
			}
			if (this.verificationId && verificationData.code && verificationData.isAutoVerified) {
				this.isAutoVerified = true;
				this.onLoginPress(verificationData.code)
			}
			Utils.showToast("The verification code has been sent successfully.")
		}
	}

	renderVerifyButton() {
		const { nextDisabled } = this.state
		return (
			<View style={{ marginTop: 32 }}>
				<Button
					variant='solid'
					disabled={nextDisabled}
					style={[styles.verficationButton, { backgroundColor: nextDisabled ? this.context.colors.separators : this.context.colors.logoRed }]}
					onPress={() => this.onLoginPress()}>
					<Text style={[this.context.pBold, { color: this.context.colors.textInverse }]}>{Constants.VERIFY}</Text>
				</Button>
			</View>
		)
	}

	render() {
		const { isLogInProcess, otpScreenError } = this.props
		const { mobileNo, userEmailAddress } = this.props.route.params
		return (
			<SafeAreaView style={{ flex: 1, backgroundColor: 'white', height: '100%' }}>
				<View style={styles.viewStyle}>
					{this.fromSignUp && <SignUpPageInfo signUpStep={2} />}
					{this.fromSignUp && <Text style={[this.context.h2, { marginBottom: 8 }]}>{Constants.ENTER_VERIFICATION_CODE}</Text>}
					<Text style={[this.context.p, { marginTop: this.fromSignUp ? 0 : 30 }]}>Verification code sent to
						<Text style={[this.context.pBold, { color: this.context.colors.text }]}> {this.forEmailVerification ? userEmailAddress : mobileNo}</Text>
					</Text>
					<Text style={[this.context.bodyMini, styles.verificationCodeText]}>Verification Code*</Text>
					<OtpInputs
						style={styles.otpInputsStyle}
						autofillFromClipboard={true}
						numberOfInputs={6}
						defaultValue={this.state.otp}
						keyboardType='numeric'
						inputContainerStyles={[styles.inputContainerStyle, { borderColor: otpScreenError ? this.context.colors.logoRed : this.context.colors.chatBubbles }]}
						inputStyles={[this.context.p, styles.inputStyle, { color: this.context.colors.textBold }]}
						focusStyles={{ borderColor: this.context.colors.textBold }}
						handleChange={(code) => {
							this.setState({ "otp": code }, () => {
								this.onInputBlur()
							})
						}}
					/>
					<View>
						{otpScreenError &&
							<View style={styles.otpErrorView}>
								<ErrorTextMsg errorText={otpScreenError} />
								<View style={styles.otpErrorView}>
									<TouchableOpacity
										onPress={() => this.resendOTP(mobileNo)}>
										<Text style={[this.context.bodyMini, styles.resendChangeText, { color: this.context.colors.logoRed }]}>Resend</Text>
									</TouchableOpacity>
									<Text style={[this.context.bodyMini, { color: this.context.colors.logoRed }]}> | </Text>
									<TouchableOpacity onPress={() => this.onPressChangeMobile()}>
										<Text style={[this.context.bodyMini, styles.resendChangeText, { color: this.context.colors.logoRed }]}>{this.forEmailVerification ? "Change email address" : "Change number"}</Text>
									</TouchableOpacity>
								</View>
							</View>
						}
					</View>
					{!otpScreenError &&
						<View style={styles.textView}>
							<Text style={this.context.bodyMini}>Didn’t get the code? </Text>
							<TouchableOpacity onPress={() => this.resendOTP(mobileNo)}>
								<Text style={[this.context.bodyMini, styles.resendChangeText, { color: this.context.colors.logoRed }]}>Resend</Text>
							</TouchableOpacity>
							<Text style={[this.context.bodyMini, { color: this.context.colors.separators }]}> | </Text>
							<TouchableOpacity onPress={() => this.onPressChangeMobile()}>
								<Text style={[this.context.bodyMini, styles.resendChangeText, { color: this.context.colors.logoRed }]}>{this.forEmailVerification ? "Change email address" : "Change number"}</Text>
							</TouchableOpacity>
						</View>
					}
					{this.renderVerifyButton()}

				</View>
				{isLogInProcess && <LoadingIndicator />}
			</SafeAreaView>
		)
	}
}

OTPScreen.contextType = ThemeContext;

const styles = StyleSheet.create({
	viewStyle: {
		flex: 1,
		marginLeft: 20,
		marginRight: 20
	},
	verificationCodeText: {
		marginTop: 20,
		marginBottom: 4,
	},
	textView: {
		flexDirection: 'row',
		marginTop: 20,
		alignItems: 'center'
	},
	resendChangeText: {
		textDecorationLine: 'underline'
	},
	verficationButton: {
		borderRadius: 6,
		paddingTop: 12,
		paddingBottom: 12
	},
	inputContainerStyle: {
		height: 52,
		width: 40,
		borderWidth: 1,
		borderRadius: 8
	},
	otpInputsStyle: {
		flexDirection: 'row',
		justifyContent: 'space-between'
	},
	inputStyle: {
		height: 45,
		width: 40,
		textAlign: 'center'
	},
	otpErrorView: {
		flexDirection: 'row',
		marginTop: 4
	}
});

const mapStateToProps = (state) => {
	return {
		isLogInProcess: state.loginInfo.isLogInProcess,
		isAnonymous: state.loginInfo.isAnonymous,
		otpScreenError: state.loginInfo.otpScreenError,
		pathUrl: state.readComic.pathUrl,
	}
}

const mapDispatchToProps = (dispatch) => {
	return {
		verifyOTPAndLinkUser(phoneNumber, verificationId, otp, callback) {
			dispatch(
				verifyOTPAndLinkUser(phoneNumber, verificationId, otp, callback)
			)
		},
		reloadComic(pathUrl) {
			dispatch(
				reloadComic(pathUrl)
			)
		},
		resendOTP(mobileNo, callback) {
			dispatch(
				resendOTP("otpScreen", mobileNo, callback)
			)
		},
		onUserAutoVerified(callback) {
			dispatch(
				onUserAutoVerified(callback)
			)
		},
		mergeUserData(anonymousUserIdToken, phoneNumber) {
			dispatch(
				mergeUserData(anonymousUserIdToken, phoneNumber)
			)
		},
		clearLoginState() {
			dispatch(
				clearLoginState()
			)
		},
	}
}
export default connect(mapStateToProps, mapDispatchToProps)(OTPScreen)
