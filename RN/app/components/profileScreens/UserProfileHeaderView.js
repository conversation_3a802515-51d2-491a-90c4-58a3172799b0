import React, { useContext } from 'react'
import { StyleSheet, TouchableOpacity, Dimensions } from 'react-native'
import { Text, View } from 'native-base';
import { scale } from 'react-native-size-matters'
import { navigationStyle, roundImage, mainHeaderContainer } from '../../config/styles'
import { Utils } from '../../config/Utils';
import { Constants } from '../../config/Constants';
import FastImage from 'react-native-fast-image'
import BadgesView from '../BadgesView'
import { ThemeContext } from '../../Contexts';
import SelectableText from '../../config/SelectableText';
import UserSession from '../../config/UserSession';
import { settings } from '../../config/settings';

let dimensions = Dimensions.get('window')
const UserProfileHeaderView = (props) => {

  const { socialLinks, profileData, totalGiftsSent, totalRecentComments, isAnotherUserProfile, friendshipStatus, isGuestUserProfile } = props;

  const context = useContext(ThemeContext);
  const genderList = [
    {
      label: "She/Her", value: "she",
    },
    {
      label: "He/Him", value: "he",
    },
    {
      label: "They/Them", value: "they",
    },
    {
      label: "Other", value: "other",
    },
    {
      label: "I prefer not to say", value: "i_prefer_not_to_say",
    }
  ];

  const openWebView = (websiteURL) => {
    var completeWebURL = null;
    if (!websiteURL.startsWith(Constants.WWW_PREFIX) && !websiteURL.startsWith(Constants.HTTPS_PREFIX) && !websiteURL.startsWith(Constants.HTTP_PREFIX)) {
      completeWebURL = `${Constants.HTTPS_PREFIX}${Constants.WWW_PREFIX}${websiteURL}`;
    } else if ((!websiteURL.startsWith(Constants.HTTPS_PREFIX) || !websiteURL.startsWith(Constants.HTTP_PREFIX)) && websiteURL.startsWith(Constants.WWW_PREFIX)) {
      completeWebURL = `${Constants.HTTPS_PREFIX}${websiteURL}`;
    } else if ((websiteURL.startsWith(Constants.HTTPS_PREFIX) || websiteURL.startsWith(Constants.HTTP_PREFIX)) && !websiteURL.includes(Constants.WWW_PREFIX)) {
      let splittedURL = websiteURL.split('//');
      if (splittedURL && splittedURL.length > 1) {
        completeWebURL = `${Constants.HTTPS_PREFIX}${Constants.WWW_PREFIX}${splittedURL[1]}`;
      }
    } else {
      completeWebURL = websiteURL;
    }

    props.openWebView(completeWebURL);
  };

  const renderLinkButton = () => {
    let websiteURL = null;
    if (socialLinks && socialLinks.length > 0 && socialLinks[socialLinks.length - 1].key == Constants.WEBSITE_LINK) {
      websiteURL = socialLinks[socialLinks.length - 1].link;
    }

    if (!websiteURL) {
      return <View></View>
    }
    const formatURL = Utils.getDomainName(websiteURL);

    return (
      <View style={styles.buttonView}>
        <TouchableOpacity
          style={[styles.buttonStyle, { backgroundColor: context.colors.textInverse, borderColor: context.colors.separators }]}
          disabled={!Utils.checkData(websiteURL)}
          onPress={() => openWebView(websiteURL)}>
          <FastImage
            style={styles.iconView}
            source={require('../../../assets/web_link_icon.png')} />
          <Text style={[context.p, { marginLeft: 8, color: context.colors.textBold }]}>{formatURL}</Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderUserActivities = () => {
    let friendsCount = 0
    let referralBonusCount = parseFloat(0).toFixed(2)
    let influenceBalance = 0
    let isUserSubscriber = false
    if (profileData) {
      const { friendCount, referralAmount, influencePoints, subscriptions } = profileData
      friendsCount = friendCount ? Utils.formatViewCount(friendCount) : 0
      referralBonusCount = Utils.checkObject(referralAmount) && referralAmount.balance ? parseFloat(referralAmount.balance).toFixed(2) : parseFloat(0).toFixed(2)
      influenceBalance = influencePoints.balance ? Utils.formatViewCount(influencePoints.balance) : 0
      isUserSubscriber = !!subscriptions?.length //For making isUseSubscriber to boolean
    }

    let giftsCount = totalGiftsSent !== null ? Utils.formatViewCount(totalGiftsSent) : '--'
    let recentCommentsCount = totalRecentComments !== null ? Utils.formatViewCount(totalRecentComments) : '--'
    let friendsCountSuffix = friendsCount != 1 ? Constants.FRIENDS : Constants.FRIEND
    let commentsCountSuffix = totalRecentComments != 1 ? Constants.COMMENTS : Constants.COMMENT

    return (
      <View style={styles.userActivityText}>
        <View style={styles.activityView}>
          <View style={styles.singleActivityView}>
            <Text style={context.pBold}>{`${friendsCount} `}</Text>
            <Text style={context.p}>{friendsCountSuffix}</Text>
          </View>
          <View style={styles.singleActivityView}>
            <Text style={context.pBold}>{isUserSubscriber ? `${giftsCount} ` : `${influenceBalance} `}</Text>
            <Text style={context.p}>{isUserSubscriber ? "Gifts sent" : Constants.INFLUENCE}</Text>
          </View>
        </View>
        <View style={[styles.activityView, { marginTop: 8 }]}>
          <View style={styles.singleActivityView}>
            <Text style={context.pBold}>{`${recentCommentsCount} `}</Text>
            <Text style={context.p}>{commentsCountSuffix}</Text>
          </View>
          <View style={styles.singleActivityView}>
            <Text style={context.pBold}>{isUserSubscriber ? `USD ${referralBonusCount} ` : `${giftsCount} `}</Text>
            <Text style={[context.p, { flex: 1 }]}>{isUserSubscriber ? "Referral" : "Gifts sent"} </Text>
          </View>
        </View>
        {!isUserSubscriber &&
          <View style={{ marginTop: 8, flexDirection: 'row' }}>
            <Text style={context.pBold}>{`USD ${referralBonusCount} `}</Text>
            <Text style={context.p}>{"Referral"}</Text>
          </View>
        }
      </View>
    );
  };

  const renderBottomSection = () => {
    const isLoggedInUser = UserSession.instance.isLoggedInUser()
    let addFriendButton = friendshipStatus === Constants.ADD_FRIEND_TITLE_CASE

    if (isGuestUserProfile && isAnotherUserProfile) {
      return
    }

    return (
      <View style={[styles.buttonView, styles.activityView]}>
        {(isAnotherUserProfile || isGuestUserProfile) &&
          <TouchableOpacity
            style={styles.activityView}
            onPress={() => { isGuestUserProfile ? props.navigateToLoginPage() : isLoggedInUser ? props.onPressHeaderButton(friendshipStatus) : props.signInSheet() }}>
            {(friendshipStatus || isGuestUserProfile) &&
              <>
                <FastImage
                  style={styles.iconView}
                  tintColor={context.colors.textBold}
                  source={isGuestUserProfile ? require('../../../assets/signin_icon.png') : addFriendButton ? require('../../../assets/invite_friends_icon.png') : require('../../../assets/friends_icon_menu.png')} />
                <Text style={[context.p, styles.iconsTextView]}>{isGuestUserProfile ? "Sign In to create your profile" : friendshipStatus}</Text>
              </>
            }
          </TouchableOpacity>
        }

        {!isAnotherUserProfile && !isGuestUserProfile &&
          <View style={[styles.activityView, { flex: 1 }]}>
            <TouchableOpacity
              style={styles.activityView}
              onPress={() => props.onPressHeaderButton(Constants.EDIT_PROFILE)}>
              <FastImage
                style={styles.iconView}
                tintColor={context.colors.textBold}
                source={require('../../../assets/edit_profile_icon.png')} />
              <Text style={[context.p, styles.iconsTextView]}>{Constants.EDIT_PROFILE}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.activityView}
              onPress={() => props.onInviteFriendButtonTap()}>
              <FastImage
                style={styles.iconView}
                tintColor={context.colors.textBold}
                source={require('../../../assets/invite_friends_icon.png')} />
              <Text style={[context.p, styles.iconsTextView]}>{Constants.INVITE_FRIENDS}</Text>
            </TouchableOpacity>
          </View>
        }
      </View >
    );
  };

  const renderHeader = () => {
    let displayName = ''
    let userGender = ''
    let aboutMe = ''
    let photoURL = ''
    if (profileData) {
      displayName = profileData.displayName ? (profileData.displayName).trim() : Constants.NON_SIGNED_IN_USER
      userGender = profileData.gender ? profileData.gender : ''
      aboutMe = profileData.aboutMe ? profileData.aboutMe : ''
      photoURL = profileData.photoURL ? profileData.photoURL : ''
    }

    if (Utils.checkData(userGender)) {
      for (const element of genderList) {
        if (element.value == userGender) {
          userGender = element.label
          break;
        }
      }
    }

    return (
      <View style={{ flex: 1 }}>
        <FastImage
          style={imageStyle()}
          resizeMode="contain"
          source={{ uri: settings.userCoverImageURL, cache: FastImage.cacheControl.web }} />
        <TouchableOpacity
          style={styles.profileImageView}
          onPress={() => props.onPressHeaderButton(Constants.EDIT_PROFILE)}
          disabled={isAnotherUserProfile || isGuestUserProfile}>
          <FastImage
            style={[roundImage, styles.roundImage, { backgroundColor: context.colors.textInverse, borderColor: context.colors.textInverse }]}
            tintColor={!photoURL ? context.colors.textBold : undefined}
            source={photoURL ? { uri: photoURL } : require('../../../assets/user_icon.png')} />
        </TouchableOpacity>
        <View style={styles.headerView}>
          <View style={[mainHeaderContainer, styles.mainContainer, { backgroundColor: context.colors.textInverse }]}>
            <View style={styles.topTextView}>
              <SelectableText textValue={displayName} textStyle={[context.h1, { paddingTop: 0 }]} multiline={true} />
              <BadgesView badgesStyle={styles.badgesStyle} badges={profileData?.badges} />
            </View>
            {(Utils.checkData(userGender)) && <Text style={[context.bodyMini, { color: context.colors.textBold, marginTop: 4 }]}>{userGender}</Text>}
            {renderUserActivities()}
            {!isGuestUserProfile && (Utils.checkData(aboutMe)) && <Text style={[context.comments, { marginTop: 12 }]}>{aboutMe}</Text>}
            {renderLinkButton()}
            {renderBottomSection()}
          </View>
        </View>
      </View>
    );
  };

  imageStyle = (w = dimensions.width, h = 210) => {
    return {
      width: w,
      aspectRatio: 40 / 21
    };
  };

  return (
    renderHeader()
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    marginBottom: 0,
    paddingBottom: navigationStyle.panelsTopMargin,
  },
  badgesStyle: {
    height: scale(20),
    width: scale(20),
  },
  profileImageView: {
    zIndex: 1,
    marginRight: dimensions.width - scale(100)
  },
  roundImage: {
    width: scale(68),
    height: scale(68),
    borderRadius: scale(34),
    borderWidth: scale(3),
    overflow: "hidden",
    marginLeft: scale(32),
    marginTop: scale(-20)
  },
  headerView: {
    marginTop: scale(-58),
    zIndex: -1
  },
  topTextView: {
    marginTop: scale(30),
    flexDirection: 'row'
  },
  buttonStyle: {
    borderWidth: 1,
    borderRadius: 8,
    paddingTop: 10,
    paddingBottom: 10,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row'
  },
  userActivityText: {
    marginTop: 12
  },
  activityView: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  iconView: {
    height: 20,
    width: 20
  },
  iconsTextView: {
    marginLeft: 8
  },
  singleActivityView: {
    flex: 0.5,
    flexDirection: 'row'
  },
  buttonView: {
    marginTop: 20
  }
});

export default UserProfileHeaderView;
