import React, { Component } from 'react'
import { Image, StyleSheet, BackHandler, Platform, Alert, Keyboard, KeyboardAvoidingView } from 'react-native'
import { View, Text } from 'native-base'
import { LoadingIndicator } from '../LoadingIndicator'
import { connect } from 'react-redux'
import { updateUserProfile, updateProfilePicURL, resendOTP, signInWithMobile, clearLoginState } from '../../redux/actions/actions'
import { Color } from '../../config/Color'
import { scale } from 'react-native-size-matters'
import TextInputComponent from '../../config/TextInputComponent'
import { SystemFont } from '../../config/Typography'
import FriendsHeaderBar from '../friendsPackage/shared/FriendsHeaderBar'
import { CommonActions } from '@react-navigation/native'
import { settings } from '../../config/settings'
import { Utils } from '../../config/Utils'
import { ScrollView } from 'react-native-gesture-handler'
import FirebaseManager from '../../config/FirebaseManager'
import { Constants } from '../../config/Constants'
import SessionManager from '../../config/SessionManager'
import { SafeAreaView } from 'react-native-safe-area-context'

class EditSocialLinks extends Component {
  
  static navigationOptions = ({ navigation }) => {
    return {
      header:(props) => <FriendsHeaderBar {...props}/>
    }
  }

  constructor(props) {
    super(props)
    this.state = {
      instagramText: "",
      facebookText: "",
      twitterText: "",
      aboutMeText: "",
      websiteText: "",
      showLoadingIndicator: false,
      maxLength: 100,
      isNextDisabled: true,
      isupdatingProfile: false,
      profileData: {},
      errors: {
        instaError: "",
        facebookError: "",
        twitterError: "",
        websiteError: "",
      }
    }

    this.socialUrlLength = 60
    this.textMaxLengthCount = 100
    this.UID = FirebaseManager.instance.currentUser().uid
    let userDetails = SessionManager.instance.getAnonymousUserDetails()
    this.completeMobileNo = userDetails ? userDetails.phoneNumber : null
    this.anonymousUserIdToken = null
    this.shouldResendOTP = false;

    this.updateProfileDetails = this.updateProfileDetails.bind(this)
    this.handleHardwareBackButton = this.handleHardwareBackButton.bind(this)
    this.onChange = this.onChange.bind(this)
    this.onInputBlur = this.onInputBlur.bind(this)
    this.checkIsFormFilled = this.checkIsFormFilled.bind(this)
    this.updateState = this.updateState.bind(this)
    this.setLoaderVisibility = this.setLoaderVisibility.bind(this)
    this.updateData = this.updateData.bind(this)
    this.saveSocialData = this.saveSocialData.bind(this)
    this.isEdited = this.isEdited.bind(this)
    this.navigateToApropiateScreen = this.navigateToApropiateScreen.bind(this)
    this.onOTPSent = this.onOTPSent.bind(this)
    this.sendOtp = this.sendOtp.bind(this)
    this.onResendOTP = this.onResendOTP.bind(this)
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps) {
      if (nextProps.navigation && nextProps.route.params) {
        if (this.state.isupdatingProfile != nextProps.route.params.isupdatingProfile) {
          this.props.navigation.setParams({
            title: nextProps.route.params.isupdatingProfile ? null : "Add Social Links"
          })
          this.setState({ isupdatingProfile: nextProps.route.params.isupdatingProfile })
        }
      }
      if (nextProps.loginScreenError && nextProps.loginScreenError != this.props.loginScreenError) {
        Utils.showError(nextProps.loginScreenError)
        this.props.clearLoginState()
      }
    }
  }

  async componentDidMount() {
    if (this.props.userDetails) {
      this.updateProfileDetails(this.props.userDetails)
    }
    this.props.navigation.setParams({
      leftButtonText: "Back",
      rightButtonText: "Next",
      isRightDisabled: this.state.isNextDisabled,
      isLoading: this.state.showLoadingIndicator,
      onRightClick: this.saveSocialData,
      title: this.state.isupdatingProfile ? null : "Add Social Links"
    })
  }

  UNSAFE_componentWillMount() {
    BackHandler.addEventListener('hardwareBackPress', this.handleHardwareBackButton);
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.handleHardwareBackButton);
  }

  handleHardwareBackButton() {
    this.props.navigation.dispatch(CommonActions.goBack())
    return true
  }

  onChange(key, value) {
    this.setState({ [key]: value }, () => {
      //this.onInputBlur(key);
    })
  }

  onInputBlur(key) {
    const { isDisable, errorObj } = this.checkIsFormFilled()
    this.props.navigation.setParams({
      isRightDisabled: isDisable
    })
    this.setState({ isNextDisabled: isDisable, errors: errorObj });
  }

  checkIsFormFilled() {
    var isDisable = false;
    var errorObj = {
      websiteError: "",
      instaError: "",
      facebookError: "",
      twitterError: "",
    }

    const { websiteText, instagramText, facebookText, twitterText, aboutMeText } = this.state;
    if (websiteText && (!Utils.isValidURL(websiteText))) {
      isDisable = true;
      errorObj["websiteError"] = "Website link is not valid"
    }
    if (instagramText && Utils.isInvalidSocialId(instagramText)) {
      isDisable = true;
      if (instagramText.indexOf(' ') >= 0) {
        errorObj["instaError"] = "Invalid Instagram Username"
      } else {
        errorObj["instaError"] = "Only require Instagram Username"
      }
    }
    if (facebookText && Utils.isInvalidSocialId(facebookText)) {
      isDisable = true;
      if (facebookText.indexOf(' ') >= 0) {
        errorObj["facebookError"] = "Invalid Facebook Username"
      } else {
        errorObj["facebookError"] = "Only require Facebook Username"
      }
    }
    if (twitterText && Utils.isInvalidSocialId(twitterText)) {
      isDisable = true;
      if (twitterText.indexOf(' ') >= 0) {
        errorObj["twitterError"] = "Invalid Twitter Handle"
      } else {
        errorObj["twitterError"] = "Only require Twitter Handle"
      }
    }
    return { isDisable, errorObj };
  }

  updateState(key, value) {
    this.setState({ [key]: value }, () => {
      this.onInputBlur()
    })
  }

  setLoaderVisibility(value) {
    this.setState({ showLoadingIndicator: value }, () => {
      this.props.navigation.setParams({
        isLoading: this.state.showLoadingIndicator,
      })
    })
  }

  updateProfileDetails(data) {
    this.setState({ profileData: data }, () => {
      this.updateData(this.state.profileData)
    })
  }

  async updateData(userData) {
    let data = null
    if (userData) {
      data = { ...userData }
    }

    if (data) {
      if (data.socialLinks && data.socialLinks.length > 0) {
        for (const key in data.socialLinks) {
          const isWebsiteIndex = (key == data.socialLinks.length - 1)
          if (data.socialLinks[key] != null && data.socialLinks[key].includes(settings.INSTAGRAM_WEB) && !isWebsiteIndex) {
            const length = Constants.INSTAGRAM_DOMAIN_URL.length
            this.setState({ instagramText: data.socialLinks[key].slice(length, data.socialLinks[key].length) })
          } else if (data.socialLinks[key] != null && data.socialLinks[key].includes(settings.FACEBOOK_WEB) && !isWebsiteIndex) {
            const length = Constants.FACEBOOK_DOMAIN_URL.length
            this.setState({ facebookText: data.socialLinks[key].slice(length, data.socialLinks[key].length) })
          } else if (data.socialLinks[key] != null && data.socialLinks[key].includes(settings.TWITTER_WEB) && !isWebsiteIndex) {
            const length = Constants.TWITTER_DOMAIN_URL.length
            this.setState({ twitterText: data.socialLinks[key].slice(length, data.socialLinks[key].length) })
          } else if (data.socialLinks[key] != null && isWebsiteIndex) {
            this.setState({ websiteText: data.socialLinks[key] })
          }
        }
      }

      this.setState({ aboutMeText: data.aboutMe ? data.aboutMe : '' }, () => {
        this.onInputBlur()
      })
    }

    this.setLoaderVisibility(false)
  }

  saveSocialData() {    
    Keyboard.dismiss()
    const { isDisable, errorObj } = this.checkIsFormFilled()
    if (!isDisable) {
      this.setState({ isNextDisabled: isDisable, errors: errorObj, showLoadingIndicator: true }, () => {
        this.props.navigation.setParams({
          isLoading: true
        })
      });

      const { instagramText, facebookText, twitterText, aboutMeText, websiteText } = this.state;
      let data = {
        data: {
          instagramLink: instagramText ? (Constants.INSTAGRAM_DOMAIN_URL + instagramText) : null,
          facebookLink: facebookText ? (Constants.FACEBOOK_DOMAIN_URL + facebookText) : null,
          twitterLink: twitterText ? (Constants.TWITTER_DOMAIN_URL + twitterText) : null,
          websiteLink: websiteText,
          aboutMe: aboutMeText,
        }
      }

      if (!this.isEdited(data.data)) {
        this.navigateToApropiateScreen(data)
        return
      }

      this.props.updateUserProfile(data.data, this.navigateToApropiateScreen, true)
    } else{
      this.setState({ isNextDisabled: isDisable, errors: errorObj });
    }
  }

  isEdited(data) {
    let oldData = this.props.userDetails
    let instagramText = null
    let facebookText = null
    let twitterText = null
    let websiteText = ''
    let aboutMe = oldData.aboutMe ? oldData.aboutMe : ''
    if (oldData.socialLinks && oldData.socialLinks.length > 0) {
      for (const key in data.socialLinks) {
        const isWebsiteIndex = (key == data.socialLinks.length - 1)
        if (data.socialLinks[key] != null && data.socialLinks[key].includes(settings.INSTAGRAM_WEB) && !isWebsiteIndex) {
          const length = Constants.INSTAGRAM_DOMAIN_URL.length
          instagramText = data.socialLinks[key].slice(length, data.socialLinks[key].length)
        } else if (data.socialLinks[key] != null && data.socialLinks[key].includes(settings.FACEBOOK_WEB) && !isWebsiteIndex) {
          const length = Constants.FACEBOOK_DOMAIN_URL.length
          facebookText = data.socialLinks[key].slice(length, data.socialLinks[key].length)
        } else if (data.socialLinks[key] != null && data.socialLinks[key].includes(settings.TWITTER_WEB) && !isWebsiteIndex) {
          const length = Constants.TWITTER_DOMAIN_URL.length
          twitterText = data.socialLinks[key].slice(length, data.socialLinks[key].length)
        } else if (data.socialLinks[key] != null && isWebsiteIndex) {
          websiteText = data.socialLinks[key]
        }
      }
    }

    let socialLinksData = {
      instagramLink: instagramText,
      facebookLink: facebookText,
      twitterLink: twitterText,
      websiteLink: websiteText,
      aboutMe: aboutMe
    }

    let oldSocialLinksData = JSON.stringify(socialLinksData)
    let editedSocialLinksData = JSON.stringify(data)

    return oldSocialLinksData != editedSocialLinksData
  }

  async navigateToApropiateScreen(data) {
    if (data) {
      let isAnonymous = FirebaseManager.instance.isUserAnonymous()
      let isProfileNotEmpty = (this.state.profileData && (this.state.profileData.displayName || this.state.profileData.gender))
      if (isProfileNotEmpty && !isAnonymous) {
        const title = Platform.OS == "android" ? null : ""
        Alert.alert(
          title,
          "Profile Updated",
          [
            {
              text: "OK",
              onPress: () => {
                Utils.navigateToSubRouteWithParams(Constants.DRAWER_HOME, Constants.USER_PROFILE_SCREEN, this.props)
              }
            },
          ],
          { cancelable: false }
        )
      } else if (!isProfileNotEmpty || isAnonymous) {
        this.sendOtp()
      }
    }
    this.setLoaderVisibility(false)
  }

  onOTPSent(verificationData) {    
    this.props.navigation.navigate(Constants.OTP_SCREEN, { mobileNo: this.completeMobileNo, verificationId: verificationData.verificationId, code: verificationData.code, isAutoVerified: verificationData.isAutoVerified, anonyIdToken: this.anonymousUserIdToken, onResendOTP: this.onResendOTP, isForLoginProcess: true })
  }

  async sendOtp() {
    this.anonymousUserIdToken = await FirebaseManager.instance.currentUser().getIdToken(); // Anonymous user ID Token if user already linked
    if (this.shouldResendOTP) {
      this.props.resendOTP(this.completeMobileNo, this.onOTPSent)
      this.shouldResendOTP = false;
    } else {
      this.props.signInWithMobile(this.completeMobileNo, this.onOTPSent)
    }
  }

  onResendOTP() {
    this.shouldResendOTP = true;
  }

  render() {
    let aboutMeLength = this.state.aboutMeText ? this.state.aboutMeText.length : 0
    let keyboardBehavior = Platform.OS == 'ios' ? 'padding' : 'undefined'
    return (
      <SafeAreaView style={styles.viewStyle}>
        <KeyboardAvoidingView style={{ flex: 1 }} behavior={keyboardBehavior} keyboardVerticalOffset={80}>
          <ScrollView style={{ flex: 1, marginBottom: scale(20) }}>
            <View style={{ marginTop: scale(15) }}>
              <View style={styles.linksContainer}>
                <Image style={styles.navigationIcons(this.state.errors["websiteError"] != '' ? true : false)} source={require('../../../assets/icons_link.png')} />
                <TextInputComponent
                  inputRef={(r) => { this.websiteTextInput = r }}
                  refState="websiteText"
                  maxLength={this.socialUrlLength}
                  selectionColor={Color.RED_TEXT_COLOR}
                  onChange={(refState, text) => {
                    this.onChange(refState, text);
                  }}
                  returnKeyType={'next'}
                  onSubmitEditing={() => { this.instagramTextInput.focus() }}
                  value={this.state.websiteText}
                  keyboardType="default"
                  textColor={Color.BLACK_COLOR}
                  fontSize={Platform.OS == "ios" ? scale(15) : scale(14)}
                  autoCapitalize={'none'}
                  placeHolder={"Website"}
                  errorText={this.state.errors["websiteError"]}
                  textInputStyle={styles.searchInput}
                  placeholderTextColor={Color.LIGHT_GREY}
                />
              </View>
              <View style={[styles.linksContainer, { marginTop: scale(10) }]}>
                <Image style={styles.navigationIcons(this.state.errors["instaError"] != '' ? true : false)} source={require('../../../assets/icons_instagram.png')} />
                <TextInputComponent
                  inputRef={(r) => { this.instagramTextInput = r }}
                  refState="instagramText"
                  maxLength={this.socialUrlLength}
                  selectionColor={Color.RED_TEXT_COLOR}
                  onChange={(refState, text) => {
                    this.onChange(refState, text);
                  }}
                  returnKeyType={'next'}
                  onSubmitEditing={() => { this.twitterTextInput.focus() }}
                  value={this.state.instagramText}
                  keyboardType="default"
                  textColor={Color.BLACK_COLOR}
                  fontSize={Platform.OS == "ios" ? scale(15) : scale(14)}
                  autoCapitalize={'none'}
                  placeHolder={"Instagram Username"}
                  errorText={this.state.errors["instaError"]}
                  textInputStyle={styles.searchInput}
                  placeholderTextColor={Color.LIGHT_GREY}
                />
              </View>
              <View style={[styles.linksContainer, { marginTop: scale(10) }]}>
                <Image style={styles.navigationIcons(this.state.errors["twitterError"] != '' ? true : false)} source={require('../../../assets/icons_twitter.png')} />
                <TextInputComponent
                  inputRef={(r) => { this.twitterTextInput = r }}
                  refState="twitterText"
                  maxLength={this.socialUrlLength}
                  selectionColor={Color.RED_TEXT_COLOR}
                  onChange={(refState, text) => {
                    this.onChange(refState, text);
                  }}
                  returnKeyType={'next'}
                  onSubmitEditing={() => { this.facebookTextInput.focus() }}
                  value={this.state.twitterText}
                  keyboardType="default"
                  textColor={Color.BLACK_COLOR}
                  fontSize={Platform.OS == "ios" ? scale(15) : scale(14)}
                  placeHolder={"Twitter Handle"}
                  errorText={this.state.errors["twitterError"]}
                  autoCapitalize={'none'}
                  textInputStyle={styles.searchInput}
                  placeholderTextColor={Color.LIGHT_GREY}
                />
              </View>
              <View style={[styles.linksContainer, { marginTop: scale(10) }]}>
                <Image style={styles.navigationIcons(this.state.errors["facebookError"] != '' ? true : false)} source={require('../../../assets/icons_facebook.png')} />
                <TextInputComponent
                  inputRef={(r) => { this.facebookTextInput = r }}
                  refState="facebookText"
                  maxLength={this.socialUrlLength}
                  selectionColor={Color.RED_TEXT_COLOR}
                  onChange={(refState, text) => {
                    this.onChange(refState, text);
                  }}
                  returnKeyType={'next'}
                  onSubmitEditing={() => { this.aboutMeTextInput.focus() }}
                  value={this.state.facebookText}
                  keyboardType="default"
                  textColor={Color.BLACK_COLOR}
                  fontSize={Platform.OS == "ios" ? scale(15) : scale(14)}
                  placeHolder={"Facebook Username"}
                  autoCapitalize={'none'}
                  errorText={this.state.errors["facebookError"]}
                  textInputStyle={styles.searchInput}
                  placeholderTextColor={Color.LIGHT_GREY}
                />
              </View>
              <View style={{ flex: 1, marginTop: scale(40), marginStart: scale(10), marginEnd: scale(10) }}>

                <TextInputComponent
                  inputRef={(r) => { this.aboutMeTextInput = r }}
                  maxLength={this.state.maxLength}
                  refState="aboutMeText"
                  selectionColor={Color.RED_TEXT_COLOR}
                  onChange={(refState, text) => {
                    this.onChange(refState, text)
                  }}
                  fontSize={Platform.OS == "ios" ? scale(15) : scale(14)}
                  value={this.state.aboutMeText}
                  placeHolder={"Brief bio"}
                  placeholderTextColor={Color.LIGHT_GREY}
                  textColor={Color.BLACK_COLOR}
                  textInputStyle={styles.aboutMeText}
                  inputTextLabel={<Text>About Me</Text>}
                  inputTextLabelStyle={styles.inputTextLabelStyle}
                />

              </View>
              <Text style={styles.messageLengthStyle}>
                {aboutMeLength + "/" + this.textMaxLengthCount}
              </Text>
            </View>

          </ScrollView>
        </KeyboardAvoidingView>
        {/* <Button
          variant='solid'
          style={styles.nextButton(this.state.isNextDisabled)}
          onPress={() => this.saveSocialData()}
          disabled={this.state.isNextDisabled}
        >
          <Text uppercase={false} style={styles.nextButtonText}>Next</Text>
        </Button> */}
        {this.state.showLoadingIndicator && <LoadingIndicator />}
      </SafeAreaView>
    )
  }
}

const styles = StyleSheet.create({
  viewStyle: {
    flex: 1,
    marginStart: scale(10),
    marginEnd: scale(10),
  },
  personalMsgText: {
    marginBottom: scale(10),
    color: Color.BLACK_COLOR,
    fontFamily: SystemFont.SELECTED_FONT,
    textAlign: "left",
    fontSize: Platform.OS == "ios" ? scale(13) : scale(12),
  },
  linksContainer: {
    height: scale(60),
    marginTop: scale(10),
    flexDirection: "row",
    justifyContent: 'center',
    borderRadius: scale(10),
    borderWidth: 0.5,
    borderColor: "transparent",
    alignItems: 'center',
  },
  searchInput: {
    width: "100%",
    height: 40,
  },
  navigationIcons: (isError) => {
    return {
      marginLeft: 10,
      width: scale(25),
      height: scale(25),
      opacity: 1,
      tintColor: Color.BLACK_COLOR,
      marginTop: isError ? scale(10) : 0,
      alignSelf: isError ? "flex-start" : 'center',
    }
  },
  aboutMeText: {
    flex: 1,
    width: "100%",
    paddingStart: scale(0),
    borderColor: Color.BLACK_COLOR,
    borderBottomWidth: 0.5,
    height: Platform.OS === 'ios' ? scale(30) : scale(35),
    paddingBottom: 0,
  },
  inputTextLabelStyle: {
    marginBottom: scale(15),
    marginLeft: scale(4),
    fontFamily: SystemFont.SELECTED_FONT,
    textAlign: 'left',
    fontSize: Platform.OS == 'ios' ? scale(13) : scale(12),
  },
  messageLengthStyle: {
    color: Color.DESCRIPTION_FONT_COLOR,
    fontFamily: SystemFont.SELECTED_FONT,
    fontSize: Platform.OS == "ios" ? scale(13) : scale(12),
    textAlign: "right",
    marginTop: scale(3),
    marginRight: scale(10),
  },
  signUpText: {
    textAlign: 'center',
    color: Color.LIGHT_GREY,
    fontFamily: SystemFont.SELECTED_FONT,
    fontWeight: 'bold',
    fontSize: scale(20),
    marginTop: scale(15)
  },
  profileText: {
    alignSelf: 'center',
    color: Color.BLACK_COLOR,
    fontFamily: SystemFont.SELECTED_FONT,
    fontWeight: 'bold',
    fontSize: scale(22),
    marginTop: scale(25)
  },
  stepsTextStyle: {
    alignSelf: 'center',
    color: Color.LIGHT_GREY,
    fontSize: scale(16),
    fontFamily: SystemFont.SELECTED_FONT,
    marginTop: scale(12),
    fontWeight: 'bold'
  },
  textStyle: {
    alignSelf: 'flex-start',
    color: Color.BLACK_COLOR,
    fontSize: Platform.OS == 'ios' ? scale(13) : scale(12),
    fontFamily: SystemFont.SELECTED_FONT,
    marginLeft: scale(5),
  },
  nextButton: (isNextDisabled) => {
    return {
      marginLeft: scale(5),
      height: scale(40),
      width: "40%",
      backgroundColor: isNextDisabled ? Color.DARK_GREY : Color.NEXT_BUTTON_COLOR,
      alignSelf: 'center',
      marginBottom: scale(10)
    }
  },
  nextButtonText: {
    fontSize: scale(19),
    fontWeight: '400',
    color: 'white',
    alignSelf: 'center',
  },
  profileTextStyle: {
    alignSelf: 'center',
    color: Color.RED_TEXT_COLOR,
    fontSize: Platform.OS == 'ios' ? scale(13) : scale(12),
    fontFamily: SystemFont.SELECTED_FONT,
    marginTop: scale(10),
  }
});

const mapStateToProps = (state) => {
  return {
    isLogInProcess: state.loginInfo.isLogInProcess,
    isAnonymous: state.loginInfo.isAnonymous,
    userDetails: state.loginInfo.userDetails,    
    loginScreenError: state.loginInfo.loginScreenError,
  }
}

const mapDispatchToProps = (dispatch) => {
  return {
    updateUserProfile(data, callback, callGetUserDetails) {
      dispatch(
        updateUserProfile(data, callback, callGetUserDetails)
      )
    },
    updateProfilePicURL(data, callback) {
      dispatch(
        updateProfilePicURL(data, callback)
      )
    },
    signInWithMobile(mobileNo, callback) {
      dispatch(
        signInWithMobile(mobileNo, callback)
      )
    },
    resendOTP(mobileNo, callback) {
      dispatch(
        resendOTP("loginScreen", mobileNo, callback)
      )
    },
    clearLoginState() {
      dispatch(
        clearLoginState()
      )
    }
  }
}
export default connect(mapStateToProps, mapDispatchToProps)(EditSocialLinks)