import React, { Component } from 'react'
import { Image, StyleSheet, TouchableOpacity, BackHandler, Platform, Alert, Keyboard, KeyboardAvoidingView, TextInput, SafeAreaView } from 'react-native'
import { View, Text, Button } from 'native-base'
import { LoadingIndicator } from '../LoadingIndicator'
import { connect } from 'react-redux'
import { updateUserProfile, updateProfilePicURL, removeProfileImage } from '../../redux/actions/actions'
import { Color } from '../../config/Color'
import TextInputComponent from '../../config/TextInputComponent'
import { ProfileImagePicker } from '../../config/ProfileImagePicker'
import { CommonActions } from '@react-navigation/native'
import { settings } from '../../config/settings'
import { Utils } from '../../config/Utils'
import { ScrollView } from 'react-native-gesture-handler'
import FirebaseManager from '../../config/FirebaseManager'
import FileCache from '../../config/FileCache'
import { Constants } from '../../config/Constants'
import SessionManager from '../../config/SessionManager'
import { ThemeContext } from '../../Contexts'
import SigningHeaderBar from '../friendsPackage/shared/SigningHeaderBar'
import SignUpPageInfo from './SignUpPageInfo'
import { Dropdown } from 'react-native-element-dropdown'
import ErrorTextMsg from '../ErrorTextMsg'
import FastImage from 'react-native-fast-image'
import UserSession from '../../config/UserSession'

class EditProfileScreen extends Component {

  static navigationOptions = ({ navigation }) => {
    return {
      header: (props) => <SigningHeaderBar {...props} />
    }
  }

  constructor(props) {
    super(props)

    let profilePicText = "Choose Profile Picture"
    if (this.props.userDetails && this.props.userDetails.photoURL) {
      profilePicText = "Edit Profile Picture"
    }

    this.state = {
      isNextDisabled: true,
      displayName: '',
      gender: null,
      userProfilePicURL: null,
      showLoadingIndicator: false,
      isupdatingProfile: false,
      aboutMeText: "",
      websiteText: "",
      showWebsiteError: false,
      isNameFieldFocused: false,
      isPronounsFieldFocused: false,
      isWebsiteFieldFocused: false,
      isBioFieldFocused: false,
      showBioFieldError: false
    }

    const params = this.props.route.params
    this.fromSignUp = params && params.fromSignUp

    this.genderList = [
      {
        label: "She/Her", value: "she",
      },
      {
        label: "He/Him", value: "he",
      },

      {
        label: "They/Them", value: "they",
      },
      {
        label: "Other", value: "other",
      },
      {
        label: "I prefer not to say", value: "i_prefer_not_to_say",
      }
    ]

    this.socialUrlLength = 60
    this.aboutMeMaxLength = 100
    this.UID = FirebaseManager.instance.currentUser().uid
    this.profileText = profilePicText
    this.updateProfileDetails = this.updateProfileDetails.bind(this)
    this.handleHardwareBackButton = this.handleHardwareBackButton.bind(this)
    this.onChange = this.onChange.bind(this)
    this.onInputBlur = this.onInputBlur.bind(this)
    this.checkIsFormFilled = this.checkIsFormFilled.bind(this)
    this.updateState = this.updateState.bind(this)
    this.setLoaderVisibility = this.setLoaderVisibility.bind(this)
    this.getProfileImage = this.getProfileImage.bind(this)
    this.saveUserProfile = this.saveUserProfile.bind(this)
    this.isEdited = this.isEdited.bind(this)
    this.saveInSharedPref = this.saveInSharedPref.bind(this)
    this.tapOnGenderRow = this.tapOnGenderRow.bind(this)
    this.updateGender = this.updateGender.bind(this)
    this.selectFileModal = this.selectFileModal.bind(this)
    this.choseFromGallery = this.choseFromGallery.bind(this)
    this.clickFromCamera = this.clickFromCamera.bind(this)
    this.uploadProfileImage = this.uploadProfileImage.bind(this)
    this.renderButton = this.renderButton.bind(this)
    this.renderDropdownItem = this.renderDropdownItem.bind(this)
    this.onProfileImageDeleted = this.onProfileImageDeleted.bind(this)
    this.updateWebsiteData = this.updateWebsiteData.bind(this)
    this.onDeleteImagePress = this.onDeleteImagePress.bind(this)
    this.navigateToAppropiateScreen = this.navigateToAppropiateScreen.bind(this)
    this.onButtonClick = this.onButtonClick.bind(this)
    this.handleProfileImage = this.handleProfileImage.bind(this)
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.route.params) {
      if (this.state.isupdatingProfile != nextProps.route.params.isupdatingProfile) {
        this.props.navigation.setParams({
          title: nextProps.route.params.isupdatingProfile ? Constants.EDIT_PROFILE : Constants.SIGN_UP
        })
        this.setState({ isupdatingProfile: nextProps.route.params.isupdatingProfile })
      }
    }
  }

  UNSAFE_componentWillMount() {
    BackHandler.addEventListener('hardwareBackPress', this.handleHardwareBackButton);
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.handleHardwareBackButton);
  }

  handleHardwareBackButton() {
    if (!this.fromSignUp) {
      this.props.navigation.dispatch(CommonActions.goBack())
    }
    return true
  }

  async componentDidMount() {
    if (this.props.userDetails) {
      this.updateProfileDetails(this.props.userDetails)
    }
    this.props.navigation.setParams({
      leftButtonText: this.fromSignUp ? null : "Back",
      onBackPress: this.handleHardwareBackButton,
      rightButtonText: "Next",
      isRightDisabled: this.state.isNextDisabled,
      isLoading: this.state.showLoadingIndicator,
      onRightClick: this.saveUserProfile,
      title: this.fromSignUp ? Constants.SIGN_UP : Constants.EDIT_PROFILE
    })

    const params = this.props.route.params
    if (params && params.fromEmailSignup) {
      Utils.showToast("Your email address is verified.", "bottom", "emailValid", 5000)
    }
  }

  updateWebsiteData(userData) {
    if (userData) {
      if (userData.socialLinks && userData.socialLinks.length > 0) {
        for (const key in userData.socialLinks) {
          const isWebsiteIndex = (key == userData.socialLinks.length - 1)
          if (userData.socialLinks[key] != null && isWebsiteIndex) {
            return userData.socialLinks[key]
          }
        }
      }
    }
    return ''
  }

  async updateProfileDetails(userData) {
    let data = null
    if (userData) {
      data = { ...userData }
    }

    if (data) {
      let userWebsiteText = this.updateWebsiteData(data)
      let userName = data.displayName.trim() == "" ? UserSession.instance.getUserLastFourMobileNoDigit() : data.displayName
      this.setState({ displayName: userName, userProfilePicURL: data.photoURL, aboutMeText: data.aboutMe, websiteText: userWebsiteText })
      let selGenderObj = null
      for (const element of this.genderList) {
        if (element.value == data.gender) {
          selGenderObj = element
          break;
        }
      }
      this.tapOnGenderRow(selGenderObj)
    }
    this.setLoaderVisibility(false)
  }

  onChange(key, value) {
    this.setState({ [key]: value }, () => {
      this.onInputBlur(key);
    })
  }

  onInputBlur() {
    const isDisable = this.checkIsFormFilled();
    this.props.navigation.setParams({
      isRightDisabled: isDisable
    })
    this.setState({ isNextDisabled: isDisable });
  }

  checkIsFormFilled() {
    var isDisable = false;
    const { displayName, gender } = this.state;
    if (!displayName || displayName.trim() == '') {
      isDisable = true;
    }

    let aboutMeLength = this.state.aboutMeText ? this.state.aboutMeText.length : 0
    if (aboutMeLength > this.aboutMeMaxLength) {
      this.setState({ showBioFieldError: true })
      isDisable = true
    } else {
      this.setState({ showBioFieldError: false })
    }
    return isDisable;
  }

  updateState(key, value) {
    this.setState({ [key]: value }, () => {
      this.onInputBlur()
    })
  }

  setLoaderVisibility(value) {
    this.setState({ showLoadingIndicator: value }, () => {
      this.props.navigation.setParams({
        isLoading: this.state.showLoadingIndicator,
      })
    })
  }

  getProfileImage(userProfilePicURL = null) {
    if (userProfilePicURL) {
      this.profileText = "Edit Profile Picture"
      this.updateState("userProfilePicURL", userProfilePicURL)
    }
  }

  // saveImageLocally = (data) => {
  //   ProfileImagePicker.saveImageLocally(this.UID, data)
  //   this.updateState("userProfilePicURL", data)
  // }

  async navigateToAppropiateScreen(data) {
    if (data) {
      if (this.fromSignUp && SessionManager.instance.getIsInviteLinkLogin()) {
        SessionManager.instance.setIsInviteLinkLogin(false)
        Utils.navigateToFriendRequests(null)
      } else if (!this.fromSignUp) {
        const title = Platform.OS == "android" ? null : ""
        Alert.alert(
          title,
          "Profile Updated",
          [
            {
              text: "OK",
              onPress: () => {
                Utils.navigateToSubRouteWithParams(Constants.DRAWER_HOME, Constants.USER_PROFILE_SCREEN, this.props)
              }
            },
          ],
          { cancelable: false }
        )
      } else {
        let params = { isForLoginProcess: this.state.isForLoginProcess, fromSignUp: this.fromSignUp }
        Utils.navigateToDrawerLoginRoute(this.props, Constants.CONFIRMATION_SCREEN, params)
      }
    }
    this.setLoaderVisibility(false)
  }

  saveUserProfile() {
    this.setLoaderVisibility(true)
    Keyboard.dismiss()
    if (!this.checkIsFormFilled()) {
      const { displayName, gender, aboutMeText, websiteText } = this.state;
      let data = { "data": { "displayName": displayName ? displayName.trim() : displayName, "gender": gender && gender.value ? gender.value : gender, "aboutMe": aboutMeText ? aboutMeText.trim() : aboutMeText, "websiteLink": websiteText } }

      if (!this.isEdited(data.data)) {
        this.navigateToAppropiateScreen(data)
        return
      }

      this.props.updateUserProfile(data.data, this.saveInSharedPref, true)
    }
  }

  isEdited(data) {
    if (!Utils.checkData(this.props.userDetails.displayName)) {
      return true
    }

    let oldData = this.props.userDetails
    let websiteLink = ''
    if (oldData.socialLinks && oldData.socialLinks.length > 0) {
      for (const key in oldData.socialLinks) {
        const isWebsiteIndex = (key == oldData.socialLinks.length - 1)
        if (oldData.socialLinks[key] != null && !oldData.socialLinks[key].includes(settings.TWITTER_WEB) && !oldData.socialLinks[key].includes(settings.FACEBOOK_WEB) && !oldData.socialLinks[key].includes(settings.INSTAGRAM_WEB) && isWebsiteIndex) {
          websiteLink = oldData.socialLinks[key]
        }
      }
    }

    let profileData = {
      displayName: oldData.displayName ? oldData.displayName : '',
      gender: oldData.gender ? oldData.gender : '',
      aboutMe: oldData.aboutMe ? oldData.aboutMe : '',
      websiteLink: websiteLink ? websiteLink : ''
    }

    let oldProfileData = JSON.stringify(profileData)
    let editedProfileData = JSON.stringify(data)

    return oldProfileData != editedProfileData
  }

  async saveInSharedPref(data) {
    if (data) {
      let userDetails = SessionManager.instance.getAnonymousUserDetails()
      let updatedData = { ...data, ...userDetails }
      SessionManager.instance.saveAnonymousUserDetails(updatedData)
      this.navigateToAppropiateScreen(data)
    }
  }

  tapOnGenderRow(item, fetchedGender = null) {
    let genderItem = item
    if (this.genderList.length > 0) {
      for (const key in this.genderList) {
        let genderValue = this.genderList[key].value
        if (genderItem && genderItem.value == genderValue) {
          if (this.genderList[key].isSelected) {
            this.genderList[key].isSelected = false
            genderItem = null
          } else {
            this.genderList[key].isSelected = true
          }
        } else {
          this.genderList[key].isSelected = false
        }
      }
    }
    this.setState({ isPronounsFieldFocused: false })
    this.updateGender(genderItem)
  }

  updateGender(itemValue) {
    this.updateState("gender", itemValue)
  }

  // getGenderLabel = (savedGender) => {
  //   for (const key in this.genderList) {
  //     let value = this.genderList[key].value
  //     if (savedGender == value) {
  //       return this.genderList[key]
  //     }
  //   }
  // }

  selectFileModal() {
    const { userProfilePicURL } = this.state
    let buttons = [
      {
        text: "Take a Picture", onPress: () => {
          this.clickFromCamera()
        }
      },
      {
        text: "Choose a Picture", onPress: () => {
          this.choseFromGallery()
        }
      },
      {
        text: "No, Thanks", onPress: () => {

        }
      }
    ];
    if (Utils.checkData(userProfilePicURL)) {
      buttons = [
        {
          text: "Take a Picture", onPress: () => {
            this.clickFromCamera()
          }
        },
        {
          text: "Choose a Picture", onPress: () => {
            this.choseFromGallery()
          }
        },
        {
          text: "Delete Profile Picture", onPress: () => {
            this.onDeleteImagePress()
          }
        },
        {
          text: "Cancel", onPress: () => {

          }
        }
      ]
    }
    if (Platform.OS == 'android') {
      if (Utils.checkData(userProfilePicURL)) {
        buttons.splice(buttons.length - 1, 1)
      }
      buttons.reverse()
    }

    Alert.alert('', 'Profile Picture', buttons, { cancelable: true });
  }

  onDeleteImagePress() {
    Alert.alert('', 'Are you sure you want to delete your profile picture?',
      [{
        text: "Cancel", onPress: () => {
        }
      },
      {
        text: "Delete", onPress: () => {
          this.props.removeProfileImage(this.onProfileImageDeleted)
        }
      }]
    )
  }

  async handleProfileImage(image) {
    if (!image) {
      return;
    }

    if (image.size >= 2 * 1024 * 1024) {
      Alert.alert('', 'Image size should not exceed 2 MB.');
      return;
    }
    this.profileText = "Edit Profile Picture"
    this.updateState("userProfilePicURL", image.path)
    this.uploadProfileImage(image, image.path)
  }

  async choseFromGallery() {
    let image = await ProfileImagePicker.openImagePicker()
    this.handleProfileImage(image)
  }
  x
  onProfileImageDeleted(data) {
    if (data.success) {
      this.profileText = "Choose Profile Picture"
      this.updateState("userProfilePicURL", null)
    }
  }

  renderButton(item) {
    const { isNextDisabled } = this.state
    const isSaveButton = (item == Constants.SAVE)

    return (
      <View style={!this.fromSignUp ? [styles.buttonStyle, { width: '46%' }] : styles.buttonStyle}>
        <Button
          variant='solid'
          disabled={isNextDisabled && isSaveButton}
          style={[styles.verifyButton, { borderColor: !isSaveButton ? this.context.colors.separators : isNextDisabled ? this.context.colors.separators : this.context.colors.logoRed, backgroundColor: !isSaveButton ? this.context.colors.textInverse : isNextDisabled ? this.context.colors.separators : this.context.colors.logoRed }]}
          onPress={() => this.onButtonClick(item)}>
          <Text style={[this.context.pBold, { color: !isSaveButton ? this.context.colors.text : this.context.colors.textInverse }]}>{item}</Text>
        </Button>
      </View>
    )
  }

  onButtonClick(item = null) {
    if (item == Constants.SAVE) {
      this.saveUserProfile()
    } else {
      this.handleHardwareBackButton()
    }
  }

  async clickFromCamera() {
    let image = await ProfileImagePicker.openCamera()
    this.handleProfileImage(image)
  }

  renderDropdownItem(item) {
    let isSelectedGender = item.isSelected

    return (
      <View style={[styles.dropdownItemView, { backgroundColor: isSelectedGender ? Color.SELECTED_DROPDOWN : this.context.colors.textInverse }]}>
        <View style={{ marginLeft: 16, marginRight: 16, marginTop: 12, marginBottom: 12, flexDirection: 'row', justifyContent: 'space-between' }}>
          <Text style={[this.context.p, { color: this.context.colors.textBold, }]}>{item.label}</Text>
          {isSelectedGender &&
            <Text style={[this.context.p, { color: this.context.colors.textBold }]}>✓</Text>
          }
        </View>
      </View>
    )
  }

  async uploadProfileImage(profileImageData, userProfilePicURL) {
    let profilePicName = profileImageData.filename ? profileImageData.filename : Platform.OS === 'ios' ? `userProfilePic_${profileImageData.creationDate}` : `userProfilePic_${profileImageData.modificationDate}`

    let base64Image = "data:image/jpg;base64," + await FileCache.default.getBase64Content(userProfilePicURL);
    let data = { "data": { "image": base64Image, "fileName": profilePicName } }
    this.props.updateProfilePicURL(data.data, this.getProfileImage)
  }

  render() {
    const { userProfilePicURL, gender, aboutMeText, websiteText, displayName, isNameFieldFocused, isPronounsFieldFocused, isWebsiteFieldFocused, isBioFieldFocused, showBioFieldError, showWebsiteError } = this.state
    let aboutMeLength = this.state.aboutMeText ? this.state.aboutMeText.length : 0

    return (
      <KeyboardAvoidingView
        behavior={Platform.OS == 'ios' ? "padding" : undefined}
        style={{ flex: 1 }}
        keyboardVerticalOffset={80}>
        <SafeAreaView style={styles.viewStyle}>
          <ScrollView style={{ flex: 1 }}>
            <View style={{ flex: 1, marginLeft: 20, marginRight: 20 }}>
              {this.fromSignUp && <SignUpPageInfo signUpStep={3} />}
              {this.fromSignUp && <Text style={this.context.h2}>{Constants.CREATE_PROFILE}</Text>}
              <TouchableOpacity
                style={styles.profileImageView}
                onPress={() => this.selectFileModal()}>
                <Image style={[styles.avatar, { backgroundColor: this.context.colors.textInverse, borderColor: userProfilePicURL ? this.context.colors.chatBubbles : 'transparent' }]} source={userProfilePicURL ? { uri: userProfilePicURL } : require('../../../assets/user.png')} />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.profileTextStyle}
                onPress={() => this.selectFileModal()}>
                <Text style={[this.context.bodyMini, { color: this.context.colors.textBold }]}>{this.profileText}</Text>
              </TouchableOpacity>
              <View style={{ marginTop: 20 }}>
                <TextInputComponent
                  inputRef={(r) => { this.displayNameTextInput = r }}
                  refState="displayName"
                  selectionColor={this.context.colors.text}
                  onChange={(refState, text) => {
                    if (!displayName) {
                      this.onChange(refState, text.toUpperCase());
                    } else {
                      this.onChange(refState, text);
                    }
                  }}
                  onBlur={(refState) => {
                    this.setState({ isNameFieldFocused: false })
                    this.onInputBlur();
                  }}
                  onFocus={() => {
                    this.setState({ isNameFieldFocused: true })
                  }}
                  value={displayName}
                  keyboardType="default"
                  textColor={this.context.colors.textBold}
                  fontSize={16}
                  placeHolder={"E.g. Mary Smith"}
                  placeholderTextColor={this.context.colors.text}
                  textInputStyle={[this.context.p, styles.inputTextView, { paddingBottom: 5, borderColor: isNameFieldFocused ? this.context.colors.text : displayName ? this.context.colors.separators : this.context.colors.chatBubbles }]}
                  inputTextLabel={<Text style={this.context.bodyMini}>Name<Text>*</Text></Text>}
                  inputTextLabelStyle={styles.inputTextLabelStyle}
                  textAlignVertical={"center"}
                  autoCapitalize={"words"}
                />
                <View style={{ marginTop: 28 }}>
                  <Text style={[this.context.bodyMini, styles.labelTextStyle]}>Pronouns</Text>
                  <Dropdown
                    maxHeight={300}
                    data={this.genderList}
                    value={gender}
                    style={[this.context.p, styles.inputTextView, { borderColor: isPronounsFieldFocused ? this.context.colors.text : gender ? this.context.colors.separators : this.context.colors.chatBubbles }]}
                    selectedTextStyle={{ color: this.context.colors.textBold }}
                    labelField="label"
                    valueField="value"
                    placeholder={"Select an option"}
                    placeholderStyle={{ color: this.context.colors.text }}
                    onChange={(item) => {
                      this.tapOnGenderRow(item)
                    }}
                    renderItem={(item) => this.renderDropdownItem(item)}
                    onFocus={() => {
                      this.setState({ isPronounsFieldFocused: true })
                    }}
                  />
                </View>
                <View style={{ marginTop: 28 }}>
                  <Text style={[this.context.bodyMini, styles.labelTextStyle]}>Link</Text>
                  <View style={[styles.linkUrlView, { borderColor: isWebsiteFieldFocused ? this.context.colors.text : websiteText ? this.context.colors.separators : this.context.colors.chatBubbles }]}>
                    <TextInput
                      ref={(r) => { this.websiteTextInput = r }}
                      refState="websiteText"
                      maxLength={this.socialUrlLength}
                      selectionColor={this.context.colors.text}
                      onChangeText={(text) => {
                        this.onChange("websiteText", text);
                      }}
                      value={websiteText}
                      keyboardType="default"
                      autoCapitalize={'none'}
                      color={this.context.colors.textBold}
                      fontSize={16}
                      style={styles.linkInputStyle}
                      placeholder={"Add URL"}
                      placeholderTextColor={this.context.colors.text}                      
                      onFocus={() => {
                        this.setState({ isWebsiteFieldFocused: true })
                      }}
                      onBlur={(refState) => {
                        this.setState({ isWebsiteFieldFocused: false })
                        this.onInputBlur();
                      }}
                      textAlignVertical={"center"}
                    />
                    {Utils.checkData(websiteText) &&
                      <TouchableOpacity
                        onPress={() => {
                          if (Utils.checkData(websiteText)) {
                            this.setState({ websiteText: '' }, () => {
                              this.onInputBlur()
                            })
                          }
                        }}>
                        <FastImage
                          source={require('../../../assets/close_window.png')}
                          style={styles.crossIconView}
                          tintColor={this.context.colors.text} />
                      </TouchableOpacity>
                    }
                  </View>
                </View>
                {showWebsiteError &&
                  <View style={styles.descView}>
                    <ErrorTextMsg errorText={"Please enter a valid URL."} />
                  </View>
                }
                <View style={{ marginTop: 28 }}>
                  <TextInputComponent
                    inputRef={(r) => { this.aboutMeTextInput = r }}
                    maxLength={1000}
                    refState="aboutMeText"
                    selectionColor={this.context.colors.text}
                    onChange={(refState, text) => {
                      this.onChange(refState, text)
                    }}
                    onFocus={() => {
                      this.setState({ isBioFieldFocused: true })
                    }}
                    onBlur={(refState) => {
                      this.setState({ isBioFieldFocused: false })
                      this.onInputBlur();
                    }}
                    keyboardType="default"
                    onSubmitEditing={() => {
                      Keyboard.dismiss()
                    }}
                    fontSize={16}
                    multiline={true}
                    value={aboutMeText}
                    placeHolder={"Enter some information about you"}
                    placeholderTextColor={this.context.colors.text}
                    textColor={this.context.colors.textBold}
                    textInputStyle={[this.context.p, styles.inputTextView, styles.aboutMeTextView, { borderColor: isBioFieldFocused ? this.context.colors.text : aboutMeText ? this.context.colors.separators : this.context.colors.chatBubbles }]}
                    inputTextLabel={<Text style={this.context.bodyMini}>Bio</Text>}
                    inputTextLabelStyle={styles.inputTextLabelStyle}>
                  </TextInputComponent>
                </View>
                <View style={{ flexDirection: 'row', justifyContent: showBioFieldError ? 'space-between' : 'flex-end' }}>
                  {showBioFieldError &&
                    <View style={styles.bioFieldErrorView}>
                      <ErrorTextMsg errorText={"A max of 100 characters allowed."} />
                    </View>
                  }
                  <Text style={[this.context.bodyMini, styles.messageLengthStyle]}>
                    {aboutMeLength + "/" + this.aboutMeMaxLength}
                  </Text>
                </View>
              </View>
              {this.fromSignUp && this.renderButton(Constants.SAVE)}
              {!this.fromSignUp &&
                <View style={{ flexDirection: 'row', justifyContent: 'space-around' }}>
                  {this.renderButton(Constants.CANCEL)}
                  {this.renderButton(Constants.SAVE)}
                </View>
              }
            </View>
          </ScrollView>
          {this.state.showLoadingIndicator && <LoadingIndicator />}
        </SafeAreaView>
      </KeyboardAvoidingView>
    )
  }
}

EditProfileScreen.contextType = ThemeContext

const styles = StyleSheet.create({
  viewStyle: {
    flex: 1
  },
  inputTextView: {
    height: 52,
    borderWidth: 1,
    borderRadius: 8,
    paddingStart: 16,
    paddingEnd: 16
  },
  inputTextLabelStyle: {
    marginTop: 0,
    marginBottom: 4,
    marginLeft: 0
  },
  errorTextStyle: {
    marginTop: 10
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 55,
    borderWidth: 0.5
  },
  profileTextStyle: {
    alignSelf: 'center',
    marginTop: 12
  },
  messageLengthStyle: {
    marginTop: 4
  },
  aboutMeTextView: {
    height: 80,
    paddingTop: 12,
    paddingBottom: 12
  },
  verifyButton: {
    borderWidth: 1,
    borderRadius: 6,
    paddingTop: 12,
    paddingBottom: 12
  },
  labelTextStyle: {
    marginBottom: 4
  },
  dropdownItemView: {
    height: 48
  },
  descView: {
    marginTop: 4,
    flexDirection: 'row'
  },
  linkUrlView: {
    height: 52,
    borderWidth: 1,
    borderRadius: 8,
    paddingStart: 16,
    paddingRight: 16,
    flexDirection: 'row',
    alignItems: 'center'
  },
  linkTextView: {    
    paddingStart: 0,
    paddingBottom: 5
  },
  crossIconView: {
    height: 12,
    width: 12,    
  },
  buttonStyle: {
    marginTop: 32,
    marginBottom: 32
  },
  bioFieldErrorView: {
    flexDirection: 'row',
    justifyContent: 'flex-start'
  },
  profileImageView: {
    alignItems: 'center',
    alignSelf: 'center',
    marginTop: 20
  },
  linkInputStyle : {
    marginEnd: 10,
    flex: 1
  }
});

const mapStateToProps = (state) => {
  return {
    isLogInProcess: state.loginInfo.isLogInProcess,
    isAnonymous: state.loginInfo.isAnonymous,
    userDetails: state.loginInfo.userDetails,
  }
}

const mapDispatchToProps = (dispatch) => {
  return {
    updateUserProfile(data, callback, callGetUserDetails) {
      dispatch(
        updateUserProfile(data, callback, callGetUserDetails)
      )
    },
    updateProfilePicURL(data, callback) {
      dispatch(
        updateProfilePicURL(data, callback)
      )
    },
    removeProfileImage(callback) {
      dispatch(
        removeProfileImage(callback)
      )
    }
  }
}
export default connect(mapStateToProps, mapDispatchToProps)(EditProfileScreen)