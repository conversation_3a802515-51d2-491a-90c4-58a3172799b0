import React, { useState, useEffect, useRef, useContext } from 'react'
import {
    StyleSheet,
    View,
    Animated,
    Image,
    Text,
    TouchableOpacity,
    Platform
} from 'react-native'
import ReactionComponent from './reactionUI/ReactionComponent';
import { navigationStyle } from '../config/styles';
import { DimenContext, ThemeContext } from '../Contexts';
import { Constants } from '../config/Constants';
import FastImage from 'react-native-fast-image';
import { Utils } from '../config/Utils';
import UserSession from '../config/UserSession';
import { Color } from '../config/Color';

const ComicBottomBar = (props) => {

    const { comicFeedEpisodes, comicData, currentPageStatus, pathUrl, isSubscribed, isPostCommentStory, commentsDisabled, isLiked, hideBottomBar } = props;

    const context = useContext(ThemeContext);
    const animatedTranslateY = useRef(new Animated.Value(0)).current;

    const [prevEpisode, setPrevEpisode] = useState(null);
    const [nextEpisode, setNextEpisode] = useState(null);

    const userUnreadNotifications = UserSession.instance.getUnreadNotifications();
    const unreadNotifCount = userUnreadNotifications > 99 ? "99+" : userUnreadNotifications;
    const youtubeDomains = [
        'googlevideo.com',
        'gvt1.com',
        'video.google.com',
        'video.l.google.com',
        'youtu.be',
        'youtube.com',
        'yt3.ggpht.com',
        'yt.be',
        'ytimg.com',
        'ytimg.l.google.com',
        'ytkids.app.goo.gl',
        'yt-video-upload.l.google.com',
        'vimeo.com'
    ];

    useEffect(() => {
        if (comicFeedEpisodes?.episodes?.length > 1) {
            let episodes = comicFeedEpisodes.episodes;
            let currEpisodeIndex = -1;
            const currentStoryID = Utils.checkData(comicData) && comicData.storyID
                ? comicData.storyID
                : currentPageStatus.storyID;

            for (const index in episodes) {
                if (currentStoryID === episodes[index].id) {
                    currEpisodeIndex = parseInt(index);
                    break;
                }
            }

            if (currEpisodeIndex !== -1) {
                setPrevEpisode(episodes[currEpisodeIndex - 1] ?? null);
                setNextEpisode(episodes[currEpisodeIndex + 1] ?? null);
            }
        }
    }, []);

    useEffect(() => {
        Animated.timing(animatedTranslateY, {
            toValue: hideBottomBar ? 200 : 0,
            duration: 200,
            useNativeDriver: true
        }).start();
    }, [hideBottomBar]);

    const renderFollowButton = () => {
        return (
            <TouchableOpacity
                style={[styles.ButtonMainView, { backgroundColor: context.colors.logoRed, borderColor: context.colors.logoRed }]}
                onPress={() => {
                    props.onFollowPress(pathUrl, isSubscribed, false, false)
                }}>
                <FastImage
                    style={styles.followButtonIcon}
                    source={require('../../assets/plus_icon.png')} />
                <Text style={[context.bodyMini, { color: context.colors.textInverse }]}>{Constants.FOLLOW}</Text>
            </TouchableOpacity>
        )
    };

    const renderViewComicButton = () => {
        const isExternalLink = comicData.actionType == "website";
        const isYoutubeLink = comicData?.domain && youtubeDomains.includes(comicData.domain);

        return (
            <TouchableOpacity
                style={[styles.ButtonMainView, { borderColor: context.colors.separators }]}
                onPress={() => { onNavButtonTap(comicData, true) }}>
                {isExternalLink &&
                    <FastImage
                        style={[styles.externalLinkIcon]}
                        tintColor={Color.WHITE_COLOR}
                        source={require('../../assets/external_link_icon.png')} />
                }
                <Text style={[context.bodyMini, { color: context.colors.textInverse }]}>{isExternalLink ? isYoutubeLink ? "Watch" : Constants.OPEN_LINK : Constants.VIEW_COMIC}</Text>
            </TouchableOpacity>
        )
    };

    const onEpisodeListClicked = () => {
        props.onSeriesHomeClicked();
    };

    const onNavButtonTap = (item, isSameComic = false) => {
        if (item.actionType == "website") {
            if (isPostCommentStory && isSameComic) {
                props.recordPageView(item.action, null, item.storyID);
                props.openWebView(item.action);
            } else {
                const storyID = item.id ? item.id : null;
                onCommentPress(storyID, item.action);
            }
        } else if (item.action || item.userId) {
            props.openChapter(item.action, item);
        }
    };

    const onCommentPress = (storyID = null, comicAction = null) => {
        const seriesName = comicData.series ?? Utils.getChannelName(pathUrl);
        let storyData = { storyID: storyID, refType: Constants.STORY, action: comicAction ? comicAction : pathUrl, series: seriesName };
        props.onShowCommentPress(Constants.COMMENT, null, null, null, storyData);
    };

    const isComicLiked = isLiked ? isLiked : comicData.isLiked;
    const isAndroidDevice = Platform.OS == "android" ? true : false;

    return (
        <View style={styles.containerView}>
            <Animated.View
                style={{
                    transform: [{ translateY: animatedTranslateY }],
                    backgroundColor: context.colors.textBold,
                    zIndex: 1
                }}>
                <View style={styles.mainContainer}>
                    <View style={styles.reactionsView}>
                        <ReactionComponent pathUrl={pathUrl} enabledWhiteTheme={true} disableComments={commentsDisabled} currentPageStatus={currentPageStatus} onIconPress={props.onIconPress} onShowCommentPress={props.onShowCommentPress} onCommentPress={onCommentPress} onLikePress={props.onLikePress} index={props.index} isLiked={isComicLiked} panelItems={comicData} />
                    </View>
                    <View style={[styles.separatorStyle, { backgroundColor: context.colors.textInverse }]} />
                    <DimenContext.Consumer>
                        {dimen => (
                            <View style={styles.navigationView(isAndroidDevice, dimen.navbarHeight)}>
                                <View style={styles.mainIconView}>
                                    <TouchableOpacity
                                        hitSlop={styles.iconArea}
                                        onPress={() => { props.onBackPress() }}>
                                        <Image style={styles.iconsView} source={require('../../assets/back_arrow_icon.png')} />
                                    </TouchableOpacity>
                                    <View style={styles.buttonHomeView}>
                                        {isPostCommentStory && renderViewComicButton()}
                                        {!isPostCommentStory && !isSubscribed && renderFollowButton()}
                                        {!isPostCommentStory && isSubscribed &&
                                            <View style={{ flexDirection: 'row', marginStart: 5 }}>
                                                <TouchableOpacity
                                                    hitSlop={styles.iconArea}
                                                    onPress={() => { props.navigateToStackTop() }}>
                                                    <Image style={styles.iconsView} source={require('../../assets/home_icon.png')} />
                                                </TouchableOpacity>
                                                <TouchableOpacity
                                                    hitSlop={styles.iconArea}
                                                    onPress={() => {
                                                        props.navigation.navigate(Constants.NOTIFICATIONS_SCREEN)
                                                    }
                                                    }
                                                    style={{ marginStart: 30 }}>
                                                    <View style={styles.notifAlignView}>
                                                        <Image style={styles.navigationIcons} source={require('../../assets/notifications_icon_white.png')} />
                                                        {userUnreadNotifications > 0 &&
                                                            <View style={[styles.notifCountView(unreadNotifCount), { borderColor: context.colors.logoRed, backgroundColor: context.colors.logoRed }]}>
                                                                <Text style={[styles.unreadNotifText, { color: context.colors.textInverse }]}>{unreadNotifCount}</Text>
                                                            </View>
                                                        }
                                                    </View>
                                                </TouchableOpacity>
                                            </View>
                                        }
                                    </View>
                                </View>
                                {(isPostCommentStory || !isSubscribed) &&
                                    <TouchableOpacity
                                        hitSlop={styles.iconArea}
                                        onPress={() => { props.navigateToStackTop() }}>
                                        <Image style={styles.iconsView} source={require('../../assets/home_icon.png')} />
                                    </TouchableOpacity>
                                }
                                <View style={styles.mainIconView}>
                                    <TouchableOpacity
                                        style={styles.navIcons}
                                        hitSlop={styles.iconArea}
                                        onPress={() => { onNavButtonTap(prevEpisode) }}
                                        disabled={!prevEpisode}>
                                        <Image style={[styles.iconsView, { opacity: 1 }]} source={!prevEpisode ? require('../../assets/prev_episode_icon_disabled.png') : require('../../assets/prev_episode_icon_white.png')} />
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        style={styles.navIcons}
                                        hitSlop={styles.iconArea}
                                        onPress={() => { onEpisodeListClicked() }}>
                                        <Image style={styles.iconsView} source={require('../../assets/episodes_list_icon_white.png')} />
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        hitSlop={styles.iconArea}
                                        onPress={() => { onNavButtonTap(nextEpisode) }}
                                        disabled={!nextEpisode}>
                                        <Image style={[styles.iconsView, { opacity: 1 }]} source={!nextEpisode ? require('../../assets/next_episode_icon_disabled.png') : require('../../assets/next_episode_icon_white.png')} />
                                    </TouchableOpacity>
                                </View>
                            </View>
                        )}
                    </DimenContext.Consumer>
                </View>
            </Animated.View>
        </View>
    );
};

const styles = StyleSheet.create({
    mainContainer: {
        flex: 1
    },
    reactionsView: {
        marginTop: 4,
        marginBottom: 4,
        marginLeft: 15,
        marginRight: 15
    },
    navigationView: (isAndroidDev, navbarHeight = 0) => {
        return {
            flex: 1,
            height: navigationStyle.navHeight + navbarHeight,
            paddingBottom: isAndroidDev ? navbarHeight : 10,
            marginLeft: 15,
            marginRight: 15,
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center'
        }
    },
    iconsView: {
        width: 20,
        height: 20
    },
    navigationIcons: {
        width: 22,
        height: 22
    },
    mainIconView: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    separatorStyle: {
        height: .25,
        width: '100%'
    },
    ButtonMainView: {
        height: 32,
        paddingLeft: 8,
        paddingRight: 8,
        borderRadius: 6,
        borderWidth: 1,
        flexDirection: 'row',
        alignItems: 'center'
    },
    followButtonIcon: {
        height: 12,
        width: 12,
        marginRight: 4
    },
    navIcons: {
        marginRight: 28
    },
    buttonHomeView: {
        marginLeft: 20
    },
    viewComicButton: {
        borderWidth: 1,
        borderRadius: 6,
        flexDirection: 'row',
        alignItems: 'center'
    },
    iconArea: {
        left: 12,
        right: 12
    },
    externalLinkIcon: {
        height: 16,
        width: 16,
        marginRight: 4,
        tintColor: Color.WHITE_COLOR
    },
    unreadNotifText: {
        fontStyle: 'normal',
        fontWeight: '400',
        fontSize: 11,
        lineHeight: 12
    },
    notifAlignView: {
        flexDirection: 'row',
        alignSelf: 'center',
    },
    notifCountView: (count) => {
        return {
            height: count == "99+" ? 17 : 20,
            width: count == "99+" ? 30 : 20,
            borderWidth: 1,
            borderRadius: 12.5,
            justifyContent: 'center',
            alignItems: 'center',
            position: 'absolute',
            right: count == "99+" ? -20 : -16,
            top: -6,
            paddingLeft: count == "99+" ? 2 : 0
        }
    },
    containerView: {
        position: 'absolute',
        left: 0,
        right: 0,
        bottom: 0
    }
});

export default ComicBottomBar;
