import React, { Component } from 'react'
import { ActivityIndicator, View, Text, Dimensions, StyleSheet } from 'react-native'
import { Color } from '../config/Color'
import { ThemeContext } from '../Contexts'

const window = Dimensions.get('window')

export class LoadingIndicator extends Component {
    constructor(props) {
        super(props)
    }
    render() {
        const { loadingMessage } = this.props
        return (
            <View style={styleSheet.viewContainer}>
                <View style={styleSheet.loginIndicatorView}>
                    <ActivityIndicator size='large' color={Color.RED_BORDER_COLOR} style={styleSheet.ActivityIndicatorView} />
                    {loadingMessage &&
                        <Text style={[this.context.p, styleSheet.textView]}>{loadingMessage || ''}</Text>
                    }
                </View>
            </View>
        )
    }
}

LoadingIndicator.contextType = ThemeContext

const styleSheet = StyleSheet.create({
    viewContainer: {
        backgroundColor: 'rgba(211, 211, 211, 0.5)',
        position: 'absolute',
        alignSelf: 'center',
        width: window.width,
        height: "100%",        
    },
    loginIndicatorView: {
        alignSelf: 'center',
        flex: 1,
        justifyContent: 'center'
    },
    ActivityIndicatorView: {
        alignSelf: 'center',
    },
    textView: {
        alignSelf: 'center',
        textAlign: 'center',
        margin: 10,
        marginLeft: 20,
        marginRight: 20
    }
})