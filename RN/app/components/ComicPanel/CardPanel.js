import React from 'react'
import { StyleSheet, Image, Text, Dimensions } from "react-native";
import { View, Button } from "native-base";
import Panel from "./Panel";
import { ThemeContext } from "../../Contexts";
import { Color } from "../../config/Color";
import { scale } from "react-native-size-matters";
import ContextMenu from 'react-native-context-menu-view';
import { buyButton } from "../../config/styles";
import { Utils } from '../../config/Utils';
import SelectableText from '../../config/SelectableText';
import SessionManager from '../../config/SessionManager';

export default class CardPanel extends Panel {

    constructor(props) {
        super(props)

        this.renderPanel = this.renderPanel.bind(this)
        this.renderTextPanel = this.renderTextPanel.bind(this)
        this.renderButton = this.renderButton.bind(this)
    }

    shouldUpdated(nextProps, nextState) {
        return false
    }

    renderButton(item) {
        if (!item.button) {
            return null
        }

        return (
            <View style={styles.buttonView}>
                <Button
                    variant='solid'
                    style={[buyButton, styles.buyButton, { backgroundColor: this.context.colors.logoRed, borderColor: this.context.colors.logoRed }]}
                    onPress={() => { this.onPanelTap(item) }}>
                    <Text style={[this.context.p, { color: this.context.colors.textInverse }]}>
                        {item.button}
                    </Text>
                </Button>
            </View>
        )
    }

    renderTextPanel(item) {
        if (!item.title && !item.description && !item.button && !item.icon) {
            return null
        }

        const isTVFeatureListedPanel = item.description ? item.description.includes("<br>") : false
        let descStyle = { ...this.context.p }
        if (isTVFeatureListedPanel) {
            descStyle.lineHeight = 25
        }

        const titleText = item.showActiveBorder ? `${item.title} (current plan)` : item.title

        return (
            <View style={styles.panelInnerView}>
                {item.icon &&
                    <View style={{ flex: 0.20 }}>
                        <Image style={[styles.iconView]} source={{ uri: Utils.resolvePath(this.props.pathUrl, item.icon) }} />
                    </View>
                }
                <View style={{ flex: item.icon ? 0.80 : 1 }}>
                    {(Utils.checkData(item.title)) &&
                        <View>
                            <SelectableText textValue={titleText} textStyle={[this.context.h2, { paddingTop: 0 }]} multiline={true} />
                        </View>
                    }
                    {(Utils.checkData(item.description)) &&
                        <View style={{ marginTop: item.title ? 8 : 0 }}>
                            {this.renderPanelDescription(item.description, descStyle)}
                        </View>
                    }
                    {item.button &&
                        <View style={styles.conditionButtonView(item.title || item.description)}>
                            {this.renderButton(item)}
                        </View>
                    }
                </View>
            </View>
        )
    }

    renderPanel() {
        const { item } = this.props.item
        const isTVFeatureListedPanel = item.description ? item.description.includes("<br>") : false

        return (
            <View style={[styles.panelMargin(isTVFeatureListedPanel), styles.shadowView]}>
                <View style={[styles.panelOuterView, { borderWidth: item.showActiveBorder ? 3 : 0, borderColor: item.showActiveBorder ? this.context.colors.highlightBorders : this.context.colors.chatBubbles, backgroundColor: this.context.colors.textInverse }]}>
                    {(item && item.image) ?
                        <ContextMenu
                            actions={[{ title: "Share", systemIcon: "square.and.arrow.up" }]}
                            onPress={async (e) => {
                                if (e.nativeEvent.index == 0) {
                                    this.props.shareImage(item);
                                }
                            }}
                        >
                            {this.renderImage()}
                        </ContextMenu>
                        : null
                    }
                    {this.renderTextPanel(item)}
                </View>
            </View>
        )
    }

    render() {
        const { item, index } = this.props.item

        return (
            <View style={{ backgroundColor: item.bgColor }}>
                {this.renderContainer()}
            </View>
        )
    }

    imageStyle = (w = 800, h = 420, margin = 2, item) => {
        let dimensions = Dimensions.get('window')
        let rightBorder = 0;
        if (item.border && item.border["border-left"] && item.border["border-right"]) {
            rightBorder = item.border["border-width"];
        }

        let aspectRatio = (dimensions.width - ((margin + rightBorder) * 2)) / w

        return {
            width: w * aspectRatio,
            height: h * aspectRatio,
            resizeMode: 'contain',
        }
    }
}

CardPanel.contextType = ThemeContext

const styles = StyleSheet.create({
    shadowView: {
        shadowColor: Color.BLACK_COLOR,
        shadowOpacity: 0.1,
        shadowRadius: 12
    },
    panelOuterView: {
        borderRadius: 12,
        overflow: 'hidden',
        elevation: 12
    },
    iconView: {
        height: scale(32),
        width: scale(32)
    },
    buttonView: {
        paddingTop: 16,
        alignItems: 'flex-start'
    },
    buyButton: {
        height: 36
    },
    conditionButtonView: (hasTitleDescription = false) => {
        return {
            marginTop: hasTitleDescription ? 12 : 0,
            marginBottom: 20
        }
    },
    panelInnerView: {
        flexDirection: 'row',
        margin: 20
    },
    panelMargin: (isTVFeatureListedPanel) => {
        return {
            marginTop: isTVFeatureListedPanel ? 20 : 0
        }
    }
})