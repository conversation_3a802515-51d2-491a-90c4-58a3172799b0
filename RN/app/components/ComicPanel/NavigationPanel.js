import React from "react";
import { StyleSheet, TouchableOpacity } from "react-native";
import { View, Text } from "native-base";
import Panel from "./Panel";
import { ThemeContext } from "../../Contexts";
import FastImage from "react-native-fast-image";
import { navigationStyle, buyButton } from "../../config/styles";
import { Utils } from "../../config/Utils";
import { Constants } from "../../config/Constants";

export default class NavigationPanel extends Panel {
    constructor(props) {
        super(props)

        this.state = {
            comicFirstEpisode: null,
            comicLatestEpisode: null,
            comicPrevEpisode: null,
            comicNextEpisode: null
        }

        const { pathUrl, seriesName } = this.props
        this.channelName = seriesName ? seriesName : Utils.getChannelName(pathUrl)
        this.currComicEndPoint = Utils.getMeaningFullURL(pathUrl)

        this.fetchedFirstAndLatestEpisode = this.fetchedFirstAndLatestEpisode.bind(this)
        this.onPressBannerButtons = this.onPressBannerButtons.bind(this)
    }

    componentDidMount() {
        const { pathUrl, isCurrCommentPage } = this.props
        let isComicPage = isCurrCommentPage || (Utils.isComicPage(pathUrl))
        if (isComicPage) {
            let requestedData = { series: this.channelName }
            this.props.getNavigationComics(requestedData, this.fetchedFirstAndLatestEpisode)
        }
    }

    shouldUpdated(nextProps, nextState) { // Restricting rerendering of rendered component
        const needsToUpdate = this.state.comicFirstEpisode != nextState.comicFirstEpisode || this.state.comicLatestEpisode != nextState.comicLatestEpisode || this.state.comicPrevEpisode != nextState.comicPrevEpisode || this.state.comicNextEpisode != nextState.comicNextEpisode || this.props.comicFeedEpisodes != nextProps.comicFeedEpisodes || this.props.isChannelSubscribed != nextProps.isChannelSubscribed || this.props.isAnyAlertsEnabled != nextProps.isAnyAlertsEnabled
        return needsToUpdate
    }

    componentDidUpdate() {
        const { comicFeedEpisodes } = this.props
        const { comicPrevEpisode, comicNextEpisode } = this.state

        if (comicPrevEpisode || comicNextEpisode || !comicFeedEpisodes) {
            return
        }

        if (comicFeedEpisodes.episodes && comicFeedEpisodes.episodes.length > 1) {
            let episodes = comicFeedEpisodes.episodes
            let currEpisodeIndex = -1
            for (const index in episodes) {
                if (this.currComicEndPoint == episodes[index].action) {
                    currEpisodeIndex = parseInt(index)
                    break;
                }
            }

            if (currEpisodeIndex != -1) {
                let prevEpisode = episodes[currEpisodeIndex - 1] ? episodes[currEpisodeIndex - 1] : null
                let nextEpisode = episodes[currEpisodeIndex + 1] ? episodes[currEpisodeIndex + 1] : null
                this.setState({ comicPrevEpisode: prevEpisode, comicNextEpisode: nextEpisode })
            }
        }
    }

    fetchedFirstAndLatestEpisode(response) {
        if (response) {
            if (response.first && response.first.action != this.currComicEndPoint) {
                this.setState({ comicFirstEpisode: response.first })
            }

            if (response.latest && response.latest.action != this.currComicEndPoint) {
                this.setState({ comicLatestEpisode: response.latest })
            }
        }
    }

    onPressBannerButtons(item) {
        const { isChannelSubscribed, seriesName } = this.props;
        if (item == Constants.FOLLOW) {
            let seriesURL = this.props.pathUrl;
            if (this.props.isExternalComic && seriesName) {
                seriesURL = Utils.getComicSeriesURLFromChannel(seriesName)
            } else {
                const isComicURL = Utils.isComicURL(this.props.pathUrl)
                if (isComicURL) {
                    seriesURL = Utils.resolvePath(this.props.pathUrl, Utils.getComicSeriesURL(this.props.pathUrl));
                }
            }
            this.props.onFollowPress(seriesURL, isChannelSubscribed, false, false)
        } else if (item == Constants.MANAGE_ALERTS) {
            Utils.navigateToManageAlertsPage()
        }

    }

    render() {
        const { comicFirstEpisode, comicLatestEpisode, comicPrevEpisode, comicNextEpisode } = this.state
        const { isChannelSubscribed, isAnyAlertsEnabled, pathUrl } = this.props

        let bannerMessage = ''
        let bannerButton = ''
        const channelName = Utils.getUserVisibleChannelName(pathUrl)
        if (!isChannelSubscribed) {
            bannerMessage = `Never miss a new comic from ${channelName}!`
            bannerButton = Constants.FOLLOW
        } else if (!isAnyAlertsEnabled) {
            bannerMessage = Constants.MANAGE_ALERTS_ACTIONSHEET_TITLE
            bannerButton = Constants.MANAGE_ALERTS
        }

        return (
            <View style={[styles.navigationContainer, styles.navigationBorderView, { borderColor: this.context.colors.chatBubbles, backgroundColor: this.context.colors.textInverse }]}>
                <View style={styles.navigationButtonsView}>
                    <View style={styles.navigationView}>
                        <TouchableOpacity
                            style={[styles.firstLatestButtonView, { borderColor: this.context.colors.separators, backgroundColor: comicFirstEpisode ? this.context.colors.textInverse : this.context.colors.separators }]}
                            onPress={() => this.onPanelTap(comicFirstEpisode)}
                            disabled={!comicFirstEpisode}>
                            <View style={styles.firstLatestInnerView}>
                                <FastImage style={styles.firstLatestIconView} source={comicFirstEpisode ? require('../../../assets/first_episode_icon.png') : require('../../../assets/disabled_first_episode_icon.png')} />
                            </View>
                        </TouchableOpacity>
                        <View style={{ flexDirection: 'row' }}>
                            <TouchableOpacity
                                style={{ borderRadius: 6, marginRight: 12, backgroundColor: comicPrevEpisode ? this.context.colors.logoRed : this.context.colors.separators }}
                                onPress={() => this.onPanelTap(comicPrevEpisode)}
                                disabled={!comicPrevEpisode}>
                                <View style={[styles.prevNextButton, { paddingRight: 16 }]}>
                                    <FastImage style={styles.prevNextIconView} source={require('../../../assets/prev_episode_icon.png')} />
                                    <Text style={[this.context.p, { marginLeft: 8, color: this.context.colors.textInverse }]}>{Constants.PREV}</Text>
                                </View>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={{ borderRadius: 6, backgroundColor: comicNextEpisode ? this.context.colors.logoRed : this.context.colors.separators }}
                                onPress={() => this.onPanelTap(comicNextEpisode)}
                                disabled={!comicNextEpisode}>
                                <View style={[styles.prevNextButton, { paddingLeft: 16 }]}>
                                    <Text style={[this.context.p, { marginRight: 8, color: this.context.colors.textInverse }]}>{Constants.NEXT}</Text>
                                    <FastImage style={styles.prevNextIconView} source={require('../../../assets/next_episode_icon.png')} />
                                </View>
                            </TouchableOpacity>
                        </View>
                        <TouchableOpacity
                            style={[styles.firstLatestButtonView, { borderColor: this.context.colors.separators, backgroundColor: comicLatestEpisode ? this.context.colors.textInverse : this.context.colors.separators }]}
                            onPress={() => this.onPanelTap(comicLatestEpisode)}
                            disabled={!comicLatestEpisode}>
                            <View style={styles.firstLatestInnerView}>
                                <FastImage style={styles.firstLatestIconView} source={comicLatestEpisode ? require('../../../assets/latest_episode_icon.png') : require('../../../assets/disabled_latest_episode_icon.png')} />
                            </View>
                        </TouchableOpacity>
                    </View>
                    {(!isChannelSubscribed || !isAnyAlertsEnabled) &&
                        <View style={[styles.followBannerView, { backgroundColor: this.context.colors.bannerBackgroundError }]}>
                            <Text style={[this.context.bodyMini, { flex: 1, color: this.context.colors.bannerTextError }]} adjustsFontSizeToFit>{bannerMessage}</Text>
                            <View style={styles.bannerButtonView}>
                                <TouchableOpacity
                                    style={[buyButton, styles.buttonStyle, { backgroundColor: this.context.colors.logoRed, borderColor: this.context.colors.logoRed }]}
                                    onPress={() => this.onPressBannerButtons(bannerButton)}>
                                    <Text style={[this.context.bodyMini, { color: this.context.colors.textInverse }]}>{bannerButton}</Text>
                                </TouchableOpacity>
                            </View>
                        </View>
                    }
                </View>
            </View>
        )
    }
}

NavigationPanel.contextType = ThemeContext

const styles = StyleSheet.create({
    navigationContainer: {
        marginBottom: navigationStyle.panelsMargin,
        marginLeft: navigationStyle.panelLeftRightMargin,
        marginRight: navigationStyle.panelLeftRightMargin
    },
    navigationBorderView: {
        borderWidth: 1,
        borderRadius: 12,
        shadowColor: "#000",
        shadowOpacity: 0.1,
        shadowRadius: 12,
        elevation: 12
    },
    navigationButtonsView: {
        marginLeft: navigationStyle.panelLeftRightMargin,
        marginRight: navigationStyle.panelLeftRightMargin
    },
    firstLatestIconView: {
        height: 24,
        width: 24
    },
    prevNextIconView: {
        height: 16,
        width: 16
    },
    prevNextButton: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingTop: 8,
        paddingBottom: 8,
        paddingRight: 10,
        paddingLeft: 10
    },
    firstLatestButtonView: {
        borderWidth: 1,
        justifyContent: 'center',
        borderRadius: 4,
        marginBottom: 1,
        marginTop: 1
    },
    firstLatestInnerView: {
        paddingLeft: 6,
        paddingRight: 6
    },
    navigationView: {
        marginTop: navigationStyle.panelsMargin,
        marginBottom: navigationStyle.panelsMargin,
        flexDirection: 'row',
        justifyContent: 'space-between'
    },
    followBannerView: {
        flex: 1,
        paddingTop: 8,
        paddingBottom: 8,
        paddingLeft: 12,
        paddingRight: 12,
        marginBottom: 20,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderRadius: 12,
    },
    buttonStyle: {
        height: 30,
        paddingLeft: 8,
        paddingRight: 8,
        position: 'relative'
    },
    bannerButtonView: {
        marginLeft: 10
    }
})