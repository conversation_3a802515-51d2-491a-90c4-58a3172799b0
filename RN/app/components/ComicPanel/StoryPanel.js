import React from 'react'
import { TouchableWithoutFeedback, StyleSheet, Text, Dimensions, TouchableOpacity, Platform } from 'react-native'
import { Button, View } from 'native-base';
import { SystemFont } from '../../config/Typography';
import { Color } from '../../config/Color';
import { scale } from 'react-native-size-matters';
import ContextMenu from 'react-native-context-menu-view';
import Panel from './Panel';
import { settings } from '../../config/settings';
import { Utils } from '../../config/Utils';
import ImageDownloader from '../ImageDownloader';
import { Constants } from '../../config/Constants';
import { navigationStyle, commentContainerStyle, roundImage, buyButton, buyButtonContainer, storyPanelStyle, comicConfigContainer, comicConfigBadgeView, comicConfigIcon, readIndicatorIcon } from '../../config/styles';
import ActionSheet from '../actionSheet/ActionSheetCustom';
import ImagePlaceHolder from '../ImagePlaceHolder';
import ReactionComponent from '../reactionUI/ReactionComponent';
import BadgesView from '../BadgesView';
import FastImage from 'react-native-fast-image'
import { ThemeContext } from '../../Contexts';
import SessionManager from '../../config/SessionManager';

export default class StoryPanel extends Panel {

	constructor(props) {
		super(props)

		const { item } = this.props
		const { commentedBy, likedBy, giftedBy, repostString, timings, publishedBy, createdAt, sentString } = item.item
		const populateReasonKey = Utils.getLatestTimeKey(timings)
		let feedPopulateReason = null
		const middleText = "ago •"

		if (populateReasonKey === Constants.REPOST) {
			let convertedTime = timings && timings.lastRepost && Utils.convertTimeSpanIntoTimeAgo(timings.lastRepost)
			feedPopulateReason = repostString ? (timings && timings.lastRepost) ? `${convertedTime} ${convertedTime != Constants.NOW ? middleText : "•"} ${repostString}` : repostString : null
		}
		else if (populateReasonKey === Constants.COMMENT) {
			let convertedTime = timings && timings.lastComment && Utils.convertTimeSpanIntoTimeAgo(timings.lastComment)
			feedPopulateReason = commentedBy ? (timings && timings.lastComment) ? `${convertedTime} ${convertedTime != Constants.NOW ? middleText : "•"} ${commentedBy}` : commentedBy : null
		}
		else if (populateReasonKey === Constants.LIKE) {
			let convertedTime = timings && timings.lastLiked && Utils.convertTimeSpanIntoTimeAgo(timings.lastLiked)
			feedPopulateReason = likedBy ? (timings && timings.lastLiked) ? `${convertedTime} ${convertedTime != Constants.NOW ? middleText : "•"} ${likedBy}` : likedBy : null
		}
		else if (populateReasonKey === Constants.SENT) {
			let convertedTime = timings && timings.lastSent && Utils.convertTimeSpanIntoTimeAgo(timings.lastSent)
			feedPopulateReason = sentString ? (timings && timings.lastSent) ? `${convertedTime} ${convertedTime != Constants.NOW ? middleText : "•"} ${sentString}` : sentString : null
		}
		else if (populateReasonKey === Constants.GIFT) {
			let convertedTime = timings && timings.lastGifted && Utils.convertTimeSpanIntoTimeAgo(timings.lastGifted)
			feedPopulateReason = giftedBy ? (timings && timings.lastGifted) ? `${convertedTime} ${convertedTime != Constants.NOW ? middleText : "•"} ${giftedBy}` : giftedBy : null
		}

		if (!feedPopulateReason && publishedBy) {
			let convertedTime = Utils.convertTimeSpanIntoTimeAgo(createdAt)
			feedPopulateReason = `${convertedTime} ${convertedTime != Constants.NOW ? middleText : "•"} ${publishedBy}`
		}

		this.currentUserId = Utils.getCurrentUserId()

		let userImageURL = null
		const panelData = this.props.item.item
		this.isRepostedStory = panelData.refType && (panelData.refType.toLowerCase() == Constants.REPOST.toLowerCase() || panelData.refType.toLowerCase() == Constants.GROUP_SENT_POST.toLowerCase() || panelData.refType.toLowerCase() == Constants.SENT_POST.toLowerCase())
		if (this.isRepostedStory) {
			userImageURL = panelData.user && panelData.user.image
		}

		this.state = {
			userImagePath: userImageURL,
			options: ['Edit', 'Delete', 'Cancel'],
			errorInImageLoading: false
		}
		this.isUserImageLoaded = true;
		this.feedPopulateReason = feedPopulateReason;

		if (!this.isRepostedStory && this.props.item.item && this.props.item.item.user) {
			this.isUserImageLoaded = false;
			const panelData = this.props.item.item
			let finalUrl = Utils.resolvePath(this.props.pathUrl, panelData.user.image)
			ImageDownloader.downloadPanelImage(finalUrl).then((filePath) => {
				this.setState({ userImagePath: filePath })
			})
		}

		this.youtubeDomains = [
			'googlevideo.com',
			'gvt1.com',
			'video.google.com',
			'video.l.google.com',
			'youtu.be',
			'youtube.com',
			'yt3.ggpht.com',
			'yt.be',
			'ytimg.com',
			'ytimg.l.google.com',
			'ytkids.app.goo.gl',
			'yt-video-upload.l.google.com',
			'vimeo.com'
		]

		this.selectedItem = null
		this.onListedCommentTap = this.onListedCommentTap.bind(this)
		this.onReplyPress = this.onReplyPress.bind(this)
		this.onEditPress = this.onEditPress.bind(this)
		this.onMoreOptionPress = this.onMoreOptionPress.bind(this)
		this.renderPanel = this.renderPanel.bind(this)
		this.renderViewInfo = this.renderViewInfo.bind(this)
		this.renderPanelDateTime = this.renderPanelDateTime.bind(this)
		this.renderFeedReason = this.renderFeedReason.bind(this)
		this.renderTitle = this.renderTitle.bind(this)
		this.renderComicButton = this.renderComicButton.bind(this)
		this.onActionSheetPress = this.onActionSheetPress.bind(this)
		this.renderComicConfigDesc = this.renderComicConfigDesc.bind(this)
		this.renderReadIndicator = this.renderReadIndicator.bind(this)

		const channelName = Utils.getChannelName(this.props.pathUrl)
		this.seriesData = SessionManager.instance.getSeriesData(channelName)
		this.comicConfig = SessionManager.instance.getComicsConfigList()
	}

	shouldUpdated(nextProps, nextState) { // Restricting rerendering of rendered component		
		const currentProps = this.props.item.item
		const newProps = nextProps.item.item

		const needsToUpdate = (this.state.userImagePath != nextState.userImagePath || currentProps.showComments != newProps.showComments || currentProps.isLiked != newProps.isLiked || !this.isUserImageLoaded || currentProps.isLiked != newProps.isLiked || (this.props.showSeriesFollowButton && this.props.alerts != nextProps.alerts) || (currentProps.giftedItems != null && newProps.giftedItems != null && JSON.stringify(currentProps.giftedItems) != JSON.stringify(newProps.giftedItems)))
		return needsToUpdate
	}

	onListedCommentTap(index) {
		const { item } = this.props.item
		this.props.onShowCommentPress(Constants.SHOW_REPLIED_COMMENT, item, index)
	}

	onReplyPress(commentItems, selectedIndex) {
		this.props.onReplyPress(commentItems, selectedIndex)
	}

	onEditPress(commentItem, index) {
		this.props.onEditPress(this.props.item.item, commentItem, index)
	}

	onMoreOptionPress(item, userId) {
		if (this.actionSheet && userId == this.currentUserId) {
			this.selectedItem = item
			this.actionSheet.show()
		}
	}

	onActionSheetPress(index) {
		if (!this.selectedItem) { return }
		if (index == 0) {
			this.props.editRepost(this.selectedItem)
		} else if (index == 1) {
			this.props.deleteRepost(this.selectedItem)
		}
	}

	renderUser(item, strDate) {
		let isTocPanel = item.template === 'toc'
		if (!item.user || isTocPanel) {
			return null
		}

		const { userId } = item.user
		const { photoURL } = this.props.userDetails
		const { isPostCommentStory } = this.props
		const seriesName = item.user.name  //+ " " + settings.forwardIconText;

		return (
			<TouchableWithoutFeedback
				onPress={() => { this.onPanelTap(item.user) }}>
				<View style={styles.container}>
					{!isPostCommentStory
						? (!this.state.errorInImageLoading && Utils.checkData(this.state.userImagePath) || !this.isRepostedStory)
							?
							<FastImage
								style={[roundImage, styles.roundImage]}
								source={{ uri: this.currentUserId == userId ? photoURL : this.state.userImagePath, cache: 'web' }}
								onLoad={() => {
									this.isUserImageLoaded = true;
								}}
								onError={() => {
									this.setState({ errorInImageLoading: true })
								}}
							/>
							:
							Utils.checkData(item.user.name) ?
								<ImagePlaceHolder
									backgroundColor={Color.PROFILE_PLACE_HOLDER_BG}
									showCircularBorder={true}
									textColor={Color.COMMENT_TEXT_COLOR}
									size={scale(48)}
									type={'circle'}>{item.user.name}</ImagePlaceHolder>
								: null
						: null
					}

					<View style={styles.userInfo(isPostCommentStory)}>
						<View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
							<View style={{ flex: 1 }}>
								<View style={{ flex: 1, flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
									<View style={{ flex: 1, flexDirection: 'row' }}>
										{(Utils.checkData(item.user.name)) &&
											<Text style={this.context.h2}>{seriesName ? seriesName.trim() : seriesName}</Text>
										}
										{<BadgesView badges={item.user.badges} />}
									</View>
									{!isPostCommentStory && !userId && this.renderComicButton(item, true)}
								</View>
							</View>
						</View>
					</View>

					{userId == this.currentUserId &&
						<View>
							<TouchableOpacity onPress={() => this.onMoreOptionPress(item, userId)} style={{ alignSelf: 'flex-end', marginTop: scale(10) }}>
								<FastImage style={styles.trippleDotIcon} source={require('../../../assets/tripple_dots.png')} />
							</TouchableOpacity>
						</View>
					}
				</View>
			</TouchableWithoutFeedback>
		)
	}

	renderPanel() {
		const { item } = this.props.item
		const { hideBottomView, showComments, isPostCommentStory } = this.props
		const valueProps = {
			index: this.props.index, panelItems: item, onCountPress: this.props.onCountPress, currentPageStatus: this.props.currentPageStatus,
			onIconPress: this.props.onIconPress, onShowCommentPress: this.props.onShowCommentPress,
			onCommentPress: this.onCommentPress, onLikePress: this.props.onLikePress, isLiked: item.isLiked
		}
		const userId = item && item.user && item.user.userId
		const isSameUserId = userId == this.currentUserId

		const comicComments = item.userComments ? item.userComments.length : 0
		const isEmptyComments = comicComments === 0 ? true : false

		return (
			<View>
				{this.renderFeedReason(item)}
				<View style={[styles.previewPanelContainer(this.seriesData, isPostCommentStory), { backgroundColor: this.context.colors.textInverse }]}>
					{(item) ?
						<View>
							{this.renderPreviewPanelImage(item)}
							<View style={styles.mainStoryTextView(isPostCommentStory, isSameUserId)}>
								{this.renderPreviewPanelDesc(item)}
								{!isPostCommentStory && !hideBottomView &&
									<>
										<ReactionComponent {...valueProps} />
										{!isEmptyComments && this.renderSeparator()}
									</>
								}
							</View>
							{showComments &&
								this.renderCommentContainer(item, commentContainerStyle(isEmptyComments), styles.separatorStyle)
							}
						</View>
						: null
					}
					{this.renderButton(item)}
				</View>
			</View>
		)
	}

	renderFeedReason(item) {
		const { isPostCommentStory } = this.props
		if (!item || isPostCommentStory) {
			return null
		}

		return (
			Utils.checkData(this.feedPopulateReason) && <View style={styles.feedPopulateReasonView(item.isFirstFeedPanel)}>
				<Text style={this.context.p}>{this.feedPopulateReason}</Text>
			</View>
		)
	}

	renderButton(item) {
		if (!item.button) {
			return null
		}

		return (
			<View style={buyButtonContainer}>
				<Button
					variant='solid'
					style={[buyButton, { backgroundColor: this.context.colors.logoRed, borderColor: this.context.colors.logoRed }]}
					onPress={() => { this.onPanelTap(item) }}
				>
					<Text style={[this.context.p, { color: this.context.colors.textInverse }]}>
						{item.button}
					</Text>
				</Button>
			</View>
		)
	}

	renderViewInfo() {
		const { item } = this.props.item
		let pageViewCount = 0
		let pageViewCountSuffix = null

		if (item.views == null) {
			return null
		}

		pageViewCount = Utils.formatViewCount(item.views)
		pageViewCountSuffix = item.views != 1 ? 'views' : "view"

		return (
			<View style={styles.panelInfoContainer}>
				<Text style={[this.context.p, styles.countText]}>{pageViewCount}</Text>
				<Text style={[this.context.p, styles.headerText]}>{pageViewCountSuffix}</Text>
			</View >
		)
	}

	renderComicButton(item, isSeriesButton = false) {
		const userId = item && item.user && item.user.userId
		const isSameUserId = userId == this.currentUserId
		const isYoutubeLink = item && item.domain && this.youtubeDomains.includes(item.domain)
		const isExternalLink = item && item.actionType == "website"

		return (
			<View style={{ marginLeft: 10, alignSelf: 'center' }}>
				<TouchableOpacity
					style={[styles.viewComicButton, styles.comicButtonTextView(isSameUserId), { borderColor: this.context.colors.separators }]}
					onPress={() => isSeriesButton ? this.onPanelTap(item.user, true) : this.onPanelTap(item, true)}>
					{(isSeriesButton || isExternalLink) &&
						<FastImage
							style={[styles.seriesIconView]}
							source={isSeriesButton ? require('../../../assets/episodes_list_icon.png') : require('../../../assets/external_link_icon.png')} />
					}
					<Text style={[this.context.bodyMini, { color: this.context.colors.textBold }]}>{isSeriesButton ? Constants.EPISODES : isExternalLink ? isYoutubeLink ? "Watch" : Constants.OPEN_LINK : Constants.VIEW_COMIC}</Text>
				</TouchableOpacity>
			</View>
		)
	}

	renderPanelDateTime(dateTime, isTocPanel = false) {
		const { isPostCommentStory } = this.props

		return (
			<Text style={[this.context.bodyMini, styles.dateTimeView(isTocPanel, isPostCommentStory)]}>{dateTime}</Text>
		)
	}

	renderTitle(item) {
		if (!item || !Utils.checkData(item.title)) {
			return
		}

		const { isPostCommentStory } = this.props
		let isTocPanel = item.template === 'toc'
		const titleText = item.title.length > Constants.TITLE_TEXT_LIMIT ? `${item.title.substring(0, Constants.TITLE_TEXT_LIMIT)}...` : item.title

		return (
			<View style={styles.titleTextView(isPostCommentStory)}>
				<Text style={[isTocPanel && !isPostCommentStory ? this.context.h2 : this.context.pBold]}>{titleText}</Text>
			</View>
		)
	}

	renderPreviewPanelImage(item) {
		if (!item) {
			return null
		}

		return (
			<View>
				<ContextMenu
					actions={[{ title: "Share", systemIcon: "square.and.arrow.up" }]}
					onPress={async (e) => {
						if (e.nativeEvent.index == 0) {
							this.props.shareImage(item);
						}
					}}>
					{this.renderImage()}
				</ContextMenu>
			</View>
		)
	}

	renderReadIndicator() {
		return (
			<View style={{ marginLeft: 16 }}>
				<FastImage
					style={readIndicatorIcon}
					source={require('../../../assets/grey_double_check_icon.png')} />
			</View>
		)
	}

	renderComicConfigDesc(item) {
		const { pathUrl, isPostCommentStory } = this.props
		const { 'show-to': freemium, rating, isRead } = item;
		let isComicFree = !freemium || freemium === Constants.EVERYONE
		let isComicforEveryone = !rating || rating === Constants.ALL_AGES

		if (Utils.isEmptyObject(this.comicConfig) || (isComicFree && isComicforEveryone)) {
			return null;
		}

		const freemiumConfig = freemium && this.comicConfig['show-to'][Constants.SUBSCRIBERS_ONLY];
		const ageRatingConfig = rating && this.comicConfig.rating[rating];
		const premiumComicIconUrl = Utils.resolvePath(pathUrl, freemiumConfig.icon)

		return (
			<View style={comicConfigContainer}>
				<View style={comicConfigContainer}>
					{!isComicFree &&
						<View style={[comicConfigBadgeView, styles.comicConfigView(isPostCommentStory), { marginRight: 12, backgroundColor: this.context.colors.bannerBackgroundError }]}>
							<FastImage
								style={comicConfigIcon}
								source={{ uri: premiumComicIconUrl }} />
							<Text style={[this.context.bodyMini, styles.freemiumTextView, { color: this.context.colors.textBold }]}>{freemiumConfig.label}</Text>
						</View>
					}
					{!isComicforEveryone &&
						<View style={[comicConfigBadgeView, styles.comicConfigView(isPostCommentStory), { backgroundColor: this.context.colors.bannerBackgroundError }]}>
							<Text style={[this.context.bodyMini, { color: this.context.colors.textBold }]}>{ageRatingConfig.label}</Text>
						</View>
					}
				</View>
				{isRead && this.renderReadIndicator()}
			</View>
		)
	}

	renderPreviewPanelDesc(item) {
		if (!item) {
			return null
		}

		const { isPostCommentStory, hideSeriesProfilePic } = this.props

		let isPostCommExternalLink = isPostCommentStory && item.actionType == "website"
		let isExternalLink = item.actionType == "website"
		let timeAgeText = Utils.isDateTimeValid(item.datetime) ? Utils.getFormattedDate(item.datetime, Constants.TIME_DATE_FORMAT) : Utils.getFormattedDate(item.createdAt, Constants.TIME_DATE_FORMAT)
		let comment = Utils.checkData(item.comments) ? item.comments : Utils.checkData(item.comment) ? item.comment : null
		let commentsCount = item.commentCount ? item.commentCount : 0
		const commentsCountSuffix = commentsCount === 1 ? "Comment" : "Comments"
		let isTocPanel = item.template === 'toc'
		let comicSeriesName = SessionManager.instance.getSeriesName(item.series)
		const isSharedStory = item.refType != Constants.UPPERCASE_STORY ? true : false

		return (
			<View>
				{!isPostCommentStory && <View style={{ marginTop: -8, marginBottom: 5 }}>{this.renderComicConfigDesc(item)}</View>}
				{!hideSeriesProfilePic && this.renderUser(item, timeAgeText)}
				{isPostCommentStory && this.renderComicConfigDesc(item)}
				{(isPostCommExternalLink && comicSeriesName && !isSharedStory && isTocPanel) &&
					<TouchableWithoutFeedback
						onPress={() => this.props.onSeriesHomeClicked()}>
						<View>
							<Text style={[this.context.h2, { marginBottom: 12 }]}>{comicSeriesName ? comicSeriesName.trim() : comicSeriesName}</Text>
						</View>
					</TouchableWithoutFeedback>}
				<View style={{ flexDirection: 'row' }}>
					<View style={{ flex: 1 }}>
						<View style={styles.titleReadIconView}>
							<TouchableOpacity
								onPress={() => this.onPanelTap(item)}
								style={{ flex: 1, marginTop: !isTocPanel ? 12 : 0 }}>
								<View style={styles.titleView}>
									{this.renderTitle(item)}
								</View>
							</TouchableOpacity>
							<View style={{ flexDirection: 'row', alignItems: 'center', marginTop: !isTocPanel ? 12 : 0 }}>
								{!this.renderComicConfigDesc(item) && item.isRead && this.renderReadIndicator()}
								{isExternalLink && !isPostCommentStory && this.renderComicButton(item, false)}
							</View>
						</View>
						{Utils.checkData(timeAgeText) && this.renderPanelDateTime(timeAgeText, isTocPanel)}
					</View>
					{isPostCommentStory && this.renderComicButton(item)}
				</View>
				{(!this.props.hideCaption && comment) &&
					<View style={styles.descContainer(comment)}>
						<TouchableOpacity style={{ paddingRight: 20 }} onPress={() => this.onPanelTap(item)}>
							{this.renderPanelDescription(comment, this.context.comments)}
						</TouchableOpacity>
					</View>
				}
				{!isPostCommentStory && this.renderSeparator()}
				{isPostCommentStory && <Text style={[this.context.pBold, { marginTop: 12 }]}>{commentsCount + " " + commentsCountSuffix}</Text>}
			</View >
		)

	}

	render() {
		const { item, index } = this.props.item

		return (
			<View style={styles.mainContainer(item.bgColor)}>
				{this.renderContainer()}
				<ActionSheet
					ref={o => this.actionSheet = o}
					options={this.state.options}
					cancelButtonIndex={2}
					tintColor={this.context.colors.logoRed}
					useNativeDriver={false}
					onPress={this.onActionSheetPress}
					styles={{ titleText: this.context.h2, messageText: this.context.p, messageBox: { paddingTop: 20 } }}
				/>
			</View>
		)
	}

	imageStyle = (w = 800, h = 420, margin = 2, item) => {
		return {
			width: "100%",
			aspectRatio: 40 / 21,
		}
	}
}

StoryPanel.contextType = ThemeContext

StoryPanel.defaultProps = {
	pathUrl: settings.apiBaseURL + "/",
	showComments: true
};

const styles = StyleSheet.create({
	mainContainer: (bgColor) => {
		return {
			flex: 1,
			backgroundColor: bgColor
		}
	},
	container: {
		flex: 1,
		flexDirection: 'row',
	},
	userInfo: (isPostCommentStory = false) => {
		return {
			flex: 1,
			marginStart: isPostCommentStory ? 0 : 8,
			alignSelf: 'center',
		}
	},
	likeButtonContainer: {
		alignSelf: 'center'
	},
	panelInfoContainer: {
		marginStart: scale(3),
		flexDirection: 'row',
		alignItems: 'flex-end'
	},
	headerText: {
		alignSelf: 'center',
		marginStart: scale(3)
	},
	domainViewContainer: {
		justifyContent: 'space-between',
		marginTop: scale(5),
	},
	countText: {
		alignSelf: 'center',
		marginLeft: scale(3)
	},
	navigationIcons: (isEnable = true) => {
		return {
			width: scale(22),
			height: scale(22),
			tintColor: isEnable ? Color.BOTTOM_ICON_TINT_COLOR : Color.DISABLE_BOTTOM_ICON_TINT_COLOR,
		}
	},
	likeIcons: (isEnable = true) => {
		return {
			tintColor: isEnable ? Color.BOTTOM_ICON_TINT_COLOR : Color.DISABLE_BOTTOM_ICON_TINT_COLOR,
		}
	},
	trippleDotIcon: {
		width: scale(16),
		height: scale(16),
		marginLeft: 5,
		alignSelf: 'center',
		tintColor: Color.DATE_TIME_COLOR
	},
	descContainer: (hasDescription) => {
		return {
			marginTop: 12, //As our quote image has some padding
			marginBottom: hasDescription ? 12 : 0
		}
	},
	seriesTitleText: {
		textAlign: 'left',
		fontWeight: 'bold'
	},
	titleText: {
		textAlign: 'left',
		marginStart: scale(6),
		marginTop: scale(5)
	},
	tocDateText: {
		textAlign: 'left',
	},
	domainName: {
		marginStart: scale(6),
		textAlign: 'left',
	},
	roundImage: {
		width: scale(40),
		height: scale(40),
		borderRadius: scale(24),
		borderColor: Color.ROUNDED_BORDER_COLOR,
		borderWidth: scale(0.5),
		overflow: "hidden",
	},
	descriptionText: {
		lineHeight: scale(20),
		color: Color.DESCRIPTION_FONT_COLOR,
		fontFamily: SystemFont.SELECTED_FONT,
		fontSize: Platform.OS == 'ios' ? scale(13) : scale(12),
	},
	commentText: {
		lineHeight: 25,
		color: Color.NORMAL_TEXT_COLOR,
		fontFamily: SystemFont.SELECTED_FONT,
		fontSize: scale(15), //corner case i.e having 15 size.
	},
	previewPanelContainer: (item, isPostCommentStory) => {
		if (isPostCommentStory) {
			return {}
		}

		if (!item.style) {
			return {
				borderRadius: 12,
				overflow: 'hidden'
			}
		}

		const borderWidth = item.style["borderWidth"];
		const borderColor = item.style["borderColor"];

		return {
			borderRadius: 12,
			overflow: 'hidden',
			borderColor: borderColor,
			borderWidth: borderWidth,
			elevation: 12
		}
	},
	bottomViewContainer: {
		flex: 1,
		flexDirection: 'row',
		marginTop: scale(10),
		marginBottom: scale(5),
	},
	bottomView: {
		flex: 1,
		flexDirection: 'row',
		justifyContent: 'center',
		marginTop: scale(10),
		marginBottom: scale(5),
	},
	feedPopulateReasonView: (isFirstFeed) => {
		return {
			paddingTop: isFirstFeed ? 0 : navigationStyle.panelsMargin,
			paddingBottom: 12
		}
	},
	dateTimeView: (isTocPanel, isPostCommentStory) => {
		return {
			textAlign: 'left',
			marginTop: isTocPanel && !isPostCommentStory ? 8 : 4
		}
	},
	mainStoryTextView: (isPostCommentStory, isSameUserId) => {
		return {
			paddingTop: storyPanelStyle.paddingTop,
			paddingLeft: 16,
			paddingRight: isPostCommentStory && isSameUserId ? 10 : 16
		}
	},
	viewComicButton: {
		borderWidth: 1,
		borderRadius: 6,
		flexDirection: 'row',
		alignItems: 'center'
	},
	titleView: {
		flex: 1,
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'space-between'
	},
	comicButtonTextView: (isSameUserId) => {
		return {
			paddingTop: 4,
			paddingBottom: 4,
			paddingLeft: isSameUserId ? 6 : 8,
			paddingRight: isSameUserId ? 6 : 8
		}
	},
	comicConfigView: (isCommentsPage) => {
		return {
			marginTop: isCommentsPage ? 12 : 0,
			marginBottom: isCommentsPage ? 0 : 12
		}
	},
	freemiumTextView: {
		marginLeft: 4
	},
	seriesIconView: {
		height: 16,
		width: 16,
		marginRight: 4
	},
	titleReadIconView: {
		flex: 1,
		flexDirection: 'row',
		alignItems: 'flex-end'
	},
	titleTextView: (isPostCommentStory) => {
		return {
			flex: 1,
			flexDirection: 'row',
			justifyContent: isPostCommentStory ? 'flex-start' : 'space-between'
		}
	}
})