import React from "react";
import { Text, StyleSheet, TouchableOpacity, Dimensions } from "react-native";
import Panel from "./Panel";
import { ThemeContext } from "../../Contexts";
import { View } from "native-base";
import FastImage from "react-native-fast-image";
import { navigationStyle, readIndicatorIcon } from "../../config/styles";
import { Utils } from "../../config/Utils";
import { Constants } from "../../config/Constants";
import SessionManager from "../../config/SessionManager";
import { settings } from "../../config/settings";

let dimensions = Dimensions.get('window')

export default class ListPanel extends Panel {

    constructor(props) {
        super(props)

        this.imageStyle = this.imageStyle.bind(this)
        this.renderPanel = this.renderPanel.bind(this)
        this.renderReadIndicator = this.renderReadIndicator.bind(this)

        this.comicConfig = SessionManager.instance.getComicsConfigList()
    }

    shouldUpdated(nextProps, nextState) {
        const currentProps = this.props.item.item
        const newProps = nextProps.item.item

        const needsToUpdate = currentProps.pathUrl != newProps.pathUrl
        return needsToUpdate
    }

    renderReadIndicator() {
        return (
            <View style={{ marginRight: 4 }}>
                <FastImage
                    style={styles.readIndicatorView}
                    source={require('../../../assets/double_check_icon.png')} />
            </View>
        )
    }

    renderPanel() {
        const { item } = this.props.item
        const { pathUrl } = this.props
        let panelDate = Utils.getFormattedDate(item.createdAt, Constants.LIST_DATE_FORMAT)
        let panelImage = item.image ? Utils.resolvePath(settings.apiBaseURL, item.image) : item.image
        let isLastFeedPanel = this.props.item.index == this.props.comicData.panels.length - 1
        let formattedPageViewCount = Utils.formatViewCount(item.views)
        let isExternalLink = item && item.actionType == "website"
        let isPremiumComic = item && item['show-to'] == Constants.SUBSCRIBERS_ONLY
        let premiumComicIconUrl = ""
        if (isPremiumComic && !Utils.isEmptyObject(this.comicConfig)) {
            const freemiumConfig = this.comicConfig['show-to'][Constants.SUBSCRIBERS_ONLY];
            premiumComicIconUrl = Utils.resolvePath(pathUrl, freemiumConfig.icon)
        }
        const isComicReaded = item && item.isRead
        const isMonthFirstComic = ((item && item.isFirstComic) || item.isFirstFeedPanel)
        const isMonthLastComic = ((item && item.isLastComic) || isLastFeedPanel)

        return (
            <View>
                {isMonthFirstComic && <Text style={[this.context.bodyMiniBold, { marginTop: 32, marginBottom: 12 }]}>{item.dateTitle}</Text>}
                <View style={[styles.innerContainerView(isMonthFirstComic, isMonthLastComic), { backgroundColor: this.context.colors.textInverse }]}>
                    <TouchableOpacity
                        style={[styles.prevPanelView, { borderColor: this.context.colors.chatBubbles, backgroundColor: this.context.colors.textInverse }]}
                        onPress={() => this.onPanelTap(item)}>
                        <FastImage
                            style={this.imageStyle(item.width, item.height, navigationStyle.panelsMargin)}
                            source={panelImage ? { uri: panelImage } : require('../../../assets/image_unavailable.jpg')}
                        />
                        <View style={[styles.panelTextView, { backgroundColor: isComicReaded ? this.context.colors.chatBubbles : this.context.colors.textInverse }]}>
                            <View style={styles.titleView}>
                                {isComicReaded && this.renderReadIndicator()}
                                <Text numberOfLines={1} style={[this.context.bodyMiniBold, { flex: 1 }]}>{item.title}</Text>
                                {isExternalLink && <FastImage
                                    style={styles.panelIconView}
                                    source={require('../../../assets/external_link_icon.png')} />}
                                {isPremiumComic && premiumComicIconUrl &&
                                    <FastImage
                                        style={styles.panelIconView}
                                        source={{ uri: premiumComicIconUrl }} />}
                            </View>
                            <View style={styles.dateViewsStyle}>
                                <Text style={this.context.bodyMini}>{panelDate}</Text>
                                <View style={styles.viewsImageStyle}>
                                    <Text style={this.context.bodyMini}>{formattedPageViewCount}</Text>
                                    <FastImage
                                        style={styles.viewImageStyle}
                                        source={require('../../../assets/eye_icon_one.png')} />
                                </View>
                            </View>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>
        )
    }

    render() {
        const { item, index } = this.props.item

        return (
            <View style={[styles.mainContainer, { backgroundColor: item.bgColor }]}>
                {this.renderContainer()}
            </View>
        )
    }

    imageStyle(w = 800, h = 420, margin = 20) {
        let aspectRatio = ((dimensions.width - ((margin) * 2)) / w) / 2.7

        return {
            width: w * aspectRatio,
            height: 65,
            overflow: "hidden"
        }
    }
}

ListPanel.contextType = ThemeContext

const styles = StyleSheet.create({
    mainContainer: {
        flex: 1,
    },
    prevPanelView: {
        flex: 1,
        flexDirection: 'row',
        borderWidth: 1,
        borderRadius: 8,
        overflow: 'hidden'
    },
    panelTextView: {
        flex: 1,
        padding: 8
    },
    dateViewsStyle: {
        flex: 1,
        flexDirection: 'row',
        marginTop: 6,
        justifyContent: 'space-between',
        alignItems: 'center'
    },
    innerContainerView: (isFirstFeedPanel, isLastFeedPanel) => {
        return {
            paddingStart: 20,
            paddingRight: 20,
            paddingTop: isFirstFeedPanel ? 20 : 8,
            paddingBottom: isLastFeedPanel ? 20 : 0,
            borderTopLeftRadius: isFirstFeedPanel ? 12 : 0,
            borderTopEndRadius: isFirstFeedPanel ? 12 : 0,
            borderBottomLeftRadius: isLastFeedPanel ? 12 : 0,
            borderBottomEndRadius: isLastFeedPanel ? 12 : 0,
            overflow: 'hidden',
        }
    },
    viewImageStyle: {
        height: 14,
        width: 14,
        marginLeft: 2
    },
    viewsImageStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    panelIconView: {
        height: 14,
        width: 14,
        marginLeft: 6
    },
    titleView: {
        flex: 1,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center'
    },
    readIndicatorView: {
        height: 16,
        width: 16
    }
}
)