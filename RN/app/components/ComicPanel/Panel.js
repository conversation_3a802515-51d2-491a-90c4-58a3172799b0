import React, { Component } from 'react'
import {
  TouchableWithoutFeedback,
  StyleSheet,
  Dimensions,
  View,
  TouchableOpacity,
  Platform,
  Image,
} from 'react-native'
import { Text, Button } from 'native-base';
import LinearGradient from 'react-native-linear-gradient';
import { navigationStyle, commentContainerStyle, buyButton } from '../../config/styles';
import { Utils } from '../../config/Utils';
import PurchasePanel from '../PurchasePanel';
import { scale } from 'react-native-size-matters';
import { Color } from '../../config/Color';
import { SystemFont } from '../../config/Typography';
import HTML, { defaultSystemFonts } from 'react-native-render-html';
import SessionManager from '../../config/SessionManager';
import ImageDownloader from '../ImageDownloader';
import { Constants } from '../../config/Constants';
import CommentsPanel from '../feed/comments/CommentsPanel';
import FastImage from 'react-native-fast-image'
import { ThemeContext } from '../../Contexts';
import { settings } from '../../config/settings';
import PhotoView from 'react-native-photo-view';
import ProgressBar from '../ProgressBar';

const dimen = Dimensions.get('window');

export default class Panel extends Component {

  constructor(props) {
    super(props)

    let filePath = null;
    this.isImageLoaded = true;
    const item = this.props.item.item
    if (item && item.image) {
      const panelData = item
      this.isImageLoaded = false;
      let finalUrl = Utils.resolvePath(this.props.pathUrl, panelData.image)
      ImageDownloader.downloadPanelImage(finalUrl, panelData.md5sum).then((filePath) => {
        this.setState({ imagePath: filePath })
        item.imageLocalPath = filePath;
      })
      filePath = panelData.image;
    }

    const subsData = SessionManager.instance.getSubsProgressData()

    this.state = {
      imagePath: filePath,
      userImagePath: null,
      episods: [],
      pageInfo: null,
      firstComicItem: null,
      subscriberCount: subsData.subscriberCount ? subsData.subscriberCount : '',
      subsTotalAmount: subsData.subsTotalAmount ? subsData.subsTotalAmount : '',
      navigationPanels: {}
    }

    this.getStoryDetails(item)

    let { isComicURL, currentPageStatus } = this.props

    if (item.lastComicPanel && isComicURL && currentPageStatus && currentPageStatus.storyID) {
      const storyID = currentPageStatus.storyID
      let data = { data: { storyIDs: [storyID] } }
      this.props.getStoryComments(data)
    }

    this.getComicStatus = this.getComicStatus.bind(this)
    this.getComicInfo = this.getComicInfo.bind(this)
    this.isFollowingSeries = this.isFollowingSeries.bind(this)
    this.getStoryDetails = this.getStoryDetails.bind(this)
    this.onCommentPress = this.onCommentPress.bind(this)
    this.onPanelTap = this.onPanelTap.bind(this)
    this.renderSeparator = this.renderSeparator.bind(this)
    this.isStoriesTemplatePanel = this.isStoriesTemplatePanel.bind(this)
    this.renderContainer = this.renderContainer.bind(this)
    this.renderPurchaseContent = this.renderPurchaseContent.bind(this)
    this.renderImage = this.renderImage.bind(this)
    this.renderLastPanel = this.renderLastPanel.bind(this)
    this.onSubscribersProgrssFetched = this.onSubscribersProgrssFetched.bind(this)
    this.renderSubsProgress = this.renderSubsProgress.bind(this)
    this.renderButton = this.renderButton.bind(this)
    this.onAnchorTap = this.onAnchorTap.bind(this)
    this._loadMoreButtonComponent = this._loadMoreButtonComponent.bind(this)
  }

  shouldComponentUpdate(nextProps, nextState) {
    let needsToUpdate = this.shouldUpdated(nextProps, nextState)

    if (!needsToUpdate) {
      const currentProps = this.props.item.item
      const newProps = nextProps.item.item

      const currentInfo = this.getComicInfo(this.props, currentProps.action);
      const newInfo = this.getComicInfo(nextProps, newProps.action);

      needsToUpdate = (currentProps.userComments != newProps.userComments || currentInfo != newInfo || currentProps.title != newProps.title || currentProps.description != newProps.description || currentProps.action != newProps.action || currentProps.comments != newProps.comments || currentProps.comment != newProps.comment || currentProps.template != newProps.template || !this.isImageLoaded || this.state.episods != nextState.episods || (this.state.episods && this.state.episods.length != nextState.episods.length) || this.props.comicData != newProps.comicData)

      if (!needsToUpdate) {
        if (currentProps.lastComicPanel && this.props.isComicURL) {
          const comicCurrentInfo = this.getComicInfo(this.props, Utils.getMeaningFullURL(this.props.pathUrl));
          const comicNewInfo = this.getComicInfo(nextProps, Utils.getMeaningFullURL(this.props.pathUrl));

          needsToUpdate = comicCurrentInfo != comicNewInfo || this.props.alerts != nextProps.alerts;
        }
      }
    }

    return (needsToUpdate == undefined) ? false : needsToUpdate;
  }

  onAnchorTap() {
    const { item } = this.props.item
    this.props.openWebView(item.action)
  }

  onSubscribersProgrssFetched(response) {
    if (response) {
      const totalSubsCount = response.newSubscription + response.subscriptionRenewCount
      this.setState({ subsTotalAmount: response.totalAmount, subscriberCount: totalSubsCount })
    }
  }

  renderSubsProgress(item) {
    if (!item || item.template != 'subscribers') {
      return null
    }

    const goalFormattedCount = Utils.formatNumber(item.goal)
    const subsFormattedCount = Utils.formatNumber(this.state.subscriberCount)
    const subsAmtFormattedValue = Utils.formatNumber(this.state.subsTotalAmount)

    let isForSubsProgBar = (item.goalType == "subscribers")
    let progressValue = this.state.subsTotalAmount / parseInt(item.goal)
    let subsGoalFormattedCount = ""
    if (isForSubsProgBar) {
      subsGoalFormattedCount = Utils.formatNumber(item.goalSubscribers)
      progressValue = this.state.subscriberCount / parseInt(item.goalSubscribers)
    }

    return (
      <View style={styles.progressContainer}>
        <View style={{ flexDirection: 'row' }}>
          <Text style={this.context.h2}>{isForSubsProgBar ? subsFormattedCount : `$${subsAmtFormattedValue}`}</Text>
          <Text style={[this.context.p, styles.raisedText]}>{isForSubsProgBar ? `subscribers of ${subsGoalFormattedCount} goal.` : `of $${goalFormattedCount} monthly goal.`}</Text>
        </View>
        <ProgressBar value={progressValue} />
        <View>
          <Text style={this.context.p}>{isForSubsProgBar ? `$${subsAmtFormattedValue} monthly revenue` : `${subsFormattedCount} subscribers`}</Text>
          {this.renderButton(item)}
        </View>
      </View>
    )
  }

  renderButton(item) {
    if (!item.button || (Utils.isSubscriptionURL(this.props.pathUrl) && settings.SUBSCRIBE_END_URL == item.action)) {
      return null
    }

    return (
      <View style={styles.buttonView}>
        <Button
          variant='solid'
          style={[buyButton, styles.buyButton(item.template == Constants.SUBSCRIBERS_TEMPLATE), { backgroundColor: this.context.colors.logoRed, borderColor: this.context.colors.logoRed }]}
          onPress={() => { this.onPanelTap(item) }}>
          <Text style={[this.context.p, { color: this.context.colors.textInverse }]}>
            {item.button}
          </Text>
        </Button>
      </View>
    )
  }

  getComicStatus(props) {
    if (!props.currentPageStatus) {
      return "";
    }

    let viewCount = props.currentPageStatus.viewCount
    let likeCount = props.currentPageStatus.likeCount
    let commentCount = props.currentPageStatus.commentCount
    let repostCount = props.currentPageStatus.repostCount
    let isLikeChanged = props.currentPageStatus.isLikeChanged
    let isLiked = props.currentPageStatus.isLiked

    return viewCount + " " + likeCount + " " + commentCount + " " + repostCount + " " + isLikeChanged + " " + isLiked
  }

  getComicInfo(props, actionURL) {
    if (!props.multiplePanelStatus || !actionURL || !props.multiplePanelStatus[actionURL]) {
      return "";
    }

    let viewCount = props.multiplePanelStatus[actionURL].viewCount
    let likeCount = props.multiplePanelStatus[actionURL].likeCount

    return viewCount + " " + likeCount
  }

  isFollowingSeries(props) {
    const { item } = props.item

    let isSubscribed = false;
    if (item.action) {
      isSubscribed = props.isSubscribed(item.action);
    }

    const isEnabled = isSubscribed;
    return isEnabled;
  }

  async getStoryDetails(item) {
    const { isComicURL } = this.props
    if ((item.template == "toc" || item.template == "story") && (!item.storyID || isComicURL)) {
      let data = {
        data: {
          action: item.action ? item.action : null,
          image: item.image ? item.image : null,
          url: item.action ? item.action : null,
          title: item.title ? item.title : null,
          description: item.description ? item.description : null,
          series: item.series ? item.series : null
        }
      }
      await this.props.getStoryDetails(data)
    }
  }

  onCommentPress(storyID, showToAnonymousUser = true, action = null) {
    let storyData = { storyID: storyID, refType: Constants.STORY, action: action ? action : this.props.pathUrl }
    this.props.onShowCommentPress(Constants.COMMENT, null, null, null, storyData, showToAnonymousUser)
  }

  onPanelTap(item, forceOpenLink = false) {

    if (item.action) {
      if (item.action == "staticPanel") {
        this.props.onIconPress(Constants.SHARE, item)
      } else if (item.actionType == "website") {
        const panelItem = this.props.item
        if (panelItem && panelItem.item && !forceOpenLink && !this.props.isPostCommentStory && (panelItem.item.template == 'navigation' || panelItem.item.template == 'carousel' || panelItem.item.template == 'episodes' || panelItem.item.template == 'list' || panelItem.item.template == 'story' || panelItem.item.template == 'toc' || panelItem.item.template == 'stories')) {
          const storyID = item.storyID ? item.storyID : item.id ? item.id : null
          this.onCommentPress(storyID, true, item.action)
        } else {
          this.props.recordPageView(item.action, null, item.storyID)
          this.props.openWebView(item.action)
        }
      } else if (item.actionType == "tinyview" && item.action == "follow") { // COM - 264 We should get rid of action=get-alerts. We will create similar panels but with something like action=follow.
        const isFollowEnabled = this.props.isSubscribed()
        this.props.onFollowPress(this.props.pathUrl, isFollowEnabled, false, false)
      } else if (item.actionType == "tinyview" && item.action == "share") {
        this.props.onIconPress(Constants.SHARE, item)
      } else if (item.actionType == "tinyview" && item.action == "signup") {
        this.props.navigateToSignUp()
      } else if (item.actionType == "tinyview" && item.action == "restore-purchases") {
        this.props.restorePurchase(false)
      } else if (item.actionType == "tinyview" && item.action == "inviteFriends") {
        this.props.navigateToInviteBottomSheet()
      } else if (item.actionType == "tinyview" && item.action == "manageAlerts") {
        Utils.navigateToManageAlertsPage()
      } else if (item.actionType == "tinyview" && item.action == Constants.GET_UNLIMITED_ACCESS) {
        this.props.getUnlimitedComicAccessSheet()
      } else if (item.actionType == "tinyview" && (item.action == Constants.UNLOCK_PREMIUM_COMIC || item.action == Constants.UNLOCK_BONUS_PANEL)) {
        this.props.getLockedComicActionSheet(item.action)
      } else if (item.action || item.userId) {
        const isSeriesNavigation = this.props.comicData && this.props.comicData.isNavFromSeriesPage
        const isFromNotifications = this.props.comicData && this.props.comicData.isNavFromNotification
        let myFeedID = item.id
        if (!isSeriesNavigation && !isFromNotifications && !SessionManager.instance.getShowAllComics() && !myFeedID) {
          let pageFeedID = this.props.comicData && this.props.comicData.myFeedID
          myFeedID = pageFeedID
        }
        this.props.openChapter(item.action, this.props.item, isSeriesNavigation, myFeedID, isFromNotifications) //item.id is myFeedId coming from BE
      }
    } else if (item.actionType == "appStore") {
      Utils.openStore()
    } else if (item.userId) {
      this.props.navigateToUserProfile(item)
    }
  }

  renderSeparator(hasTopMargin = true) {
    if (this.props.isHideReactionComponentIcons && this.props.isHideReactionComponentIcons() && !hasTopMargin) {
      hasTopMargin = true
    }

    return (
      <View style={[styles.separatorStyle, { backgroundColor: this.context.colors.separators }]} />
    )
  }

  renderPanelDescription(description, descriptionStyle) {
    return (
      <HTML contentWidth={dimen.width} textSelectable={true} allowFontScaling baseStyle={descriptionStyle} source={{ html: description ? description : " " }} imagesMaxWidth={dimen.width} tagsStyles={{ "a": { "fontStyle": "normal", "fontFamily": descriptionStyle.fontFamily }, "b": this.context.pBold, "p": { paddingTop: 0, marginBottom: 0, marginTop: 0 } }} systemFonts={[...defaultSystemFonts, descriptionStyle.fontFamily]} renderersProps={{ a: { onPress: this.onAnchorTap } }} />
    )
  }

  isStoriesTemplatePanel() {
    const { item } = this.props.item
    const isStoriesTemplatePanel = item.template && item.template == Constants.TEMPLATE_TYPE_STORIES
    return isStoriesTemplatePanel
  }

  renderLastPanel() { // Showing comic info and comments on the comic page.
    const { item } = this.props.item
    const { isComicURL, showComments, currentPageStatus } = this.props

    if (!item.lastComicPanel || !isComicURL) {
      return null
    }

    const comicComments = (currentPageStatus && currentPageStatus.commentCount) ? currentPageStatus.commentCount : 0
    const isEmptyComments = comicComments === 0 ? true : false
    const commentsCountSuffix = (comicComments === 1) ? "Comment" : "Comments"

    return (
      <View style={[styles.bottomSeriesContainer, styles.lastPanelContainer, { backgroundColor: this.context.colors.textInverse }]}>
        <View style={styles.bottomBlockView}>
          <View style={{ flex: 1 }}>
            <TouchableOpacity
              style={{ flex: 1, justifyContent: 'center' }}
              onPress={() => { this.onListedCommentTap(true) }}>
              <Text style={this.context.pBold}>{comicComments + " " + commentsCountSuffix}</Text>
            </TouchableOpacity>
          </View>
          <TouchableOpacity
            style={[styles.commentButtonView, { borderColor: this.context.colors.separators, backgroundColor: this.context.colors.textInverse }]}
            onPress={() => { this.onListedCommentTap(false) }}>
            <Text style={[this.context.bodyMini, { color: this.context.colors.textBold }]}>{Constants.POST_A_COMMENT}</Text>
          </TouchableOpacity>
        </View>
        {isEmptyComments && <Text style={[this.context.p, styles.emptyCommentsText]}>{Constants.EMPTY_COMMENTS_TEXT}</Text>}
        {showComments &&
          this.renderCommentContainer(item, [commentContainerStyle(isEmptyComments), { marginTop: isEmptyComments ? 12 : 0 }])
        }
      </View>
    )
  }

  renderCommentContainer(item, containerStyle = {}) {
    const { isComicURL, pathUrl } = this.props
    let visibleCount = this.seriesData ? isComicURL ? this.seriesData.showCommentsComics : this.seriesData.showCommentsFeed : 2

    return (
      <View style={containerStyle}>
        {item.userComments && item.userComments.length > visibleCount &&
          <TouchableOpacity style={{ marginTop: Utils.isComicPage(pathUrl) ? 12 : 0, marginBottom: 4 }} onPress={() => { this.onListedCommentTap() }} >
            <Text style={this.context.p}>{Constants.SEE_PREVIOUS_COMMENT}</Text>
          </TouchableOpacity>
        }
        {this.renderUserComments(item)}
      </View>
    )
  }

  renderUserComments(panelItems) {
    const valueProps = {
      showRepliedComment: false, onCountPress: this.props.onCountPress, onCommentLikeCountPress: this.props.onCommentLikeCountPress,
      likeDislikeComment: this.props.likeDislikeComment, onReplyPress: this.onReplyPress,
      onEditPress: this.onEditPress, navigateToUserProfile: this.props.navigateToUserProfile, deleteComment: this.props.deleteComment,
      onFlagCommentTap: this.props.onFlagCommentTap, onBlockUserTap: this.props.onBlockUserTap,
      onListedCommentTap: this.onListedCommentTap, panelItems: panelItems
    }

    let commentsArray = []

    let visibleCount = this.seriesData ? this.props.isComicURL ? this.seriesData.showCommentsComics : this.seriesData.showCommentsFeed : null
    if (visibleCount == undefined || visibleCount == null) {
      visibleCount = 2
    }
    if (panelItems && panelItems.userComments && panelItems.userComments.length > 0) {
      for (const index in panelItems.userComments) {
        if (index < visibleCount) {
          commentsArray.push(panelItems.userComments[index])
        }
      }
      if (visibleCount > 1) {
        commentsArray.reverse()
      }

    }

    return commentsArray.map((item, index) => {
      if (visibleCount > 5) {
        visibleCount = 5
      }
      if (index >= visibleCount) {
        return null
      }
      return (
        <CommentsPanel key={item.commentId + item.commentText} index={index} item={item} {...valueProps} />
      )
    })
  }

  _loadMoreButtonComponent() {
    if (!this.props.hasMoreData || this.props.isComicURL || this.props.showFooterPlaceHolder) {
      return null
    }

    return (
      <View style={styles.bottomContainer}>
        <TouchableOpacity style={[styles.chevronContainer, { borderColor: this.context.colors.separators }]} onPress={this.props.onEndReached}>
          <Text style={{ ...this.context.bodyMini }}>{Constants.LOAD_MORE}</Text>
        </TouchableOpacity>
      </View>
    )
  }

  renderContainer() {
    const { item } = this.props.item
    const { index } = this.props.item
    const { isPostCommentStory, comicData } = this.props
    const isStoryTocTemp = item.template && (item.template == "toc" || item.template == "story")
    let isPanelReqTopMargin = false
    if (Utils.isComicURL(this.props.pathUrl) && isStoryTocTemp) {
      if (index == 0 || (index == 1 && !SessionManager.instance.hasAnySubscriptionPurchase())) {
        isPanelReqTopMargin = true
      }
    }

    let isLastFeedPanel = this.props.item.index == (comicData && comicData.panels.length - 1)

    return (
      <View style={styles.containerStyle(item, isPostCommentStory, isPanelReqTopMargin, isLastFeedPanel)}>
        <View style={styles.innerContainerStyle(item, isPostCommentStory)}>
          {item.inAppPurchase
            ?
            this.renderPurchaseContent(item)
            :
            this.renderPanel()
          }
        </View>
        {isLastFeedPanel && this._loadMoreButtonComponent()}
      </View>
    )
  }

  renderPurchaseContent(panelItem) {
    return (
      <PurchasePanel key={"purchasePanel " + panelItem.productId} item={panelItem} requestPurchase={this.props.requestPurchase} loadingProductId={this.props.loadingProductId} />
    )
  }

  renderImage() {
    const { isAlreadyPurchased, isAlreadyPurchasedFromInfluence } = this.props
    const { item } = this.props.item
    const { index } = this.props.item
    const { totalPanelCount, isZoomPanEnable } = this.props
    const { comicData } = this.props

    if (item && item.template == "episodes") {
      return;
    }

    const isComicPageURL = comicData && comicData.panels && Utils.isComicURL(this.props.pathUrl) && (!Utils.isTinyviewComicsPage(this.props.pathUrl) || Utils.isSubscriptionURL(this.props.pathUrl))

    const imageStyle = this.imageStyle(item.width, item.height, navigationStyle.panelLeftRightMargin, item)

    return (
      (!Utils.isPremiumComic(this.props.comicData) || isAlreadyPurchased || isAlreadyPurchasedFromInfluence)
        ?
        <TouchableWithoutFeedback
          onPress={() => {
            if (!item.button) {
              this.onPanelTap(item)
            }
          }}
          onLongPress={() => {
            if (Platform.OS === 'ios' && parseInt(Platform.Version, 10) <= 12) {
              this.props.shareImage(item)
            }
          }}
          accessible={true}
          accessibilityLabel={"PanelImage" + index}>
          <View style={styles.panelImageView(item.template, isComicPageURL)}>
            <View style={styles.imageContainerStyle(item)}>
              {(!Utils.checkData(this.state.imagePath) || Platform.OS == "android") ?
                <FastImage
                  style={{ ...imageStyle }}
                  source={this.state.imagePath ? { uri: this.state.imagePath } : require('../../../assets/image_unavailable.jpg')}
                  resizeMode='contain'
                  onLoad={() => {
                    this.isImageLoaded = true;
                  }}
                />
                :
                <PhotoView
                  showsHorizontalScrollIndicator={false}
                  showsVerticalScrollIndicator={false}
                  minimumZoomScale={1}
                  maximumZoomScale={isZoomPanEnable ? 4 : 1}
                  style={{ ...imageStyle }}
                  source={{ uri: this.state.imagePath }}
                  onLoad={() => {
                    this.isImageLoaded = true;
                  }}
                />
              }
            </View>
          </View>
        </TouchableWithoutFeedback>

        :
        <TouchableWithoutFeedback
          onLongPress={() => {
            if (Platform.OS === 'ios' && parseInt(Platform.Version, 10) <= 12) {
              this.props.shareImage(item)
            }
          }}
          onPress={() => {
            if (!item.button) {
              this.onPanelTap(item)
            }
          }}
          accessible={true}
          accessibilityLabel={"PanelImage" + index}>
          <View style={styles.panelImageView(item.template, isComicPageURL)}>
            <View style={styles.imageContainerStyle(item)}>
              {Utils.log("Comic images" + item.image)}
              <FastImage
                style={[this.imageStyle(item.width, item.height, navigationStyle.panelLeftRightMargin, item)
                ]}
                source={{ uri: this.state.imagePath }}
                onLoad={() => {
                  this.isImageLoaded = true;
                  Utils.log("below Image load success " + item.title)
                }}
              />
              {(totalPanelCount - 1) === index &&
                <LinearGradient start={{ x: 0, y: 1 }} end={{ x: 0, y: 0 }}
                  colors={['white', 'rgba(255, 255, 255, 0.9)', 'rgba(255, 255, 255, 0.8)', 'rgba(255, 255, 255, 0.7)', 'rgba(255, 255, 255, 0.4)', 'rgba(255, 255, 255, 0.2)']}// transparent
                  style={[this.imageStyle(item.width, item.height, navigationStyle.panelLeftRightMargin, item), {
                    position: 'absolute', backgroundColor: 'transparent',
                  }
                  ]}
                />
              }
            </View>
          </View>
        </TouchableWithoutFeedback>
    )
  }
}

Panel.contextType = ThemeContext

const styles = StyleSheet.create({
  containerStyle: (item, isPostCommentStory, isPanelReqTopMargin, isLastFeedPanel) => {
    if (isPostCommentStory) {
      return {}
    }
    const isStoryTOCTemp = item.template == "story" || item.template == "toc"
    const isSubscribersEpisodeTemp = item.template == "subscribers" || item.template == Constants.TEMPLATE_TYPE_STORIES
    const isListPanel = item.template == "list"

    return {
      shadowColor: "#000",
      shadowOpacity: isStoryTOCTemp || isSubscribersEpisodeTemp ? 0.1 : 0,
      shadowRadius: isStoryTOCTemp || isSubscribersEpisodeTemp ? 12 : 0,
      paddingBottom: ((isListPanel && !isLastFeedPanel) || item.lastComicPanel) ? 0 : navigationStyle.panelsMargin,
      paddingTop: isPanelReqTopMargin ? navigationStyle.panelsMargin : 0,
      marginLeft: isStoryTOCTemp ? 0 : navigationStyle.panelLeftRightMargin,
      marginRight: isStoryTOCTemp ? 0 : navigationStyle.panelLeftRightMargin
    }
  },
  innerContainerStyle: (item, isPostCommentStory) => {
    if (isPostCommentStory) {
      return {}
    }

    const isMarginRequired = (item.template == "story" || item.template == "toc");

    return {
      marginLeft: isMarginRequired ? navigationStyle.panelLeftRightMargin : 0,
      marginRight: isMarginRequired ? navigationStyle.panelLeftRightMargin : 0
    }
  },
  bottomSeriesContainer: {
    marginTop: navigationStyle.panelsMargin,
    marginBottom: navigationStyle.panelsMargin,
    marginLeft: navigationStyle.panelLeftRightMargin,
    marginRight: navigationStyle.panelLeftRightMargin,
  },
  navigationButton: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  navigationIcons: (enable = true) => {
    return {
      alignItems: 'center',
      width: scale(22),
      height: scale(22),
      opacity: enable ? 1 : 0.2,
      tintColor: enable ? Color.TOP_NAVBAR_ICON_TINT_COLOR : Color.DISABLE_BOTTOM_ICON_TINT_COLOR,
    }
  },
  buttonText: (enable = true) => {
    return {
      textAlign: 'center',
      marginLeft: scale(-10),
      color: enable ? Color.RED_TEXT_COLOR : Color.DISABLE_BOTTOM_ICON_TINT_COLOR,
      fontSize: Platform.OS == 'ios' ? scale(13) : scale(12),
    }
  },
  bottomViewContainer: {
    flex: 1,
    marginTop: scale(0)
  },
  descriptionStyle: {
    color: Color.DESCRIPTION_FONT_COLOR,
    fontFamily: SystemFont.SELECTED_FONT,
    fontSize: Platform.OS == 'ios' ? scale(13) : scale(12),
  },
  htmlContainerStyle: {
    marginTop: 10,
    marginBottom: 2,
  },
  titleText: {
    color: Color.BLACK_COLOR,
    fontFamily: SystemFont.SELECTED_FONT,
    fontWeight: 'bold',
    fontSize: scale(26),
  },
  homeButtonContainer: {
    alignItems: 'flex-start',
    flexDirection: 'row'
  },
  homeText: {
    textAlign: 'left',
    fontWeight: 'bold'
  },
  likeText: {
    color: Color.DESCRIPTION_FONT_COLOR,
    fontFamily: SystemFont.SELECTED_FONT,
    marginLeft: scale(-5),
    fontSize: Platform.OS == 'ios' ? scale(13) : scale(12),
    fontWeight: 'bold'
  },
  viewCountText: (isComicPage) => {
    return {
      alignSelf: isComicPage ? 'flex-start' : 'center',
      marginTop: scale(10)
    }
  },
  topLine: {
    backgroundColor: Color.STORYPANEL_BOTTOM_BORDER_COLOR,
    width: '100%',
    height: 1,
    marginTop: 15
  },
  imageContainerStyle: (item) => {
    if (!item.border) {
      return
    }

    const width = item.border["border-width"];
    const color = item.border["border-color"];
    const hasTopBorder = item.border["border-top"];
    const hasLeftBorder = item.border["border-left"];
    const hasRightBorder = item.border["border-right"];
    const hasBottomBorder = item.border["border-bottom"];

    return {
      borderColor: color,
      borderTopWidth: hasTopBorder ? width : 0,
      borderLeftWidth: hasLeftBorder ? width : 0,
      borderRightWidth: hasRightBorder ? width : 0,
      borderBottomWidth: hasBottomBorder ? width : 0,
    }
  },
  separatorStyle: {
    height: 0.5,
    width: '100%'
  },
  followingButton: {
    height: 23,
    marginLeft: 10,
  },
  followButton: {
    height: 23,
    marginLeft: 10,
  },
  followText: {
    fontSize: scale(10)
  },
  lastPanelContainer: {
    borderRadius: 12
  },
  panelImageView: (isTemplate = null, isComicPage) => {
    return {
      marginTop: !isTemplate && isComicPage ? 20 : 0
    }
  },
  progressContainer: {
    paddingTop: 16
  },
  raisedText: {
    flex: 1,
    marginLeft: 8,
    alignSelf: "center"
  },
  buttonView: {
    paddingTop: 16,
    alignItems: 'flex-start'
  },
  buyButton: (isSubscribeButton) => {
    return {
      height: 36,
      position: isSubscribeButton ? 'relative' : 'absolute'
    }
  },
  commentButtonView: {
    height: 30,
    marginTop: 2,
    marginLeft: 10,
    paddingLeft: 8,
    paddingRight: 8,
    paddingTop: 4,
    paddingBottom: 4,
    borderRadius: 6,
    borderWidth: 1,
    alignItems: 'center'
  },
  bottomBlockView: {
    marginTop: 12,
    marginLeft: 16,
    marginRight: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  emptyCommentsText: {
    flex: 1,
    marginTop: 12,
    marginLeft: 16,
    marginRight: 16
  },
  bottomContainer: {
    height: 50,
    marginTop: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  chevronContainer: {
    height: 36,
    width: 100,
    paddingHorizontal: 8,
    flexDirection: 'row',
    backgroundColor: Color.WHITE_COLOR,
    borderRadius: 6,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  chevronIcon: {
    width: 12,
    height: 12
  }
})