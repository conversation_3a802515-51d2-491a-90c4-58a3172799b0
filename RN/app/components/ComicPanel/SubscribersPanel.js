import React from "react";
import { StyleSheet, TouchableWithoutFeedback } from "react-native";
import { View } from "native-base";
import Panel from "./Panel";
import { Utils } from "../../config/Utils";
import { storyPanelStyle, navigationStyle } from "../../config/styles";
import SelectableText from "../../config/SelectableText";
import { ThemeContext } from "../../Contexts";

export default class SubscribersPanel extends Panel {

    constructor(props) {
        super(props)

        this.renderSubsPanel = this.renderSubsPanel.bind(this)
        this.renderTextPanel = this.renderTextPanel.bind(this)

        const { item } = this.props.item
        const data = { "scope": item.series }
        this.props.getSubscribersProgress(data, this.onSubscribersProgrssFetched)
    }

    shouldUpdated(nextProps, nextState) {
        const needsToUpdate = this.state.subsTotalAmount != nextState.subsTotalAmount || this.state.subscriberCount != nextState.subscriberCount
        return needsToUpdate
    }

    renderTextPanel(item) {
        if (!item.title && !item.description) {
            return null
        }

        return (
            <TouchableWithoutFeedback>
                <View>
                    {(Utils.checkData(item.title)) &&
                        <SelectableText textValue={item.title} textStyle={[this.context.h2, styles.headingTextView]} multiline={true} />
                    }
                    {(Utils.checkData(item.description)) &&
                        <View style={{ marginTop: 8 }}>
                            {this.renderPanelDescription(item.description, this.context.p)}
                        </View>
                    }
                </View>
            </TouchableWithoutFeedback>
        )
    }

    renderSubsPanel(item) {
        return (
            <View style={styles.mainTextView}>
                <View style={[styles.subsbarView, { backgroundColor: this.context.colors.textInverse }]}>
                    {this.renderTextPanel(item)}
                    {this.renderSubsProgress(item)}
                </View>
            </View>
        )
    }

    render() {
        const { item, index } = this.props.item

        return (
            <View style={{ backgroundColor: item.bgColor }}>
                <View style={styles.mainContainer}>
                    {this.renderSubsPanel(item)}
                </View>
            </View>
        )
    }
}

SubscribersPanel.contextType = ThemeContext

const styles = StyleSheet.create({
    mainContainer: {
        shadowOpacity: 0.1,
        shadowColor: '#000000',
        shadowRadius: 12,
        marginBottom: navigationStyle.panelsMargin,
        marginLeft: navigationStyle.panelLeftRightMargin,
        marginRight: navigationStyle.panelLeftRightMargin
    },
    subsbarView: {
        paddingTop: 16,
        paddingBottom: storyPanelStyle.paddingBottom,
        paddingLeft: navigationStyle.panelLeftRightMargin,
        paddingRight: navigationStyle.panelLeftRightMargin
    },
    mainTextView: {
        borderRadius: 12,
        overflow: 'hidden',
        elevation: 12
    },
    buyButton: {
        height: 36,
        position: 'relative'
    },
    headingTextView: {
        paddingTop: 0
    }
})