import React from 'react'
import {
  TouchableWithoutFeedback,
  StyleSheet,
  Text,
  Dimensions,
  TouchableOpacity,
  ImageBackground,
} from 'react-native'
import { navigationStyle, storyPanelStyle } from '../../config/styles'
import { View } from 'native-base';
import { scale } from 'react-native-size-matters';
import ContextMenu from 'react-native-context-menu-view';
import Panel from './Panel';
import { Utils } from '../../config/Utils';
import SelectableText from '../../config/SelectableText';
import { Constants } from '../../config/Constants';
import UserSession from '../../config/UserSession';
import { ThemeContext } from '../../Contexts';
import SessionManager from '../../config/SessionManager';
import FastImage from 'react-native-fast-image';

export default class ComicPanel extends Panel {

  constructor(props) {
    super(props)

    this.activeSeriesTab = SessionManager.instance.getActiveSeriesTab()

    this.fetchedFirstAndLatestComic = this.fetchedFirstAndLatestComic.bind(this)
    this.onListedCommentTap = this.onListedCommentTap.bind(this)
    this.onReplyPress = this.onReplyPress.bind(this)
    this.onEditPress = this.onEditPress.bind(this)
    this.renderComicNavigationButton = this.renderComicNavigationButton.bind(this)
    this.renderPanel = this.renderPanel.bind(this)
    this.navigationImageStyle = this.navigationImageStyle.bind(this)
    this.imageStyle = this.imageStyle.bind(this)
    this.renderHeadingPanel = this.renderHeadingPanel.bind(this)
    this.renderTextSubsPanel = this.renderTextSubsPanel.bind(this)
    this.renderTextPanel = this.renderTextPanel.bind(this)
    this.onNavPanelsFetched = this.onNavPanelsFetched.bind(this)
    this.renderSeriesViewNavigator = this.renderSeriesViewNavigator.bind(this)

    this.channelName = Utils.getChannelName(this.props.pathUrl)
    this.seriesData = SessionManager.instance.getSeriesData(this.channelName)

    const { item } = this.props.item
    const { comicData, currentPageStatus } = this.props

    if (item && item.template == "episodes") {
      let isFromFollowingTab = !SessionManager.instance.getShowAllComics()
      let requestedComicData = {}
      if (!comicData.aboveStory && !comicData.belowStory) {
        if (comicData.isNavFromSeriesPage) {
          requestedComicData = { action: currentPageStatus.pageURL, series: this.channelName }
        } else {
          requestedComicData = { action: currentPageStatus.pageURL, series: this.channelName, forFeedPage: isFromFollowingTab }
          if (isFromFollowingTab) {
            requestedComicData = { ...requestedComicData, ...{ storyID: comicData.myFeedID } }
          } else {
            requestedComicData = { ...requestedComicData, ...{ storyID: currentPageStatus.storyID } }
          }
        }
      } else {
        requestedComicData = { action: currentPageStatus.pageURL, series: this.channelName }
      }
      this.props.getNavigationComics(requestedComicData, this.onNavPanelsFetched)
    }
  }

  onNavPanelsFetched(response) {
    const { comicData } = this.props
    let currentComicData = { image: comicData.ogImage }
    let requiredComics = {}
    if (!comicData.aboveStory && !comicData.belowStory) {
      requiredComics = { ...response, current: currentComicData }
    } else {
      requiredComics = { ...response, top: comicData.aboveStory, bottom: comicData.belowStory, current: currentComicData }
    }
    this.setState({ navigationPanels: requiredComics })
  }

  onListedCommentTap(showToAnonymousUser = true) {
    const { item } = this.props.item
    if (isNaN(parseInt(showToAnonymousUser))) {
      this.props.onShowCommentPress(Constants.SHOW_REPLIED_COMMENT, null, null, null, { storyID: item.storyID }, showToAnonymousUser)
      return
    }
    this.props.onShowCommentPress(Constants.SHOW_REPLIED_COMMENT, null, null, null, { storyID: item.storyID })
  }

  onReplyPress(commentItems, selectedIndex) {
    let isPurchasedSubsGift = UserSession.instance.getCurrentUserBadges()
    if (!UserSession.instance.isLoggedInUser() || (UserSession.instance.isLoggedInUser() && !isPurchasedSubsGift.length > 0)) {
      this.onListedCommentTap(false)
    } else {
      this.onListedCommentTap()
    }
  }

  onEditPress(commentItem, index) {
    this.onListedCommentTap()
  }

  componentDidMount() {
    if (Utils.checkObject(this.state.firstComicItem)) {
      Utils.log("Fetched First comic")
      return
    }

    // if (this.isStoriesTemplatePanel() && !Utils.isTinyviewComicsPage(this.props.pathUrl)) {
    //   let requestedData = { series: this.channelName, isContinueReading: true }
    //   this.props.getContinueReadingComic(requestedData)
    // }
  }

  fetchedFirstAndLatestComic(response) {
    if (response) {
      this.setState({ firstComicItem: response.first })
    }
  }

  shouldUpdated(nextProps, nextState) { // Restricting rerendering of rendered component
    const currentProps = this.props.item.item
    const newProps = nextProps.item.item

    const needsToUpdate = this.state.subsTotalAmount != nextState.subsTotalAmount || this.state.subscriberCount != nextState.subscriberCount || this.state.firstComicItem != nextState.firstComicItem || currentProps.button != newProps.button || currentProps.showComments != newProps.showComments || (currentProps.lastComicPanel && this.props.isComicURL && this.getComicStatus(this.props) != this.getComicStatus(nextProps)) || this.state.navigationPanels != nextState.navigationPanels
    return needsToUpdate
  }

  componentDidUpdate() {
    const { item } = this.props.item
    if (item && item.template == Constants.TEMPLATE_TYPE_STORIES) {
      this.activeSeriesTab = SessionManager.instance.getActiveSeriesTab()
    }
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps) {
      if (!this.state.firstComicItem || (nextProps.continueReadingComic && this.state.firstComicItem.unReadCount != nextProps.continueReadingComic.unReadCount)) {
        this.setState({ firstComicItem: nextProps.continueReadingComic })
      }
    }
  }

  renderTextPanel(item) {
    if (!item.title && (!item.description && (item.actionType == "website" && !item.action || item.button || item.image))) {
      return null
    }

    let isSubsTemplate = item && (item.template == 'subscribers')
    let isBonusTemplate = item && (item.template == Constants.BONUS)
    let titleTextStyle = this.context.h1
    let isEmptyFollowingTabPanel = item && item.isEmptyFollowingTabPanel
    if (isSubsTemplate) {
      titleTextStyle = this.context.h2
    } else if (isEmptyFollowingTabPanel) {
      titleTextStyle = this.context.pBold
    }

    let description = item.description ? item.description : (item.actionType == "website" && item.action && !item.button && !item.image) ? item.action : null
    if (description && item.actionType == "website" && item.action && !item.button) {
      description = "<a href=" + item.action + ">" + description + "</a>"
    }

    const isStoriesTemplate = this.isStoriesTemplatePanel() && Utils.isChannelURL(this.props.pathUrl)    
    const hasFirstComicItem = !Utils.isEmptyObject(this.state.firstComicItem)

    return (
      <TouchableWithoutFeedback>
        <View>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
            {(Utils.checkData(item.title)) &&
              <SelectableText textValue={item.title} textStyle={[titleTextStyle, styles.headingTextView(isSubsTemplate, isBonusTemplate, isEmptyFollowingTabPanel)]} multiline={true} />
            }
            {this.renderSeriesViewNavigator(item)}
          </View>
          {(Utils.checkData(description) && (!isStoriesTemplate || hasFirstComicItem)) &&
            <View style={{ marginTop: 8 }}>
              {this.renderPanelDescription(description, this.context.p)}
            </View>
          }
          {isStoriesTemplate && this.renderComicNavigationButton()}
        </View>
      </TouchableWithoutFeedback>
    )
  }

  renderHeadingPanel(item) {
    if (!item.template == "newsfeed" && !item.titleOn) {
      return null
    }

    return (
      <View>
        {(Utils.checkData(item.titleOn)) &&
          <SelectableText textValue={item.titleOn} textStyle={[this.context.h1, { marginTop: 20, paddingTop: 0 }]} multiline={true} />
        }
      </View>
    )
  }


  renderComicNavigationButton() {
    const { firstComicItem } = this.state
    if (!firstComicItem || Utils.isEmptyObject(firstComicItem)) {
      return (
        <Text style={[this.context.p, { marginTop: 20 }]}>{'You are up-to-date! No new episodes.'}</Text>
      )
    }

    const isFirstComic = firstComicItem.isFirstEpisode
    let panelDate = Utils.getFormattedDate(firstComicItem.createdAt, Constants.LIST_DATE_FORMAT)
    let formattedPageViewCount = Utils.formatViewCount(firstComicItem.views)
    const isExternalLink = firstComicItem.actionType == "website"
    const unReadCount = firstComicItem.unReadCount > 99 ? "99+" : firstComicItem.unReadCount

    return (
      <View style={[styles.navigationPanelView, styles.panelOuterView, styles.panelInnerView, { backgroundColor: this.context.colors.textInverse }]}>
        <TouchableOpacity
          onPress={() => this.onPanelTap(firstComicItem)}
          style={[styles.episodeButton, { borderColor: this.context.colors.chatBubbles }]}>
          <ImageBackground
            style={this.navigationImageStyle(firstComicItem.width, firstComicItem.height, navigationStyle.panelsMargin)}
            source={firstComicItem.image ? { uri: firstComicItem.image } : require('../../../assets/image_unavailable.jpg')}
          />
          <View style={styles.firstEpisodePanel}>
            <View style={styles.navigationTitleView}>
              <View style={styles.navTextView}>
                <Text numberOfLines={1} style={this.context.bodyMiniBold}>{isFirstComic ? Constants.FIRST_EPISODE : Constants.NEXT_EPISODE}</Text>
                {!isFirstComic &&
                  <View style={[styles.contiReadCountView(unReadCount), { borderColor: this.context.colors.logoRed, backgroundColor: this.context.colors.logoRed }]}>
                    <Text style={[styles.contiReadText, { color: this.context.colors.textInverse }]}>{unReadCount}</Text>
                  </View>}
              </View>
              {isExternalLink &&
                <FastImage
                  style={styles.externalLinkIcon}
                  source={require('../../../assets/external_link_icon.png')} />}
            </View>
            <View style={styles.panelDateViewsStyle}>
              <Text numberOfLines={1} style={[this.context.bodyMini, { flex: 1 }]}>{isFirstComic ? panelDate : firstComicItem.title}</Text>
              {isFirstComic &&
                <View style={styles.panelViewsImageStyle}>
                  <Text style={this.context.bodyMini}>{formattedPageViewCount}</Text>
                  <FastImage
                    style={styles.eyeImageStyle}
                    source={require('../../../assets/eye_icon_one.png')} />
                </View>}
            </View>
          </View>
        </TouchableOpacity>
      </View >
    )
  }

  renderTextSubsPanel(item) {
    if (item.template == 'subscribers') {
      return (
        <View style={styles.panelOuterView}>
          <View style={[styles.subsbarInnerView, { backgroundColor: this.context.colors.textInverse }]}>
            {this.renderTextPanel(item)}
            {this.renderSubsProgress(item)}
          </View>
        </View>
      )
    } else {
      return (
        <View>{this.renderTextPanel(item)}
          {this.renderSubsProgress(item)}
        </View>
      )
    }
  }

  renderSeriesViewNavigator(item) {
    if (!item || !item.template || item.template != Constants.TEMPLATE_TYPE_STORIES) {
      return null
    }

    const showAllComics = this.activeSeriesTab === Constants.SHOW_ALL_COMICS

    return (
      <View style={styles.navigationView}>
        <TouchableOpacity
          disabled={showAllComics}
          style={{ marginRight: 24, alignItems: 'center' }}
          onPress={() => this.props.onChangeSeriesTab(Constants.SHOW_ALL_COMICS)}>
          <Text style={[showAllComics ? this.context.pBold : this.context.p, { marginBottom: showAllComics ? 5 : 8 }]}>{Constants.ALL}</Text>
          {showAllComics && <View style={[styles.separatorStyle, { backgroundColor: this.context.colors.textBold }]} />}
        </TouchableOpacity>
        <TouchableOpacity
          disabled={!showAllComics}
          style={{ alignItems: 'center' }}
          onPress={() => this.props.onChangeSeriesTab(Constants.SHOW_UNREAD_COMICS)}>
          <Text style={[!showAllComics ? this.context.pBold : this.context.p, { marginBottom: !showAllComics ? 5 : 8 }]}>{Constants.UNREAD}</Text>
          {!showAllComics && <View style={[styles.separatorStyle, { backgroundColor: this.context.colors.textBold }]} />}
        </TouchableOpacity>
      </View>
    )
  }

  renderPanel() {
    const { item } = this.props.item

    if (item.template == "newsfeed" && item.titleOn) {
      return (
        this.renderHeadingPanel(item)
      )
    }

    return (
      <View>
        {this.renderTextSubsPanel(item)}
        {(item && item.image) ?
          <ContextMenu
            actions={[{ title: "Share", systemIcon: "square.and.arrow.up" }]}
            onPress={async (e) => {
              if (e.nativeEvent.index == 0) {
                this.props.shareImage(item);
              }
            }}
          >
            {this.renderImage()}
          </ContextMenu>
          : null
        }
        {item && item.template != Constants.SUBSCRIBERS_TEMPLATE && item.button &&
          <View style={{ marginTop: 12, marginBottom: 20 }}>
            {this.renderButton(item)}
          </View>
        }
      </View>
    )
  }

  render() {
    const { item, index } = this.props.item
    const { isComicURL } = this.props

    return (
      <View style={{ backgroundColor: item.bgColor }}>
        {this.renderContainer()}
        {this.renderLastPanel()}
      </View>
    )
  }

  navigationImageStyle(w = 800, h = 420, margin = 2) {
    let dimensions = Dimensions.get('window')

    let aspectRatio = ((dimensions.width - ((margin) * 2)) / w) / 2.7

    return {
      width: w * aspectRatio,
      height: 65,
      overflow: "hidden"
    }
  }

  imageStyle = (w = 800, h = 420, margin = 2, item) => {
    let dimensions = Dimensions.get('window')
    let rightBorder = 0;
    if (item.border && item.border["border-left"] && item.border["border-right"]) {
      rightBorder = item.border["border-width"];
    }

    let aspectRatio = (dimensions.width - ((margin + rightBorder) * 2)) / w

    return {
      width: w * aspectRatio,
      height: h * aspectRatio,
      resizeMode: 'contain',
    }
  }
}

ComicPanel.contextType = ThemeContext

const styles = StyleSheet.create({
  buyButton: (isSubscribeButton) => {
    return {
      height: 36,
      position: isSubscribeButton ? 'relative' : 'absolute'
    }
  },
  episodeButton: {
    flexDirection: 'row',
    borderRadius: 8,
    borderWidth: 1,
    overflow: 'hidden'
  },
  progress: {
    marginTop: 8,
    marginBottom: 8
  },
  panelOuterView: {
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 12
  },
  panelInnerView: {
    paddingTop: navigationStyle.panelsMargin,
    paddingBottom: navigationStyle.panelsMargin,
    paddingLeft: navigationStyle.panelLeftRightMargin,
    paddingRight: navigationStyle.panelLeftRightMargin
  },
  navigationPanelView: {
    marginTop: scale(20)
  },
  subsbarInnerView: {
    paddingTop: 16,
    paddingBottom: storyPanelStyle.paddingBottom,
    paddingLeft: navigationStyle.panelLeftRightMargin,
    paddingRight: navigationStyle.panelLeftRightMargin
  },
  headingTextView: (isSubsTemplate = null, isBonusTemplate = null, isEmptyFollowingTabPanel = null) => {
    return {
      flex: 1,
      paddingTop: 0,
      marginTop: !isSubsTemplate && !isEmptyFollowingTabPanel ? 20 : 0,
      marginBottom: isBonusTemplate ? 20 : 0
    }
  },
  navigationView: {
    flexDirection: 'row',
    alignItems: 'flex-end'
  },
  separatorStyle: {
    height: 3,
    width: '120%',
  },
  firstEpisodePanel: {
    flex: 1,
    padding: 8
  },
  panelDateViewsStyle: {
    flex: 1,
    flexDirection: 'row',
    marginTop: 6,
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  panelViewsImageStyle: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  eyeImageStyle: {
    height: 14,
    width: 14,
    marginLeft: 2
  },
  externalLinkIcon: {
    height: 14,
    width: 14,
    marginLeft: 5
  },
  navigationTitleView: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  contiReadCountView: (count) => {
    return {
      height: count == "99+" ? 17 : 20,
      width: count == "99+" ? 30 : 20,
      marginLeft: 8,
      borderWidth: 1,
      borderRadius: 12,
      justifyContent: 'center',
      alignItems: 'center'
    }
  },
  contiReadText: {
    fontStyle: 'normal',
    fontWeight: '400',
    fontSize: 11,
    lineHeight: 12
  },
  navTextView: {
    flex: 1,
    flexDirection: 'row'
  }
})