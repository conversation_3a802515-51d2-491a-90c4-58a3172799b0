import React from 'react'
import {
  TouchableWithoutFeedback,
  StyleSheet,
  Text,
  Platform,
  Dimensions,
  TouchableOpacity
} from 'react-native'
import { View } from 'native-base';
import { Utils } from '../../config/Utils';
import { SystemFont } from '../../config/Typography';
import { Color } from '../../config/Color';
import { scale } from 'react-native-size-matters';
import ContextMenu from 'react-native-context-menu-view';
import Panel from './Panel';
import { storyPanelStyle } from '../../config/styles';
import FastImage from 'react-native-fast-image'
import { Constants } from '../../config/Constants';
import { ThemeContext } from '../../Contexts';


export default class DirectoryPanel extends Panel {

  constructor(props) {
    super(props)

    this.renderSeriesInfo = this.renderSeriesInfo.bind(this)
    this.renderPanel = this.renderPanel.bind(this)
    this.renderPanelImage = this.renderPanelImage.bind(this)
  }

  shouldUpdated(nextProps, nextState) {
    return this.props.alerts != nextProps.alerts
  }

  renderPanelContent() {
    const { item } = this.props.item
    if (!item.title && !item.description) {
      return null
    }

    return (
      <View>
        <View style={styles.seriesInfo}>
          <View style={styles.panelContent}>
            {item.title &&
              <Text style={this.context.h2}>{item.title}</Text>
            }
          </View>
          {this.renderSeriesInfo()}
          {this.renderButton()}
        </View>
      </View>
    )
  }

  renderSeriesInfo() {
    const { item } = this.props.item
    let pageViewCount = 0
    let pageLikeCount = 0

    if (this.props.multiplePanelStatus && item.action && this.props.multiplePanelStatus[item.action]) {
      let viewCount = this.props.multiplePanelStatus[item.action].viewCount
      let likeCount = this.props.multiplePanelStatus[item.action].likeCount

      if (viewCount == 0) viewCount = viewCount + 1

      pageViewCount = Utils.formatViewCount(viewCount)
      pageLikeCount = Utils.formatViewCount(likeCount)
    }

    const pageLikeCountSuffix = pageLikeCount != 1 ? "Followers" : "Follower"
    const pageViewCountSuffix = pageViewCount != 1 ? "Views" : "View"

    return (
      <View>
        <View style={styles.viewsTextView}>
          <Text style={this.context.pBold}>{pageViewCount}</Text>
          <Text style={this.context.p}> {pageViewCountSuffix}</Text>
        </View>
        <View style={styles.followTextView}>
          <Text style={this.context.pBold}>{pageLikeCount}</Text>
          <Text style={this.context.p}> {pageLikeCountSuffix}</Text>
        </View>
      </View>
    )
  }

  renderPanel() {
    const { item } = this.props.item
    return (
      <TouchableWithoutFeedback
        style={{ flex: 1 }}
        onPress={() => {
          if (!item.button) {
            this.onPanelTap(item)
          }
        }}>
        <View style={styles.mainContainer}>
          <View style={[styles.previewPanelContainer, { backgroundColor: this.context.colors.textInverse }]}>
            <View style={styles.panelInnerView}>
              <View style={{ flexDirection: 'row' }}>
                {this.renderPanelImage()}
                <View style={styles.descriptionTextView}>
                  {this.renderPanelContent()}
                </View>
              </View>
              {item.description &&
                <View style={styles.panelDescriptionView}>
                  {this.renderPanelDescription(item.description, this.context.p)}
                </View>
              }
            </View>
          </View>
        </View>
      </TouchableWithoutFeedback>
    )
  }

  renderButton() {
    const { item } = this.props.item

    const isEnabled = this.isFollowingSeries(this.props)
    const buttonTitle = isEnabled ? Constants.FOLLOWING : Constants.FOLLOW;

    return (
      <TouchableOpacity
        style={styles.followButtonView}
        onPress={() => { this.props.onFollowPress(Utils.resolvePath(this.props.pathUrl, item.action), isEnabled, false, false) }}
      >
        <FastImage
          style={styles.followFollowingImage}
          source={isEnabled ? require('../../../assets/following_icon.png') : require('../../../assets/follow_icon.png')}
        />
        <Text style={[this.context.p, styles.buttonTextView, { color: this.context.colors.textBold }]}>{buttonTitle}</Text>
      </TouchableOpacity>
    )
  }

  renderPanelImage() {
    const { item } = this.props.item

    return (
      <View>
        {(item && item.image) ?
          <ContextMenu
            actions={[{ title: "Share", systemIcon: "square.and.arrow.up" }]}
            onPress={async (e) => {
              if (e.nativeEvent.index == 0) {
                this.props.shareImage(item);
              }
            }}
          >
            {this.renderImage()}
          </ContextMenu>
          : null
        }
      </View>
    )
  }

  render() {
    const { item, index } = this.props.item
    Utils.log("Render Directory Panel " + item.action)

    return (      
      <View style={{ backgroundColor: item.bgColor }}>
        {this.renderContainer()}
      </View>      
    )
  }

  imageStyle = (w, h, margin = 2, item) => {
    let dimensions = Dimensions.get('window')
    let rightBorder = 0;
    if (item.border && item.border["border-left"] && item.border["border-right"]) {
      rightBorder = item.border["border-width"];
    }

    rightBorder = rightBorder == 0 ? scale(0.5) : rightBorder
    let aspectRatio = (dimensions.width - ((margin + rightBorder) * 13)) / w

    return {
      width: w * aspectRatio,
      height: h * aspectRatio,
      resizeMode: 'contain'
    }
  }
}

DirectoryPanel.contextType = ThemeContext;

const styles = StyleSheet.create({
  mainContainer: {
    shadowOpacity: 0.1,
    shadowColor: Color.BLACK_COLOR,
    shadowRadius: 12
  },
  followfollowingText: {
    marginLeft: 8
  },
  followFollowingImage: {
    height: 18,
    width: 18,
    alignSelf: 'center'
  },
  navigationIcons: () => {
    return {
      width: scale(25),
      height: scale(25),
      tintColor: Color.BOTTOM_ICON_TINT_COLOR,
    }
  },
  descriptionText: {
    color: Color.GREY_999999,
    fontFamily: SystemFont.SELECTED_FONT,
    fontSize: scale(14), //Platform.OS == 'ios' ? scale(11.5) : scale(11),    
  },
  followingText: {
    color: Color.RED_TEXT_COLOR,
    fontFamily: SystemFont.SELECTED_FONT,
    fontSize: scale(14),
    paddingLeft: Platform.OS == 'ios' ? 10 : 10,
    paddingRight: Platform.OS == 'ios' ? 10 : 10,
  },
  followText: {
    textAlign: 'center',
    color: 'white',
    fontFamily: SystemFont.SELECTED_FONT,
    fontSize: scale(14),
  },
  followTextKey: {
    color: Color.GREY_999999,
    fontFamily: SystemFont.SELECTED_FONT,
    fontSize: scale(14),
  },
  previewPanelContainer: {
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 12
  },
  descriptionTextView: {
    flex: 1,
    paddingLeft: storyPanelStyle.paddingLeft
  },
  panelContent: {
    flexDirection: 'row'
  },
  followButtonView: {
    flexDirection: 'row',
    marginTop: 11
  },
  panelDescriptionView: {
    marginTop: 16
  },
  seriesInfo: {
    flex: 1
  },
  panelInnerView: {
    paddingTop: 16,
    paddingBottom: storyPanelStyle.paddingBottom,
    paddingLeft: storyPanelStyle.paddingLeft,
    paddingRight: storyPanelStyle.paddingRight
  },
  viewsTextView: {
    marginTop: 10,
    flexDirection: 'row'
  },
  followTextView: {
    marginTop: 8,
    flexDirection: 'row'
  },
  buttonTextView: {
    marginLeft: 8
  }
})