import { StyleSheet } from 'react-native';
import { scale } from 'react-native-size-matters';
import { Color } from '../../config/Color';
import { colors } from '../../config/styles';

export default StyleSheet.create({
  button: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    backgroundColor: colors.buttonBackground,
    margin: 5,
  },  
  navigationButton: {
    flex: 1,
    height: scale(40),
  },  
  navigationIcons: (disable) => {
    return {
      width: scale(22),
      height: scale(22),
      opacity: disable ? 0.2 : 1,
      tintColor: disable ? Color.DISABLE_BOTTOM_ICON_TINT_COLOR : Color.BOTTOM_ICON_TINT_COLOR
    }
  },
});
