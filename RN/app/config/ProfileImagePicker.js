import { Platform, PermissionsAndroid, Alert, Linking } from 'react-native';
import ImagePicker from 'react-native-image-crop-picker';
import { Constants } from './Constants';
import FileCache from './FileCache';
import { settings } from './settings';
import { Utils } from './Utils';

export class ProfileImagePicker {

  static requestCameraPermission = async (error = null) => {
    let permissionStatus = null;
    if (Platform.OS === 'android') {
      try {
        permissionStatus = await PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.CAMERA);
        if (!permissionStatus) {
          permissionStatus = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.CAMERA,
            {
              'title': 'Camera Permission',
              'message': 'A<PERSON> would like to access your camera',
              'buttonPositive': 'OK',
              'buttonNegative': 'CANCEL',
            }
          )
        }
      } catch (err) {
        Utils.log("permissionStatus_Error", err);
        permissionStatus = false
      }
    } else {
      if (Constants.E_NO_CAMERA_PERMISSION == error) {
        permissionStatus = 'denied'
      } else {
        permissionStatus = true;
      }
    }

    if (permissionStatus == 'never_ask_again' || permissionStatus == 'denied') {
      Alert.alert("", "You need to go to Settings and allow Camera permission.", [
        {
          text: "Cancel",
        },
        {
          text: "Settings", onPress: () => {
            Linking.openSettings()
          }
        }
      ])
      permissionStatus = false
    }

    return permissionStatus
  };

  static requestStoragePermission = async (error = null) => {
    let permissionStatus = null;
    if (Platform.OS === 'android' && Platform.Version <= 29) {
      try {
        const permission = PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE;
        permissionStatus = await PermissionsAndroid.check(permission);
        if (!permissionStatus) {
          const msg = 'App would like to access your Photos permission to pick/save the image'
          permissionStatus = await PermissionsAndroid.request(
            permission,
            {
              'title': 'Permission Needed',
              'message': msg,
              'buttonPositive': 'OK',
              'buttonNeutral': 'CANCEL',
            }
          )
        }
      } catch (err) {
        Utils.log("permissionStatus_Error", err);
        permissionStatus = false
      }

      if (permissionStatus == 'never_ask_again' || permissionStatus == 'denied') {
        let errorMsg = "You need to go to Settings and allow External storage permission."
        Alert.alert("", errorMsg, [
          {
            text: "Cancel",
          },
          {
            text: "Settings", onPress: () => {
              Linking.openSettings()
            }
          }
        ])
        permissionStatus = false
      }
    } else {
      permissionStatus = true;
    }

    return permissionStatus;
  }

  static openImagePicker = async () => {
    let isStoragePermitted = await ProfileImagePicker.requestStoragePermission();
    if (isStoragePermitted) {
      return ImagePicker.openPicker({
        width: 400,
        multiple: false,
        height: 400,
        cropping: true,
        cropperCircleOverlay: true,
        compressImageQuality: 0.7
      }).then(image => {
        return image
      }).catch(error => {
        if (error.code != Constants.E_PICKER_CANCELLED) {
          Utils.showError(error.message)
        }
        Utils.log("openImagePicker error", error)
      });
    }
  }

  static openCamera = async () => {
    let isCameraPermitted = await ProfileImagePicker.requestCameraPermission();
    if (isCameraPermitted) {
      return ImagePicker.openCamera({
        width: 400,
        height: 400,
        cropping: true,
        cropperCircleOverlay: true
      }).then(image => {
        return image
      }).catch(error => {
        if (error.code == Constants.E_NO_CAMERA_PERMISSION) {
          ProfileImagePicker.requestCameraPermission(error.code);
        } else if (error.code != Constants.E_PICKER_CANCELLED) {
          Utils.showError(error.message)
        }
        Utils.log("openCamera error", error)
      });
    }
  }

  static loadProfileImage = async (UID, callback) => {
    let profilePicURL = null
    try {
      let isStale = await FileCache.default.isFileStaleForURL(null, null, `${settings.RNFS_PROFILE_IMAGE_URL}${UID}`, settings.profilePicExpireTime)
      if (isStale && callback) {
        callback(null)
      } else {
        const dataInJSON = await FileCache.default.readFile(`${settings.RNFS_PROFILE_IMAGE_URL}${UID}`)
        profilePicURL = Utils.getParsedData(dataInJSON)
        callback(profilePicURL)
      }
    } catch (error) {
      Utils.log("loadProfileImage error", error)
    }
  }

}