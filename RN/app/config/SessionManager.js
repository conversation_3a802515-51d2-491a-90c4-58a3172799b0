import { Alert } from "react-native";
import { Constants } from "./Constants";
import FileCache from "./FileCache";
import FirebaseManager from "./FirebaseManager";
import { settings } from "./settings";
import { Utils } from "./Utils";
import UserSession from "./UserSession";
import moment from "moment";

export default class SessionManager {
  static instance = new SessionManager()

  constructor() {
    this.purchasedItems = []
    this.prePanels = {};
    this.shownAlertMessage = true
    this.appLaunchCount = 0;
    this.isErrorAlertVisible = false
    this.seriesDetails = {}
    this.likeComics = []
    this.showAllComics = true;
    this.seriesAdmins = {};
    this.giftItemList = []
    this.lastPurchase = [];
    this.subscriptionItems = []
    // this.visitedSharedComic = []
    this.anonymousUserIdToken = null;
    this.isHomePageUpdated = false;
    this.anonymousUserDetails = null
    this.profileImageURL = null;
    this.allPruchasedSubData = [];
    this.isLoginFromInviteLink = false
    this.activeSeriesTab = Constants.SHOW_ALL_COMICS
    this.containerInsets = { top: 0, bottom: 0, left: 0, right: 0 }

    //<<<<<<< Keeping it locally as if we didn't get API response
    this.comicConfigurations = {
      "rating": {
        "teens-and-up": {
          "label": "Teens and Up"
        },
        "explicit-content": {
          "label": "Explicit Content"
        },
        "all-ages": {
          "label": "All Ages"
        }
      },
      "show-to": {
        "subscribers-only": {
          "label": "Premium",
          "icon": "/tinyview/app/icons8-crown-60.png"
        },
        "everyone": {
          "label": "Free"
        }
      }
    }
    this.differentActionsInfluencePoints = { "READ_BONUS_PANEL": 1, "SIGNUP": 5, "FRIEND_REQUEST_SENDER": 5, "FRIEND_REQUEST_RECEIVER": 5, "INSTALL_APP": 5, "REFERRER": 1, "READ_PREMIUM_COMIC": 5 }
    //Keeping it locally as if we didn't get API response >>>>>

    this.currentComicPageData = {}

    this.subsProgressData = {}

    this.bgTime = 0

    this.currentPendingNotification = null
    this.currentPendingBranchLink = null
    this.currentPendingDynamicLink = null
    this.generatedEmailAuthLink = false
    this.giftedComicLink = null

    this.listOfInstalledApps = [Constants.COPYLINK, Constants.TINYVIEW, Constants.MESSAGES_LINK, Constants.MORE.toLowerCase()]

    this.getPurchasedItems.bind(this);
    this.updatePurchaseItems.bind(this);
    // this.updatePreviousPagePanels.bind(this);
    this.updateSeriesAdmins.bind(this);
    this.updateSeriesDetails.bind(this);
    this.getSeriesData.bind(this);
    this.getSeriesAdmins.bind(this);
    this.shouldUpdateHomePage.bind(this);
    this.setHomePageIsUpdated.bind(this);
    this.setPendingNotification.bind(this);
    this.getPendingNotification.bind(this);
    this.setPendingBranchLink.bind(this);
    this.getPendingBranchLink.bind(this);
    this.setPendingDynamicLink.bind(this);
    this.getPendingDynamicLink.bind(this);
    this.setActiveSeriesTab.bind(this);
    this.getActiveSeriesTab.bind(this);
    this.updateContainerInsets.bind(this);
    this.getContainerInsets.bind(this);
    this.setLastGiftedComicLink.bind(this);
    this.getLastGiftedComicLink.bind(this);
  }

  getBGStayTime() {
    return this.bgTime
  }

  updateBGStayTime() {
    this.bgTime = moment()
  }

  updateContainerInsets(insets) {
    this.containerInsets = insets
  }

  getContainerInsets() {
    return this.containerInsets
  }

  getCurrectComicPageData() {
    return this.currentComicPageData;
  }

  setCurrectComicPageData(data) {
    this.currentComicPageData = data;
  }

  getSubsProgressData() {
    return this.subsProgressData;
  }

  setSubsProgressData(data) {
    this.subsProgressData = data;
  }

  getLastPurchasedData() {
    if (this.allPruchasedSubData != null) {
      return this.allPruchasedSubData[this.allPruchasedSubData.length - 1]
    }

    return null
  }

  setLastGiftedComicLink(link) {
    this.giftedComicLink = link;
  }

  getLastGiftedComicLink() {
    return this.giftedComicLink;
  }

  isLastGiftedComicLink(link) {
    return this.giftedComicLink === link;
  }

  getLatestSubscription() {
    if (this.getLastPurchasedData() && this.getLastPurchasedData().productID) {
      const subsProductID = this.getLastPurchasedData().productID
      if (subsProductID === Constants.PIZZA_PRODUCT_ID) {
        return Constants.PIZZA.toLowerCase()
      } else if (subsProductID === Constants.ART_SUPPLIES_PRODUCT_ID) {
        return Constants.ART_SUPPLIES.toLowerCase()
      } else if (subsProductID === Constants.COFFEE_PRODUCT_ID) {
        return Constants.COFFEE.toLowerCase()
      } else if (subsProductID === Constants.BAGEL_PRODUCT_ID) {
        return Constants.BAGEL.toLowerCase()
      } else if (subsProductID === Constants.COOKIE_PRODUCT_ID) {
        return Constants.COOKIE.toLowerCase()
      }
    }

    return null
  }

  getAllPurchasedData() {
    return this.allPruchasedSubData;
  }

  setActiveSeriesTab(data) {
    this.activeSeriesTab = data
  }

  getActiveSeriesTab() {
    return this.activeSeriesTab
  }

  updatePurchasedData(data) {
    if (Array.isArray(data) && data.length === 0) {
      this.allPruchasedSubData = []
    } else {
      var indexToAdd = this.allPruchasedSubData.length;
      var numberOfElementToRemove = 0
      for (const key in this.allPruchasedSubData) {
        if (data.productID === this.allPruchasedSubData[key].productID) {
          indexToAdd = key
          numberOfElementToRemove = 1
        }
      }
      this.allPruchasedSubData.splice(indexToAdd, numberOfElementToRemove, data)
    }
  }

  getPurchasedItemsElement(productId) {
    if (this.allPruchasedSubData) {
      for (const key in this.allPruchasedSubData) {
        if (this.allPruchasedSubData[key].productID == productId) {
          return this.allPruchasedSubData[key]
        }
      }
    }

    return null;
  }

  getShowAllComics() {
    return this.showAllComics;
  }

  updateShowAllComics(value) {
    this.showAllComics = value;
  }

  updateGiftItemList(value) { // move this code in render.
    for (const key in value) {
      const { reactionName } = value[key]
      if (reactionName && reactionName.toLowerCase() == Constants.LIKE.toLowerCase()) {
        // value[key].image = require('../../assets/like_fill.png')
        // value[key].unLikeImage = require('../../assets/like_unfilled.png')
        value[key].unLikeTitle = Constants.LIKED
      }
    }
    this.giftItemList = value
    FileCache.default.writeFile(Constants.ALL_GIFTS, JSON.stringify(this.giftItemList))
  }

  getGiftItemList() {
    return this.giftItemList
  }

  getGiftItemDetails(giftName) {
    if (this.giftItemList) {
      for (const key in this.giftItemList) {
        if (this.giftItemList[key].reactionName == giftName) {
          return this.giftItemList[key]
        }
      }
    }
    return null
  }

  updateComicsConfigList(value) {
    this.comicConfigurations = value
  }

  getComicsConfigList() {
    return this.comicConfigurations
  }

  getGiftItemCost(productID) {
    if (this.giftItemList) {
      for (const key in this.giftItemList) {
        if (this.giftItemList[key].productId == productID) {
          return this.giftItemList[key].cost
        }
      }
    }
    return 0
  }

  updateSubscriptionItems(item) {
    let haveSubsItem = false
    for (const key in this.subscriptionItems) {
      if (this.subscriptionItems[key].inAppPurchase == item.inAppPurchase) {
        haveSubsItem = true
        break
      }
    }

    if (!haveSubsItem) {
      this.subscriptionItems.push(item)
    }
  }

  getSubscriptionItemsCost(productID) {
    if (this.subscriptionItems) {
      for (const key in this.subscriptionItems) {
        if (this.subscriptionItems[key].inAppPurchase == productID) {
          let subsCost = parseFloat(this.subscriptionItems[key].price)
          return subsCost
        }
      }
    }
    return 0
  }

  setIsInviteLinkLogin(value) {
    this.isLoginFromInviteLink = value
  }

  getIsInviteLinkLogin() {
    return this.isLoginFromInviteLink
  }

  clearUserDetails() {
    this.profileImageURL = null
    UserSession.instance.updateUserDetails(null)
    UserSession.instance.updateUnlockedComics(null)
    UserSession.instance.updateUnlockedComics([])
    UserSession.instance.updateUnreadNotifications(0)
  }

  setIsGeneratingEmailLink(value) {
    this.generatedEmailAuthLink = value
  }

  getIsGeneratingEmailLink() {
    return this.generatedEmailAuthLink
  }

  // updateVisitedSharedComic(visitedSharedComic) {
  //   this.visitedSharedComic.push(visitedSharedComic)
  // }

  // isSharedComicVisited(sharedComicEndPoint) {
  //   let visitedSharedComic = this.visitedSharedComic
  //   let isSharedComicVisited = false;
  //   if (visitedSharedComic && visitedSharedComic.length > 0) {
  //     for (const url in visitedSharedComic) {
  //       if (url.includes(sharedComicEndPoint)) {
  //         isSharedComicVisited = true;
  //         break;
  //       }
  //     }
  //   }
  //   return isSharedComicVisited;
  // }

  saveAnonymousUserDetails(userDetails) {
    this.anonymousUserDetails = userDetails
  }

  getAnonymousUserDetails() {
    return this.anonymousUserDetails
  }

  getLikeComics() {
    return this.likeComics;
  }

  // updateLikeComics(comic, needsToAdd = true) {
  //   if (!needsToAdd) {
  //     const indexToRemove = this.likeComics.indexOf(comic)
  //     if (indexToRemove != -1) {
  //       this.likeComics.splice(indexToRemove, 1);
  //     }
  //   } else {
  //     if (typeof (comic) == "string") {
  //       if (!this.likeComics.includes(comic)) {
  //         this.likeComics.push(comic)
  //       }
  //     } else {
  //       this.likeComics = comic;
  //     }
  //   }
  // }

  setHomePageIsUpdated(value) {
    this.isHomePageUpdated = value;
  }

  shouldUpdateHomePage() {
    return this.isHomePageUpdated;
  }

  updateAppLaunchCount(count) {
    Utils.log("App launch count " + count)
    this.appLaunchCount = count;
  }

  updateSeriesDetails(seriesDetails) {
    this.seriesDetails = seriesDetails;

    this.updateSeriesAdmins();
  }

  getSeriesFreeEpisodes(seriesId) {
    if (seriesId && this.seriesDetails && this.seriesDetails[seriesId]) {
      return this.seriesDetails[seriesId].freeEpisodes ? this.seriesDetails[seriesId].freeEpisodes : -1
    }

    return -1
  }

  getSeriesFreeHours(seriesId) {
    if (seriesId && this.seriesDetails && this.seriesDetails[seriesId]) {
      return this.seriesDetails[seriesId].freeHours ? this.seriesDetails[seriesId].freeHours : -1
    }

    return -1
  }

  getSeriesName(seriesId) {
    if (seriesId && this.seriesDetails && this.seriesDetails[seriesId]) {
      return this.seriesDetails[seriesId].title;
    }

    return null
  }

  getSeriesIdByTitle(title) {
    if (title && this.seriesDetails) {
      for (let key in this.seriesDetails) {
        if (this.seriesDetails[key].title === title) {
          return key;
        }
      }
    }

    return null;
  }

  getSeriesStyle(seriesId) {
    if (seriesId && this.seriesDetails && this.seriesDetails[seriesId]) {
      return this.seriesDetails[seriesId].style;
    }

    return null
  }

  getSeriesCredits(seriesId) {
    if (seriesId && this.seriesDetails && this.seriesDetails[seriesId]) {
      return this.seriesDetails[seriesId].credits;
    }

    return null
  }

  getSeriesData(seriesId) {
    if (seriesId && this.seriesDetails && this.seriesDetails[seriesId]) {
      return this.seriesDetails[seriesId]
    }
    return null
  }

  getSeriesAdmins(channelName) {
    if (this.seriesAdmins) {
      return this.seriesAdmins[channelName];
    }
    return null;
  }

  isTinyviewAdmin() {
    const userMobileNo = FirebaseManager.instance.currentUser().phoneNumber
    if (!userMobileNo) { return false }

    let isTinyviewAdmin = false

    const superAdmins = this.getSeriesAdmins(settings.TINYVIEW_CHANNEL_NAME);
    if (superAdmins) {
      if (superAdmins.includes(userMobileNo)) {
        isTinyviewAdmin = true;
      }
    }

    return isTinyviewAdmin
  }

  isSeriesAdmin(seriesName) {
    const userMobileNo = FirebaseManager.instance.currentUser().phoneNumber
    if (!userMobileNo) { return false }

    let isAdminUser = false

    const seriesAdmins = this.getSeriesAdmins(seriesName);
    if (seriesAdmins) {
      if (seriesAdmins.includes(userMobileNo)) {
        isAdminUser = true;
      }
    }

    return isAdminUser
  }

  isAnySeriesAdmin() {
    const userMobileNo = FirebaseManager.instance.currentUser().phoneNumber
    if (!userMobileNo) { return false }

    let isAdminUser = false

    for (const key in this.seriesAdmins) {
      const numbers = this.seriesAdmins[key];
      if (numbers && numbers.includes(userMobileNo)) {
        isAdminUser = true;
        break;
      }
    }

    return isAdminUser
  }

  isAdminUser(seriesName) {
    const userMobileNo = FirebaseManager.instance.currentUser().phoneNumber
    if (!userMobileNo) { return false }

    let isAdminUser = this.isTinyviewAdmin()

    if (!isAdminUser) {
      isAdminUser = this.isSeriesAdmin(seriesName)
    }

    return isAdminUser
  }

  updateSeriesAdmins() {
    let admins = {};

    for (const iterator in this.seriesDetails) {
      let seriesData = this.getSeriesData(iterator)
      if (seriesData && seriesData.roles && seriesData.roles.length > 0) {
        for (const index in seriesData.roles) {
          if (!admins[iterator]) {
            admins[iterator] = []
          }

          admins[iterator] = seriesData.roles[index].admins
          break
        }
      }
    }

    this.seriesAdmins = admins;
  }

  // getAppLaunchCount() {
  //   return this.appLaunchCount;
  // }

  getPurchasedItems() {
    return this.purchasedItems;
  }

  updatePurchaseItems(items) {
    this.purchasedItems = items;
  }

  hasAnySubscriptionPurchase() {
    let hasSubs = this.getPurchasedSubscription() ? true : false;
    return hasSubs;
  }

  getPurchasedSubscription() {
    if (this.purchasedItems && this.purchasedItems.length > 0) {
      for (const key in this.purchasedItems) {
        if (this.purchasedItems.hasOwnProperty(key)) {
          const element = this.purchasedItems[key];
          if (Utils.isSubsProduct(element)) {
            return element
          }
        }
      }
    }

    return null
  }

  getTopLevelPurchasedSubscription() {
    if (this.purchasedItems && this.purchasedItems.length > 0) {
      if (this.purchasedItems.includes(Constants.PIZZA_PRODUCT_ID)) {
        return Constants.PIZZA_PRODUCT_ID
      } else if (this.purchasedItems.includes(Constants.ART_SUPPLIES_PRODUCT_ID)) {
        return Constants.ART_SUPPLIES_PRODUCT_ID
      } else if (this.purchasedItems.includes(Constants.COFFEE_PRODUCT_ID)) {
        return Constants.COFFEE_PRODUCT_ID
      } else if (this.purchasedItems.includes(Constants.BAGEL_PRODUCT_ID)) {
        return Constants.BAGEL_PRODUCT_ID
      } else if (this.purchasedItems.includes(Constants.COOKIE_PRODUCT_ID)) {
        return Constants.COOKIE_PRODUCT_ID
      }
    }

    return null
  }

  updateAppList(appLink) {
    if (appLink == Constants.FACEBOOK_APP_LINK || appLink == Constants.ANDROID_FACEBOOK_APP_LINK) {
      this.listOfInstalledApps.push(Constants.FACEBOOK_LINK)
    } else if (appLink == Constants.INSTAGRAM_APP_LINK || appLink == Constants.ANDROID_INSTAGRAM_APP_LINK) {
      this.listOfInstalledApps.push(Constants.INSTAGRAM_LINK)
    } else if (appLink == Constants.X_APP_LINK || appLink == Constants.ANDROID_X_APP_LINK) {
      this.listOfInstalledApps.push(Constants.X_LINK)
    } else if (appLink == Constants.WHATSAPP_APP_LINK || appLink == Constants.ANDROID_WHATSAPP_APP_LINK) {
      this.listOfInstalledApps.push(Constants.WHATSAPP_LINK)
    }
  }

  isAppInstalled(app) {
    return this.listOfInstalledApps.includes(app)
  }

  getStoryHighestGift(gifts, reactionName) {
    if (this.giftItemList && this.giftItemList.length > 0) {
      let allGifts = JSON.parse(JSON.stringify(this.giftItemList))
      allGifts = allGifts.sort((a, b) => b.cost - a.cost)

      for (const gift of allGifts) {
        if (gift.reactionName == "LIKE") {
          continue
        }
        if (gifts[gift.reactionName] > 0) {
          return gift.reactionName
        }
      }
    } else {
      if (gifts[Constants.PIZZA] > 0) {
        return Constants.PIZZA
      } else if (gifts[Constants.ART_SUPPLIES] > 0) {
        return Constants.ART_SUPPLIES
      } else if (gifts[Constants.COFFEE] > 0) {
        return Constants.COFFEE
      } else if (gifts[Constants.BAGEL] > 0) {
        return Constants.BAGEL
      } else if (gifts[Constants.COOKIE] > 0) {
        return Constants.COOKIE
      } else {
        return reactionName
      }
    }
  }

  getStorySecondHighestGift(gifts, reactionName) {
    if (this.giftItemList && this.giftItemList.length > 0) {
      let allGifts = JSON.parse(JSON.stringify(this.giftItemList)).reverse()
      allGifts = allGifts.sort((a, b) => b.cost - a.cost)

      for (const gift of allGifts) {
        if (gift.reactionName == "LIKE") {
          continue
        }
        if (gifts[gift.reactionName] > 0 && this.getStoryHighestGift(gifts, reactionName) != gift.reactionName) {
          return gift.reactionName
        }
      }
    } else {
      if (gifts[Constants.COOKIE] > 0) {
        return Constants.COOKIE
      } else if (gifts[Constants.BAGEL] > 0) {
        return Constants.BAGEL
      } else if (gifts[Constants.COFFEE] > 0) {
        return Constants.COFFEE
      } else if (gifts[Constants.ART_SUPPLIES] > 0) {
        return Constants.ART_SUPPLIES
      } else if (gifts[Constants.PIZZA] > 0) {
        return Constants.PIZZA
      } else {
        return reactionName
      }
    }
  }

  // getPanelsForRedirection(pathUrl, panels, forNextPre = true) {
  //   let values = [];
  //   for (const key in panels) {
  //     if (panels.hasOwnProperty(key)) {
  //       const element = panels[key];

  //       let actionURL = null;

  //       if (typeof (element) == "string") {
  //         actionURL = Utils.getMeaningFullURL(element);
  //       } else if (element && element.action) {
  //         const completeURL = Utils.resolvePath(pathUrl, element.action)
  //         actionURL = Utils.getMeaningFullURL(completeURL);
  //       }

  //       if (!actionURL || element.actionType || (forNextPre && element && !element.image) || values.indexOf(actionURL) != -1) {
  //         continue;
  //       }

  //       values.push(actionURL)
  //     }
  //   }

  //   return values;
  // }

  needsToShowAlertMessage() {
    return this.shownAlertMessage;
  }

  showErrorAlert(title, message) {
    if (this.isErrorAlertVisible) { return }

    this.isErrorAlertVisible = true;

    Alert.alert(title, message, [
      {
        text: "OK", onPress: () => {
          this.isErrorAlertVisible = false;
        }
      }
    ], {
      cancelable: false,
      onDismiss: () => {
        this.isErrorAlertVisible = false;
      }
    })
  }

  //Notification

  setPendingNotification(notification) {
    this.currentPendingNotification = notification
  }

  getPendingNotification() {
    return this.currentPendingNotification
  }

  setPendingBranchLink(notification) {
    this.currentPendingBranchLink = notification
  }

  getPendingBranchLink() {
    return this.currentPendingBranchLink
  }

  setPendingDynamicLink(linkData) {
    this.currentPendingDynamicLink = linkData
  }

  getPendingDynamicLink() {
    return this.currentPendingDynamicLink
  }

  setInfluencePointsValuesForActions(value) {
    this.differentActionsInfluencePoints = value
  }

  getInfluencePointsValuesForActions() {
    return this.differentActionsInfluencePoints
  }

}
