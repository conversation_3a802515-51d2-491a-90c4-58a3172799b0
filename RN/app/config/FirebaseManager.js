import { getApp } from '@react-native-firebase/app'
import auth, { getAuth } from '@react-native-firebase/auth';
import '@react-native-firebase/crashlytics';
import messaging from '@react-native-firebase/messaging';
import '@react-native-firebase/analytics';
import '@react-native-firebase/remote-config';
import '@react-native-firebase/perf';
import PropTypes from 'prop-types'
import { Platform, Linking, Alert } from 'react-native'
import { settings } from './settings'
import DeepLinkManager from './DeepLinkManager';
import { Utils } from './Utils';
import store from '../redux/store'
import { batch } from 'react-redux'
import {
  getUserDetails,
  readComicAction,
  updateAlertSubscription,
  signOutUser,
  updateUserProfile,
  updateProfilePicURL,
  getUnlockComicURLs,
  getUserNotification
} from '../redux/actions/actions'
import NavigationService from './NavigationService'
import { SharedPreferences } from '../config/SharedPreferences';
import SessionManager from './SessionManager'
import { Constants } from './Constants'
import PushNotificationIOS from "@react-native-community/push-notification-ios";
import PushNotification from "react-native-push-notification";
import UserSession from './UserSession'
import T from './../../app/redux/actions/types'
import FileCache from './FileCache'
import ApiService from '../service/ApiService'
import { cloudFunctions } from '../service/cloudFunctions'
import appleAuth from '@invertase/react-native-apple-authentication'
import {
  GoogleOneTapSignIn,
  statusCodes,
  isSuccessResponse,
  isErrorWithCode,
  isNoSavedCredentialFoundResponse
} from '@react-native-google-signin/google-signin';
import ImageDownloader from '../components/ImageDownloader'

const FIREBASE_SIGNUP_ERROR = -102
const FIREBASE_SIGNIN_PHONE_ERROR = -103
const FIREBASE_SIGNIN_OTP_VERIF_ERROR = -104

export default class FirebaseManager {
  static instance = new FirebaseManager()

  constructor() {
    this.updateAlertSubscription.bind(this)
    this.signInAnonymously.bind(this)

    this.isUserAvailable.bind(this)
    this.addListeners.bind(this)
    this.handleNotification.bind(this)
    this.addNewTransaction.bind(this)
    this.setReferrer.bind(this)
    this.showPermissionSettingsAlert.bind(this);
    this.askForNotificationPermission.bind(this);
    this.signInWithMobileNumber.bind(this);
    this.verifyOTPAndLinkUser.bind(this);

    this.getRemoteConfigurations(this)
    this.getHomeComicURL(this)
    this.getISWTURL(this)
    this.getWhatsTinyviewURL(this)
    this.addInitialNotificationListener(this)

    this.isSettingAlertVisible = false;
    this.getInfoRetryCount = 0;
    Utils.log("FirebaseManager ini")
    this.addListeners()

    getApp().remoteConfig()
      .setDefaults({
        home_url: '/index.json',
        iswt_url: '/in-science-we-trust/index.json',
        whats_tinyview_url: '/tinyview/index.json',
        //channels: JSON.stringify([{"title":"In Science We Trust","link":"/in-science-we-trust/index.json"},{"title":"What's Tinyview?","link":"/tinyview/index.json"}]),
      })
    getApp().remoteConfig().fetchAndActivate().then(activated => {
      if (activated) {
        Utils.log('Defaults set, fetched & activated!');
      } else {
        Utils.log('Defaults set, however activation failed.');
      }
    })
  }

  isUserAvailable() {
    return getAuth().currentUser != null
  }

  currentUser() {
    return getAuth().currentUser
  }

  getFirebaseToken() {
    return this.token
  }

  async signOutUser() {
    const data = { "deviceToken": this.token };
    await this.callSignOutUserAPI(data);
    await getAuth().signOut();
    try {
      await GoogleOneTapSignIn.signOut();
    } catch (error) {
      Utils.log("Error in Google SignOut " + error);
    }
  }

  async deleteUserAccount() {
    await this.callDeleteAccountAPI();
    await getAuth().signOut();
  }

  addInitialNotificationListener() { // The call to getInitialNotification should happen within a React lifecycle method after mounting (e.g. componentDidMount or useEffect)       
    getApp().messaging().getInitialNotification()
      .then((notificationOpen) => {
        if (notificationOpen) {
          // App was opened by a notification
          // Get the action triggered by the notification being opened
          const action = notificationOpen.action;
          // Get information about the notification that was opened
          let notification = null;
          if (notificationOpen.data) {
            notification = notificationOpen.data;
          } else {
            notification = notificationOpen.notification;
          }

          Utils.log('Initial Notification received!');
          SessionManager.instance.setPendingNotification(notification)
          //this.handleNotification(notification, true)
        }
      });
  }

  addListeners() {
    Utils.log("Message: addListeners ")
    getApp().messaging().onTokenRefresh(token => {
      Utils.log('Refreshed Token: ', token)
      this.updateUserData()
    })

    getApp().messaging().onMessage(notification => {
      // When app in the forground
      Utils.log('Notification received in Forground!');

      if (!notification || !notification.notification) {
        return
      }

      let notificationData = notification.data
      if (!notification.data) {
        notificationData = {}
      }

      // Sending local push notification
      PushNotification.localNotification({
        channelId: settings.notifChannelId,
        message: notification.notification.body,
        title: notification.notification.title,
        invokeApp: true,
        userInfo: { ...notificationData, fromLocalNotification: "true" }
      })
    });

    getApp().messaging().onNotificationOpenedApp((notificationOpen) => {
      // Get information about the notification that was opened. App was in Background.
      SessionManager.instance.updateBGStayTime()
      let notification = null;
      if (notificationOpen.data) {
        notification = notificationOpen.data;
      } else {
        notification = notificationOpen.notification;
      }

      notification.fromBackground = true

      Utils.log('Notification opened!');
      this.handleNotification(notification, true)
    });

    //Configure for local notification
    PushNotification.configure({
      onNotification: function (notification) {

        if (!notification) {
          return
        }

        Utils.log("Notfication Received In PushNotification :", notification);

        if (!notification.foreground && (!notification.data || notification.data.fromLocalNotification != "true")) {
          Utils.log("Not handling non foreground notification in PushNotification :", notification);
          return
        }

        let notificationData = null
        if (notification.data) {
          notificationData = notification.data;
        }

        Utils.log('Notification opened!');
        FirebaseManager.instance.handleNotification(notificationData, false)
        notification.finish(PushNotificationIOS.FetchResult.NoData);
      },
    })
  }

  handleNotification(notification, shouldWait = true) {
    Utils.log("notification " + JSON.stringify(notification))
    const redirection_type = notification["redirection_type"] ? notification["redirection_type"] : notification["redirectionType"]
    const path = notification["deeplink_path"] ? notification["deeplink_path"] : notification["deeplinkPath"];
    if (redirection_type && (redirection_type.toLowerCase() == "join_friend" || redirection_type.toLowerCase() == Constants.USER_PROFILE_ACTION_TYPE.toLowerCase())) {
      setTimeout(() => {
        var userID = notification["uid"] ? notification["uid"] : null
        Utils.navigateToSubRouteWithParams(Constants.DRAWER_HOME, Constants.USER_PROFILE_SCREEN, null, { clickedUserID: userID })
      }, shouldWait ? settings.REDIRECTION_TIME_TO_WAIT : 200)
    } else if (redirection_type && redirection_type.toLowerCase() == "STORY".toLowerCase()) {
      var storyData = notification
      storyData.forceOpen = true //{storyID: "zm8INPvSGyckBDetrmTj", "forceOpen": true} 
      storyData.fromNotification = true
      setTimeout(() => {
        Utils.navigateToSubRouteWithParams('DrawerHome', Constants.POST_COMMENTS_SCREEN, null, { storyData })
      }, shouldWait ? settings.REDIRECTION_TIME_TO_WAIT : 200)

      if (notification["fromUserID"] && notification["action"] == settings.INFLUENCE_POINTS_ACTIONS.SHARED_LINK_REFERRER && this.currentUser()) { // Addind influence point to sender
        const data = {
          action: settings.INFLUENCE_POINTS_ACTIONS.SHARED_LINK_REFERRER,
          referrerID: notification["fromUserID"],
          url: notification["url"]
        }
        this.addInfluencePoint(data)
      }
    } else if (redirection_type && redirection_type.toLowerCase() == "request_accept".toLowerCase()) {
      setTimeout(() => {
        let params = { componentToRender: Constants.FRIENDS }
        Utils.navigateToSubRouteWithParams('DrawerHome', Constants.FRIENDS_COMPONENT, null, params)
      }, shouldWait ? settings.REDIRECTION_TIME_TO_WAIT : 200)
    } else if (redirection_type && redirection_type.toLowerCase() == "friend_request") {
      setTimeout(() => {
        Utils.navigateToFriendRequests(null)
      }, shouldWait ? settings.REDIRECTION_TIME_TO_WAIT : 200)
    }
    else if (redirection_type && redirection_type.toLowerCase() == "added_influence".toLowerCase()) {
      setTimeout(() => {
        Utils.navigateToInfluencePage(null)
      }, shouldWait ? settings.REDIRECTION_TIME_TO_WAIT : 200)
    } else if (redirection_type && redirection_type.toLowerCase() == Constants.MANAGE_ALERTS_ACTION_TYPE.toLowerCase()) {
      setTimeout(() => {
        Utils.navigateToManageAlertsPage(null, { shouldRefreshProps: true })
      }, shouldWait ? settings.REDIRECTION_TIME_TO_WAIT : 200)
    } else if (path) {
      const redirectionPath = Utils.resolvePath(settings.getComicHomeURL(), path);
      setTimeout(() => {
        if (path == settings.getInfluencePageURL()) {
          Utils.navigateToInfluencePage(null)
        } else {
          NavigationService.resetAndNavigate('Home', { comicHome: redirectionPath, hasUserInfo: true, isNavFromNotification: true })
        }
      }, shouldWait ? settings.REDIRECTION_TIME_TO_WAIT : 200);
    }
  }

  async fetchRemoteConfigurations() {
    let keys = ['home_url', 'iswt_url', 'whats_tinyview_url']
    var data = {}
    await getApp().remoteConfig().ensureInitialized()
    const values = getApp().remoteConfig().getAll()
    keys.forEach(key => {
      if (values[key]) {
        Utils.log("Found")
        data[key] = values[key].asString()
      } else {
        Utils.log("not found")
      }
    })
    this.remoteConfiguration = data;
    return this.remoteConfiguration;
  }

  getRemoteConfigurations(key) {
    if (this.remoteConfiguration != null && this.remoteConfiguration != undefined) {
      if (this.remoteConfiguration.hasOwnProperty(key)) {
        return this.remoteConfiguration[key];
      }
    }
  }

  getHomeComicURL() {
    return this.getRemoteConfigurations('home_url')
  }

  getISWTURL() {
    return this.getRemoteConfigurations('iswt_url')
  }

  getWhatsTinyviewURL() {
    return this.getRemoteConfigurations('whats_tinyview_url')
  }

  isSignInWithEmailLink(url) {
    return getAuth().isSignInWithEmailLink(url)
  }

  async handleEmailAuthLink(data) {
    if (data && data.url) {
      data.url = decodeURIComponent(data.url)
      Utils.log('handleEmailAuthLink 1 ' + data.url);
      let reqParam = ""
      let paramArray = /[?&]uuid%3D([^&]+)/.exec(data.url)
      if (paramArray != null) {
        reqParam = paramArray[1]
      }

      Utils.log('handleEmailAuthLink 2 ' + reqParam);
      if (reqParam != null && reqParam != undefined && reqParam != "undefined") {
        store.dispatch({
          type: T.SHOW_LOGIN_INDICATOR
        })

        let requestedData = { flowDataID: reqParam }
        let userEmailData = await FirebaseManager.instance.getUserEmailData(requestedData)

        if (userEmailData != null && userEmailData.data != null) {
          let authGivesError = false
          let isLinkAlreadyUsed = false
          let mergeUserError = false
          let userData = userEmailData.data
          let forAlertEmailAuth = userData.flow == 'alerts' ? true : false // When user added email via Manage Alert flow
          let forSubsEmailAuth = userData.flow == 'subs_sign_in' ? true : false // When user added email via Subscription purchasing flow
          let isMergingUserFlow = userData.flow == 'merge' ? true : false // To merge two permanent users
          let emailMetadata = userData.metadata
          let currentUserID = Utils.getCurrentUserId()

          if (UserSession.instance.isLoggedInUser()) { //If user already logged in with the same email
            let currentUserEmail = UserSession.instance.getCurrentUserInfo() && UserSession.instance.getCurrentUserInfo().email
            if (currentUserEmail && currentUserEmail == userData.email) {
              store.dispatch({
                type: T.HIDE_LOGIN_INDICATOR
              })
              if (forAlertEmailAuth || isMergingUserFlow) {
                Utils.navigateToManageAlertsPage(this.props, { emailVerified: true })
              }

              return;
            }
          }

          let requestedData = { type: 'email', email: userData.email.toLowerCase() }
          let userProfile = await FirebaseManager.instance.getUserProfile(requestedData)
          let alreadyUser = userProfile && userProfile.length > 0
          let userAlerts = alreadyUser ? userProfile[0].alerts : null

          if (!userData.customToken) { // Same device
            Utils.log("E-Mail authentication link is created and open from same device")

            if (isMergingUserFlow) {
              if (alreadyUser) {
                try {
                  let requiredData = { anonymousID: userData.targetUserID, forceMerge: true }
                  await FirebaseManager.instance.mergeAuthenticatedUser(requiredData)
                } catch (error) {
                  Utils.log("mergeAuthenticatedUser API is giving error ", error)
                  mergeUserError = true
                }
              }

              if (!mergeUserError) {
                let credentials = auth.EmailAuthProvider.credentialWithLink(userData.email, data.url)
                await getAuth().currentUser.linkWithCredential(credentials)
                  .then((response) => {
                    console.log("Current User linked with E-mail address " + JSON.stringify(response));
                  }).catch((error) => {
                    console.log("linkWithCredential gives error ", error);
                    authGivesError = true
                  });
              }
            } else if (alreadyUser) {
              Utils.log("User already exists in our database. Going to signIn user with E-mail address")
              await getAuth().signInWithEmailLink(userData.email, data.url)
                .then((response) => {
                  console.log("Current user signedIn with E-mail address " + JSON.stringify(response));
                }).catch((error) => {
                  console.log("signInWithEmailLink gives error ", error);
                  if (error.code == "auth/invalid-action-code") {
                    isLinkAlreadyUsed = true
                  } else {
                    authGivesError = true
                  }
                });

              try {
                let sourceTokenId = userData.anonymousIdToken
                if (!authGivesError && !isLinkAlreadyUsed && sourceTokenId) {
                  await FirebaseManager.instance.mergeAuthenticatedUser({ anonymousIdToken: sourceTokenId })
                }
              } catch (error) {
                Utils.log("mergeAuthenticatedUser API is giving error ", error)
              }
            } else {
              let credentials = auth.EmailAuthProvider.credentialWithLink(userData.email, data.url)
              await getAuth().currentUser.linkWithCredential(credentials)
                .then((response) => {
                  console.log("Current user signedUp with E-mail address " + JSON.stringify(response));
                }).catch((error) => {
                  console.log("linkWithCredential gives error ", error);
                  authGivesError = true
                });
            }

          } else if (userData.customToken && currentUserID != userData.sourceUserID) { //Different device
            Utils.log("E-Mail authentication link is created and open from different device")

            if (UserSession.instance.isLoggedInUser()) {
              await store.dispatch(signOutUser(null))
            }

            if (isMergingUserFlow) {
              await getAuth().signInWithCustomToken(userData.customToken)
                .then((response) => {
                  console.log("Current user signedUp with E-Mail address " + JSON.stringify(response));
                }).catch((error) => {
                  console.log("signInWithCustomToken gives error ", error);
                  authGivesError = true
                });

              if (!authGivesError) {
                if (alreadyUser) {
                  try {
                    let requestedData = { 'anonymousID': userData.targetUserID, 'forceMerge': true }
                    await FirebaseManager.instance.mergeAuthenticatedUser(requestedData)
                  } catch (error) {
                    Utils.log("mergeAuthenticatedUser API is giving error ", error)
                    mergeUserError = true
                  }
                }

                if (!authGivesError && !mergeUserError) {
                  let credentials = auth.EmailAuthProvider.credentialWithLink(userData.email, data.url)
                  await getAuth().currentUser.linkWithCredential(credentials)
                    .then((response) => {
                      console.log("Current User linked with E-mail address " + JSON.stringify(response));
                    }).catch((error) => {
                      console.log("linkWithCredential gives error ", error);
                      authGivesError = true
                    });
                }
              }
            } else if (alreadyUser) {
              Utils.log("User already exists in our database. Going to signIn user with E-mail address")
              await getAuth().signInWithEmailLink(userData.email, data.url)
                .then((response) => {
                  console.log("Current user signedIn with E-mail address " + JSON.stringify(response));
                }).catch((error) => {
                  console.log("signInWithEmailLink gives error ", error);
                  if (error.code == "auth/invalid-action-code") {
                    isLinkAlreadyUsed = true
                  } else {
                    authGivesError = true
                  }
                });

              try {
                let sourceTokenId = userData.anonymousIdToken
                if (!authGivesError && !isLinkAlreadyUsed && sourceTokenId) {
                  await FirebaseManager.instance.mergeAuthenticatedUser({ anonymousIdToken: sourceTokenId })
                }
              } catch (error) {
                Utils.log("mergeAuthenticatedUser API is giving error ", error)
              }
            } else {
              await getAuth().signInWithCustomToken(userData.customToken)
                .then((response) => {
                  console.log("Current user signedUp with E-Mail address " + JSON.stringify(response));
                }).catch((error) => {
                  console.log("signInWithCustomToken gives error ", error);
                  authGivesError = true
                });

              let credentials = auth.EmailAuthProvider.credentialWithLink(userData.email, data.url)
              await getAuth().currentUser.linkWithCredential(credentials)
                .then((response) => {
                  console.log("Current User linked with E-mail address " + JSON.stringify(response));
                }).catch((error) => {
                  console.log("linkWithCredential gives error ", error);
                  authGivesError = true
                });
            }
          }

          let hasAnyErrorOccur = authGivesError || isLinkAlreadyUsed || mergeUserError
          if (!hasAnyErrorOccur) {
            let params = { "email": userData.email }
            await store.dispatch(updateUserProfile(params, null, true))
          }

          if (UserSession.instance.isLoggedInUser()) {
            let currentUserEmail = FirebaseManager.instance.currentUser() && FirebaseManager.instance.currentUser().email
            let currentUserMobileNumber = FirebaseManager.instance.currentUser() && FirebaseManager.instance.currentUser().phoneNumber
            if (!Utils.checkData(currentUserEmail) && !Utils.checkData(currentUserMobileNumber)) {
              await FirebaseManager.instance.signInAnonymously()
              let keyToSave = [settings.INITIAL_INTRO_LOAD_KEY, settings.SHOW_NOTIFICATION_PERMISSION_ALERT_KEY]
              await SharedPreferences.clearSharedPrefrences(keyToSave);
              await SharedPreferences.storePurchasesProducts([]);
              await FileCache.default.delete()
              SharedPreferences.setShowAllComicsValue(true)
              SessionManager.instance.clearUserDetails()
            }
          }

          let seriesToFollow = userData.series
          if (!hasAnyErrorOccur) {
            if (seriesToFollow) {
              const seriesUrl = Utils.resolvePath(settings.apiBaseURL, seriesToFollow.action)
              store.dispatch(updateAlertSubscription(true, seriesUrl, false))
            }
            FirebaseManager.instance.onAuthSuccess()
          }

          setTimeout(() => {
            if (hasAnyErrorOccur) {
              let params = { isVerifFailed: true, flowDataID: reqParam, userEmailAddress: userData.email, shouldRefreshProps: true, forEmailAlert: forAlertEmailAuth || isMergingUserFlow }
              Utils.navigateToDrawerLoginRoute(this.props, Constants.EMAIL_VERIFY_SCREEN, params)
            } else if (forSubsEmailAuth) {
              const currentURL = (store.getState() && store.getState().readComic) ? store.getState().readComic.pathUrl : null;
              Utils.log('handleEmailAuthLink 3 ' + currentURL);
              if (!Utils.isSubscriptionURL(currentURL) && userData.customToken && currentUserID != userData.sourceUserID) { //Different device
                NavigationService.resetAndNavigate('Home', { comicHome: settings.getSubscribeURL(), hasUserInfo: true, isAuthCompletedForSubs: true, productId: emailMetadata.productId })
              } else {
                let params = { isEmailVerfied: true, isAuthCompletedForSubs: true }
                Utils.navigateToReader(params)
              }
            } else if (forAlertEmailAuth || isMergingUserFlow) {
              let params = { emailVerified: true, seriesFollowed: userData.series, currentUserAlerts: userAlerts }
              if (seriesToFollow) {
                params.seriesFollowed = seriesToFollow.title
              }
              Utils.navigateToManageAlertsPage(this.props, params)
            } else if (Utils.checkObject(emailMetadata) && emailMetadata.redirection_type && emailMetadata.redirection_type.toLowerCase() == Constants.FRIEND_REQUEST.toLowerCase()) {
              SessionManager.instance.setIsInviteLinkLogin(false)
              Utils.navigateToFriendRequests(null);
            } else if (Utils.checkObject(emailMetadata) && emailMetadata.redirection_type && emailMetadata.redirection_type.toLowerCase() == Constants.SEE_FRIENDS.toLowerCase()) {
              Utils.navigateToSeeFriendsPage(null)
            } else {
              const showInviteFriendsSheet = Utils.checkObject(emailMetadata) && emailMetadata.redirection_type && emailMetadata.redirection_type.toLowerCase() == Constants.INVITE_FRIEND.toLowerCase()
              let params = { isEmailVerfied: true, forceReload: true, isLoginSuccess: true, showInviteSheet: showInviteFriendsSheet }
              NavigationService.openHomePage(params)
            }
          }, settings.REDIRECTION_TIME_TO_WAIT)
        } else {
          Utils.log('handleEmailAuthLink 4 userEmailData not found from API ' + userEmailData);
          store.dispatch({
            type: T.HIDE_LOGIN_INDICATOR
          })
          Utils.showError("Something went wrong. Email link data not available.", "Error")
        }
      } else {
        let params = { isVerifFailed: true, flowDataID: null, userEmailAddress: null, shouldRefreshProps: true }
        Utils.navigateToDrawerLoginRoute(this.props, Constants.EMAIL_VERIFY_SCREEN, params)
      }
    } else {
      Utils.log('handleEmailAuthLink 5 URL : ' + data.url);
    }

    store.dispatch({
      type: T.HIDE_LOGIN_INDICATOR
    })

  }

  async onAuthSuccess() {
    FirebaseManager.instance.updateUserData()
    store.dispatch(getUnlockComicURLs())
    store.dispatch(getUserNotification({ countOnly: 1 }))
    SessionManager.instance.shownAlertMessage = true
    store.dispatch({
      type: T.SAVE_USER_ANONYMOUS_STATE,
      payload: FirebaseManager.instance.isUserAnonymous()
    })
  }

  async sendEmailAuthLink(dataFlowID, userEmail, emailType, metadata) {
    const actionCodeSettings = {
      handleCodeInApp: true,
      url: settings.emailFallbackURL + "?uuid=" + dataFlowID,
      iOS: {
        bundleId: settings.APP_PACKAGE_ID
      },
      android: {
        packageName: settings.APP_PACKAGE_ID,
        installApp: false,
      }
    }

    await getAuth().sendSignInLinkToEmail(userEmail, actionCodeSettings)
      .then((response) => {
        console.log("Authentication E-Mail send to user's email successfully " + response);
        let isEmailAlert = emailType == 'alerts' ? true : false
        let isFromAuth = emailType == 'subs_sign_in' ? true : false
        let params = { userEmailAddress: userEmail, shouldRefreshProps: true, flowDataID: dataFlowID, forEmailAlert: isEmailAlert, forAuthOnly: isFromAuth, isVerifFailed: null, metadata: metadata }
        Utils.navigateToDrawerLoginRoute(this.props, Constants.EMAIL_VERIFY_SCREEN, params)
      }).catch((error) => {
        console.log("Getting error in sending authentication E-Mail to user's email ", error);
      });
  }

  async signInAnonymously() {
    try {
      await getAuth().signInAnonymously()
      Utils.log("New Anonymous User Created " + JSON.stringify(FirebaseManager.instance.currentUser()))
      const data = await this.createAndGetUser()
      return data
    } catch (error) {
      Utils.log(error.code + error)
      FirebaseManager.instance.recordError(FIREBASE_SIGNUP_ERROR, error, "FIREBASE_ANONYMOUS_SIGNUP_FAILED");
      return null;
    }
  }

  async createUser() {
    try {
      const data = { "uid": this.currentUser().uid }
      Utils.log('Calling API to Create User on BE')
      const createUser = ApiService.instance.callFunction('CREATE_USER')
      let result = await createUser({ data })
      Utils.log('API Response of Create User ' + JSON.stringify(result))
      return result.data
    } catch (error) {
      Utils.log('createUser API: Failed ' + error.code + error)
      throw error;
    }
  }

  async createAndGetUser() {
    try {
      const userData = await this.createUser()
      Utils.log("User Created in DB " + JSON.stringify(userData))

      let userRes = {}
      if (userData) {
        const dobObj = Utils.checkData(userData.dob) ? moment(userData.dob).format("DD/MM/YYYY") : null
        userRes = { "displayName": userData.displayName, "gender": userData.gender, "dob": dobObj }
      }

      UserSession.instance.updateUserDetails({ ...userData, ...userRes })
      if (userData.monthlyReadCounter) {
        UserSession.instance.updateReadedFreeComicsCount(userData.monthlyReadCounter.count)
      }
      if (userData.subscriptions && userData.subscriptions.length > 0) {
        var subscriptionIDs = []
        for (const key in userData.subscriptions) {
          const element = userData.subscriptions[key];
          subscriptionIDs.push(element.productID)
          if (Utils.isSubsProduct(element.productID)) {
            SessionManager.instance.updatePurchasedData(element)
          }
        }
        await SharedPreferences.storePurchasesProducts(subscriptionIDs);
      } else {
        SessionManager.instance.updatePurchasedData([])
        await SharedPreferences.storePurchasesProducts([]);
      }

      await SharedPreferences.setShowAllComicsValue(Utils.checkObject(userData.showAllComics) ? userData.showAllComics : true)
      SessionManager.instance.profileImageURL = userData.photoURL

      batch(() => {
        store.dispatch({
          type: T.UPDATE_USER_DETAILS,
          payload: userData
        })
        store.dispatch({
          type: T.UPDATE_ALERTS,
          payload: userData.alerts
        })
      })

      return userData
    } catch (error) {
      Utils.log("createUser failed " + error)
      SessionManager.instance.profileImageURL = null
      SessionManager.instance.showAllComics = true

      return null
    }
  }

  isUserAnonymous() {
    const currentUser = getAuth().currentUser
    if (currentUser) {
      return currentUser.phoneNumber == null && currentUser.email == null
    }
    return true
  }

  async signInWithMobileNumber(mobNumber) { // mobNumber with ISD code
    try {
      const data = await getAuth().signInWithPhoneNumber(mobNumber)
      Utils.log("data " + JSON.stringify(data))
      return data;
    } catch (error) {
      Utils.log("Error in signInWithMobileNumber " + error)
      FirebaseManager.instance.recordError(FIREBASE_SIGNIN_PHONE_ERROR, error, "FIREBASE_SIGNIN_PHONE_NUMBER_FAILED");
      throw error
    }
  }

  async verifyOTPAndLinkUser(phoneNumber, verifId, otp) {
    let credential = null;
    const anonymousUserIdToken = SessionManager.instance.anonymousUserIdToken || await this.currentUser().getIdToken();
    let alreadyUserProfile = null;
    const phoneData = { phoneNumber: phoneNumber }
    try {
      credential = auth.PhoneAuthProvider.credential(verifId, otp);
      const currUser = getAuth().currentUser
      alreadyUserProfile = await this.getUserProfile(phoneData)

      if (!this.isUserAnonymous()) { // Auto sign in case
        await this.updateUserProfile(phoneData);
        if (alreadyUserProfile && alreadyUserProfile.length > 0) {
          await this.mergeUserData(anonymousUserIdToken)

          setTimeout(() => {
            store.dispatch(getUserDetails())
          }, 500);
        }

        return currUser;
      }

      if (alreadyUserProfile && alreadyUserProfile.length == 0) {
        const res = await currUser.linkWithCredential(credential);
        await SharedPreferences.saveData(settings.SIGNED_IN_USER_DATA, JSON.stringify(res.user._user))
        Utils.log("User linked", JSON.stringify(res.user._user));
        await this.updateUserProfile(phoneData);
        Utils.log("Current user " + JSON.stringify(getAuth().currentUser));
        Utils.log("res " + res)

        return res.user;

      } else {
        const signedUser = await getAuth().signInWithCredential(credential)
        Utils.log("Current user " + JSON.stringify(getAuth().currentUser));

        await this.mergeUserData(anonymousUserIdToken)

        setTimeout(() => {
          store.dispatch(getUserDetails())
        }, 500);

        return signedUser.user;
      }

    } catch (error) {
      Utils.log("Error in verifyOTPAndLinkUser " + error)
      const currUser = getAuth().currentUser

      if (!this.isUserAnonymous()) { // Auto sign in case
        await this.updateUserProfile(phoneData);
        if (alreadyUserProfile && alreadyUserProfile.length > 0) {
          await this.mergeUserData(anonymousUserIdToken)

          setTimeout(() => {
            store.dispatch(getUserDetails())
          }, 500);
        }

        return currUser;
      }

      Utils.log("Current user Error " + JSON.stringify(currUser));
      FirebaseManager.instance.recordError(FIREBASE_SIGNIN_OTP_VERIF_ERROR, error, "FIREBASE_SIGNIN_OTP_VERIF_FAILED");

      if (error.code == "auth/provider-already-linked" || (Platform.OS == 'android' && error.code == "auth/unknown")) {
        try {
          const unlinkedUser = await getAuth().currentUser.unlink('phone')
          if (unlinkedUser) {
            return await this.verifyOTPAndLinkUser(phoneNumber, verifId, otp)
          } else {
            Alert.alert("Error", "User already linked with same credentials");
          }
        } catch (errora) {
          FirebaseManager.instance.recordError(FIREBASE_SIGNIN_OTP_VERIF_ERROR, errora, "FIREBASE_SIGNIN_OTP_VERIF_FAILED");
          Utils.log("New Error " + JSON.stringify(errora))
          throw errora
        }

      } else if (error.code == "auth/credential-already-in-use") {
        const signedUser = await getAuth().signInWithCredential(credential)
        Utils.log("Current user " + JSON.stringify(getAuth().currentUser));

        await this.mergeUserData(anonymousUserIdToken)

        return signedUser.user;
      } else {
        throw error
      }
    }
  }

  async googleSignInCallback(result, anonymousUserIdToken) {
    if (isSuccessResponse(result)) {
      // 3). Get SignIn Token
      const { idToken, user } = result.data
      const { name, email, photo, id } = user

      var requestedData = null
      if (email) {
        requestedData = { type: 'email', email: email.toLowerCase() }
      }

      let userProfile = await FirebaseManager.instance.getUserProfile(requestedData)
      let alreadyUser = userProfile && userProfile.length > 0

      const isAnonymousUser = this.isUserAnonymous()
      var signedInUser = null
      var forceMerged = false
      // 4). Create a Google credential with the token
      const googleCredential = auth.GoogleAuthProvider.credential(idToken);

      if (isAnonymousUser) {
        try {
          signedInUser = await getAuth().signInWithCredential(googleCredential)
          await this.createAndGetUser()
        } catch (error) {
          Utils.log("Error in signInWithGoogle " + error)
          throw Error("There is something wrong with the Google Sign In. Please try again.")
        }
      } else {
        try {
          if (this.currentUser().phoneNumber != null && alreadyUser && !userProfile[0].phoneNumber) {
            forceMerged = true
            let requiredData = { anonymousID: userProfile[0].id, forceMerge: true }
            await this.mergeAuthenticatedUser(requiredData)
          }

          signedInUser = await this.currentUser().linkWithCredential(googleCredential)
        } catch (err) {
          Utils.log("Error in linkWithCredential " + err)
          if (err.code == "auth/credential-already-in-use") {
            throw Error("A user with this email address already exists.")
          } else if (err.code == "auth/email-already-in-use") {
            throw new Error("A user with this email address already exists.")
          } else {
            throw err
          }
        }
      }

      try { //If we get any error in merging user data, we will continue with the flow
        if ((alreadyUser || isAnonymousUser) && !forceMerged) {
          let requiredData = { anonymousIdToken: anonymousUserIdToken }
          await this.mergeAuthenticatedUser(requiredData)
        }
      } catch (mergeError) {
        Utils.log("Error in Merge User Error" + mergeError)
      }

      if (photo && (!alreadyUser || (alreadyUser && !userProfile[0].photoURL))) {
        let imageUrl = await ImageDownloader.downloadImage(photo)
        let base64Image = "data:image/jpg;base64," + await FileCache.default.getBase64Content(imageUrl);
        let imageFileName = `userProfilePic_${Math.floor(Date.now() / 1000)}`

        let data = { "image": base64Image, "fileName": imageFileName }
        await store.dispatch(updateProfilePicURL(data, null))
      }

      var params = { "googleID": id } //Updating user's google ID
      if (!alreadyUser || (alreadyUser && !userProfile[0].email)) {
        params["email"] = signedInUser.user.email
      }

      if (name) {
        params["displayName"] = name
        params["isSocialLogin"] = 1 //1 means don't update the profile.
      }

      await store.dispatch(updateUserProfile(params, null, true))

      Utils.log(`Firebase authenticated via Google, UID: ${JSON.stringify(signedInUser)}`);
      return signedInUser.user

    } else if (isNoSavedCredentialFoundResponse(result)) {
      // Android and Apple only.
      // No saved credential found (user has not signed in yet, or they revoked access)      
      Utils.log("isNoSavedCredentialFoundResponse Found")
      throw Error("No saved credential found. Please try again.")
    }
  }

  async signInWithGoogle() {
    const anonymousUserIdToken = SessionManager.instance.anonymousUserIdToken || await this.currentUser().getIdToken();
    // 1). Check if your device supports Google Play
    await GoogleOneTapSignIn.checkPlayServices()
    return GoogleOneTapSignIn.presentExplicitSignIn({
      nonce: new Date().getTime().toString(), // nonce is supported on all platforms!        
    }).then((result) => {
      return this.googleSignInCallback(result, anonymousUserIdToken)
    }).catch((error) => {
      Utils.log("Catch block Error in signInWithGoogle " + error)
      if (isErrorWithCode(error)) {
        switch (error.code) {
          case statusCodes.ONE_TAP_START_FAILED:
            // Android-only, you probably have hit rate limiting.
            // You can still call `presentExplicitSignIn` in this case.
            Utils.log('One tap start failed. You probably have hit rate limiting');
            throw new Error('Google play services not available');
          case statusCodes.PLAY_SERVICES_NOT_AVAILABLE:
            // Android: play services not available or outdated.
            // Get more details from `error.userInfo`.
            // Web: when calling an unimplemented api (requestAuthorization)
            // or when the Google Client Library is not loaded yet.
            Utils.log('Device does not contain Google play services.');
            throw new Error('Google play services not available');
          default:
            throw error
        }
      } else {
        // an error that's not related to google sign in occurred
        throw error
      }
    })
  }

  async signInWithApple() {
    const anonymousUserIdToken = SessionManager.instance.anonymousUserIdToken || await this.currentUser().getIdToken();

    try {
      var appleAuthRequestResponse = null
      try {
        // 1). start a apple sign-in request
        appleAuthRequestResponse = await appleAuth.performRequest({
          requestedOperation: appleAuth.Operation.LOGIN,
          requestedScopes: [appleAuth.Scope.EMAIL, appleAuth.Scope.FULL_NAME],
        });
      } catch (error) {
        Utils.log("Error in appleAuthRequestResponse " + error)
      }

      if (appleAuthRequestResponse) {
        // 2). if the request was successful, extract the token and nonce
        const { identityToken, nonce, email, fullName } = appleAuthRequestResponse;

        // can be null in some scenarios
        if (identityToken) {

          var requestedData = null
          if (email && !email.includes('privaterelay.appleid.com')) {
            requestedData = { type: 'email', email: email.toLowerCase() }
          } else {
            requestedData = { type: 'appleID', appleID: appleAuthRequestResponse.user }
          }

          let userProfile = await FirebaseManager.instance.getUserProfile(requestedData)
          let alreadyUser = userProfile && userProfile.length > 0

          // 3). create a Firebase `AppleAuthProvider` credential        
          const appleCredential = auth.AppleAuthProvider.credential(identityToken, nonce);

          // 4). use the created `AppleAuthProvider` credential to start a Firebase auth request,
          //     in this example `signInWithCredential` is used, but you could also call `linkWithCredential`
          //     to link the account to an existing user      

          const isAnonymousUser = this.isUserAnonymous()
          var signedUser = null
          var forceMerged = false

          if (isAnonymousUser) {
            try {
              signedUser = await getAuth().signInWithCredential(appleCredential)
              await this.createAndGetUser()
            } catch (error) {
              Utils.log("Error in signInWithApple " + error)
              throw Error("There is something wrong with the Apple Sign In. Please try again.")
            }
          } else {
            try {
              if (this.currentUser().phoneNumber != null && alreadyUser && !userProfile[0].phoneNumber) {
                forceMerged = true
                let requiredData = { anonymousID: userProfile[0].id, forceMerge: true }
                await this.mergeAuthenticatedUser(requiredData)
              }

              signedUser = await this.currentUser().linkWithCredential(appleCredential)
            } catch (err) {
              Utils.log("Error in linkWithCredential " + err)
              if (err.code == "auth/credential-already-in-use") {
                throw Error("A user with this email address already exists.")
              } else if (err.code == "auth/email-already-in-use") {
                throw new Error("A user with this email address already exists.")
              } else {
                throw err
              }
            }
          }

          try { //If we get any error in merging user data, we will continue with the flow
            if ((alreadyUser || isAnonymousUser) && !forceMerged) {
              let requiredData = { anonymousIdToken: anonymousUserIdToken }
              await this.mergeAuthenticatedUser(requiredData)
            }
          } catch (mergeError) {
            Utils.log("Error in Merge User Error" + mergeError)
          }

          var params = { "appleID": appleAuthRequestResponse.user }
          if (!alreadyUser || (alreadyUser && !userProfile[0].email)) {
            params["email"] = signedUser.user.email
          }

          const { givenName, familyName } = fullName
          if (familyName || givenName) {
            params["displayName"] = givenName + " " + familyName
            params["isSocialLogin"] = 1 //1 means don't update the profile.
          }

          await store.dispatch(updateUserProfile(params, null, true))

          Utils.log(`Firebase authenticated via Apple, UID: ${JSON.stringify(signedUser)}`);
          return signedUser.user
        } else {
          Utils.log('Error in fetching token from Apple. Please retry.');
          throw new Error('Error in fetching token from Apple. Please retry.');
        }
      } else {
        Utils.log("Error in Apple authentication response.")
      }
    } catch (error) {
      Utils.log("Catch block Error in signInWithApple " + error)
      throw error
    }
  }

  async mergeUserData(anonymousUserIdToken) {
    const currentURL = (store.getState() && store.getState().readComic) ? store.getState().readComic.pathUrl : null;
    if (currentURL) {
      store.dispatch(readComicAction(currentURL))
    }
    await this.mergeAuthenticatedUser({ anonymousIdToken: anonymousUserIdToken })
  }

  async resendOTP(phoneNumber) {
    return getAuth().verifyPhoneNumber(phoneNumber, 5, true).then((phoneAuthSnapshot) => {
      Utils.log('phoneAuthSnapshot is ' + JSON.stringify(phoneAuthSnapshot))
      return phoneAuthSnapshot;
    }, (error) => {
      Utils.log("Error resendOTP " + error);
      throw error;
    }).catch((error) => {
      Utils.log("Error resendOTP " + error);
      throw error;
    })
  }

  async getUserProfile(reqData) {
    try {
      Utils.log("Going to call getUserProfile API")
      const getProfile = ApiService.instance.callFunction('GET_USER_PROFILE');
      let result = await getProfile({ ...reqData })
      if (result != null) {
        Utils.log('Success - API calling for getUserProfile' + JSON.stringify(result));
      }
      return result
    } catch (error) {
      Utils.log('getUserProfile: Failed ', error.code, error)
      throw error;
    }
  }

  async updateUserSettings(value) {
    try {
      Utils.log('Calling API updateUserSettings')
      const updateSettings = ApiService.instance.callFunction('UPDATE_USER_SETTINGS')
      let result = await updateSettings({ data: { showAllComics: value } })
      Utils.log('API Response updateUserSettings ' + JSON.stringify(result))
      return result
    } catch (error) {
      Utils.log('updateUserSettings: Failed ' + error.code + error)
      throw error;
    }
  }

  async callSignOutUserAPI(reqData) {
    try {
      const signOutUser = ApiService.instance.callFunction('SIGN_OUT_USER')
      let result = await signOutUser({ data: reqData })
      return result
    } catch (error) {
      Utils.log('signOutUser: Failed ', error.code, error)
      throw error;
    }
  }

  async callDeleteAccountAPI(reqData = {}) {
    try {
      Utils.log('Going to call callDeleteAccountAPI ', reqData)
      const deleteAccount = ApiService.instance.callFunction('DELETE_USER_ACCOUNT')
      let result = await deleteAccount({ data: reqData })
      return result
    } catch (error) {
      Utils.log('deleteAccountAPI: Failed ', error.code, error)
      throw error;
    }
  }

  async mergeAuthenticatedUser(reqData) {
    try {
      const mergeUser = ApiService.instance.callFunction('MERGE_AUTHENTICATED_USER')
      var result = await mergeUser({ data: reqData })
      Utils.log("Merged Account" + JSON.stringify(result));
      return result
    } catch (error) {
      Utils.log('mergeAuthenticatedUser: Failed ' + error.code + error)
      throw error;
    }
  }

  async updateUserData() {
    try {
      this.token = await getApp().messaging().getToken()
      const addUserDeviceToken = ApiService.instance.callFunction('ADD_USER_DEVICE_TOKEN')

      Utils.log("deviceId/UID: " + getAuth().currentUser.uid + " Token: " + this.token)

      const currentUser = getAuth().currentUser
      if (currentUser) {
        getApp().crashlytics().setUserId(currentUser.uid)
        DeepLinkManager.instance.setUser(currentUser.uid)
      }

      await addUserDeviceToken({
        data: {
          token: this.token,
          deviceType: Platform.OS == "android" ? "android" : "ios"
        }
      })
    } catch (error) {
      Utils.log('Update: ' + error.code + error + "  line:" + error.lineNumber)
    }
  }

  async determinedNotificationStatus() {
    let authorizeStatus = await getApp().messaging().hasPermission()
    return authorizeStatus != messaging.AuthorizationStatus.NOT_DETERMINED
  }

  async hasNotificationPermission() {
    const authorizeStatus = await getApp().messaging().hasPermission()
    const hasPermission = authorizeStatus == messaging.AuthorizationStatus.AUTHORIZED
    if (hasPermission) {
      store.dispatch({
        type: T.NOTIFICATION_PERMISSION_GRANTED,
      })
    } else {
      store.dispatch({
        type: T.NOTIFICATION_PERMISSION_REVOKED,
      })
    }

    return hasPermission
  }

  showPermissionSettingsAlert() {
    if (this.isSettingAlertVisible) { return }

    this.isSettingAlertVisible = true;

    Alert.alert("", "You need to go to Settings and allow Notifications.", [
      {
        text: "Cancel", onPress: () => {
          this.isSettingAlertVisible = false;
        }
      },
      {
        text: "Settings", onPress: () => {
          this.isSettingAlertVisible = false;
          Platform.OS == 'ios' ? Linking.openURL('app-settings://notification/${com.newput.tinyview}') : Linking.openSettings()
        }
      }
    ], {
      cancelable: false,
      onDismiss: () => {
        this.isSettingAlertVisible = false;
      }
    })
  }

  async askForNotificationPermission(defaultSubs = true) {
    try {
      let authorizeStatus = await getApp().messaging().hasPermission()
      if (authorizeStatus == messaging.AuthorizationStatus.DENIED) {
        this.showPermissionSettingsAlert()
      } else {
        authorizeStatus = await getApp().messaging().requestPermission()
      }
      return authorizeStatus == messaging.AuthorizationStatus.AUTHORIZED;
    } catch (error) {
    }
  }

  async updateAlertSubscription(channelUrl, subscribedAlerts, subscribe) {
    var alert = Utils.getChannelName(channelUrl)
    Utils.log('subscribedAlerts : ' + JSON.stringify(subscribedAlerts) + ' alert Name : ' + alert);

    try {
      var result = null
      const seriesURL = Utils.getComicSeriesURL(channelUrl);
      if (!subscribe) {
        var unsubscribeToAlert = ApiService.instance.callFunction('SUBSCRIBE_TO_ALERT')
        const params = {
          data: {
            alert: alert,
            pageUrl: seriesURL,
            isFollow: false
          }
        }
        Utils.log("Calling Unsubscribe alert API " + JSON.stringify(params))
        result = await unsubscribeToAlert(params)
        Utils.log('Unsubscribed alerts API Completed: ' + result)
      } else {
        var subscribeToAlert = ApiService.instance.callFunction('SUBSCRIBE_TO_ALERT')
        const deviceType = Platform.OS == "android" ? "android" : "ios"
        const device = { token: this.token, type: deviceType }
        const params = {
          data: {
            alert: alert,
            pageUrl: seriesURL,
            device,
            isFollow: true
          }
        }
        Utils.log("Calling subscribe alert API " + JSON.stringify(params))
        result = await subscribeToAlert(params)
        Utils.log('Subscribed alerts API Completed: ' + result)
      }

      return result
    } catch (error) {
      if (error.code == 'messaging/permission_error') {
        alert("Go to Settings > Tinyview > Notifications > Allow Notifications")
      }
      Utils.log("updateAlertSubscription Error " + error.code + error)
      throw error
    }
  }

  async addNewTransaction(requestedData) {
    try {
      Utils.log("Calling addNewTransaction API with data " + JSON.stringify(requestedData))
      const addNewTransaction = ApiService.instance.callFunction('ADD_NEW_TRANSACTION')
      const result = await addNewTransaction({ ...requestedData })
      return result
    } catch (error) {
      Utils.log('addNewTransaction: Failed ' + error.code + error)
      throw error
    }
  }

  async validateTransactionOnStore(reqData, purchaseData = null) {
    try {
      let reqPayload = { data: { receiptData: reqData, receipt: purchaseData } }
      const validateTransaction = ApiService.instance.callFunction('VALIDATE_TRANSACTION')
      let result = await validateTransaction({ ...reqPayload })
      return result
    } catch (error) {
      Utils.log('validateTransactionOnStore: Failed ' + error.code + error)
      return [];
    }
  }

  async setReferrer(reqData) {
    try {
      Utils.log("Calling setReferrer API with data " + JSON.stringify(reqData))
      const setReferrer = ApiService.instance.callFunction('SET_REFERRER')
      const result = await setReferrer({ data: reqData })
      if (result && result.status == 200) {
        Utils.log('got referred by API response ' + JSON.stringify(result));
      }
      return result
    } catch (error) {
      Utils.log('Error on setReferrer : ' + error.code + error)
      throw error
    }
  }

  async recordPageView(reqData) {
    try {
      Utils.log("Calling recordPageview API with pageUrl " + JSON.stringify(reqData));

      const call = ApiService.instance.callFunction('RECORD_PAGEVIEW')
      const result = await call({ data: reqData })
      Utils.log('got recordPageview API response ' + JSON.stringify(result));
      return result
    } catch (error) {
      Utils.log('Error on recordPageview : ' + error.code + error)
      throw error
    }
  }

  async getPageInfo(pageUrl) {
    try {
      Utils.log("Calling getPageInfo API with pageUrl " + pageUrl)

      const getPageInfo = ApiService.instance.callFunction('GET_PAGEVIEW_INFO')
      const result = await getPageInfo({
        pageUrl: pageUrl
      })
      Utils.log('getPageInfo API response ' + JSON.stringify(result));
      var data = {
        likeCount: null,
        updatedAt: result.updatedAt,
        viewCount: result.viewCount,
        commentCount: result.commentCount,
        repostCount: result.repostCount,
        storyID: result.storyId,
        pageURL: pageUrl,
        giftsCount: result.giftsCount,
        giftedItems: result.giftedItems,
        isLiked: result.isLiked,
        isRead: result.isRead,
        seriesReadCount: result.seriesReadCount
      }
      if (!Utils.isHomeURL(pageUrl)) {
        data.likeCount = result.likeCount
      }

      return data
    } catch (error) {
      Utils.log('Error on page status API : ' + error.code + error)
      throw error
    }
  }

  async getMultiplePanelInfo(pages) {
    try {
      Utils.log("Calling getMultiplePanelInfo API with pageUrl " + pages)

      const getMultiplePanelInfo = ApiService.instance.callFunction('GET_MULTIPLE_PAGEVIEW_INFO')
      const result = await getMultiplePanelInfo({ data: { pages: pages } })
      Utils.log("getMultiplePageInfo API Response " + JSON.stringify(result))
      return result
    } catch (error) {
      Utils.log('Error on getting multiple page status API : ' + error.code + error)
      throw error
    }
  }

  async getUserDetails(reqData) {
    try {
      Utils.log('Calling getUserDetails API' + reqData);
      const getUserDetails = ApiService.instance.callFunction('GET_USER_DETAILS');
      const userInfo = await getUserDetails({ ...reqData })
      Utils.log('Got userInfo data' + JSON.stringify(userInfo.data));
      return userInfo.data
    } catch (error) {
      throw error
    }
  }

  async updateUserProfile(reqData) {
    try {
      Utils.log('updating user profile');
      const updateProfile = ApiService.instance.callFunction('UPDATE_USER_PROFILE');
      let response = await updateProfile({ data: reqData })
      Utils.log('updated user profile', reqData, "response => ", response);
      return response
    } catch (error) {
      Utils.log('updateUserProfile: Failed ', error, error.message, "reqData", reqData)
      throw error;
    }
  }

  async updateProfilePicURL(reqData) {
    try {
      Utils.log('updating user profile pic');
      const updateProfilePicURL = ApiService.instance.callFunction('UPDATE_PROFILE_PIC_URL')
      let response = await updateProfilePicURL({ data: reqData })
      return response
    } catch (error) {
      Utils.log('updateProfilePicURL: Failed ', error.message, "reqData", reqData)
      throw error;
    }
  }

  async unfriend(reqData) {
    Utils.log('Going to unfriend' + reqData);
    try {
      const unfriend = ApiService.instance.callFunction('UNFRIEND')
      const response = await unfriend({ ...reqData })
      Utils.log('Unfriend response', response);
      return response;
    } catch (error) {
      Utils.log('Error in unfriending user', error);
      throw error
    }
  }

  async getFriendRequest(reqData) {
    Utils.log('Going to get friend request ' + JSON.stringify(reqData));
    try {
      const friendRequest = ApiService.instance.callFunction('GET_FRIEND_REQUEST')
      const response = await friendRequest({ data: { ...reqData, status: 'pending' } })
      Utils.log('friendRequest response', response);
      return response;
    } catch (error) {
      Utils.log('Error in getting friend request', error);
      throw error
    }
  }

  async getFriendList(reqData) {
    Utils.log('Going to get friend ' + JSON.stringify(reqData));
    try {
      const getMyFriend = ApiService.instance.callFunction('GET_MY_FRIENDS')
      const response = await getMyFriend({ userID: Utils.getCurrentUserId(), ...reqData })
      Utils.log('friends response', response);
      return response;
    } catch (error) {
      Utils.log('Error in getting friends', error);
      throw error
    }
  }

  async sendFriendRequest(reqData) {
    try {
      Utils.log('Calling sendFriendRequest API ' + JSON.stringify(reqData));
      const sendRequest = ApiService.instance.callFunction('SEND_FRIEND_REQUEST')
      const response = await sendRequest({ data: reqData })
      Utils.log(`Friend Request sent to ${reqData}`, response);
      return response
    } catch (error) {
      Utils.log('Error in sendFriendRequest Firebase', error);
      throw error
    }
  }

  async updateFriendRequest(reqData) {
    Utils.log('Performing action on friend request' + reqData);
    try {
      const actionOnFriendRequest = ApiService.instance.callFunction('UPDATE_FRIEND_REQUEST')
      const response = await actionOnFriendRequest({ data: reqData })
      Utils.log('action on friends request response', response);
      return response;
    } catch (error) {
      Utils.log('Error in performing action friend request', error);
      throw error
    }
  }

  async sendTextToContacts(reqData) {
    try {
      Utils.log(`Calling sendTextToContacts => Request ${JSON.stringify(reqData)}`);
      const sendTextMessage = ApiService.instance.callFunction('SEND_TEXT_MESSAGE')
      const response = await sendTextMessage({ data: reqData })
      Utils.log(`sendTextToContacts => Invitation sent to ${reqData}`, response);
      return response
    } catch (error) {
      Utils.log('Error in sendTextToContacts Firebase', error);
      throw error
    }
  }

  async fetchAppConfigList() { //Fetching list of gifts and comicConfig
    Utils.log('API Calling - getAppConfig');
    try {
      const fetchAppConfig = ApiService.instance.callFunction('GET_APP_CONFIG')
      const response = await fetchAppConfig({})
      Utils.log('fetched App config', response);
      return response.data;
    } catch (error) {
      Utils.log('Error in fetching app config', ' code: ', error.code, ' details: ', error.details, ' errorMsg: ', error.errorMsg);
      throw error
    }
  }

  async getAllComics(reqData) {
    Utils.log('API Calling - getAllComics ' + JSON.stringify(reqData));
    try {
      const getAllComics = ApiService.instance.callFunction('GET_ALL_COMICS')
      const response = await getAllComics({ data: reqData })
      Utils.log('fetched All Comics', response);
      return response;
    } catch (error) {
      Utils.log('Error in fetching Comics', ' code: ', error.code, ' details: ', error.details, ' errorMsg: ', error.errorMsg);
      throw error
    }
  }

  // Feed API'S

  async getUserFeeds(reqData) {
    Utils.log('API Calling - getUserFeeds ' + JSON.stringify(reqData));
    try {
      const getUserFeeds = ApiService.instance.callFunction('GET_USER_FEED')
      const response = await getUserFeeds({ data: reqData })
      Utils.log('fetched All Feeds', response);
      return response;
    } catch (error) {
      Utils.log('Error in fetching Feed', ' code: ', error.code, ' details: ', error.details, ' errorMsg: ', error.errorMsg);
      throw error
    }
  }

  async getSeriesComics(reqData) {
    Utils.log('API Calling - getSeriesComics ' + JSON.stringify(reqData));
    try {
      const seriesComics = ApiService.instance.callFunction('GET_SERIES_COMICS')
      const response = await seriesComics({ data: reqData })
      Utils.log('API Response - getSeriesComics', response);
      return response;
    } catch (error) {
      Utils.log('Error in getSeriesComics', ' code: ', error.code, ' details: ', error.details, ' errorMsg: ', error.errorMsg);
      throw error
    }
  }

  async getStoryDetails(reqData) {
    Utils.log('API Calling - getStoryDetails ' + JSON.stringify(reqData));
    try {
      const storyDetails = ApiService.instance.callFunction('GET_STORY_DETAILS')
      const response = await storyDetails({ ...reqData })
      Utils.log('API Response - getStoryDetails', response);
      return response.data;
    } catch (error) {
      Utils.log('Error in fetching story details', ' code: ', error.code, ' details: ', error.details, ' errorMsg: ', error.errorMsg);
      throw error
    }
  }

  async fetchFeedLikes(reqData) {  // This API returns all user's list who liked/gifted to the story.
    Utils.log('getting user list who liked the story ' + JSON.stringify(reqData));
    try {
      const fetchFeedLikes = ApiService.instance.callFunction('FETCH_STORY_LIKES')
      const response = await fetchFeedLikes({ ...reqData })
      Utils.log('fetched user list who liked story', response);
      return response.data;
    } catch (error) {
      Utils.log('Error in fetching user list who liked story ', ' code: ', error.code, ' details: ', error.details, ' errorMsg: ', error.errorMsg);
      throw error
    }
  }

  async fetchCommentLikes(reqData) {  // This API returns all user's list who liked the story's comment.
    Utils.log('getting user list who liked a comment ' + JSON.stringify(reqData));
    try {
      const fetchCommentLikes = ApiService.instance.callFunction('FETCH_COMMENT_LIKES')
      const response = await fetchCommentLikes({ ...reqData })
      Utils.log('fetched user list who liked a comment', response);
      return response.data;
    } catch (error) {
      Utils.log('Error in fetching user list who liked a comment ', ' code: ', error.code, ' details: ', error.details, ' errorMsg: ', error.errorMsg);
      throw error
    }
  }

  async fetchRepostedUsersList(reqData) {  // This API returns all user's list who reposted a story.
    Utils.log('getting user list who reposted a story ' + JSON.stringify(reqData));
    try {
      const fetchRepostedUsers = ApiService.instance.callFunction('FETCH_REPOSTED_USERS_LIST')
      const response = await fetchRepostedUsers({ data: reqData })
      Utils.log('fetched user list who reposted a story', response);
      return response.data;
    } catch (error) {
      Utils.log('Error in fetching user list who reposted a story ', ' code: ', error.code, ' details: ', error.details, ' errorMsg: ', error.errorMsg);
      throw error
    }
  }

  async fetchWithWhomStorySharedUL(reqData) {   // This API returns all user's list with whom story shared.
    Utils.log('getting user list with whom story is shared' + JSON.stringify(reqData));
    try {
      const fetchWithWhomStorySharedUL = ApiService.instance.callFunction('FETCH_WITH_WHOM_STORY_SHARED')
      const response = await fetchWithWhomStorySharedUL({ ...reqData })
      Utils.log('fetched user list with whom story is shared', response);
      return response.data.data;
    } catch (error) {
      Utils.log('Error in fetching user list with whom story is shared ', ' code: ', error.code, ' details: ', error.details, ' errorMsg: ', error.errorMsg);
      throw error
    }
  }

  async getFeedLikes(reqData) {  // This API returns is user liked/gifted to the story or not.
    Utils.log('getting story likes ' + JSON.stringify(reqData));
    try {
      const getFeedLikes = ApiService.instance.callFunction('GET_STORY_LIKES')
      const response = await getFeedLikes({ data: reqData })
      Utils.log('fetched story likes', response);
      return response.data;
    } catch (error) {
      Utils.log('Error in getting story likes', ' code: ', error.code, ' details: ', error.details, ' errorMsg: ', error.errorMsg);
      throw error
    }
  }

  async likeFeed(reqData) {
    Utils.log('liking feed' + JSON.stringify(reqData));
    try {
      const trace = await this.startAPITrace(null, cloudFunctions.LIKE_STORY)
      const likeFeed = ApiService.instance.callFunction('LIKE_STORY');
      const response = await likeFeed({ data: reqData })
      trace.putAttribute("Url", cloudFunctions.LIKE_STORY)
      trace.setHttpResponseCode(200)
      this.stopTrace(trace)
      Utils.log('Liked Feed', response);
      return response.data;
    } catch (error) {
      Utils.log('Error in liking Feed', ' code: ', error.code, ' details: ', error.details, ' errorMsg: ', error.errorMsg);
      throw error
    }
  }

  async disLikeFeed(reqData) {
    Utils.log('Dislike feed' + JSON.stringify(reqData));
    try {
      const trace = await this.startAPITrace(null, cloudFunctions.DISLIKE_STORY)
      const disLikeFeed = ApiService.instance.callFunction('DISLIKE_STORY')
      const response = await disLikeFeed({ data: reqData })
      trace.putAttribute("Url", cloudFunctions.DISLIKE_STORY)
      trace.setHttpResponseCode(200)
      this.stopTrace(trace)
      Utils.log('disLiked Feed', response);
      return response.data;
    } catch (error) {
      Utils.log('Error in Dislike Feed', ' code: ', error.code, ' details: ', error.details, ' errorMsg: ', error.errorMsg);
      throw error
    }
  }

  async getFeedComments(reqData) {
    Utils.log('fetching Comments ' + JSON.stringify(reqData));
    try {
      const getFeedComments = ApiService.instance.callFunction('FETCH_COMMENTS')
      const response = await getFeedComments({ ...reqData })
      Utils.log('Getting Feed Comments ', response);
      return response.data;
    } catch (error) {
      Utils.log('Error in Getting Feed Comments ', ' code: ', error.code, ' details: ', error.details, ' errorMsg: ', error.errorMsg);
      throw error
    }
  }

  async addComment(reqData) {
    Utils.log('Commenting on Feed ' + JSON.stringify(reqData));
    try {
      const trace = await this.startAPITrace(null, cloudFunctions.ADD_COMMENT)
      const addComment = ApiService.instance.callFunction('ADD_COMMENT')
      const response = await addComment({ data: reqData })
      trace.putAttribute("Url", cloudFunctions.ADD_COMMENT)
      trace.setHttpResponseCode(200)
      this.stopTrace(trace)
      Utils.log('Commented on Feed', response);
      return response.data;
    } catch (error) {
      Utils.log('Error in addComment method', ' code: ', error.code, ' details: ', error.details, ' errorMsg: ', error.errorMsg);
      throw error
    }
  }

  async likeFeedComment(reqData) {
    Utils.log('Liking User comment ' + JSON.stringify(reqData));
    try {
      const likeFeedComments = ApiService.instance.callFunction('LIKE_COMMENT')
      const response = await likeFeedComments({ data: reqData })
      Utils.log('Liked User comment ' + JSON.stringify(response));
      return response.data;
    } catch (error) {
      Utils.log('Error in liking Feed Comments', ' code: ', error.code, ' details: ', error.details, ' errorMsg: ', error.errorMsg);
      throw error
    }
  }

  async disLikeFeedComment(reqData) {
    Utils.log('Disliking User comment ' + JSON.stringify(reqData));
    try {
      const disLikeFeedComments = ApiService.instance.callFunction('DISLIKE_COMMENT')
      const response = await disLikeFeedComments({ data: reqData })
      Utils.log('disLiked User comment ' + JSON.stringify(response));
      return response.data;
    } catch (error) {
      Utils.log('Error in Disliking Feed Comment', ' code: ', error.code, ' details: ', error.details, ' errorMsg: ', error.errorMsg);
      throw error
    }
  }

  async editFeedComment(reqData) {
    Utils.log('editing Feed comment ' + JSON.stringify(reqData));
    try {
      const editFeedComment = ApiService.instance.callFunction('EDIT_COMMENT')
      const response = await editFeedComment({ data: reqData })
      Utils.log('Edited Feed comment' + JSON.stringify(response));
      return response.data;
    } catch (error) {
      Utils.log('Error in editig Feed Comment', ' code: ', error.code, ' details: ', error.details, ' errorMsg: ', error.errorMsg);
      throw error
    }
  }

  async deleteFeedComment(reqData) {
    Utils.log('deleting Feed comment ' + JSON.stringify(reqData));
    try {
      const deleteFeedComment = ApiService.instance.callFunction('DELETE_COMMENT');
      const response = await deleteFeedComment({ ...reqData })
      Utils.log('deleted Feed comment' + response);
      return response.data;
    } catch (error) {
      Utils.log('Error in deleting Feed Comment', ' code: ', error.code, ' details: ', error.details, ' errorMsg: ', error.errorMsg);
      throw error
    }
  }

  async getUserRepost(reqData) {
    Utils.log('API Calling - getUserRepost ' + JSON.stringify(reqData));
    try {
      const getUserFeeds = ApiService.instance.callFunction('GET_MY_REPOST')
      const response = await getUserFeeds({ data: reqData })
      Utils.log('fetched All User Repost', response);
      return response.data;
    } catch (error) {
      Utils.log('Error in fetching User Repost', ' code: ', error.code, ' details: ', error.details, ' errorMsg: ', error.errorMsg);
      throw error
    }
  }

  async addRepost(reqData) {
    Utils.log('Reposting comic on user profile ' + JSON.stringify(reqData));
    try {
      const addRepost = ApiService.instance.callFunction('ADD_REPOST')
      const response = await addRepost({ data: reqData })
      Utils.log('Reposted comic on user profile', response);
      return response.data;
    } catch (error) {
      Utils.log('Error in Reposting Firebase', ' code: ', error.code, ' details: ', error.details, ' errorMsg: ', error.errorMsg);
      throw error
    }
  }

  async sendPost(reqData) {
    Utils.log('Posting comic on user profile ' + JSON.stringify(reqData));
    try {
      const sendPost = ApiService.instance.callFunction('SEND_POST')
      const response = await sendPost({ data: reqData })
      Utils.log('Posted comic on user profile', response);
      return response.data;
    } catch (error) {
      Utils.log('Error in Posting Firebase', ' code: ', error.code, ' details: ', error.details, ' errorMsg: ', error.errorMsg);
      throw error
    }
  }

  async editRepost(reqData) {
    Utils.log('editing Repost ' + JSON.stringify(reqData));
    try {
      const editRepost = ApiService.instance.callFunction('EDIT_REPOST')
      const response = await editRepost({ data: reqData })
      Utils.log('Edited Repost' + response);
      return response.data;
    } catch (error) {
      Utils.log('Error in editig Repost', ' code: ', error.code, ' details: ', error.details, ' errorMsg: ', error.errorMsg);
      throw error
    }
  }

  async deleteRepost(reqData) {
    Utils.log('deleting Repost ' + JSON.stringify(reqData));
    try {
      const deleteRepost = ApiService.instance.callFunction('DELETE_REPOST')
      const response = await deleteRepost({ ...reqData })
      Utils.log('deleted Repost comment' + response);
      return response.data;
    } catch (error) {
      Utils.log('Error in deleting Repost', ' code: ', error.code, ' details: ', error.details, ' errorMsg: ', error.errorMsg);
      throw error
    }
  }

  async getSeriesCarousel(requestedData) {
    Utils.log('API Calling - getSeriesCarousel ' + JSON.stringify(requestedData));
    try {
      const getSeries = ApiService.instance.callFunction('GET_SERIES_CAROUSEL')
      const response = await getSeries({ ...requestedData })
      Utils.log('fetched series carousel ' + JSON.stringify(response));
      return response;
    } catch (error) {
      Utils.log('Error in get series carousel', ' code: ', error.code, ' details: ', error.details, ' errorMsg: ', error.errorMsg);
      throw error
    }
  }

  async deleteSentPost(reqData) {
    Utils.log('deleting sent post ' + JSON.stringify(reqData));
    try {
      const deleteSentPost = ApiService.instance.callFunction('DELETE_SENT_POST')
      const response = await deleteSentPost({ ...reqData })
      Utils.log('deleted Repost comment' + response);
      return response.data;
    } catch (error) {
      Utils.log('Error in deleting sent post', ' code: ', error.code, ' details: ', error.details, ' errorMsg: ', error.errorMsg);
      throw error
    }
  }

  async flagUserComment(reqData) {
    Utils.log('Going to Flag user comment ' + JSON.stringify(reqData));
    try {
      const flagComment = ApiService.instance.callFunction('FLAG_COMMENT')
      const response = await flagComment({ data: reqData })
      Utils.log('Flag comment API response' + JSON.stringify(response));
      return response;
    } catch (error) {
      Utils.log('Error in Flag comment', ' code: ', error.code, ' details: ', error.details, ' errorMsg: ', error.errorMsg);
      throw error
    }
  }

  async callBlockUserAPI(reqData) {
    Utils.log('Going to call Block User API ' + JSON.stringify(reqData));
    try {
      const flagComment = ApiService.instance.callFunction('BLOCK_USER')
      const response = await flagComment({ data: reqData })
      Utils.log('Block User API response' + JSON.stringify(response))
      return response
    } catch (error) {
      Utils.log('Error in Block User API', ' code: ', error.code, ' details: ', error.details, ' errorMsg: ', error.errorMsg);
      throw error
    }
  }

  async getUserFriendshipStatus(reqData) {
    Utils.log('Going to call get friendship status API' + JSON.stringify(reqData));
    try {
      const getStatus = ApiService.instance.callFunction('GET_FRIENDSHIP_STATUS')
      const response = await getStatus({ ...reqData })
      Utils.log('Get Friendship Status API response ' + JSON.stringify(response))
      return response
    } catch (error) {
      Utils.log('Error in Get Friendship Status API ' + error.code + error)
      throw error
    }
  }

  async getSubscribersProgreesAPI(reqData) {
    Utils.log('Going to call get Subscribers Progress API ' + JSON.stringify(reqData));
    try {
      const getProgress = ApiService.instance.callFunction('GET_SUBSCRIBERS_PROGRESS')
      const response = await getProgress({ ...reqData })
      Utils.log('get Subscribers Progress API response ' + JSON.stringify(response))
      return response
    } catch (error) {
      Utils.log('Error in Subscribers Progress API', ' code: ', error.code, ' details: ', error.details, ' errorMsg: ', error.errorMsg);
      throw error
    }
  }

  async addInfluencePoint(reqData) {
    Utils.log('API calling for adding influence points');
    try {
      const addPoints = ApiService.instance.callFunction('ADD_INFLUENCE_POINT')
      const userInfluence = await addPoints({ data: reqData })
      if (userInfluence != null) {
        Utils.log('Success - API calling for adding influence points' + JSON.stringify(userInfluence));
      }
      return userInfluence
    } catch (error) {
      Utils.log("Error in addInfluencePoint API call " + error.code + error)
      throw error
    }
  }

  async getUserNotification(reqData) {
    Utils.log('API calling for user notifications');
    try {
      const getNotifications = ApiService.instance.callFunction('GET_NOTIFICATIONS')
      const response = await getNotifications({ data: reqData })
      if (response != null) {
        Utils.log('Success - API calling for get user notifications' + JSON.stringify(response));
      }
      return response
    } catch (error) {
      Utils.log("Error in getUserNotification API call " + error.code + error)
      throw error
    }
  }

  async getFeedEpisodes(reqData) {
    Utils.log('API calling for episodes data' + JSON.stringify(reqData));
    try {
      const getEpisodes = ApiService.instance.callFunction('GET_EPISODES')
      const response = await getEpisodes({ data: reqData })
      if (response != null) {
        Utils.log('Success - API calling for getEpisodes' + JSON.stringify(response));
      }
      return response
    } catch (error) {
      Utils.log("Error in getEpisodes API call " + error.code + error)
      throw error
    }
  }

  async getNavigationComics(reqData) {
    Utils.log('API calling for navigation panels data ' + JSON.stringify(reqData));
    try {
      const getNavPanels = ApiService.instance.callFunction('GET_NAVIGATION')
      const response = await getNavPanels({ ...reqData })
      if (response != null) {
        Utils.log('Success - API calling for getNavigationComics' + JSON.stringify(response));
      }
      return response
    } catch (error) {
      Utils.log("Error in getNavigationComics API call " + error.code + error)
      throw error
    }
  }

  async redeemInfluencePoint(reqData) {
    Utils.log("API calling for redeeming influence points");
    try {
      const redeemPoints = ApiService.instance.callFunction('REDEEM_INFLUENCE_POINT')
      const response = await redeemPoints({ data: reqData })
      if (response != null) {
        Utils.log("Success - API calling for redeeming influence points" + JSON.stringify(response));
      }
      return response
    } catch (error) {
      Utils.log("Error in redeemInfluencePoint API call " + error.code + error)
      throw error
    }
  }

  async removeProfileImage() {
    Utils.log("API calling for removing current user profile image");
    try {
      const deleteImage = ApiService.instance.callFunction('REMOVE_PROFILE_IMAGE')
      const response = await deleteImage({ data: {} })
      if (response != null) {
        Utils.log("Success - API calling for removing current user profile image" + JSON.stringify(response));
      }
      return response
    } catch (error) {
      Utils.log("Error in removeProfileImage API call " + error.code + error)
      throw error
    }
  }

  async getUnlockComicURLs() {
    Utils.log("API calling for Getting unlocked premium comics");
    try {
      const purchasedComicsList = ApiService.instance.callFunction('GET_UNLOCK_COMIC_URLS')
      const response = await purchasedComicsList({})
      if (response != null) {
        Utils.log("Success - API calling for Getting unlocked premium comics " + JSON.stringify(response.data));
      }
      return response
    } catch (error) {
      Utils.log("Error in getUnlockComicURLs API call " + error.code + error)
      throw error
    }
  }

  async sharedComicVisited(reqData) {
    Utils.log('API Calling - visit-shared-comic ' + JSON.stringify(reqData));
    try {
      const comicVisited = ApiService.instance.callFunction('VISITED_SHARED_COMIC')
      const response = await comicVisited({ ...reqData })
      Utils.log('API Response - visit-shared-comic', response);
      return response
    } catch (error) {
      Utils.log('Error in visit-shared-comic API call', error.code + error);
      throw error
    }
  }

  async hasComicReadAccess(reqData) {
    Utils.log("API calling for has comic read access" + JSON.stringify(reqData));
    try {
      const hasComicAccess = ApiService.instance.callFunction('HAS_COMIC_READ_ACCESS')
      const response = await hasComicAccess(reqData)
      if (response != null) {
        Utils.log("Success - API calling for has comic read access " + JSON.stringify(response));
      }
      return response
    } catch (error) {
      Utils.log("Error in hasComicReadAccess API call " + error.code + error)
      throw error
    }
  }

  async callAPITogetPortalLinkForStripe() {
    Utils.log('Going to call Restore Purchase for Stripe Purchase');
    try {
      const getPortalLink = ApiService.instance.callFunction('PORTAL_LINK')
      const data = await getPortalLink({})
      if (data != null) {
        Utils.log('Calling callAPITogetPortalLinkForStripe API ' + JSON.stringify(data))
      }
      return data;
    } catch (error) {
      Utils.log("Error in callAPITogetPortalLinkForStripe " + error.code + error)
      throw (error)
    }
  }

  async sendNotificationsLastSeen(reqData) {
    Utils.log("Going to call API for sending Notifications last seen " + JSON.stringify(reqData));
    try {
      const notifLastSeen = ApiService.instance.callFunction('UPDATE_LAST_SEEN_NOTIFICATION_AT')
      const response = await notifLastSeen({ data: reqData })
      if (response != null) {
        Utils.log("Success - API calling for sending Notifications last seen " + JSON.stringify(response))
      }
      return response
    } catch (error) {
      Utils.log("Error in sending Notifications last seen" + error.code + error)
    }
  }

  async storeUserEmailData(reqData) {
    Utils.log("Going to call API for adding email flow data " + JSON.stringify(reqData))
    try {
      const storeEmailData = ApiService.instance.callFunction('ADD_EMAIL_FLOW_DATA')
      const response = await storeEmailData({ data: reqData })
      if (response != null) {
        Utils.log("Success - API calling for adding email flow data " + JSON.stringify(response))
      }
      return response
    } catch (error) {
      Utils.log("Error in calling addEmailFlowData API " + error)
      throw error
    }
  }

  async getUserEmailData(reqData) {
    Utils.log("Going to call API for getting email flow data " + JSON.stringify(reqData))
    try {
      const getEmailData = ApiService.instance.callFunction('GET_EMAIL_FLOW_DATA')
      const response = await getEmailData(reqData)
      if (response != null) {
        Utils.log("Success - API calling to get email flow data " + JSON.stringify(response))
      }
      return response
    } catch (error) {
      Utils.log("Error in calling for getting email flow data " + error)
      throw error
    }
  }

  async setNotificationAlerts(reqData) {
    Utils.log("Going to call API for setting notification alerts " + JSON.stringify(reqData))
    try {
      const setNotifAlert = ApiService.instance.callFunction('SET_NOTIFICATION_SETTINGS')
      const response = await setNotifAlert({ data: reqData })
      if (response != null) {
        Utils.log("Success - API calling to set notification alerts " + JSON.stringify(response))
      }
      return response
    } catch (error) {
      Utils.log("Error in calling for setting notification alerts " + error)
      throw error
    }
  }

  async getUserRecentComments(reqData) {
    Utils.log("Going to call API to get user recent comments " + JSON.stringify(reqData))
    try {
      const getRecentComments = ApiService.instance.callFunction('USER_RECENT_COMMENTS')
      const response = await getRecentComments({ ...reqData })
      if (response != null) {
        Utils.log("Success - API calling get user recent comments " + JSON.stringify(response))
      }
      return response
    } catch (error) {
      Utils.log("Error in calling getUserRecentComments API " + JSON.stringify(error))
      throw error
    }
  }

  async getUserGiftsSent(reqData) {
    Utils.log("Going to call API to get user gifts sent " + JSON.stringify(reqData))
    try {
      const getGiftsSent = ApiService.instance.callFunction('USER_GIFTS_SENT')
      const response = await getGiftsSent({ ...reqData })
      if (response != null) {
        Utils.log("Success - API calling get user gifts sent " + JSON.stringify(response))
      }
      return response
    } catch (error) {
      Utils.log("Error in calling getUserGiftsSent API " + JSON.stringify(error))
      throw error
    }
  }

  recordError(code, error, logMessage = null) {
    if (logMessage) { // for recording extra log message
      getApp().crashlytics().log(logMessage)
    }
    if (code) { // for recording extra log message
      getApp().crashlytics().log(code.toString())
    }
    const currentUser = getAuth().currentUser;
    if (currentUser) {
      getApp().crashlytics().setUserId(currentUser.uid)
    }

    getApp().crashlytics().recordError(error)
  }

  logEvent(event, params, screenName = null) {
    const currentUser = getAuth().currentUser;
    if (currentUser) {
      getApp().analytics().setUserId(currentUser.uid)
    }

    getApp().analytics().logEvent(event, params);
  }

  async startTrace(screenName = null, url = null) {
    try {
      if (!screenName && url) {
        if (Utils.isHomeURL(url)) {
          screenName = "home_page"
        } else if (Utils.isComicURL(url)) {
          if (Utils.isDirectoryPageURL(url)) {
            screenName = "directory_page"
          } else {
            screenName = "comic_page"
          }
        } else {
          screenName = "series_page"
        }
      }

      const trace = getApp().perf().newTrace(screenName);
      await trace.start()
      if (url) {
        const endPath = Utils.getMeaningFullURL(url)
        trace.putAttribute("Url", endPath)
      }
      return Promise.resolve(trace)
    } catch (error) {
      Utils.log("Error in starting tracing on Firebase " + error)
    }
  }

  async stopTrace(trace) {
    try {
      if (!trace) {
        return
      }
      await trace.stop()
    } catch (error) {
      Utils.log("Error in stopping tracing on Firebase " + error)
    }
  }

  async startAPITrace(url, endPoint, method = "GET") {
    try {
      if (!url && endPoint) {
        url = settings.cloudFunctionBaseURL + "/" + endPoint
      }
      const trace = getApp().perf().newHttpMetric(url, method);
      await trace.start()
      return trace
    } catch (error) {
      Utils.log("Error in starting network tracing on Firebase " + error)
    }
  }

}

FirebaseManager.propTypes = {
  instance: PropTypes.instanceOf(FirebaseManager),
  subscribeToChannel: PropTypes.func
}