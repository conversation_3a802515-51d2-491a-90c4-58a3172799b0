import * as RNIap from 'react-native-iap'
import { Platform } from 'react-native'
import FirebaseManager from './FirebaseManager'
import { settings } from './settings'
import { finishedPurchase, purchaseFailed, storeCancelledPurchase } from '../redux/actions/actions'
import { SharedPreferences } from './SharedPreferences';
import { Utils } from './Utils'
import SessionManager from './SessionManager'
import { ReplacementModesAndroid } from 'react-native-iap'
import UserSession from './UserSession'
import moment from 'moment'
import { AppEventsLogger } from 'react-native-fbsdk-next'

const IAP_ERROR_CODE = -101;

export default class IAPManager {
  static default = new IAPManager()

  constructor() {

    this.initializeIAP.bind(this)
    this.getProducts.bind(this)
    this.addListeners.bind(this)
    this.requestPurchase.bind(this)
    this.validateTransaction.bind(this)
    this.storeTransaction.bind(this)
    this.getAvailablePurchases.bind(this)
    this.getGiftsAndConsumed.bind(this)

    this.initializeIAP()

    this.purchaseListeners = {
      updateListner: () => { },
      failureListner: () => { }
    }

    this.extraPurchaseData = null

    // RNIap.consumeAllItemsAndroid().then(result => Utils.log(result)).catch(error => Utils.log(error))
  }

  async updateSendboxMode() {
    const value = await SharedPreferences.getData(settings.IAP_PURCHASE_MODE_KEY)
    this.iapProductionMode = value == 'true';
    return this.iapProductionMode
  }

  getSendboxMode() {
    if (this.iapProductionMode == null || this.iapProductionMode == undefined) {
      this.iapProductionMode = settings.isProduction;
    }
    return this.iapProductionMode;
  }

  async initializeIAP() {
    try {
      const connectionEstablished = await RNIap.initConnection()
      if (connectionEstablished) {
        Utils.log('Connection established for In-App purchase.')

        this.purchaseListeners = this.addListeners()
      } else {
        FirebaseManager.instance.recordError(IAP_ERROR_CODE, new Error("There is problem while establishing the connect with playstore/appstore"));
        Utils.log('There is problem while establishing the connect with playstore/appstore')
      }
    } catch (error) {
      FirebaseManager.instance.recordError(IAP_ERROR_CODE, error);
      Utils.log(error.status + error)
    }
  }

  addListeners() {
    Utils.log('Purchases Listners added')
    var updateListner = RNIap.purchaseUpdatedListener(async (purchase) => {
      try {

        Utils.log('Purchases Updated listener called for >> ' + purchase.productId)

        const result = await this.storeTransaction(purchase)

        if (result && result.productId) {
          if (Utils.isSubsProduct(result.productId)) {
            SessionManager.instance.updatePurchasedData(purchase)
          }
          await SharedPreferences.storePurchasesProducts(result.productId);

          finishedPurchase(result, this.extraPurchaseData)
        } else {
          const isPurchaseConsumable = Utils.isGiftProduct(purchase.productId)
          const purchaseFinished = await RNIap.finishTransaction({ purchase: purchase, isConsumable: isPurchaseConsumable })
          Utils.log('Purchase Finished iOS');
          storeCancelledPurchase()
        }
        FirebaseManager.instance.logEvent('TINYVIEW_PURCHASE', { 'productID': purchase.productId, 'transactionID': purchase.transactionId });

      } catch (error) {
        purchaseFailed(error)
      }
    })
    var failureListner = RNIap.purchaseErrorListener(error => {
      Utils.log('Purchase Error Listener: \n' + error)
      FirebaseManager.instance.recordError(IAP_ERROR_CODE, new Error(error.message));

      if (error.code == "E_ALREADY_OWNED") {
        return;
      }

      purchaseFailed(error)
    })

    return {
      updateListner,
      failureListner
    }
  }

  async validateTransaction(transactionReceiptData, purchaseData = null) {
    let isiOSRec = Platform.OS == 'ios'
    if (purchaseData) {
      isiOSRec = purchaseData.store.toLowerCase() == "apple";
    }

    let reqData = null
    try {
      reqData = isiOSRec ? transactionReceiptData.transactionReceipt : (transactionReceiptData.transactionReceipt) ? JSON.parse(transactionReceiptData.transactionReceipt) : JSON.parse(transactionReceiptData.dataAndroid);
    } catch (error) {
      reqData = transactionReceiptData.transactionReceipt ? transactionReceiptData.transactionReceipt : transactionReceiptData.dataAndroid
    }

    const validateTrn = await FirebaseManager.instance.validateTransactionOnStore(reqData, purchaseData)
    const validTransactions = (validateTrn && validateTrn.result) ? JSON.parse(validateTrn.result) : [];
    let foundProduct = false;
    if (validTransactions && validTransactions.length > 0) {
      for (const key in validTransactions) {
        const element = validTransactions[key];
        if (element.productId == transactionReceiptData.productId || element.productId == transactionReceiptData.productID) {
          foundProduct = true;
          break;
        }
      }
    }

    return foundProduct ? validTransactions : [];
  }

  async storeTransaction(purchase, restoredPurchase = false) {
    try {
      let validTransactions = await this.validateTransaction(purchase);

      if (validTransactions && validTransactions.length > 0) {
        // const productInfos = await this.getProducts([purchase.productId]);
        // const productInfo = productInfos[0]

        const reqData = { data: { transaction: purchase, platform: Platform.OS, isRestorePurchase: restoredPurchase ? 1 : 0 } }
        const transactionLogInfo = await FirebaseManager.instance.addNewTransaction(reqData)
        Utils.log('Purchase added on our backend: ' + transactionLogInfo)
        const isPurchaseConsumable = Utils.isGiftProduct(purchase.productId)
        if (transactionLogInfo.status == 1) {
          const purchaseFinished = await RNIap.finishTransaction({ purchase: purchase, isConsumable: isPurchaseConsumable })
          Utils.log('Purchase Finished ' + purchaseFinished);

          // Facebook SDK Events for tracking
          try {
            if (isPurchaseConsumable) {
              let purchaseAmount = SessionManager.instance.getGiftItemCost(purchase.productId)
              if (purchaseAmount != 0) {
                AppEventsLogger.logPurchase(purchaseAmount, "USD", { [AppEventsLogger.AppEventParams.ContentID]: purchase.productId })
              }
            } else if (Utils.isSubsProduct(purchase.productId)) {
              let purchaseAmount = SessionManager.instance.getSubscriptionItemsCost(purchase.productId)
              if (purchaseAmount != 0) {
                AppEventsLogger.logEvent(AppEventsLogger.AppEvents.Subscribe, purchaseAmount, {
                  [AppEventsLogger.AppEventParams.ContentID]: purchase.productId,
                  [AppEventsLogger.AppEventParams.Currency]: "USD"
                });
              }
            }
          } catch (error) {
            Utils.log("Facebook SDK Failed")
          }
          return { productId: purchase.productId }
        } else {
          Utils.log('Purchase failed to Added on firestore')
          await RNIap.finishTransaction({ purchase: purchase, isConsumable: isPurchaseConsumable })
          FirebaseManager.instance.recordError(IAP_ERROR_CODE, new Error("Couldn't able to store the purchase"));
          throw "Couldn't sync purchases"
        }
      } else {
        Utils.log('Purchase is not valid anymore')
        try {
          if (purchase) {
            const isPurchaseConsumable = Utils.isGiftProduct(purchase.productId)
            await RNIap.finishTransaction({ purchase: purchase, isConsumable: isPurchaseConsumable })
            Utils.log('Transaction not valid but finished it');
          }
        } catch (error) {
          return {}
        }
        return {}
      }

    } catch (error) {
      throw error
    }
  }

  async requestPurchase(product, alreadyPurchasedSubsID = null, extraData = null) {
    try {
      var result;
      const productId = product.productId.toLowerCase();

      if (extraData) {
        IAPManager.default.extraPurchaseData = extraData
      }

      var userUID = ""
      if (UserSession.instance.currentUser()) {
        userUID = UserSession.instance.currentUser().uid
      }

      if (productId.includes('monthly') || productId.includes('yearly') || productId.includes('subscription')) { // monthly, yearly, subscription
        let oldPurchaseToken = null
        let androidPurchaseMode = ReplacementModesAndroid.WITH_TIME_PRORATION
        if (alreadyPurchasedSubsID) {
          const lastPurchase = SessionManager.instance.getLastPurchasedData()
          if (lastPurchase && lastPurchase.transactionReceipt) {
            if (lastPurchase.transactionReceipt.purchaseToken) {
              oldPurchaseToken = lastPurchase.transactionReceipt.purchaseToken
            } else {
              oldPurchaseToken = lastPurchase.transactionReceipt
            }
          } else if (lastPurchase) {
            oldPurchaseToken = lastPurchase.purchaseToken
          }
        }
        result = await RNIap.requestSubscription({ "sku": product.productId, "obfuscatedAccountIdAndroid": userUID, "appAccountToken": userUID, "andDangerouslyFinishTransactionAutomaticallyIOS": false, "purchaseTokenAndroid": oldPurchaseToken, "replacementModeAndroid": androidPurchaseMode, "subscriptionOffers": [{ "sku": product.productId, "offerToken": product.offerToken }] })
      } else {
        result = await RNIap.requestPurchase({ "sku": product.productId, "skus": [product.productId], "obfuscatedAccountIdAndroid": userUID, "appAccountToken": userUID, "andDangerouslyFinishTransactionAutomaticallyIOS": false })
      }
      Utils.log('Requested Purchase: ' + result)
      return result
    } catch (error) {
      FirebaseManager.instance.recordError(IAP_ERROR_CODE, error);

      if (Utils.isGiftProduct(product.productId) && error.code == "E_ALREADY_OWNED") {
        const isSuccess = await this.getGiftsAndConsumed(product.productId)
        if (isSuccess) {
          this.requestPurchase(product, alreadyPurchasedSubsID, extraData)
        } else {
          purchaseFailed(error)
        }
      } else {
        throw error
      }
    }
  }

  async getGiftsAndConsumed(productId) {
    try {
      const availablePurchases = await RNIap.getAvailablePurchases()
      const giftsNeedsToCheck = []
      availablePurchases.forEach(element => {
        if (Utils.isGiftProduct(element.productId) && element.productId == productId) {
          giftsNeedsToCheck.push(element)
        }
      });

      Utils.log('Total Gifts needs to check ' + giftsNeedsToCheck)

      for (const i in giftsNeedsToCheck) {
        const purchase = giftsNeedsToCheck[i];
        const isTxnFinished = await RNIap.finishTransaction({ purchase: purchase, isConsumable: true })
        Utils.log('Is Transaction Finished ' + isTxnFinished)
      }

      return true
    } catch (error) {
      FirebaseManager.instance.recordError(IAP_ERROR_CODE, error);
      return false
    }
  }

  async getAvailablePurchases() {
    try {
      const availablePurchases = await RNIap.getAvailablePurchases()
      Utils.log('Store All Time Total Purchases ' + availablePurchases)
      const purchasesNeedsToCheck = []
      availablePurchases.forEach(element => {
        const txnMomentDate = moment(Number(element.transactionDate))
        const currentDateMoment = moment()
        const olderDays = Math.ceil(moment.duration(currentDateMoment.diff(txnMomentDate)).asDays())
        Utils.log('Transaction old days ' + olderDays)

        if (olderDays <= 35) { // #Warning: Only considering a month old txn. Need to change this if we have yearly subscription
          purchasesNeedsToCheck.push(element)
        }
      });

      Utils.log('Total Purchases needs to check ' + purchasesNeedsToCheck)

      var response = []
      for (const i in purchasesNeedsToCheck) {
        const purchase = purchasesNeedsToCheck[i];

        if (Utils.isSubsProduct(purchase.productId)) {
          Utils.log('Calling Validating purchase for productID : ' + purchase.productId)
          const result = await this.storeTransaction(purchase, true)
          if (result && result.productId) {
            response.push(result.productId)
            SessionManager.instance.updatePurchasedData(purchase)
            await SharedPreferences.storePurchasesProducts(result.productId);
            break
          }
        }
      }

      Utils.log('Valid Purchases ' + JSON.stringify(response))
      return response
    } catch (error) {
      FirebaseManager.instance.recordError(IAP_ERROR_CODE, error);
      throw error
    }
  }

  async IsSubscriptionPurchaseValid() {
    var isValid = false
    const availablePurchases = await RNIap.getAvailablePurchases()
    const needToCheckPurchases = []
    for (const iterator of availablePurchases) {
      if (Utils.isSubsProduct(iterator.productId)) {
        needToCheckPurchases.push(iterator)
      }
    }

    for (const i in needToCheckPurchases) {
      const purchase = needToCheckPurchases[i];

      Utils.log('Calling Validating purchase for productID : ' + purchase.productId)
      isValid = await this.validateTransaction(purchase)
      if (isValid) {
        break
      }
    }
    return isValid
  }

  async getProducts(items) {
    try {
      const inapp = await RNIap.getProducts({ skus: items })
      const subs = await RNIap.getSubscriptions({ skus: items })
      var productsList = []
      var addedItems = []
      inapp.concat(subs).forEach(value => {
        for (const iterator of items) {
          if (iterator == value.productId && !addedItems.includes(value.productId)) {
            if (Platform.OS == "android") { // After Android Billing v5, we need to make below changes
              if (Utils.isSubsProduct(value.productId)) {
                if (value["subscriptionOfferDetails"] != null && value["subscriptionOfferDetails"].length > 0) {
                  value.offerToken = value["subscriptionOfferDetails"][0].offerToken
                  value.localizedPrice = value["subscriptionOfferDetails"][0].pricingPhases.pricingPhaseList[0].formattedPrice
                  value.billingPeriod = value["subscriptionOfferDetails"][0].pricingPhases.pricingPhaseList[0].billingPeriod
                  value.currency = value["subscriptionOfferDetails"][0].pricingPhases.pricingPhaseList[0].priceCurrencyCode
                  value.price = value["subscriptionOfferDetails"][0].pricingPhases.pricingPhaseList[0].priceAmountMicros / 1000000 // It's in micro-units, where 1,000,000 micro-units equal one unit of the currency.
                }
              } else {
                if (value["oneTimePurchaseOfferDetails"] != null) {
                  value.localizedPrice = value["oneTimePurchaseOfferDetails"].formattedPrice
                  value.currency = value["oneTimePurchaseOfferDetails"].priceCurrencyCode
                  value.price = value["oneTimePurchaseOfferDetails"].priceAmountMicros // It's in micro-units, where 1,000,000 micro-units equal one unit of the currency.
                }
              }
            }
            productsList.push(value)
            addedItems.push(value.productId)
          }
        }
        // if (items.includes(value.productId) && !addedItems.includes(value.productId)) {

        // }
      })

      return productsList
    } catch (error) {
      FirebaseManager.instance.recordError(IAP_ERROR_CODE, error);
      throw error
    }
  }
}