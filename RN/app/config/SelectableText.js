import React from 'react';
import { Text, TextInput, Platform } from 'react-native';

const SelectableText = (props) => {

  const { textValue, textStyle, multiline, needsSelectable } = props;

  const isSelectable = (needsSelectable != null && needsSelectable != undefined) ? needsSelectable : true

  return (
    (Platform.OS === "ios" && isSelectable)
      ? <TextInput scrollEnabled={false} style={textStyle} value={textValue} editable={false} multiline={multiline} />  // iOS requires a textinput for word selections
      : <Text style={textStyle} selectable={true} selectionColor='orange'>{textValue}</Text>   // Android can do word selections just with <Text>

  );
};

export default SelectableText;