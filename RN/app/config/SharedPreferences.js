import AsyncStorage from '@react-native-async-storage/async-storage';
import { settings } from './settings';
import SessionManager from './SessionManager';
import { Utils } from './Utils';


export class SharedPreferences {

  static getData = async (key) => {
    try {
      const value = await AsyncStorage.getItem(key);
      if (value !== null) {
        Utils.log('Get data from saved preferences ' + value)
      }
      return value
    } catch (error) {
      return null
    }
  }

  static saveData = async (key, value) => {
    try {
      return await AsyncStorage.setItem(key, value);
    } catch (error) {
      return null
    }
  };

  static updateAppLaunch = async () => {
    let launchCount = await SharedPreferences.getData(settings.APP_LAUNCH_COUNT_KEY);

    launchCount = (launchCount == null || launchCount == undefined) ? 1 : parseInt(launchCount) + 1

    await SharedPreferences.saveData(settings.APP_LAUNCH_COUNT_KEY, launchCount.toString());
    SessionManager.instance.updateAppLaunchCount(parseInt(launchCount));
  }

  // static addComicLike = async (value) => {
  //   try {
  //     if (typeof (value) == "string") {
  //       let likes = SessionManager.instance.getLikeComics();
  //       if (!likes.includes(value)) {
  //         likes.push(value)
  //       }

  //       return await SharedPreferences.saveData(settings.USER_LIKE_COMIC_KEY, JSON.stringify(likes));
  //     } else {
  //       return await SharedPreferences.saveData(settings.USER_LIKE_COMIC_KEY, JSON.stringify(value));
  //     }
  //   } catch (error) {
  //     return null
  //   }
  // };

  // static removeComicLike = async (value) => {
  //   try {

  //     let likes = SessionManager.instance.getLikeComics();
  //     const indexToRemove = likes.indexOf(value)
  //     likes.splice(indexToRemove, 1);

  //     return await SharedPreferences.saveData(settings.USER_LIKE_COMIC_KEY, JSON.stringify(likes));
  //   } catch (error) {
  //     return null
  //   }
  // };

  // static getUserLikeComicURLs = async () => {
  //   try {
  //     let likedComics = null
  //     const likeURLs = await SharedPreferences.getData(settings.USER_LIKE_COMIC_KEY);
  //     if (likeURLs == null || likeURLs == undefined) {
  //       likedComics = []
  //     } else {
  //       likedComics = JSON.parse(likeURLs);
  //     }

  //     SessionManager.instance.updateLikeComics(likedComics);
  //     return likedComics

  //   } catch (error) {
  //     return null
  //   }
  // };

  // static updateShowFollowedComics = async (isFollowed) => {
  //   try {
  //     return await SharedPreferences.saveData(settings.IS_SHOW_FOLLOWED_COMIC, isFollowed.toString());
  //   } catch (error) {
  //     return null
  //   }
  // };

  // static getIsShowFollowedComics = async () => {
  //   try {
  //     const showFollowedComics = await SharedPreferences.getData(settings.IS_SHOW_FOLLOWED_COMIC);
  //     if (showFollowedComics == null || showFollowedComics == undefined) {
  //       return true;
  //     } else {
  //       return showFollowedComics.toLowerCase() == "true";
  //     }
  //   } catch (error) {
  //     return false
  //   }
  // };


  static storePurchasesProducts = async (value) => {
    try {
      if (!Array.isArray(value)) {
        let addedProducts = await SharedPreferences.getStorePurchasesProducts();
        let alreadyAdded = false;        
        
        for (const key in addedProducts) {
          const purchase = addedProducts[key];
          if (purchase == value) {
            alreadyAdded = true
          }
        }

        if (!alreadyAdded) {
          addedProducts.push(value)
        }

        SessionManager.instance.updatePurchaseItems(addedProducts);
        return await SharedPreferences.saveData(settings.APP_STORE_PURCHASES_ID_KEY, JSON.stringify(addedProducts));
      } else {
        SessionManager.instance.updatePurchaseItems(value);
        return await SharedPreferences.saveData(settings.APP_STORE_PURCHASES_ID_KEY, JSON.stringify(value));
      }
    } catch (error) {
      return null
    }
  };

  static getStorePurchasesProducts = async () => {
    try {
      const purchases = await SharedPreferences.getData(settings.APP_STORE_PURCHASES_ID_KEY);
      if (purchases == null || purchases == undefined) {
        return [];
      } else {
        return JSON.parse(purchases);
      }
    } catch (error) {
      return null
    }
  };

  static clearSharedPrefrences = async (excludedKeys) => {
    Utils.log("Keys to kept", excludedKeys)
    try {
      if (excludedKeys.length > 0) {
        let allKeys = await AsyncStorage.getAllKeys()
        for (const key of allKeys) {
          if (excludedKeys.includes(key)) {
            let parseKeyIndex = allKeys.indexOf(key);
            allKeys.splice(parseKeyIndex, 1)
          }
        }
        await AsyncStorage.multiRemove(allKeys)
        return
      }

      await AsyncStorage.clear();
    } catch (error) {
      throw error
    }
  }

  static setShowAllComicsValue = async (value) => {
    try {
      SessionManager.instance.updateShowAllComics(value)
      return await SharedPreferences.saveData(settings.SHOW_ALL_COMICS_KEY, value.toString());
    } catch (error) {
      return null
    }
  };

  static getShowAllComicsValue = async () => {
    let showAllComics = await SharedPreferences.getData(settings.SHOW_ALL_COMICS_KEY);
    if (showAllComics == null || showAllComics == undefined) {
      showAllComics = "true";
      SharedPreferences.saveData(settings.SHOW_ALL_COMICS_KEY, showAllComics);
      // FirebaseManager.instance.updateUserSettings(true)
    }

    SessionManager.instance.updateShowAllComics(showAllComics === "true")
    return showAllComics === "true"
  }  

}