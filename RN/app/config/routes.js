import { createStackNavigator } from '@react-navigation/stack';
import { createDrawerNavigator } from '@react-navigation/drawer';
import MyInfluenceComponent from '../components/screens/MyInfluenceComponent';
import DrawerMenu from '../components/DrawerMenu';
import SplashScreen from '../components/screens/SplashScreen';
import { settings } from './settings';
import LoginScreen from '../components/profileScreens/LoginScreen';
import OTPScreen from '../components/profileScreens/OTPScreen';
import EditProfileScreen from '../components/profileScreens/EditProfileScreen'
import FriendsComponent from '../components/friendsPackage/FriendsComponent';
import UserListScreen from '../components/feed/UserListScreen';
import PostCommentsScreen from '../components/feed/comments/PostCommentsScreen';
import DOBScreen from '../components/profileScreens/DOBScreen';
import UserProfileScreen from '../components/profileScreens/UserProfileScreen';
import EditSocialLinks from '../components/profileScreens/EditSocialLinks';
import ConfirmationScreen from '../components/profileScreens/ConfirmationScreen';
import WhySignUpScreen from '../components/profileScreens/WhySignUpScreen'
import RepostScreen from '../components/feed/repost/RepostScreen';
import Reader from '../components/screens/Reader';
import SendPostScreen from '../components/feed/repost/SendPostScreen';
import StoryReactionsListScreen from '../components/feed/StoryReactionsListScreen';
import AccountDeletedScreen from '../components/screens/AccountDeletedScreen';
import NotificationsScreen from '../components/screens/NotificationsScreen';
import SigningHeaderBar from '../components/friendsPackage/shared/SigningHeaderBar';
import StoryHeaderBar from '../components/friendsPackage/shared/StoryHeaderBar';
import ManageAlertsScreen from '../components/screens/ManageAlertsScreen';
import EmailVerifyScreen from '../components/profileScreens/EmailVerifyScreen';

const HomeStack = createStackNavigator();

function HomeNavigator() {
  return (
    <HomeStack.Navigator
      initialRouteName='Home'
      screenOptions={{ gestureEnabled: false, headerLeft: null, headerShown: false }}
    >
      <HomeStack.Screen
        name='Home'
        component={Reader}
      />
      <HomeStack.Screen
        name='PostCommentsScreen'
        component={PostCommentsScreen}
      />
      <HomeStack.Screen
        name='UserProfileScreen'
        component={UserProfileScreen}
      />
      <HomeStack.Screen
        name='UserListScreen'
        component={UserListScreen}
        options={{ headerShown: true, header: (props) => <StoryHeaderBar {...props} /> }}
      />
      <HomeStack.Screen
        name='StoryReactionsListScreen'
        component={StoryReactionsListScreen}
        options={{ headerShown: true, header: (props) => <StoryHeaderBar {...props} /> }}
      />
      <HomeStack.Screen
        name='MyInfluenceComponent'
        component={MyInfluenceComponent}
      />
      <HomeStack.Screen
        name='NotificationsScreen'
        component={NotificationsScreen}
      />
      <HomeStack.Screen
        name='ManageAlertsScreen'
        component={ManageAlertsScreen}
        options={{ headerShown: true, header: (props) => <SigningHeaderBar {...props} /> }}
      />
      <HomeStack.Screen
        name='FriendsComponent'
        component={FriendsComponent}
        options={{ headerShown: false }}
      />
    </HomeStack.Navigator>
  )
}


// const MainNavigator = createStackNavigator({
//   Home: { screen: Reader },
//   PostCommentsScreen: { screen: PostCommentsScreen },
//   UserProfileScreen: { screen: UserProfileScreen },
//   UserListScreen: { screen: UserListScreen },
//   StoryReactionsListScreen: { screen: StoryReactionsListScreen },
//   MyInfluenceComponent: { screen: MyInfluenceComponent },
//   NotificationsScreen: { screen: NotificationsScreen }
// }, {
//   defaultNavigationOptions: {
//     gesturesEnabled: false,
//     headerLeft: null,
//     headerShown: false
//   },
//   mode: 'card',
//   transitionConfig: () => (Platform.OS == 'ios' ? StackViewTransitionConfigs.SlideFromRightIOS : {
//     screenInterpolator: sceneProps => {
//       const { layout, position, scene } = sceneProps;
//       const { index } = scene;

//       const translateX = position.interpolate({
//         inputRange: [index - 1, index, index + 1],
//         outputRange: [layout.initWidth, 0, 0]
//       });

//       const opacity = position.interpolate({
//         inputRange: [
//           index - 1,
//           index - 0.99,
//           index,
//           index + 0.99,
//           index + 1
//         ],
//         outputRange: [0, 1, 1, 0.3, 0]
//       });

//       return { opacity, transform: [{ translateX }] };
//     },
//   }),
// });

const SingInStack = createStackNavigator();

function SingInNavigator() {
  return (
    <SingInStack.Navigator>
      <SingInStack.Screen
        name='LoginScreen'
        component={LoginScreen}
        options={LoginScreen.navigationOptions}
      />
      <SingInStack.Screen
        name='OtpScreen'
        component={OTPScreen}
        options={OTPScreen.navigationOptions}
      />
      <SingInStack.Screen
        name='EmailVerifyScreen'
        component={EmailVerifyScreen}
        options={EmailVerifyScreen.navigationOptions}
      />
      <SingInStack.Screen
        name='EditProfileScreen'
        component={EditProfileScreen}
        options={EditProfileScreen.navigationOptions}
      />
      <SingInStack.Screen
        name='DOBScreen'
        component={DOBScreen}
        options={DOBScreen.navigationOptions}
      />
      <SingInStack.Screen
        name='EditSocialLinks'
        component={EditSocialLinks}
        options={EditSocialLinks.navigationOptions}
      />
      <SingInStack.Screen
        name='ConfirmationScreen'
        component={ConfirmationScreen}
        options={ConfirmationScreen.navigationOptions}
      />
      <SingInStack.Screen
        name='WhySignUpScreen'
        component={WhySignUpScreen}
        options={WhySignUpScreen.navigationOptions}
      />
    </SingInStack.Navigator>
  )
}

// const SingInNavigator = createStackNavigator({
//   LoginScreen: { screen: LoginScreen },
//   OtpScreen: { screen: OTPScreen },
//   FriendsComponent: { screen: FriendsComponent },
//   EditProfileScreen: { screen: EditProfileScreen },
//   DOBScreen: { screen: DOBScreen },
//   EditSocialLinks: { screen: EditSocialLinks },
//   ConfirmationScreen: { screen: ConfirmationScreen },
//   WhySignUpScreen: { screen: WhySignUpScreen },
// });

const FeedStack = createStackNavigator();

function FeedNavigator() {
  return (
    <FeedStack.Navigator
    >
      <FeedStack.Screen
        name='SendPostScreen'
        component={SendPostScreen}
        options={SendPostScreen.navigationOptions}
      />
      <FeedStack.Screen
        name='RepostScreen'
        component={RepostScreen}
        options={RepostScreen.navigationOptions}
      />
    </FeedStack.Navigator>
  )
}

// const FeedNavigator = createStackNavigator({
//   SendPostScreen: { screen: SendPostScreen },
//   RepostScreen: { screen: RepostScreen }
// });

const DeletedAccountStack = createStackNavigator();

function DeletedAccountNavigator() {
  return (
    <DeletedAccountStack.Navigator
      screenOptions={{ gestureEnabled: false, headerLeft: null, headerShown: false }}
    >
      <DeletedAccountStack.Screen
        name='AccountDeletedScreen'
        component={AccountDeletedScreen}
      />
    </DeletedAccountStack.Navigator>
  )
}

// const DeletedAccountNavigator = createStackNavigator({
//   AccountDeletedScreen: { screen: AccountDeletedScreen },
// }, {
//   defaultNavigationOptions: {
//     gesturesEnabled: false,
//     headerLeft: null,
//     headerShown: false
//   },
//   headerMode: 'null'
// });

const SplashStack = createStackNavigator();

function SplashNavigator() {
  return (
    <SplashStack.Navigator
      screenOptions={{ headerShown: false }}

    >
      <SplashStack.Screen
        name='SplashScreen'
        component={SplashScreen}
      />
    </SplashStack.Navigator>
  )
}

// const AuthNavigator = createStackNavigator({
//   SplashScreen: { screen: SplashScreen },
// }, {
//   defaultNavigationOptions: {
//     headerShown: false
//   },
//   headerMode: 'null'
// });

const DrawerStack = createDrawerNavigator();

function DrawerNavigator() {
  return (
    <DrawerStack.Navigator
      initialRouteName='DrawerHome'
      screenOptions={{ headerShown: false, overlayColor: '#000000d6', drawerType: 'front', drawerPosition: 'right', drawerStyle: { width: settings.DRAWER_WIDTH }, swipeEnabled: false }}
      drawerContent={(props) => <DrawerMenu {...props} />}
    >
      <DrawerStack.Screen
        name='DrawerHome'
        component={HomeNavigator}
      />
    </DrawerStack.Navigator>
  )
}

// const DrawerNavigator = createDrawerNavigator(
//   {
//     DrawerHome: { screen: MainNavigator },
//   },
//   {
//     contentComponent: DrawerMenu,
//     overlayColor: '#000000d6',
//     drawerWidth: settings.DRAWER_WIDTH,
//     drawerPosition: 'right'
//   }
// );

const MainStack = createStackNavigator();

function MainNavigator() {
  return (
    <MainStack.Navigator
      initialRouteName='Splash'
      screenOptions={{ gestureEnabled: false, headerLeft: null, headerShown: false }}
    >
      <MainStack.Screen
        name='Splash'
        component={SplashNavigator}
      />
      <MainStack.Screen
        name='DrawerMenu'
        component={DrawerNavigator}
      />
      <MainStack.Screen
        name='DrawerLogin'
        component={SingInNavigator}
      />
      <MainStack.Screen
        name='FeedScreen'
        component={FeedNavigator}
      />
      <MainStack.Screen
        name='AccountDeletedNavigator'
        component={DeletedAccountNavigator}
      />
    </MainStack.Navigator>
  )
}

// const AppNavigator = createSwitchNavigator({
//   Auth: AuthNavigator,
//   App: MainApp,
// });

// const MainApp = createStackNavigator({
//   DrawerMenu: { screen: DrawerNavigator },
//   DrawerLogin: { screen: SingInNavigator },
//   FeedScreen: { screen: FeedNavigator },
//   AccountDeletedNavigator: { screen: DeletedAccountNavigator }
// },
//   {
//     defaultNavigationOptions: {
//       gesturesEnabled: false,
//       headerLeft: null,
//       headerShown: false,
//     },
//     headerMode: 'null',
//   })


export default MainNavigator
