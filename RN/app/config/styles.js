import { Dimensions, Platform } from 'react-native';
import { scale } from 'react-native-size-matters';
import { Color } from './Color';
import { SystemFont } from './Typography';
import { getStatusBarHeight } from 'react-native-status-bar-height';
import { hasDynamicIsland } from 'react-native-device-info';
import { Constants } from './Constants';

export const colors = {
  background: '#F5F2F9',
  errorText: '#FA3256',
  headerText: '#444444',
  buttonBackground: '#39BD98',
  buttonText: '#FFFFFF',
  inputBackground: '#FFFFFF',
  inputDivider: '#E4E2E5',
};

const statusBarHeight = getStatusBarHeight()
const isHeaderHasDynamicIsland = hasDynamicIsland()
const dimensions = Dimensions.get('window')

const androidBottomNavHeight = 60
const iOSBottomNavHeight = 74
export const navigationStyle = {
  navHeight: Platform.OS == "android" ? androidBottomNavHeight : iOSBottomNavHeight, //Bottom Nav Height
  secondaryBottomNavHeight: 50, //Nav height of the above the bottom bar
  panelsMargin: 20,
  storyPanelMargin: 10, //previous it was 0
  panelLeftRightMargin: 20,
  panelsTopMargin: 20,
  footerNavColor: Color.BOTTOM_NAVBAR_BACKGROUND_COLOR,
  panelRadius: 12,
  statusBarHeight: (Platform.OS == 'ios' && isHeaderHasDynamicIsland) ? statusBarHeight : statusBarHeight,
  iOSNavHeight: isHeaderHasDynamicIsland ? statusBarHeight + 100 : statusBarHeight + 60,
  androidTopMargin: Platform.Version >= 35 ? statusBarHeight : 0, //Android Nav Height
  androidBottomPadding: Platform.Version >= 35 ? 10 : 0, //Android Nav Height
}

export const storyPanelStyle = {
  paddingTop: 12,
  paddingLeft: 16,
  paddingRight: 16,
  paddingBottom: 16
}

export const roundImage = {
  width: scale(40),
  height: scale(40),
  borderRadius: scale(20),
  borderColor: Color.ROUNDED_BORDER_COLOR,
  borderWidth: scale(0.5),
  overflow: "hidden",
}

export const notificationRoundImage = {
  width: scale(32),
  height: scale(32),
  borderRadius: scale(16),
  borderColor: Color.ROUNDED_BORDER_COLOR,
  borderWidth: scale(0.5),
  overflow: "hidden",
}

export const episodeIcons = {
  width: 15,
  height: 15,
  alignSelf: 'center'
}

export const userProfilePic = (size) => {
  return {
    width: size,
    height: size,
    overflow: 'hidden',
    borderRadius: size / 2,
    borderWidth: 0.5
  }
}

export const commentContainerStyle = (commentsEmpty) => {
  return {
    marginLeft: storyPanelStyle.paddingLeft,
    marginRight: storyPanelStyle.paddingRight,
    marginTop: 12,
    marginBottom: commentsEmpty ? 0 : 12
  }
}

export const bottomTextView = {
  alignSelf: 'center'
}

export const buyButton = {
  position: 'absolute',
  borderRadius: 6,
  paddingLeft: 12,
  paddingRight: 12,
  height: 35,
  elevation: 0,
  justifyContent: 'center',
  borderLeftWidth: 0,
  borderTopWidth: 0,
  paddingTop: 0,
  paddingBottom: 0,
}

export const linkIcons = {
  width: 18,
  height: 18,
  opacity: 1,
  marginTop: 2
}

export const socialIcons = {
  height: 24,
  width: 24,
  marginLeft: 10,
}

export const buyButtonContainer = {
  height: 35,
  width: '80%',
  marginTop: 10,
  justifyContent: 'flex-start',
}

export const verifyTextStyle = {
  marginTop: scale(10),
  marginBottom: scale(25),
  textAlign: 'center',
  color: Color.BLACK_COLOR,
  fontSize: scale(14),
  fontFamily: SystemFont.SELECTED_FONT,
}

export const followButton = {
  borderRadius: 6,
  backgroundColor: Color.IN_APP_PANEL_BUTTON_COLOR,
  borderColor: Color.IN_APP_PANEL_BACKGROUND_COLOR,
  elevation: 0,
  paddingTop: 0,
  paddingBottom: 0,
  borderWidth: 0,
  borderLeftWidth: 0,
  borderTopWidth: 0
}

export const followingButton = {
  borderColor: Color.SEPARATOR_COLOR,
  backgroundColor: 'transparent',
  elevation: 0,
  borderRadius: 6,
  paddingTop: 0,
  paddingBottom: 0,
  borderWidth: 1
}

export const backButtonView = {
  height: 44,
  width: 44,
  borderRadius: 22,
  overflow: "hidden",
}

export const comicConfigContainer = {
  flex: 1,
  flexDirection: 'row',
  flexWrap: 'wrap',
  alignItems: 'center'
}

export const comicConfigBadgeView = {
  flexDirection: 'row',
  alignItems: 'center',
  borderRadius: 100,
  paddingTop: 4,
  paddingBottom: 4,
  paddingLeft: 12,
  paddingRight: 12
}

export const comicConfigIcon = {
  height: 12,
  width: 12
}

export const bannerMsgContainer = {
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  paddingTop: 8,
  paddingBottom: 8,
  paddingLeft: 12,
  paddingRight: 12,
}

export const bannerTextView = {
  flex: 1,
  justifyContent: 'center'
}

export const bannerCrossIconView = {
  marginLeft: 12
}

export const bannerCrossIcon = {
  height: 16,
  width: 16
}

export const readIndicatorIcon = {
  height: 24,
  width: 24
}

export const mainHeaderContainer = {
  paddingLeft: navigationStyle.panelLeftRightMargin,
  paddingRight: navigationStyle.panelLeftRightMargin,
  marginLeft: navigationStyle.panelLeftRightMargin,
  marginRight: navigationStyle.panelLeftRightMargin,
  marginTop: navigationStyle.panelsTopMargin + 12,
  marginBottom: navigationStyle.panelsTopMargin,
  borderRadius: navigationStyle.panelRadius,
  shadowColor: Color.BLACK_COLOR,
  shadowOpacity: 0.1,
  shadowRadius: 12,
  elevation: 12
}

export const loadingViewStyle = (aspectRatio) => {
  return {
    width: dimensions.width,
    height: Constants.LOADING_GIF_DIMEN.height * aspectRatio,
    resizeMode: 'contain',
    overflow: 'hidden'
  }
}

