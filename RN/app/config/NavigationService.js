import { CommonActions, StackActions } from '@react-navigation/native';
import { Constants } from './Constants';

let _navigator;

function setTopLevelNavigator(navigatorRef) {
  _navigator = navigatorRef;
}

function getCurrentNavigator() {
  const navState = _navigator ? _navigator.getCurrentRoute() : null
  return navState
}

function navigate(routeName, params, keyValue = null) {
  if (_navigator == null || _navigator == undefined) {
    return
  }
  _navigator.dispatch(
    CommonActions.navigate({
      name: routeName,
      params,
      key: keyValue
    })
  );
}

function navigateToSubRoute(routeName, params, subRoute, subParams) {
  if (_navigator == null || _navigator == undefined) {
    return
  }
  _navigator.dispatch(
    CommonActions.navigate({
      name: routeName,
      params: {
        screen: subRoute,
        params: subParams,
      },
    })
  );
}

function navigateToReader(subParams) {
  if (_navigator == null || _navigator == undefined) {
    return
  }
  _navigator.dispatch(
    CommonActions.navigate({
      name: "DrawerHome",
      params: {
        screen: "Home",
        params: subParams,
      },
      merge: true
    })
  );
}

function openHomePage(params) {
  navigate("Home", params)
}

function popToTop() {
  if (_navigator == null || _navigator == undefined) {
    return
  }

  _navigator.dispatch(
    StackActions.popToTop()
  )
}

function resetAndNavigate(routeName, params = null, parentName = "DrawerMenu") {
  let data = params
  const currentRoute = getCurrentRoute()

  if ((!currentRoute || currentRoute == "Home" || currentRoute == Constants.NOTIFICATIONS_SCREEN) && _navigator) {
    _navigator.dispatch(
      StackActions.push(
        routeName,
        { ...data }
      )
    );
  } else {
    navigate("Home", { ...params, forcePush: true, })
  }
}

function resetStackAndNavigate(routeName, params = null, parentName = "DrawerMenu") {
  if (_navigator == null || _navigator == undefined) {
    return
  }

  _navigator.dispatch(
    CommonActions.reset({
      index: 0,
      routes: [{ name: routeName, params: params }]
    })
  )
}

function getCurrentRoute() {
  const navState = _navigator ? _navigator.getCurrentRoute() : null
  const nav = navState ? navState.name : null
  if (!nav) {
    return null
  }

  return nav
}

export default {
  navigate,
  getCurrentNavigator,
  navigateToSubRoute,
  navigateToReader,
  resetAndNavigate,
  resetStackAndNavigate,
  setTopLevelNavigator,
  getCurrentRoute,
  openHomePage,
  popToTop
};