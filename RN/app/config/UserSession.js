import { Constants } from "./Constants";
import FirebaseManager from "./FirebaseManager";
import { Utils } from "./Utils";
import NetworkUtils from "./NetworkUtils";

export default class UserSession {
  static instance = new UserSession()

  constructor() {
    this.userDetails = {}
    this.unlockedComics = []
    this.monthlyReadCounter = -1
    this.unreadNotifications = 0

    this.hasPrevilage = this.hasPrevilege.bind(this)
    this.hasAggregatesPrevilage = this.hasAggregatesPrevilege.bind(this)
    this.isUserDetailsEmpty = this.isUserDetailsEmpty.bind(this)
    this.checkCommentPrevilegeAndShowMessage = this.checkCommentPrevilegeAndShowMessage.bind(this)
    this.getMonthlyMaxComicReadQuota = this.getMonthlyMaxComicReadQuota.bind(this)
    this.isMonthlyComicLimitApplied = this.isMonthlyComicLimitApplied.bind(this)
  }

  updateUserDetails(userDetails) {
    this.userDetails = userDetails
  }

  updateUnlockedComics(comicList) {
    this.unlockedComics = comicList
  }

  getCurrentUserInfo() {
    return this.userDetails
  }

  getCurrentUserNotifAlerts() {
    if (this.userDetails && this.userDetails.notificationSettings) {
      return this.userDetails.notificationSettings
    }
    return null
  }

  updateCurrentUserNotifAlerts(notifSettings) {
    if (this.userDetails) {
      this.userDetails.notificationSettings = notifSettings
    }
  }

  addNewUnlockedComic(comicUrl) {
    if (this.unlockedComics != null) {
      this.unlockedComics.push(comicUrl)
    }
  }

  isUserBlocked() {
    let isUserBlockToComment = false
    if (this.userDetails && this.userDetails.permissions) {
      isUserBlockToComment = !this.userDetails.permissions.includes(Constants.COMMENT_PERMISSION_KEY)
    }

    return isUserBlockToComment
  }

  getMonthlyMaxComicReadQuota() {
    return (this.userDetails && Utils.checkObject(this.userDetails.monthlyFreeComic)) ? this.userDetails.monthlyFreeComic : -1
  }

  isMonthlyComicLimitApplied() {
    const limit = (this.userDetails && Utils.checkObject(this.userDetails.monthlyFreeComic)) ? this.userDetails.monthlyFreeComic : -1
    return limit >= 0
  }

  updateReadedFreeComicsCount(comicCount) {
    this.monthlyReadCounter = comicCount
  }

  ReadedFreeComicsCount() {
    return this.monthlyReadCounter
  }

  getUserLastFourMobileNoDigit() {
    let firebaseInstance = FirebaseManager.instance
    let userPhoneNumber = firebaseInstance.currentUser() ? firebaseInstance.currentUser()._user.phoneNumber : null
    let lastFourMobileNoDigit = userPhoneNumber ? `x${userPhoneNumber.substr(-4)}` : ""
    return lastFourMobileNoDigit
  }

  getUserMaskedEmailAddress() {
    let email = this.getUserEmailAddress()
    if (email) {
      const emailArray = email.split('@');
      const localPart = emailArray[0];
      const domainPart = emailArray[1];
      let maskedLocalPart = '*';

      if (localPart.length >= 4) {
        const maskedChar = (localPart.length > 4) ? 3 : 2;
        maskedLocalPart = localPart.charAt(0) + '*'.repeat(maskedChar) + localPart.charAt(localPart.length - 1);
      } else if (localPart.length >= 2) {
        const maskedChar = (localPart.length > 2) ? 2 : 1;
        maskedLocalPart = localPart.charAt(0) + '*'.repeat(maskedChar);
      }
      return maskedLocalPart + '@' + domainPart;
    }

    return userEmailAddress
  }

  isAnyUserAlertsEnabled() {
    let alertsSettings = this.getCurrentUserNotifAlerts()
    if (alertsSettings) {
      if ((alertsSettings['push'] && alertsSettings['push'].enabled) || (alertsSettings['email'] && alertsSettings['email'].enabled)) { // Will add Sms while implementing Sms alerts.
        return true
      }
    }
    return false
  }

  isAllUserAlertsEnabled() {
    let alertsSettings = this.getCurrentUserNotifAlerts()
    if (alertsSettings) {
      if ((alertsSettings['push'] && alertsSettings['push'].enabled) && (alertsSettings['email'] && alertsSettings['email'].enabled)) { // Will add Sms while implementing Sms alerts.
        return true
      }
    }
    return false
  }

  currentUser() {
    return FirebaseManager.instance.currentUser()
  }

  isUserAnonymous() {
    return FirebaseManager.instance.isUserAnonymous()
  }

  isLoggedInUser() {
    return FirebaseManager.instance.currentUser() != null && !this.isUserAnonymous()
  }

  hasPrevilege() {
    return this.currentUser() && !this.isUserAnonymous()
  }

  hasAggregatesPrevilege() {
    return this.currentUser() && !this.isUserAnonymous()
  }

  checkPrevilegeAndShowMessage(props = null, actionSheet) {
    if (!this.currentUser() || this.isUserAnonymous()) {
      if (actionSheet) {
        actionSheet.show()
      }
      return false
    }

    return true
  }

  isUserDetailsEmpty() {
    const isConnected = NetworkUtils.instance.isAvailable()
    if (isConnected) {
      if (this.userDetails) {
        const { displayName } = this.userDetails
        if (!Utils.checkData(displayName) || displayName.includes(Constants.ANONYMOUS_USER) || displayName.includes(Constants.NON_SIGNED_IN_USER)) {
          return true
        }
      } else {
        return true
      }
    }

    return false
  }

  checkCommentPrevilegeAndShowMessage(pathUrl) {
    if (!Utils.checkIsPrivilegeUser(pathUrl)) {
      return false
    }
    return true
  }

  getCurrentUserBadges() {
    if (this.userDetails) {
      return this.userDetails.badges
    }
    return null
  }

  isUserAComicCreator() {
    if (this.userDetails && this.userDetails.badges) {
      return this.userDetails.badges.includes("creator")
    }
    return false
  }

  getUserInfluencePoints() {
    if (this.userDetails && this.userDetails.influencePoints) {
      return this.userDetails.influencePoints
    }
    return { total: 0, balance: 0 }
  }

  isComicUnlocked(comicURL) {
    if (this.unlockedComics) {
      return this.unlockedComics.includes(comicURL)
    }
    return false
  }

  getFriendsCountForFreeAccess() {
    if (this.userDetails) {
      return this.userDetails.minFriendFreeComicAccess
    }
    return 0
  }

  getUserFriendsCount() {
    if (this.userDetails) {
      return this.userDetails.friendCount
    }
    return 0
  }

  getUserReferralAmount() {
    if (this.userDetails?.referralAmount) {
      return this.userDetails.referralAmount
    }
    return { total: 0, consume: 0, balance: 0 }
  }

  getUserReferralBalance() {
    if (this.getUserReferralAmount()) {
      const balance = this.getUserReferralAmount()?.balance ?? 0
      return balance
    }
    return 0
  }

  isFriendsCountReachedFreeAccess() {
    if (this.userDetails) {
      if (this.getFriendsCountForFreeAccess() == -1) {
        return false
      }
      return this.getFriendsCountForFreeAccess() <= this.getUserFriendsCount()
    }
    return false
  }

  updateUnreadNotifications(unreadNotif) {
    this.unreadNotifications = unreadNotif
  }

  getUnreadNotifications() {
    if (this.unreadNotifications) {
      return this.unreadNotifications
    }
    return 0
  }

  getUserEmailAddress() {
    if (this.userDetails && this.userDetails.email) {
      return this.userDetails.email
    }
    return null
  }

}
