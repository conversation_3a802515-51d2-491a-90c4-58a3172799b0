import RNFS from "react-native-fs"
import moment from "moment"
import { settings } from "../config/settings"
import { Utils } from "./Utils"

const defaultCacheDirectory = RNFS.DocumentDirectoryPath + "/cache"
const FILE_EXPIRED_TIME = settings.fileExpireTime

export default class FileCache {
  static default = new FileCache()

  constructor() {
    this.downloadInProgress = {}

    this.clearURLSchema.bind(this)
    this.getFileContentForURL.bind(this)
    this.getLocalPathForURL.bind(this)
    this.isFileExistsForUrl.bind(this)
    this.downloadFile.bind(this)
    this.delete.bind(this)
    this.isFileStaleForURL.bind(this)
    this.getBase64Content.bind(this)

    this.createFileInfo.bind(this)
    this.getCacheDirectoryContent.bind(this)
  }

  clearURLSchema(url) {
    return url.includes("https") ? url.split("https://")[1] : url.split("http://")[1]
  }

  async getFileContentForURL(url, vid) {
    return RNFS.readFile(this.getLocalPathForURL(url, vid))
  }

  async getBase64Content(filePath) {
    try {
      return await RNFS.readFile(filePath, "base64")
    } catch (error) {
      return null
    }
  }

  getLocalPathForURL(url, vid) {
    var localPathUrl = this.clearURLSchema(url)
    if (vid) {
      localPathUrl = localPathUrl + "." + vid
    }
    return defaultCacheDirectory + "/" + localPathUrl
  }

  async isFileExistsForUrl(url, vid) {
    return this.isFileExists(this.getLocalPathForURL(url, vid))
  }

  async isFileExists(path) {
    return RNFS.exists(path)
  }

  async isFileStaleForURL(url, vid, key = null, fileExpireTime = FILE_EXPIRED_TIME) {
    var pathUrl = ""
    if (key) {
      pathUrl = defaultCacheDirectory + "/" + key
    } else {
      pathUrl = this.getLocalPathForURL(url, vid)
    }

    var isExists = await RNFS.exists(pathUrl)
    if (isExists) {
      try {
        var fileStat = await RNFS.stat(pathUrl)
        if (moment().diff(moment(fileStat.mtime), "m") >= fileExpireTime) {
          return true
        } else {
          return false
        }
      } catch (error) {
        Utils.log(error)
        throw error
      }
    }
    return true
  }

  async writeFile(key, data) {
    let filePath = defaultCacheDirectory + "/" + key
    try {
      let isExists = await this.isFileExists(filePath)
      if (isExists) {
        await RNFS.unlink(filePath)
      }
      let res = await RNFS.writeFile(filePath, data)
      Utils.log("File saved in RNFS", res)
    } catch (error) {
      Utils.log("error in writeFile", error)
    }
  }

  async readFile(key) {
    let filePath = defaultCacheDirectory + "/" + key
    try {
      let res = await RNFS.readFile(filePath)
      await RNFS.stat(filePath)
      return res
    } catch (error) {
      Utils.log("error in readFile in RNFS", error)
    }
  }

  async downloadFile(url, vid) {
    Utils.log(url)
    var downloadURL = url
    var splitURL = url.split("?")
    if (splitURL.length > 1 && splitURL[1].includes("mockParam=")) {
      url = splitURL[0]
    }
    if (this.downloadInProgress[url] != true) {
      this.downloadInProgress[url] = true
      var pathUrl = this.getLocalPathForURL(url, vid)

      var dirs = pathUrl.split("/")
      dirs.pop()

      let temporaryDownloadPath = pathUrl + "." + "tmp"
      
      await RNFS.mkdir(dirs.join("/"))
      var downloadInfo = RNFS.downloadFile({
        fromUrl: downloadURL,
        toFile: temporaryDownloadPath,
        readTimeout: 5000,
      })

      try {
        var downloadResult = await downloadInfo.promise
      } catch (error) {
        delete this.downloadInProgress[url]
        throw error
      }

      delete this.downloadInProgress[url]

      if (downloadResult.statusCode == 200) {
        try {
          await RNFS.moveFile(temporaryDownloadPath, pathUrl)
          return true
        } catch (error) {
          try {
            await RNFS.unlink(pathUrl)
            await RNFS.moveFile(temporaryDownloadPath, pathUrl)
          } catch (error) {
            Utils.log("eror in writing again " + error)
            return false
          }
        }
      } else {
        await RNFS.unlink(temporaryDownloadPath)
        return false
      }
    } else {
      Utils.log("image is already downloading :" + this.downloadInProgress[url])
    }
  }

  async copyFile(sourcePath, destPath) {
    try {
      await RNFS.copyFile(sourcePath, destPath)
      return true
    } catch (error) {
      try {
        await RNFS.unlink(destPath)
        await RNFS.copyFile(sourcePath, destPath)
        return true
      } catch (error) {
        Utils.log("eror in copy again " + error)
        return false
      }
    }
  }

  async delete(url = defaultCacheDirectory) {
    return RNFS.unlink(url)
  }

  createFileInfo(title, path, type, modifiedTime, content) {
    return {
      title,
      path,
      type,
      modifiedTime,
      content,
    }
  }

  async getCacheDirectoryContent(path = defaultCacheDirectory) {
    var localFileDirecotry = []
    let result = await RNFS.readDir(path)

    for (let i = 0; i < result.length; i++) {
      let element = result[i]
      let { name, path, mtime } = element

      if (name.includes("jpg", "png")) {
        var fileName = name.split(".")
        fileName.pop()
        fileName = fileName.join(".")
      } else {
        var fileName = name
      }

      if (element.isFile()) {
        localFileDirecotry.push(this.createFileInfo(fileName, path, "file", mtime, []))
      } else {
        var folderContent = await this.getCacheDirectoryContent(element.path)
        localFileDirecotry.push(this.createFileInfo(name, path, "folder", mtime, folderContent))
      }
    }

    return localFileDirecotry
  }
}
