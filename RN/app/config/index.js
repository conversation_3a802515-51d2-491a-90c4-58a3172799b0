export default {
  MAX_ATTACHMENT_SIZE: 5000000,
  C<PERSON><PERSON>NC<PERSON>_SYMBOL: '$',
  CURRENCY_SYMBOL_ISO: 'AUD',
  // aws: {
  //   ACCESS_ID: `${process.env.NEXT_PUBLIC_AWS_ACCESS_ID}`,
  //   ACCESS_KEY: `${process.env.NEXT_PUBLIC_AWS_ACCESS_KEY}`,
  //   REGION: `${process.env.NEXT_PUBLIC_AWS_REGION}`,
  // },
  // s3: {
  //   BUCKET_NAME: `${process.env.NEXT_PUBLIC_AWS_BUCKET_NAME}`,
  //   DIRECTORY_NAME: `${process.env.NEXT_PUBLIC_AWS_DIR_NAME}`
  // },
  apiGateway: {
    DEV_URL: `https://tinyview-dev-ttogjejltq-uc.a.run.app/v1/api`,
    PROD_URL: `https://api.tinyview.com/v1/api`,
    // TEMPLATE: `${process.env.NEXT_PUBLIC_TEMPLATE_API_GATEWAY}`,
    // CHARITY: `${process.env.NEXT_PUBLIC_CHARITY_API_GATEWAY}`,
    // CHECKOUT: `${process.env.NEXT_PUBLIC_CHECKOUT_API_GATEWAY}`
  },
  // google: {
  //   addressAPIKey: `${process.env.NEXT_PUBLIC_GOOGLE_ADDRESS_KEY}`
  // },
  // tikit_url: {
  //   WEBSITE: `${process.env.NEXT_PUBLIC_TIKIT_WEBSITE_URL}`,
  //   ADMIN: `${process.env.NEXT_PUBLIC_TIKIT_ADMIN_URL}`,
  // },
  // default_charity_img: 'https://tikitbook-dev-documents.s3-ap-southeast-2.amazonaws.com/template/3gmzwa9txW7emeuProxtqr.png',
  // default_charity_logo: 'https://tikitbook-dev-documents.s3-ap-southeast-2.amazonaws.com/template/jj9vD5pePyRhQzPj6hou3X.jpeg'
}
