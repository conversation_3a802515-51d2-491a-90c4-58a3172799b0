import React, { Component } from "react";
import { Text, TextInput, View, StyleSheet, Platform, TouchableOpacity, Image, Dimensions } from "react-native";
import { SystemFont } from './Typography'
import { scale } from 'react-native-size-matters'
import { Color } from "./Color";
import { Utils } from "./Utils";
import { roundImage } from "./styles"
import ImagePlaceHolder from "../components/ImagePlaceHolder";
import FastImage from 'react-native-fast-image'
import { Constants } from "./Constants";
import UserSession from "./UserSession";
import { ThemeContext } from "../Contexts";

const dimensions = Dimensions.get('window');
export default class CommentTextInputComponent extends Component {
  constructor(props) {
    super(props);

    this.renderProfileImage = this.renderProfileImage.bind(this)
    this.navigateToUserProfile = this.navigateToUserProfile.bind(this)
  }

  navigateToUserProfile() {
    let routeName = Constants.USER_PROFILE_SCREEN
    if (UserSession.instance.isLoggedInUser()) {
      this.props.navigation.push(routeName)
    }
  }

  renderProfileImage() {
    const { displayName, photoURL } = this.props.userDetails

    return (
      <View style={styles.imageView}>
        <TouchableOpacity onPress={() => { this.navigateToUserProfile() }} >
          {(Utils.checkData(photoURL))
            ?
            <FastImage source={{ uri: photoURL }} resizeMode="cover" style={[roundImage, styles.userProfileView]} />
            :
            <ImagePlaceHolder
              backgroundColor={Color.PROFILE_PLACE_HOLDER_BG}
              showCircularBorder={true}
              textColor={Color.COMMENT_TEXT_COLOR}
              size={32}
              type={'circle'}>{Utils.checkData(displayName) ? displayName : Constants.NON_SIGNED_IN_USER}</ImagePlaceHolder>
          }
        </TouchableOpacity>
      </View>

    )
  }

  render() {
    const { topLabel, inputTextLabel, errorText, refState, value, keyboardType, secureTextEntry, placeHolder, textColor,
      maxLength, autoFocus, selectionColor, inputRef, placeholderTextColor,
      multiline, textInputStyle, fontSize, autoCapitalize, inputTextLabelStyle, returnKeyType, editable = true, keyValue = null } = this.props

    return (
      <View style={styles.mainTopView}>
        {inputTextLabel && <Text style={[errorText != "" && errorText != null ? styles.errorTextStyle : styles.labelTextStyle, inputTextLabelStyle]}>{inputTextLabel}</Text>}
        {Utils.checkData(topLabel) &&
          <View style={{ paddingBottom: 12, flexDirection: 'row' }}>
            <Text style={[this.context.p, styles.topCommentLabelTextStyle]}>Replying to</Text>
            <Text style={[this.context.p, styles.topCommentLabelTextStyle, { color: this.context.colors.textBold, marginLeft: 3 }]}>{topLabel}</Text>
            <Text style={[this.context.pBold, { marginLeft: 8 }]}>·</Text>
            <TouchableOpacity
              onPress={this.props.onReplyCancel}>
              <Text style={[this.context.p, styles.topCommentLabelTextStyle, { marginLeft: 8, color: this.context.colors.textBold }]}>{Constants.CANCEL}</Text>
            </TouchableOpacity>
          </View>
        }
        <View style={{ flexDirection: 'row' }}>
          {this.renderProfileImage()}
          <TextInput
            key={keyValue}
            multiline={multiline ? multiline : false}
            selectionColor={selectionColor}
            ref={(r) => { inputRef && inputRef(r) }}
            editable={editable}
            autoFocus={autoFocus}
            secureTextEntry={secureTextEntry != "" && secureTextEntry != null ? secureTextEntry : false}
            style={[styles.rectangleTextInput, textInputStyle]}
            onChangeText={(text) => this.props.onChange(refState, text)}
            onTouchStart={() => this.props.onClickTextInput()}
            onBlur={() => this.props.onBlur ? this.props.onBlur(refState) : null}
            onFocus={() => this.props.onFocus ? this.props.onFocus(refState) : null}
            value={value}
            minHeight={44}
            color={this.context.p.color}            
            returnKeyType={returnKeyType || 'default'}
            fontSize={fontSize ? fontSize : 22}
            placeholder={placeHolder}
            fontFamily={this.context.p.fontFamily}
            maxLength={maxLength != null ? maxLength : 30}
            placeholderTextColor={placeholderTextColor}
            keyboardType={keyboardType != null ? keyboardType : "default"}
            textAlignVertical={"top"}
            autoCapitalize={autoCapitalize || 'sentences'}
            onSubmitEditing={() => this.props.onSubmitEditing && this.props.onSubmitEditing()}
          />
          <TouchableOpacity
            style={{ alignSelf: 'center', paddingLeft: 12 }}
            onPress={() => { this.props.onSendIconPress() }}
            disabled={!editable || value == ""}>
            <Image style={!editable || value == "" ? styles.disabled_send_icon : styles.send_icon} source={require('../../assets/post_comment_icon.png')} />
          </TouchableOpacity>
        </View>
        {errorText != "" && errorText != null ? <Text style={styles.errorTextStyle}>{errorText}</Text> : null}
      </View>
    );
  }
}

CommentTextInputComponent.contextType = ThemeContext;

const styles = StyleSheet.create({
  mainTopView: {
    width: '100%',
    paddingLeft: 20,
    paddingRight: 20,
  },
  rectangleTextInput: {
    flex: 1,
    paddingStart: 20,
    maxHeight: 100
  },
  labelTextStyle: {
    marginLeft: scale(5),
    marginBottom: 5,
    marginTop: 4,
    color: Color.BLACK_COLOR,
    textAlign: 'left',
    fontSize: Platform.OS == 'ios' ? scale(13) : scale(12),
  },
  errorTextStyle: {
    marginLeft: scale(5),
    marginBottom: 2,
    marginTop: scale(-5),
    color: 'red',
    fontFamily: SystemFont.SELECTED_FONT,
    textAlign: 'left',
    fontSize: Platform.OS == 'ios' ? scale(13) : scale(12),
  },
  topCommentLabelTextStyle: {
    fontFamily: SystemFont.SELECTED_FONT,
    textAlign: 'left',
    fontSize: scale(12)
  },
  seprator: {
    height: 1,
    width: '100%',
    marginBottom: 10,
    backgroundColor: Color.LIGHTER_GREY,
  },
  send_icon: {
    width: scale(22),
    height: scale(22),
    tintColor: Color.BOTTOM_ICON_TINT_COLOR,
  },
  disabled_send_icon: {
    width: scale(22),
    height: scale(22),
    tintColor: Color.DISABLE_BOTTOM_ICON_TINT_COLOR,
  },
  userProfileView: {
    height: 32,
    width: 32
  },
  imageView: {
    alignSelf: 'center',
    marginRight: 8
  }
});
