import NetInfo from "@react-native-community/netinfo";
import { getUserDetails } from "../redux/actions/actions";
import store from "../redux/store";

export default class NetworkUtils {
    static instance = new NetworkUtils()

    constructor() {
        this.isNetAvailable = false;
        this.addNetworListener.bind(this);
        this.isAvailable.bind(this);

        this.addNetworListener()
    }

    addNetworListener() {
        const self = this;
        NetInfo.addEventListener(state => {
            if (!self.isNetAvailable && state.isConnected) {
                store.dispatch(getUserDetails())
            }
            self.isNetAvailable = state.isConnected
        });
    }

    isAvailable() {
        return this.isNetAvailable
    }

    static async isNetworkAvailable() {
        const response = await NetInfo.fetch();
        return response.isConnected;
    }


}