import React from 'react';
import { settings } from './settings';
import { Alert, Platform, Linking, Vibration } from 'react-native'
import moment from 'moment';
import InAppReview from "react-native-in-app-review";
import { SharedPreferences } from './SharedPreferences';
import SessionManager from './SessionManager';
import FirebaseManager from './FirebaseManager';
import { CommonActions } from '@react-navigation/native';
import NavigationService from './NavigationService';
import { Constants } from './Constants';
import Clipboard from '@react-native-clipboard/clipboard';
import { Toast } from 'native-base';
import UserSession from './UserSession';
import Share from "react-native-share";
const directory = require('../../directory.json')
import dataCountry from "react-native-country-picker-module/src/constants/countries.json"
import ToastMessage from '../components/ToastMessage';

const DAY_MILLIS = 1000 * 60 * 60 * 24;

moment.updateLocale('en', {
  relativeTime: {
    future: 'in %s',
    past: '% s ago',
    s: 'Now',
    ss: '%ss',
    m: '%dm',
    mm: '%dm',
    h: '%dh',
    hh: '%dh',
    d: '%dd',
    dd: '%dd',
    M: '%d mo.',
    MM: '%d mo.',
    y: '%dy',
    yy: '%dy'
  }
});

export class Utils {

  static getCurrentUserNumber() {
    try {
      return FirebaseManager.instance.currentUser().phoneNumber
    } catch (error) {
      return null
    }
  }

  static getCurrentUserId() {
    try {
      return FirebaseManager.instance.currentUser().uid
    }
    catch (err) {
      return null
    }
  }

  static resolvePath = (pageURL, url) => {
    let relative = url + '';

    const oldProdBaseURL = 'https://cdn.tinyview.com';
    const oldDevBaseURL = 'https://cdn-dev.tinyview.com';

    if (relative.includes('http')) {
      if (!relative.includes(settings.apiBaseURL)) {
        relative = relative.replace(oldDevBaseURL, settings.apiBaseURL);
        relative = relative.replace(oldProdBaseURL, settings.apiBaseURL);
      }
      return relative;
    }

    const base = pageURL;

    if (relative.startsWith('/')) {
      return settings.apiBaseURL + relative
    }

    var stack = base.split('/')
    var parts = relative.split('/')
    stack.pop()

    for (index in parts) {
      if (parts[index] == ".") {
        continue
      }
      if (parts[index] == "..") {
        stack.pop()
      } else {
        stack.push(parts[index])
      }
    }
    var finalUrl = stack.join('/')
    return finalUrl
  }

  static checkData(data) {
    return data != null && data != undefined && data != "";
  }

  static checkObject(data) {
    return data != null && data != undefined;
  }

  static getSeriesProfileURL(channelName) {
    return settings.apiBaseURL + `/${channelName}/${channelName}-profile.jpg`
  }

  static getDomainName(url) {
    if (!url) {
      return null
    }
    let regex = '^(?:https?:)?(?:\/\/)?([^\/\?]+)'
    let domainName = url.match(regex)
    if (domainName != null && domainName.length >= 2 && typeof domainName[1] === 'string') {
      return domainName[1].startsWith("www.") ? domainName[1].substring(4) : domainName[1];
    }
    return url
  }

  // static formatDamainURL(url) {
  //   let formatURL = url.replace("https://", "").replace("http://").replace("www.", "");
  //   if (formatURL.includes("/")) {
  //     const parts = formatURL.split("/");
  //     if (parts.length > 1) {
  //       return parts[0]
  //     } else {
  //       return formatURL;
  //     }
  //   }
  //   return formatURL;
  // }

  static formatDamainURL(url) {
    let formatURL = url.replace("https://", "").replace("http://").replace("www.", "");
    if (formatURL.includes("/")) {
      const parts = formatURL.split("/");
      if (parts.length > 1 && parts[1] == "") {
        return parts[0]
      } else {
        return formatURL;
      }
    }

    return formatURL;
  }

  static getFormattedDate(timeSpan, stringFormat = 'dddd, MMMM D, YYYY') {
    if (!timeSpan) {
      return null;
    }
    return moment(timeSpan).format(stringFormat)
  }

  static isDateTimeValid(timeSpan) {
    return timeSpan && moment(timeSpan).isValid()
  }

  static convertTimeSpanIntoTimeAgo(timeSpan, withoutSuffix = true) {
    return moment(timeSpan).fromNow(withoutSuffix)
  }

  static convertTimeSpanForComments(timeSpan, withoutSuffix = true) {
    var todayDate = new Date()
    var commentDate = new Date(timeSpan)
    var totalDaysofComment = Math.floor(Math.abs((commentDate - todayDate) / DAY_MILLIS));

    if (totalDaysofComment >= 365) {
      return `${Math.floor(totalDaysofComment / 365)}y`
    } else if (totalDaysofComment >= 7 && totalDaysofComment < 365) {
      return `${Math.floor(totalDaysofComment / 7)}w`
    } else if (totalDaysofComment < 7 && totalDaysofComment > 0) {
      return `${totalDaysofComment}d`
    } else {
      return moment(timeSpan).fromNow(withoutSuffix)
    }
  }

  static isFutureDate(datetime) {
    var todayDate = moment();
    var futureDate = moment(datetime, 'YYYY-MM-DDTHH:mm:ss.SSSZ');

    if (futureDate.isValid() && !todayDate.isAfter(futureDate)) {
      return true
    }
    return false
  }

  static isSameMonthAndYear = (timestamp1, timestamp2) => {
    const date1 = new Date(timestamp1);
    const date2 = new Date(timestamp2);

    return date1.getFullYear() === date2.getFullYear() && date1.getMonth() === date2.getMonth();
  };

  static getLatestTimeKey(timings) {
    if (timings) {
      const { lastComment = 0, lastRepost = 0, lastLiked = 0, lastSent = 0, lastGifted = 0 } = timings
      if (lastComment > lastRepost && lastComment > lastLiked && lastComment > lastSent && lastComment > lastGifted) {
        return Constants.COMMENT
      } else if (lastRepost > lastComment && lastRepost > lastLiked && lastRepost > lastSent && lastRepost > lastGifted) {
        return Constants.REPOST
      } else if (lastLiked > lastComment && lastLiked > lastRepost && lastLiked > lastSent && lastLiked > lastGifted) {
        return Constants.LIKE
      } else if (lastSent > lastComment && lastSent > lastRepost && lastSent > lastLiked && lastSent > lastGifted) {
        return Constants.SENT
      } else if (lastGifted > lastComment && lastGifted > lastRepost && lastGifted > lastLiked && lastGifted > lastSent) {
        return Constants.GIFT
      }
    }
  }

  static getMeaningFullURL = (pageUrl) => {
    if (pageUrl == null || pageUrl == undefined) {
      return null
    }
    let logURL = ''
    const baseURL = settings.apiBaseURL
    var splitedURL = pageUrl.split(baseURL)
    if (splitedURL.length > 1) {
      logURL = splitedURL[1];
    } else {
      logURL = pageUrl;
    }

    return logURL;
  }

  static isComicURL = (url) => {
    if (url == null) return false
    let urlLength = this.getURLEndPointLength(url)
    return urlLength > 2
  }

  static isChannelURL = (url) => {
    if (url == null) return false
    let urlLength = this.getURLEndPointLength(url)
    return urlLength == 2
  }

  static isHomeURL = (url) => {
    if (url == null || url == undefined) return false
    let urlLength = this.getURLEndPointLength(url)
    return urlLength == 1
  }

  static isSubscriptionURL = (url) => {
    if (url == null || url == undefined) return false
    let endPointURL = this.getMeaningFullURL(url)
    const subscriptionURL = Utils.getMeaningFullURL(settings.getSubscribeURL())
    return endPointURL == subscriptionURL
  }

  static isTVSeriesURL = (url) => {
    if (url == null || url == undefined) return false
    let endPointURL = this.getMeaningFullURL(url)
    const whatsTinyViewEndPointURL = Utils.getMeaningFullURL(settings.getWhatsTinyviewURL())
    return endPointURL == whatsTinyViewEndPointURL
  }

  static isUpdatePageURL = (url) => {
    if (url == null || url == undefined) return false
    let endPointURL = this.getMeaningFullURL(url)
    return endPointURL == settings.UPDATE_COMIC_END_POINT_URL
  }

  static isInfluencePageURL = (url) => {
    if (url == null || url == undefined) return false
    let endPointURL = this.getMeaningFullURL(url)
    return endPointURL == settings.getInfluencePageURL()
  }

  static isDirectoryPageURL = (url) => {
    if (url == null || url == undefined) return false
    let endPointURL = this.getMeaningFullURL(url)
    return endPointURL == settings.DIRECTORY_COMIC_END_POINT_URL
  }

  static isTinyviewComicsPage = (url) => { //Except series page
    if (url == null || url == undefined) return false
    return Utils.isHomeURL(url) || Utils.isTVSeriesURL(url) || Utils.isDirectoryPageURL(url) || Utils.isSubscriptionURL(url) || Utils.isUpdatePageURL(url)
  }

  static isTinyviewPage = (url) => { // Inlcude series page also
    if (url == null || url == undefined) return false
    return Utils.isHomeURL(url) || Utils.isChannelURL(url) || Utils.isTVSeriesURL(url) || Utils.isDirectoryPageURL(url) || Utils.isSubscriptionURL(url) || Utils.isUpdatePageURL(url)
  }

  static isComicPage = (url) => { //Comic page except directory and subscribe page
    if (url == null || url == undefined) return false
    return Utils.isComicURL(url) && !Utils.isDirectoryPageURL(url) && !Utils.isSubscriptionURL(url)
  }

  static getURLEndPointLength = (url) => {
    let pathUrl = Utils.getMeaningFullURL(url)
    let pathAttributes = pathUrl.split('/')
    if (pathUrl.startsWith('/')) {
      pathAttributes.splice(0, 1) //removing 1st empty item from array
    }
    // if pathAttributes.length = 1 then it is Home page | if = 2 then it is channel page | if > then it is comic's page.
    return pathAttributes.length
  }

  static getComicSeriesURL = (url) => {
    let pathUrl = Utils.getMeaningFullURL(url)
    let pathAttributes = pathUrl.split('/')
    if (pathUrl.startsWith('/')) {
      pathAttributes.splice(0, 1) //removing 1st empty item from array
    }

    if (pathAttributes.length == 1 && pathAttributes[0] == "index.json") {
      return "/tinyview/" + pathAttributes[0];
    } else if (pathAttributes.length == 2) {
      return "/" + pathAttributes[0] + "/index.json";
    } else {
      return "/" + pathAttributes[0] + (pathAttributes.length == 1 ? "" : "/index.json");
    }
  }

  static getComicSeriesURLFromChannel = (channelName) => {
    return settings.apiBaseURL + "/" + channelName + "/index.json"
  }

  static getComicSeriesFeedURL = (url) => {
    const seriesId = Utils.getChannelName(url)
    return settings.apiBaseURL + "/" + seriesId + "/newsfeed.json"
  }

  // static getComicHomeFeedURL = (url) => {
  //   return settings.apiBaseURL + "/newsfeed.json"
  // }

  static getComicSeriesFooterURL = (url) => {
    let pathUrl = Utils.getMeaningFullURL(url)
    let pathAttributes = pathUrl.split('/')
    if (pathUrl.startsWith('/')) {
      pathAttributes.splice(0, 1) //removing 1st empty item from array
    }

    if (pathAttributes.length > 1) {
      return "/" + pathAttributes[0] + "/footer.json";
    }

    return null
  }

  static getComicSeriesHeaderURL = (url) => {
    const seriesID = Utils.getChannelName(url)
    return "/" + seriesID + "/header.json"
  }

  // static isDatePassed = (date) => {
  //   var checkedDate = moment(date, "DD/MM/YYYY")
  //   let isPassed = checkedDate.isBefore(moment());
  //   Utils.log('isDatePassed - ' + isPassed)
  //   return isPassed;
  // }

  static updateInstalledAppList = () => {
    if (Platform.OS == 'ios') {
      const appLinks = [Constants.FACEBOOK_APP_LINK, Constants.INSTAGRAM_APP_LINK, Constants.X_APP_LINK, Constants.WHATSAPP_APP_LINK]
      appLinks.forEach((paticularAppLink) => {
        Linking.canOpenURL(paticularAppLink).then((isAppPresent) => {
          if (isAppPresent) {
            SessionManager.instance.updateAppList(paticularAppLink)
          }
        })
      })
    } else {
      const androidAppLinks = [Constants.ANDROID_WHATSAPP_APP_LINK, Constants.ANDROID_X_APP_LINK, Constants.ANDROID_INSTAGRAM_APP_LINK, Constants.ANDROID_FACEBOOK_APP_LINK]
      androidAppLinks.forEach((paticularAppLink) => {
        Share.isPackageInstalled(paticularAppLink).then((isAppPresent) => {
          if (isAppPresent.isInstalled) {
            SessionManager.instance.updateAppList(paticularAppLink)
          }
        })
      })
    }
  }

  static getChannelName(url) { // returns in-science-we-trust 
    let channel = '';
    const baseURL = settings.apiBaseURL + "/"
    var splitedURL = url.split(baseURL)
    if (splitedURL.length > 1) {
      if (splitedURL[1] == 'index.json') {
        channel = settings.TINYVIEW_CHANNEL_NAME;
      } else {
        const channels = splitedURL[1].split('/');
        if (channels.length > 0) {
          channel = channels[0];
        } else {
          channel = settings.TINYVIEW_CHANNEL_NAME;
        }
      }
    } else {
      channel = settings.TINYVIEW_CHANNEL_NAME;
    }

    return channel;
  }

  static getUserVisibleChannelName(url, seriesID = null) { // returns In Science We Trust 
    var channelName = seriesID
    if (!channelName) {
      channelName = Utils.getChannelName(url)
    }

    const seriesName = SessionManager.instance.getSeriesName(channelName)
    if (seriesName) {
      return seriesName;
    }

    if (channelName) {
      if (channelName.includes("-")) {
        const parts = channelName.split("-")
        for (const key in parts) {
          const element = parts[key];
          parts[key] = Utils.capitalize(element);
        }
        channelName = parts.join(" ")
      } else {
        channelName = Utils.capitalize(channelName);
      }
    }
    return channelName;
  }

  static getWebURL(url) {
    const webBaseURL = settings.webBaseURL + "/";
    const baseURL = settings.apiBaseURL + "/"
    let webURL = url.replace('/index.json', '/');
    webURL = webURL.replace(baseURL, webBaseURL);
    var lastChar = webURL[webURL.length - 1];
    if (lastChar == "/") {
      webURL = webURL.substring(0, webURL.length - 1);
    }

    // var splitedURL = url.split(baseURL)
    // if (splitedURL.length > 1) {
    //   if (splitedURL[1] == 'index.json') {
    //     webURL = webBaseURL;
    //   } else {
    //     var channels = splitedURL[1]
    //     if (channels.includes('/index.json')) {
    //       channels = channels.replace('/index.json', '');
    //     }
    //     webURL = webBaseURL + channels;
    //   }           
    // } else {
    //   webURL = webBaseURL;
    // }

    Utils.log('Derived Web URL: ' + webURL + ' from path url ' + url);
    return webURL;
  }

  static getWebChannelURL(url) {
    const channelURL = Utils.getChannelName(url);
    let webURL = settings.webBaseURL + "/";
    if (channelURL != settings.TINYVIEW_CHANNEL_NAME) {
      webURL = webURL + channelURL;
    }

    const lastChar = webURL[webURL.length - 1];
    if (lastChar == "/") {
      webURL = webURL.substring(0, webURL.length - 1);
    }

    webURL = this.formatDamainURL(webURL);

    Utils.log('Derived Web URL: ' + webURL + ' from path url ' + url);
    return webURL;
  }

  static showError = (error, title = 'Error') => {
    if (!error) {
      return
    }

    if (typeof (error) == 'string') {
      Alert.alert(title, error);
    } else if (error.message) {
      Alert.alert(title, error.message);
    } else if (error.customMsg) {
      Alert.alert(title, error.customMsg);
    }
  }

  static showWebShareLink = (link) => {
    Alert.alert("", "We are having trouble reaching our servers possibly because you are using an ad blocker. Gift links or influence points won’t work.", [{
      "text": "Continue Sharing", onPress: () => {
        Share.open({ "url": link })
      }
    }, { "text": "Cancel" }])
  }

  static showFlagCommentAlert = () => {
    Alert.alert(
      "Report submitted",
      "Thank you for reporting this comment",
      [{ "text": "OK" }]
    );
  }

  static showBlockUserAlert = (message) => {
    Alert.alert(
      "Blocked",
      message,
      [{ "text": "OK" }]
    );
  }

  static vibrateDevice(timeInMS = 100) {
    if (Platform.OS == "android") {
      Vibration.vibrate(timeInMS)
    } else {
      import('react-native-haptic-feedback').then((hapticEfect) => {
        hapticEfect.trigger("impactLight");
      })
    }
  }

  static formatViewCount = (count) => {
    if (count >= 10000) {
      // Nine Zeroes for Billions
      let absCountNumber = Math.abs(Number(count)) >= 1.0e+9
        ? (Math.abs(Number(count)) / 1.0e+9).toFixed(2) + "B"
        // Six Zeroes for Millions 
        : Math.abs(Number(count)) >= 1.0e+6
          ? (Math.abs(Number(count)) / 1.0e+6).toFixed(2) + "M"
          // Three Zeroes for Thousands
          : (Math.abs(Number(count)) / 1.0e+3).toFixed(1) + "K"

      absCountNumber = absCountNumber.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
      return absCountNumber;
    }

    return count != undefined && count != null ? count.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,') : null
  }

  static formatNumber = (value) => {
    return value.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
  }

  static isSubsProduct = (proId) => {
    if (!proId) {
      return false
    }
    const productId = proId.toLowerCase();
    return productId.includes('monthly') || productId.includes('yearly') || productId.includes('subscription')
  }

  static isGiftProduct = (proId) => {
    if (!proId) {
      return false
    }
    const productId = proId.toLowerCase();
    return productId.includes('gifts')
  }

  static isMonthlySubsProd = (product) => {
    let isMothly = false;
    if (Platform.OS == 'ios') {
      if (product.subscriptionPeriodUnitIOS) {
        isMothly = product.subscriptionPeriodUnitIOS.toLowerCase().includes("month")
      }
    } else {
      const prodInfo = product.billingPeriod ? product.billingPeriod : product.subscriptionPeriodAndroid
      if (prodInfo) {
        isMothly = prodInfo.toLowerCase().includes("p1m")
      } else {

      }
    }

    return isMothly;
  }

  static isYearlySubsProd = (product) => {
    let isYearly = false;
    if (Platform.OS == 'ios') {
      if (product.subscriptionPeriodUnitIOS) {
        isYearly = product.subscriptionPeriodUnitIOS.toLowerCase().includes("year")
      }
    } else {
      if (product.subscriptionPeriodAndroid) {
        isYearly = product.subscriptionPeriodAndroid.toLowerCase().includes("p1y")
      }
    }

    return isYearly;
  }

  static isPremiumComic = (comicData) => {
    return comicData && comicData['show-to'] && (comicData['show-to'] == Constants.SUBSCRIBERS_ONLY)
  }

  static getPanelURL(panelName) {
    return settings.apiBaseURL + '/tinyview/app/' + panelName // + '?mockParam=' + Date.now()
  }

  static excludeHTML(htmlText) {
    return htmlText ? htmlText.replace(/(<([^>]+)>)/ig, "") : '';
  }

  static capitalize(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  static getDayDifferences(firstDate, secondDate) {
    // Take the difference between the dates and divide by milliseconds per day.
    // Round to nearest whole number to deal with DST.
    return Math.round((secondDate - firstDate) / (DAY_MILLIS));
  }

  static async showInAppReview() {
    try {
      if (settings.isDebugMode) {
        return;
      }
      let lastDate = await SharedPreferences.getData(settings.IN_APP_REVIEW_DATE_KEY);
      let isCompletedAMonth = false;
      if (lastDate) {
        isCompletedAMonth = Utils.getDayDifferences(new Date(lastDate), new Date()) > 30
      }
      if (InAppReview.isAvailable() && (Platform.OS == 'ios' || !lastDate || isCompletedAMonth)) {
        await SharedPreferences.saveData(settings.IN_APP_REVIEW_DATE_KEY, new Date().toString())
        InAppReview.RequestInAppReview();
      }
    } catch (error) {
      Utils.log("Error in showInAppReview " + error);
    }
  }

  static getFollowingSeries = (followingChannels) => { // It excluded Tinyview Channel
    const channelURLS = []
    for (const key in followingChannels) {
      const element = Utils.getChannelName(Utils.resolvePath(settings.getComicHomeURL(), followingChannels[key]));
      if (!Utils.isComicURL(followingChannels[key]) && element != settings.TINYVIEW_CHANNEL_NAME) {
        channelURLS.push(followingChannels[key])
      }
    }

    return channelURLS;
  }

  static isFollowedComicChannel = (seriesName, followingChannels = []) => {
    let isFollowing = false

    const channelURLS = Utils.getFollowingSeries(followingChannels);

    for (const key in channelURLS) {
      const element = Utils.getChannelName(Utils.resolvePath(settings.getComicHomeURL(), channelURLS[key]));
      isFollowing = seriesName.includes(element)
      if (isFollowing) {
        break;
      }
    }

    return isFollowing;
  }

  static shouldIncludeHomePanel = (seriesName, followingChannels) => {
    return this.isFollowedComicChannel(seriesName, followingChannels);
  }

  static getChannelURL = (channelName) => {
    let url = null
    if (!channelName) { return url }

    if (channelName.startsWith('/')) {
      url = channelName + "/index.json"
    } else {
      url = "/" + channelName + "/index.json"
    }

    return url;
  }

  static isEmptyObject = (obj) => {
    return !obj || Object.keys(obj).length <= 0
  }

  static isNotFollowingAnySeries = (obj) => {
    return !obj || Object.keys(obj).length <= 0 || (Object.keys(obj).length == 1 && Object.keys(obj)[0].toLowerCase() == settings.TINYVIEW_CHANNEL_NAME)
  }

  static getRandomColor = () => {
    return 'rgb(' + (Math.floor(Math.random() * 200)) + ',' + (Math.floor(Math.random() * 200)) + ',' + (Math.floor(Math.random() * 200)) + ')';
  }

  static checkIsPrivilegeUser = (pathUrl) => {
    const channelName = Utils.getChannelName(pathUrl)
    const isPrivilegeUser = SessionManager.instance.isAdminUser(channelName)
    return isPrivilegeUser
  }

  static checkIsTinyviewAdminUser = () => {
    const isTinyviewAdmin = SessionManager.instance.isTinyviewAdmin()
    return isTinyviewAdmin
  }

  static checkIsSeriesAdminUser = (pathUrl) => {
    let isSeriesAdmin = false
    if (pathUrl) {
      const channelName = Utils.getChannelName(pathUrl)
      isSeriesAdmin = SessionManager.instance.isSeriesAdmin(channelName)
    } else {
      isSeriesAdmin = SessionManager.instance.isAnySeriesAdmin()
    }

    return isSeriesAdmin
  }

  static isSubscriberAndNotSeriesAdmin = (pathUrl = null) => {
    return SessionManager.instance.hasAnySubscriptionPurchase() && !this.checkIsSeriesAdminUser(pathUrl)
  }

  static remainingSeriesQuota = (comicReadCount, seriesName) => {
    if (comicReadCount === null || comicReadCount === "") {
      return 0;
    }

    const freeComics = SessionManager.instance.getSeriesFreeEpisodes(seriesName);
    const remainingQuota = freeComics - comicReadCount;

    return remainingQuota > 0 ? remainingQuota : 0;
  }

  static isComicAccessibleWithinTime = (dateTime, seriesName) => {
    if (!dateTime) {
      return false
    }

    const freeComicsHours = SessionManager.instance.getSeriesFreeHours(seriesName);
    const timeDifference = Math.abs(Date.now() - new Date(dateTime).getTime());
    return timeDifference <= freeComicsHours * 60 * 60 * 1000;
  }

  static checkPurchaseStatus(inAppProducts) {
    const cachedPurchases = SessionManager.instance.getPurchasedItems();
    let hasBought = false;

    if (!cachedPurchases || cachedPurchases.length == 0) {
      return hasBought
    }
    if (!inAppProducts || inAppProducts.length == 0) {
      return true
    }

    for (const key in cachedPurchases) {
      const purchase = cachedPurchases[key];
      for (const inAppProductID in inAppProducts) {
        if (purchase == inAppProductID) {
          hasBought = true;
          break;
        }
      }
    }
    return hasBought;
  }

  static getComicNavigationPanel = () => {
    return {
      "md5sum": "64bed4e81805fe45780e2e81a42edc33",
      "template": "navigation"
    }
  }

  static getLastComicInfoPanel = () => {
    return {
      "md5sum": "cb17f387e68d09b697a7c305348a796a",
      "lastComicPanel": true,
    }
  }

  static getInfluenceImagePanel = () => {
    return {
      "image": "/tinyview/influence-points/invite-and-share.jpg",
      "width": 800,
      "height": 600,
      "md5sum": "35d30886bfe63e0a7d32f8d6415b7edd",
      "border": {
        "border-color": "#000000",
        "border-width": 2,
        "border-top": true,
        "border-bottom": true,
        "border-left": true,
        "border-right": true
      }
    }
  }

  static getNotFollowSeriesPanel = () => {
    return {
      "description": "When you follow one or more series, we will show you their latest episodes here.",
      "button": "All Series",
      "action": settings.DIRECTORY_COMIC_END_POINT_URL,
      "md5sum": "098765432111111",
      "isEmptyFollowingTabPanel": true
    }
  }

  static getTinyviewSubscriptionPanel = () => {
    return {
      title: Constants.SUBSCRIBERS_COMIC_TITLE,
      image: "/tinyview/app/tinyview-premium-comic-with-button.jpg",
      height: 1200,
      width: 1600,
      md5sum: 'bcd4dc10fa7d6asde5c86d65f93f9008',
      action: Constants.UNLOCK_PREMIUM_COMIC,
      actionType: "tinyview"
    }
  }

  static getTinyviewUnlimitedReadPanel = () => {
    return {
      image: "/tinyview/app/tinyview-free-comics-limit-with-button.jpg",
      height: 1200,
      width: 1600,
      md5sum: '517c0436f97c3efa99arer3439ba16883e',
      action: Constants.GET_UNLIMITED_ACCESS,
      actionType: "tinyview"
    }
  }

  static getTinyviewBonusPanel = () => {
    return {
      image: "/tinyview/app/bonus-panel-with-button.jpg",
      height: 600,
      width: 800,
      md5sum: '48bb7584835625264trtre41340fb4543453',
      template: 'bonus',
      action: Constants.UNLOCK_BONUS_PANEL,
      actionType: "tinyview"
    }
  }

  static navigateToInfluencePage = (props, params = {}) => {
    if (props) {
      let routeName = Constants.MY_INFLUENCE_COMPONENT
      props.navigation.push(routeName)
    } else {
      Utils.navigateToSubRouteWithParams('DrawerHome', Constants.MY_INFLUENCE_COMPONENT, null)
    }
  }

  static navigateToDrawerLoginRoute = (props, routeName, params = {}) => {
    if (props && routeName) {
      props.navigation.navigate('DrawerLogin', {
        screen: routeName,
        params: params
      }
      )
    } else {
      NavigationService.navigateToSubRoute('DrawerLogin', null, routeName, params)
    }
  }

  static navigateToDeleteAccountRoute = (props, routeName, params = {}) => {
    if (props && routeName) {
      props.navigation.navigate('AccountDeletedNavigator', {},
        CommonActions.navigate({
          name: routeName,
          params: params
        })
      )
    } else {
      NavigationService.navigate('AccountDeletedNavigator', params)
    }
  }

  static pushToDrawerLoginRoute = (props, routeName, params = {}) => {
    if (props && routeName) {
      props.navigation.push('DrawerLogin', {},
        CommonActions.navigate({
          name: routeName,
          params: params
        })
      )
    }
  }

  static navigateToSubRouteWithParams = (parentRoute, subRoute, props = null, subParams = {}, parentParams = {}) => {
    if (props) {
      props.navigation.navigate(
        parentRoute,
        {
          screen: subRoute,
          params: subParams,
        }
      )
    } else {
      NavigationService.navigateToSubRoute(parentRoute, parentParams, subRoute, subParams)
    }
  }

  static navigateToManageAlertsPage = (props = null, subParams = {}, delayTime = 0) => {
    setTimeout(() => {
      Utils.navigateToSubRouteWithParams(Constants.DRAWER_HOME, Constants.MANAGE_ALERTS_SCREEN, props, subParams)
    }, delayTime)
  }

  static navigateToReader = (subParams = {}, delayTime = 0) => {
    setTimeout(() => {
      NavigationService.navigateToReader(subParams)
    }, delayTime)
  }

  static navigateToFriendRequests = (props, params = {}) => {
    if (props) {
      props.navigation.navigate(
        "DrawerHome",
        {},
        CommonActions.navigate({
          name: 'FriendsComponent',
          params: { componentToRender: Constants.REQUESTS, ...params },
        })
      )
    } else {
      NavigationService.navigateToSubRoute("DrawerHome", null, 'FriendsComponent', { componentToRender: Constants.REQUESTS, ...params })
    }
  }

  static navigateToSeeFriendsPage = (props, params = {}) => {
    if (props) {
      props.navigation.navigate(
        "DrawerHome",
        {},
        CommonActions.navigate({
          name: 'FriendsComponent',
          params: { componentToRender: Constants.FRIENDS, ...params },
        })
      )
    } else {
      NavigationService.navigateToSubRoute("DrawerHome", null, 'FriendsComponent', { componentToRender: Constants.FRIENDS, ...params })
    }
  }

  static navigateToInviteFriends = (props, params = {}) => {
    if (props) {
      props.navigation.navigate(
        "DrawerHome",
        {},
        CommonActions.navigate({
          name: 'FriendsComponent',
          params: { componentToRender: 'Add Friends', ...params },
        })
      )
    } else {
      NavigationService.navigateToSubRoute("DrawerHome", null, 'FriendsComponent', { componentToRender: 'Add Friends', ...params })
    }
  }

  static log(...message) {
    if (__DEV__) {
      console.log(JSON.stringify(message))
    }
  }

  static getParsedData(dataInJson) {
    let parsedData = []
    try {
      parsedData = dataInJson ? JSON.parse(dataInJson) : null
      return parsedData
    } catch (e) {
      Utils.log("Error in getParsedData", e)
      return parsedData
    }
  }

  static isValidURL(value) {
    var expression = /^((https?):\/\/)?([w|W]{3}\.)+[a-zA-Z0-9\-\.]{3,}\.[a-zA-Z]{2,}(\.[a-zA-Z]{2,})?$/
    var regex = new RegExp(expression);
    return value.match(regex)
  }

  static isInvalidSocialId(value) {
    let isValid = value.indexOf(' ') >= 0 || value.startsWith("/") || value.includes('www') || value.includes('https') || value.includes('http') || value.includes('.com')
    return isValid
  }

  static notANumericExpr(value) {
    let expression = /[^0-9]/
    var regex = new RegExp(expression)
    return value.match(regex)
  }

  static containsCurlyBrackets(value) {
    let expression = /{(\w+)}/
    var regex = new RegExp(expression)
    return value.match(regex)
  }

  static getReplyMsgForCount(comments) {
    if (comments && comments.length) {
      return comments.length == 1 ? "1 Reply" : comments.length + " Replies"
    }
    return ""
  }

  static copyToClipboard(msg, toastMsg = "Message copied") {
    Clipboard.setString(msg);
    Utils.showToast(toastMsg)
  }

  static showToast(msg, position = "bottom", id = null, visibleTime = null, title = null) {
    Toast.show({
      render: () => {
        return <ToastMessage message={msg} title={title} />
      }, duration: visibleTime ? visibleTime : Constants.TOAST_DURATION, placement: position, id: id
    })
  }

  static getStoryPageTitle(response) {
    let title = null
    if (response && response.user && response.user.name) {
      title = response.user.name
    }

    if (title == null && this.checkData(response) && this.checkData(response.action)) {
      title = this.getUserVisibleChannelName(`${settings.apiBaseURL}${response.action}`)
    }

    if (title == null) {
      return ""
    }

    return title + "'s" + " Post"
  }

  static openStore() {
    var storeURL = null
    if (Platform.OS == 'android') {
      storeURL = "market://details?id=" + settings.APP_PACKAGE_ID
    } else {
      storeURL = "itms-apps://itunes.apple.com/app/tinyview-comics/id1478702420?mt=8"
    }

    Linking.canOpenURL(storeURL).then((canOpen) => {
      if (canOpen) {
        Linking.openURL(storeURL);
      }
    }).catch((error) => {
      Utils.log("Error in openStore")
    })
  }

  static isComicPanel = (panel) => {
    return (panel.action && (!panel.actionType || panel.actionType != "website")) ? true : false
  }

  static capitalizeFirstLetter(str) {
    if (!str) {
      return ""
    }
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  static getValidPanels(panels, panelLength, isFollowedSeries = true, pathUrl, isSharedByPremiumUser = false) {
    var pannelInfo = []
    const purchasedSubID = SessionManager.instance.getTopLevelPurchasedSubscription();
    panelLength = Utils.checkObject(panelLength) ? panelLength : panels.length
    //const isAnyAlertEnabled = UserSession.instance.isAnyUserAlertsEnabled()

    for (var i = 0; i < panelLength; i++) {
      if ((panels && panels[i] != null)) {
        let panelItem = panels[i];
        if (panelItem.action == "follow" && panelItem.actionType == "tinyview" && isFollowedSeries) {
          continue
        }

        if (panelItem.action) {
          if (panelItem.action == Constants.IN_APP_PURCHASE) {
            SessionManager.instance.updateSubscriptionItems(panelItem)
          }
        }

        let shouldAddPanel = true;

        if (panelItem.inAppProducts) {
          for (const key in panelItem.inAppProducts) {
            if (panelItem.inAppProducts.hasOwnProperty(key)) {
              const reqObject = panelItem.inAppProducts[key];
              const requirePurchaseKey = Object.keys(reqObject)[0];
              const needsPurchase = reqObject[requirePurchaseKey];
              shouldAddPanel = false;
              if (needsPurchase && purchasedSubID == requirePurchaseKey) {
                shouldAddPanel = true;
                break
              } else if (!needsPurchase && purchasedSubID != requirePurchaseKey) {
                shouldAddPanel = true;
              } else if (!needsPurchase && purchasedSubID == requirePurchaseKey) {
                shouldAddPanel = false
                break
              }
            }
          }
        }
        if (panelItem.template && panelItem.template == "bonus") {
          shouldAddPanel = true;
          let isComicUnlocked = false
          let comicUrl = Utils.getMeaningFullURL(pathUrl)
          isComicUnlocked = UserSession.instance.isComicUnlocked(comicUrl)
          if (!SessionManager.instance.hasAnySubscriptionPurchase() && !isComicUnlocked && !isSharedByPremiumUser) {
            const purchaseBonusPanel = Utils.getTinyviewBonusPanel()
            panelItem = { ...panelItem, ...purchaseBonusPanel }
          }

          panelItem = { ...panelItem, title: "Bonus Panel for Premium Users" }
        }

        if (panelItem.action == "inviteFriends" && UserSession.instance.getMonthlyMaxComicReadQuota() < 0) {
          shouldAddPanel = false
        }

        if (panelItem.template == "carousel" && (Utils.isDirectoryPageURL(pathUrl) || Utils.isSubscriptionURL(pathUrl) || Utils.isTVSeriesURL(pathUrl))) {
          shouldAddPanel = false
        }

        if (shouldAddPanel) {
          pannelInfo.push({
            ...panelItem
          })
        }
      }
    }
    return pannelInfo
  }

  static getPanelLength = (comicData) => {
    let panelLength = 0
    if (comicData && comicData.panels) {
      if (Utils.checkObject(comicData.previewPanels)) {
        panelLength = comicData.previewPanels
      } else if (comicData.panels.length < 2) { // For below 2 panels does not show anything.
        panelLength = 0
      } else if (comicData.panels.length >= 2 && comicData.panels.length < 5) { //between 2 to 4 panels -> 1.
        panelLength = 1
      } else if (comicData.panels.length >= 5) { //For 5 and above panels -> around 25% of comic panels will be shown.
        panelLength = (comicData.panels.length / 4) + 1
      }
    }

    return panelLength
  }

  static isUserEligibleToComment = () => {
    let isPurchasedSubsGift = UserSession.instance.getCurrentUserBadges()
    if ((isPurchasedSubsGift == null || isPurchasedSubsGift.length == 0) && !UserSession.instance.isLoggedInUser()) {
      let sheetMessage = 'You must be a premium user to comment.'
      let sheetOptions = [Constants.UPGRADE_TO_A_PREMIUM_PLAN, Constants.GOT_IT]
      return { sheetMessage, sheetOptions }
    } else if (isPurchasedSubsGift.length > 0 && !UserSession.instance.isLoggedInUser()) {
      let sheetMessage = 'You must be signed in to comment.'
      let sheetOptions = [Constants.SIGN_IN, Constants.GOT_IT]
      return { sheetMessage, sheetOptions }
    } else if (UserSession.instance.isUserDetailsEmpty()) {
      let sheetMessage = 'You need to complete your profile first.'
      let sheetOptions = [Constants.EDIT_PROFILE, Constants.CANCEL]
      return { sheetMessage, sheetOptions }
    } else if (!isPurchasedSubsGift.length > 0 && UserSession.instance.isLoggedInUser()) {
      let sheetMessage = 'You must be a premium user to comment.'
      let sheetOptions = [Constants.UPGRADE_TO_A_PREMIUM_PLAN, Constants.GOT_IT]
      return { sheetMessage, sheetOptions }
    }
    return null
  }

  static getSeriesSpecificStyle(series) {
    var seriesStyle = SessionManager.instance.getSeriesStyle(series)
    if (!seriesStyle) {
      seriesStyle = directory[series].style
    }
    return seriesStyle
  }

  static getUserCountryCode(phoneNumber, indexLength = 1) {
    let countryData = null
    const userNumber = phoneNumber.replace("+", "")
    countryData = this.findCountryCode(phoneNumber, userNumber.substring(0, indexLength))

    if (!countryData) {
      return this.getUserCountryCode(phoneNumber, indexLength + 1)
    } else {
      let code = countryData.countryCode
      if (countryData.countryCallingCode == "1") {
        code = "US"
      }
      return { countryCallingCode: "+" + countryData.countryCallingCode, countryCode: code }
    }
  }

  static findCountryCode(phoneNumber, code) {
    let countryCallingCode = null
    let countryCode = null
    for (const country of dataCountry) {
      if (country.callingCode == code) {
        if (!phoneNumber.includes("-") || code.includes("-")) {
          countryCallingCode = country.callingCode
          countryCode = country.code
          break
        }
      }
    }
    if (countryCallingCode && countryCode) {
      return { countryCallingCode, countryCode }
    } else {
      return null
    }
  }

  static async shouldShowOptionalAlert() {
    const showOptionalAlertTimestampKey = settings.SHOW_OPTIONAL_UPDATE_ALERT_TIMESTAMP_AT
    const showOptionalAlertDisplayedTime = await SharedPreferences.getData(showOptionalAlertTimestampKey)
    const currentTimeInSeconds = Math.floor((new Date().getTime()) / 1000)
    if (showOptionalAlertDisplayedTime) {
      const nextValidAlertTime = parseInt(showOptionalAlertDisplayedTime) + (7 * 24 * 60 * 60); // One week in seconds
      if (currentTimeInSeconds < nextValidAlertTime) {
        return false;
      }
    }
    SharedPreferences.saveData(showOptionalAlertTimestampKey, currentTimeInSeconds.toString());
    return true;
  }

  static isGuestUserProfile(data) {
    if (Utils.checkObject(data)) {
      const { userName, phoneNumber, email } = data
      if ((!Utils.checkData(userName) || userName == Constants.NON_SIGNED_IN_USER) && !Utils.checkData(phoneNumber) && !Utils.checkData(email)) {
        return true
      }
      return false
    }
    return false
  }

}