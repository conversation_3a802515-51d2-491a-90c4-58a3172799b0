import VersionInfo from 'react-native-version-info';
import BuildConfig from 'react-native-build-config'
import FirebaseManager from './FirebaseManager';
import { Platform } from 'react-native';

let isDev = BuildConfig.DEVELOPMENT == true || BuildConfig.APP_ENVIRONMENT === "DEVELOPMENT"

const IAP_PURCHASE_MODE_KEY = 'iap_mode_key';
const OTP_SCREEN = 'OtpScreen'
const EDIT_PROFILE_SCREEN = 'EditProfileScreen'
const DOB_SCREEN = "DOBScreen"
const LOGIN_SCREEN = 'LoginScreen'
const USER_PROFILE_SCREEN = 'UserProfileScreen'
const EDIT_SOCIAL_LINKS = "EditSocialLinks"
const DRAWER_LOGIN_NAVIGATOR = "DrawerLogin"
const INSTAGRAM_WEB = "instagram.com"
const FACEBOOK_WEB = "facebook.com"
const TWITTER_WEB = "twitter.com"

let imageBaseURL = "https://assets.tinyview.com"
let apiBaseURL = 'https://cdn.tinyview.com';
let cloudFunctionBaseURL = 'https://us-central1-tinyview-d78fb.cloudfunctions.net'
let webBaseURL = 'https://tinyview.com';
let supportEmail = '<EMAIL>';
let manageSubscriptionsUrl = Platform.OS === 'ios' ? 'https://apps.apple.com/account/subscriptions' : 'https://play.google.com/store/account/subscriptions'
let termsAndConditionUrl = 'https://tinyview.com/terms-conditions'
let privacyPolicyUrl = 'https://tinyview.com/privacy-policy'
let subscribeURL = '/tinyview/subscribe/index.json'
let appName = "Tinyview"
let notifChannelId = "tinyview"
let SWITCH_SENDBOX_MODE_TIME = 3500;
let TINYVIEW_TITLE = "TINYVIEW";
let TINYVIEW_CHANNEL_NAME = "tinyview";
let APP_PACKAGE_ID = "com.newput.tinyview"
let redirectionTimeToWait = 500
let emailFallbackURL = "https://social.tinyview.com/email-link-login"
let tinyviewShopURL = "https://shop.tinyview.com"
let isAndroidDevice = Platform.OS == "android"
let isAndroid14OrBelowDevice = isAndroidDevice && Platform.Version <= 34;

if (isDev) {
  imageBaseURL = "https://assets-dev.tinyview.com"
  apiBaseURL = 'https://cdn-dev.tinyview.com'; //'https://cdn.tinyview.com'; 
  cloudFunctionBaseURL = 'https://us-central1-tinyview-dev.cloudfunctions.net'
  webBaseURL = 'https://dev.tinyview.com';
  supportEmail = '<EMAIL>';
  appName = "TinyviewDev"
  notifChannelId = "tinyview-dev"
  emailFallbackURL = "https://tinyview.test-app.link/email-link-login"

  if (Platform.OS == "android") {
    APP_PACKAGE_ID = "com.newput.tinyviewdev"
  }
}

function getComicHomeURL() {
  let endPoint = FirebaseManager.instance.getHomeComicURL() ? FirebaseManager.instance.getHomeComicURL() : "/index.json"
  return apiBaseURL + endPoint;
}

function getComicFeedHomeURL() {
  let endPoint = "/newsfeed.json"
  return apiBaseURL + endPoint;
}

// function getISWTURL() {
//   let endPoint = FirebaseManager.instance.getISWTURL() ? FirebaseManager.instance.getISWTURL() : '/in-science-we-trust/index.json'
//   return apiBaseURL + endPoint
// }

function getInfluencePageURL() {
  return "/tinyview/influence-points/index.json"
}

function getAppVersionsURL() {
  return "/tinyview/update/versions.json"
}

function getWhatsTinyviewURL() {
  let endPoint = FirebaseManager.instance.getWhatsTinyviewURL() ? FirebaseManager.instance.getWhatsTinyviewURL() : 'tinyview/index.json'
  return apiBaseURL + endPoint
}

function getSubscribeURL() {
  return apiBaseURL + subscribeURL
}

const claimInfluenceEmailTitle = 'Claiming referral bonus'
const claimInfluenceEmailDescription = 'Hey there!\n \nI had referred you to Tinyview Comics app earlier. Somehow I didn’t get the credit for that referral. Can you please click on the following link so I can get the referral bonus (influence points)? This will help me get one premium comic for free.\n \nHere is the link:'
const SET_REFERRAL = "SET_REFERRAL"

const INFLUENCE_POINTS_ACTIONS = {
  INSTALL_APP: "INSTALL_APP",
  SHARED_LINK_REFERRER: "REFERRER",
}

const settings = {
  env: process.env.NODE_ENV,
  isProduction: process.env.NODE_ENV === 'production',
  isDevBuild: isDev,
  appName,
  domainName: 'tinyview.com',
  apiBaseURL,
  cloudFunctionBaseURL,
  webBaseURL,
  getComicHomeURL,
  // getISWTURL,
  getWhatsTinyviewURL,
  getSubscribeURL,
  getComicFeedHomeURL,
  getInfluencePageURL,
  getAppVersionsURL,
  OTP_SCREEN,
  homePath: "/index.json",
  SUBSCRIBE_END_URL: subscribeURL,
  DIRECTORY_COMIC_URL: apiBaseURL + "/tinyview/comic-series-directory/index.json",
  DIRECTORY_DETAILS_URL: apiBaseURL + "/tinyview/comic-series-directory/directory.json",
  DIRECTORY_COMIC_END_POINT_URL: "/tinyview/comic-series-directory/index.json",
  UPDATE_COMIC_END_POINT_URL: '/tinyview/update/index.json',
  userCoverImageURL: apiBaseURL + '/tinyview/app/user-profile-default-cover.jpg',
  stylingJSONEndPoint: "/tinyview/styles.json",
  fileExpireTime: 1 * 60, //IN MINUTES
  feedResetTime: 1 * 60, //IN MINUTES
  profilePicExpireTime: 24 * 60,
  isDebugMode: __DEV__,
  INITIAL_INTRO_LOAD_KEY: 'introduction_key',
  SHOW_NOTIFICATION_PERMISSION_ALERT_KEY: 'show_notification_permission_alert',
  APP_STORE_PURCHASES_ID_KEY: 'app_store_purchase_key',
  APP_LAUNCH_COUNT_KEY: 'app_launch_count_key',
  IN_APP_REVIEW_DATE_KEY: 'in_app_review_date_key',
  USER_LIKE_COMIC_KEY: 'user_like_comic_key',
  IS_SHOW_FOLLOWED_COMIC: 'is_show_followed_comic_key',
  USER_CONTACTS_KEY: 'user_contacts_key',
  SHOW_ALL_COMICS_KEY: 'show_all_comics_key',
  SP_COUNTRY_CODE_KEY: 'sp_country_code_key',
  INFLUENCE_POINTS_SP_KEY: 'influence_points_sp_key',
  LAST_VISITED_NOTIFICATION: 'LastVisitedNotification',
  SIGNED_IN_USER_DATA: 'SIGNED_IN_USER_DATA',
  SHOW_OPTIONAL_UPDATE_ALERT_TIMESTAMP_AT: 'show_optional_update_alert_timestamp_at',
  IAP_PURCHASE_MODE_KEY,
  SWITCH_SENDBOX_MODE_TIME,
  supportEmail,
  TINYVIEW_TITLE,
  TINYVIEW_CHANNEL_NAME,
  manageSubscriptionsUrl,
  termsAndConditionUrl,
  privacyPolicyUrl,
  claimInfluenceEmailTitle,
  claimInfluenceEmailDescription,
  SET_REFERRAL,
  appVersion: VersionInfo.appVersion,
  buildVersion: VersionInfo.buildVersion,
  LATEST_COMICS: 'Latest Comics',
  DRAWER_WIDTH: 280,
  notifChannelId,
  forwardIconText: "»",
  REDIRECTION_TIME_TO_WAIT: redirectionTimeToWait,
  RNFS_PROFILE_IMAGE_URL: 'rnfsProfileImageUrl',
  SHARED_PREF_PROFILE_DETAILS: 'sharedPrefProfileDetails',
  RECEIVED: 'Received',
  SENT: 'Sent',
  EDIT_PROFILE_SCREEN,
  DOB_SCREEN,
  LOGIN_SCREEN,
  USER_PROFILE_SCREEN,
  DRAWER_LOGIN_NAVIGATOR,
  EDIT_SOCIAL_LINKS,
  INSTAGRAM_WEB,
  FACEBOOK_WEB,
  TWITTER_WEB,
  APP_PACKAGE_ID,
  INFLUENCE_POINTS_ACTIONS,
  PROGRESS_VIEW_OFFSET: 0,
  IMAGE_BASE_URL: imageBaseURL,  
  emailFallbackURL,
  tinyviewShopURL,
  isAndroidDevice,
  isAndroid14OrBelowDevice
};


export { settings }