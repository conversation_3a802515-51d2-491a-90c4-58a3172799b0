import React, { Component } from "react";
import { Text, TextInput, View, StyleSheet, Platform } from "react-native";
import { SystemFont } from './Typography'
import { scale } from 'react-native-size-matters'
import { Color } from "./Color";

export default class TextInputComponent extends Component {
  constructor(props) {
    super(props);
  }

  render() {
    const { inputTextLabel, errorText, refState, value, keyboardType, secureTextEntry, placeHolder, textColor,
      textInputborderBottomColor, maxLength, autoFocus, selectionColor, inputRef, placeholderTextColor,
      multiline, textInputStyle, fontSize, autoCapitalize, inputTextLabelStyle, returnKeyType, editable = true, textAlignVertical, autoCorrect } = this.props

      const autoCorrectValue = autoCorrect != null ? autoCorrect : true
    return (
      <View style={{ flex: 1 }}>
        {inputTextLabel && <Text style={[errorText != "" && errorText != null ? styles.errorTextStyle : styles.labelTextStyle, inputTextLabelStyle]}>{inputTextLabel}</Text>}
        <TextInput
          multiline={multiline ? multiline : false}
          selectionColor={selectionColor}
          ref={(r) => { inputRef && inputRef(r) }}
          editable={editable}
          autoFocus={autoFocus}
          secureTextEntry={secureTextEntry != "" && secureTextEntry != null ? secureTextEntry : false}
          style={[styles.rectangleTextInput, textInputStyle]}
          onChangeText={(text) => this.props.onChange(refState, text)}
          onBlur={() => this.props.onBlur ? this.props.onBlur(refState) : null}
          onFocus={() => this.props.onFocus ? this.props.onFocus(refState) : null}
          value={value}
          color={textColor}
          returnKeyType={returnKeyType || 'done'}
          fontSize={fontSize ? fontSize : 22}
          placeholder={placeHolder}
          fontFamily={SystemFont.SELECTED_FONT}
          maxLength={maxLength != null ? maxLength : 30}
          placeholderTextColor={placeholderTextColor}
          keyboardType={keyboardType != null ? keyboardType : "default"}
          textAlignVertical={textAlignVertical ? textAlignVertical : "top"}
          autoCorrect={autoCorrectValue}
          autoCapitalize={autoCapitalize || 'sentences'}
          onSubmitEditing={() => this.props.onSubmitEditing && this.props.onSubmitEditing()}        
        />
        {errorText != "" && errorText != null ? <Text style={styles.errorTextStyle}>{errorText}</Text> : null}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  rectangleTextInput: {
    width: "100%",
    height: scale(50),
    paddingStart: 5,
  },
  labelTextStyle: {
    marginLeft: scale(5),
    marginBottom: 5,
    marginTop: 4,
    color: Color.BLACK_COLOR,
    textAlign: 'left',
    fontSize: Platform.OS == 'ios' ? scale(13) : scale(12),
  },
  errorTextStyle: {
    marginLeft: scale(5),
    marginBottom: 2,
    marginTop: scale(-5),
    color: 'red',
    fontFamily: SystemFont.SELECTED_FONT,
    textAlign: 'left',
    fontSize: Platform.OS == 'ios' ? scale(13) : scale(12),
  },
});
