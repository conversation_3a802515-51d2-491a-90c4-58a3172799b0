import branch from 'react-native-branch'
import store from '../redux/store'
import { Utils } from './Utils';
import FirebaseManager from './FirebaseManager';
import { settings } from './settings';
import SessionManager from './SessionManager';
import NavigationService from './NavigationService';
import NetworkUtils from './NetworkUtils';
import { Ajax } from '../api/ajax'
import { Constants } from './Constants';
import UserSession from './UserSession';
import { Linking } from 'react-native';
import T from '../redux/actions/types'

export default class DeepLinkManager {
  static instance = new DeepLinkManager()

  constructor() {
    this.referrerId = null;
    this.branchParams = null
    this.appLastState = null

    this.handleBranchEvent = this.handleBranchEvent.bind(this)
    this.onSubscribe = this.onSubscribe.bind(this)
  }

  async onSubscribe({ error, params }) {
    this.branchParams = params
    if (error) {
      Utils.log('Error from Branch: ' + error)
      return
    }
    if (!params) {
      Utils.log('Branch: Params null')
      return
    }

    Utils.log('Get branch call back ' + JSON.stringify(params));
    if (params["+clicked_branch_link"]) {
      this.trackEventOnAnalytics(params);
    }

    if (this.appLastState == "background") {
      this.handleBranchEvent(params)
    } else {
      SessionManager.instance.setPendingBranchLink(params)
      this.appLastState = "background"
    }
  }

  async handleBranchEvent(params) {
    SessionManager.instance.updateBGStayTime()
    if (params["+clicked_branch_link"]) {
      if (params["actionType"] && params["actionType"] == Constants.ADD_FRIEND) { //To support older link created
        setTimeout(() => {
          if (FirebaseManager.instance.isUserAnonymous()) {
            Utils.navigateToDrawerLoginRoute(null, Constants.LOGIN_SCREEN, { "isForLoginProcess": true });
          } else {
            Utils.navigateToFriendRequests(null);
          }
        }, settings.REDIRECTION_TIME_TO_WAIT);
      } else if (params["actionType"] && params["actionType"] == Constants.INVITE_FRIEND_ACTION_TYPE) {
        const currentUserUID = Utils.getCurrentUserId()
        if (currentUserUID == params["shared_by"]) {
          Utils.log("Sender is tapping on the link")
          return
        }

        store.dispatch({
          type: T.SHOW_LOADING
        })

        setTimeout(async () => {
          let userDetails = UserSession.instance.getCurrentUserInfo()
          let sendFriendRequestParams = {
            requests: [{ receiverUID: currentUserUID, status: "pending" }],
            senderDetails: {
              senderName: params["senderName"],
              senderPhoneNumber: params["senderNumber"],
              senderProfilePic: params["senderProfilePic"],
              senderUID: params["senderUID"]
            },
          }
          if (userDetails) {
            if (userDetails.userName) {
              sendFriendRequestParams.requests[0]["receiverName"] = userDetails.userName
            }
            if (userDetails.phoneNumber) {
              sendFriendRequestParams.requests[0]["receiverPhoneNumber"] = userDetails.phoneNumber
            }
            if (userDetails.photoURL) {
              sendFriendRequestParams.requests[0]["receiverProfilePic"] = userDetails.photoURL
            }
          }

          var response = null
          try {
            response = await FirebaseManager.instance.sendFriendRequest(sendFriendRequestParams) //Call API to add Friend//Call API to update friend request  
          } catch (error) {
            Utils.log('Deep link Manager: Send Friends request API failed ' + error);
          }

          store.dispatch({
            type: T.HIDE_LOADING
          })
          let friendRequestStatus = response && response.data && response.data.requestStatus
          if (FirebaseManager.instance.isUserAnonymous()) {
            Utils.navigateToDrawerLoginRoute(null, Constants.LOGIN_SCREEN, { "isForLoginProcess": true, "senderInfo": { "senderName": params["senderName"], "senderNumber": params["senderNumber"], "senderProfilePic": params["senderProfilePic"] }, "shouldRefreshProps": true });
          } else if (Utils.checkObject(friendRequestStatus) && friendRequestStatus == 2) {
            Utils.navigateToSeeFriendsPage(null)
            setTimeout(() => {
              Utils.showToast(`You’re already friends with ${params["senderName"]}`)
            }, 2000)
          } else {
            Utils.navigateToFriendRequests(null);
          }
        }, settings.REDIRECTION_TIME_TO_WAIT);
      } else if (params["actionType"] && params["actionType"] == Constants.FRIEND_REQUEST) {
        setTimeout(() => {
          if (!FirebaseManager.instance.isUserAnonymous()) {
            Utils.navigateToFriendRequests(null);
          } else {
            Utils.navigateToDrawerLoginRoute(null, Constants.LOGIN_SCREEN, { "isForLoginProcess": true });
          }
        }, settings.REDIRECTION_TIME_TO_WAIT);
      } else if (params["actionType"] && params["actionType"].toLowerCase() == "added_influence".toLowerCase()) {
        setTimeout(() => {
          Utils.navigateToInfluencePage(null)
        }, settings.REDIRECTION_TIME_TO_WAIT)
      } else if (params["actionType"] && params["actionType"].toLowerCase() == Constants.MANAGE_ALERTS_ACTION_TYPE.toLowerCase()) {
        setTimeout(() => {
          Utils.navigateToManageAlertsPage(null, { "shouldRefreshProps": true })
        }, settings.REDIRECTION_TIME_TO_WAIT)
      } else if (params["actionType"] && params["actionType"].toLowerCase() == Constants.SEE_FRIENDS_ACTION_TYPE.toLowerCase()) {
        setTimeout(() => {
          Utils.navigateToSeeFriendsPage(null)
        }, settings.REDIRECTION_TIME_TO_WAIT)
      } else if (params["actionType"] && params["actionType"].toLowerCase() == Constants.UPPERCASE_STORY.toLowerCase()) {
        var storyData = params
        storyData.forceOpen = true
        setTimeout(() => {
          Utils.navigateToSubRouteWithParams('DrawerHome', Constants.POST_COMMENTS_SCREEN, null, { storyData })
        }, settings.REDIRECTION_TIME_TO_WAIT)
      } else {
        this.redirectToPath(params);
      }

      this.callAPIToUpdateURLReferral(params)
    }
  }

  callAPIToUpdateURLReferral = async (params) => {
    const currentUserID = Utils.getCurrentUserId()
    if (params["shared_by"] == currentUserID) { //Link generated user is tapping on the link
      return
    }

    if (params["~feature"] == Constants.REFERRAL && params["~campaign"] && params["~channel"]) {
      if (!SessionManager.instance.hasAnySubscriptionPurchase()) {
        let requestedData = { "utm_campaign": params["~campaign"], "utm_medium": params["~feature"], "utm_source": params["~channel"] }
        await FirebaseManager.instance.setReferrer(requestedData) //Call API to update the referral
      }

      if (params["~campaign"] != Constants.CREATOR && params["shared_by"] && params["deeplink_url"]) {
        this.updateSharedURLReferral({
          action: settings.INFLUENCE_POINTS_ACTIONS.SHARED_LINK_REFERRER,
          referrerID: params["shared_by"],
          url: params["deeplink_url"]
        })
      }
    }
  }

  updateSharedURLReferral = async (data) => {
    if (FirebaseManager.instance.currentUser()) {
      await FirebaseManager.instance.addInfluencePoint(data)
    } else {
      Utils.log("updateSharedURLReferral API : Current user not available")
    }
  }

  trackEventOnAnalytics = (params) => {
    let data = {}
    data["clicked_branch_link"] = params["+clicked_branch_link"] ? params["+clicked_branch_link"] : ""
    data["click_timestamp"] = params["+click_timestamp"] ? params["+click_timestamp"] : ""
    data["link_title"] = params["$og_title"] ? params["$og_title"] : ""
    data["link_image"] = params["$og_image_url"] ? params["$og_image_url"] : ""
    data["utm_campaign"] = params["~campaign"] ? params["~campaign"] : ""
    data["utm_medium"] = params["~channel"] ? params["~channel"] : ""
    data["utm_source"] = params["~feature"] ? params["~feature"] : ""
    data["is_first_session"] = params["+is_first_session"] ? params["+is_first_session"] : ""

    const event_name = params["+is_first_session"] ? "branch_install" : "branch_open"

    Utils.log('Tracking event on Firebase Analytics : event_Name : ' + event_name + ' params : ' + JSON.stringify(data))
    // tracking the event to Firebase
    FirebaseManager.instance.logEvent(event_name, data);
  }

  redirectToPath(params) {
    let comicURL = params["deeplink_path"];
    if (params["deeplink_url"] != null && params["deeplink_url"] != undefined) {
      comicURL = Utils.resolvePath(settings.apiBaseURL, params["deeplink_url"]);
    }

    const currentURL = (store.getState() && store.getState().readComic) ? store.getState().readComic.pathUrl : null;
    if (comicURL && (!currentURL && !Utils.isHomeURL(comicURL)) || (currentURL && currentURL != comicURL)) {
      setTimeout(() => {
        if (params["deeplink_url"] && params["deeplink_url"] == settings.getInfluencePageURL()) {
          Utils.navigateToInfluencePage(null)
        } else {
          const linkSharedByPremiumUser = params.isSharedByPremiumUser === "true" || params.isSharedByPremiumUser === true
          NavigationService.resetAndNavigate('Home', { comicHome: comicURL, hasUserInfo: true, isNavFromNotification: true, isSharedByPremiumUser: linkSharedByPremiumUser })
          if (linkSharedByPremiumUser) {
            const reqData = { storyID: params["storyID"], userID: params["shared_by"] }
            FirebaseManager.instance.sharedComicVisited(reqData)

            const currentUserUID = Utils.getCurrentUserId()
            if (currentUserUID == params["shared_by"]) {
              Utils.log("Sender himself tapping on the link")
              return
            }

            SessionManager.instance.setLastGiftedComicLink(comicURL)
            setTimeout(() => {
              Utils.showToast(`This is a gift link from ${params["senderName"]}`, "bottom", "giftedLinkMsg")
            }, 2000)
          }
        }
      }, settings.REDIRECTION_TIME_TO_WAIT);
    }
  }

  setUser(userID) {
    Utils.log('setting user on branch ' + userID);
    branch.setIdentity(userID)
  }

  subscribeBranch() {
    Utils.log('on branch subscribe');
    if (!this.branchSubscriber) {
      this.branchSubscriber = branch.subscribe({ onOpenComplete: this.onSubscribe })
    }
  }

  unsubscribeFromBranch() {
    if (this.branchSubscriber) {
      this.branchSubscriber();
      this.branchSubscriber = null
    }
    this.referrerId = null;
  }

  handleDynamicLink() {
    try {
      Linking.getInitialURL().then(link => { //For app opened from closed state
        Utils.log('Dynamic link received from App closed ' + JSON.stringify(link));
        link && FirebaseManager.instance.isSignInWithEmailLink(link) && this.handleInitialLinkAppOpens({ url: link })
      });

      Linking.addEventListener('url', (event) => { //For app opened from background
        Utils.log('Dynamic link received from App BG ' + JSON.stringify(event));
        if (event && event.url && FirebaseManager.instance.isSignInWithEmailLink(event.url)) {
          FirebaseManager.instance.handleEmailAuthLink(event)
        }
      });
    } catch (error) {
      console.log("handle email authentication gives error " + error)
    }
  }

  handleInitialLinkAppOpens(data) {
    // App is opened through dynamic link    
    SessionManager.instance.setPendingDynamicLink(data)
  }

  async getUniversalObject(identifier, data) {
    let contentDescription = data.description ? data.description : ''
    if (Utils.isComicURL(data.pathURL) && data.comments) {
      contentDescription = data.comments
    }

    let customData = {
      shared_by: data.currentUser.uid,
      deeplink_path: data.pathURL,
      deeplink_url: Utils.getMeaningFullURL(data.pathURL),
    }

    if (data.actionType) {
      customData.actionType = data.actionType
      if (data.senderName) {
        customData.senderName = data.senderName
      }
      if (data.senderNumber) {
        customData.senderNumber = data.senderNumber
      }
      if (data.senderProfilePic) {
        customData.senderProfilePic = data.senderProfilePic
      }
      if (data.senderUID) {
        customData.senderUID = data.senderUID
      }
    }

    if (data.storyID) {
      customData.storyID = data.storyID
    }

    if (data.isSharedByPremiumUser) {
      customData.isSharedByPremiumUser = data.isSharedByPremiumUser.toString()
      customData.senderName = data.senderName
    }

    const imageAbsURL = Utils.resolvePath(data.pathURL, data.ogImagePath)

    let universalObject = await branch.createBranchUniversalObject(identifier, {
      locallyIndex: true,
      title: data.title,
      canonicalUrl: data.webURL,
      contentDescription: contentDescription,
      contentImageUrl: imageAbsURL,
      contentMetadata: {
        customMetadata: customData,
      }
    })

    return universalObject;
  }

  async createLink(data, propertiesParam = {}) {
    const webURL = Utils.getWebURL(data.pathURL);
    const identifier = webURL
    data.webURL = webURL;

    const branchUniversalObject = await this.getUniversalObject(identifier, data);
    const channelName = Utils.getChannelName(data.pathURL);
    let linkProperties = {
      campaign: propertiesParam.campaign ? propertiesParam.campaign : 'viral',
      feature: propertiesParam.feature ? propertiesParam.feature : 'app',
      channel: propertiesParam.channel ? propertiesParam.channel : 'tinyview',
      tags: [channelName]
    }

    let properties = {
      $og_url: webURL,
      $og_type: 'website',
    }

    if (data && !data.storyType || data.storyType != 'website') {
      properties["$desktop_url"] = webURL
    }

    const { url, error } = await branchUniversalObject.generateShortUrl(linkProperties, properties);
    return { url, error };
  }

  async getBranchLinkURL(configFor, userDetails, recPhoneNumber) {
    const isConnected = NetworkUtils.instance.isAvailable()
    try {
      if (isConnected) {
        const currentUser = FirebaseManager.instance.currentUser()
        const pathURL = settings.getComicFeedHomeURL() //Showing title, description, and other details from newsfeed file.      

        return Ajax.readComic(pathURL).then(async res => {
          const description = res.comics.description
          const ogImagePath = res.comics.ogImage
          const title = res.comics.title

          const homePageIndexURL = settings.getComicHomeURL()
          let data = { title, description, ogImagePath, "pathURL": homePageIndexURL, currentUser };

          if (data.description) {
            data.description = Utils.excludeHTML(data.description);
          }

          if (data.comments) {
            data.comments = Utils.excludeHTML(data.comments);
          }

          data.actionType = configFor;
          if (userDetails) {
            data.senderNumber = userDetails.phoneNumber
            data.senderName = userDetails.displayName
            data.senderProfilePic = userDetails.photoURL
          }

          if (recPhoneNumber) {
            data.receiverContact = recPhoneNumber;
          }

          if (currentUser && currentUser.uid) {
            data.senderUID = currentUser.uid
          }

          const { url, error } = await this.createLink(data)
          Utils.log('branch url has created for => ' + data.actionType + ' ' + url);
          if (error && !url) {
            throw error;
          }
          return url;

        }).catch(async (comicError) => {
          const currentState = store.getState();

          const pathURL = settings.getComicHomeURL()
          const title = "Tinyview"//
          const description = "" //currentState.readComic.list[pathURL].description
          const ogImagePath = ""//currentState.readComic.list[pathURL].ogImage ? currentState.readComic.list[pathURL].ogImage : currentState.readComic.list[pathURL].panels[0].image;
          let data = { title, description, pathURL, ogImagePath, currentUser };

          if (data.description) {
            data.description = Utils.excludeHTML(data.description);
          }

          data.actionType = configFor;
          if (recPhoneNumber) {
            data.receiverContact = recPhoneNumber;
          }

          const { url, error } = await this.createLink(data)

          if (error && !url) {
            throw error;
          }
          return url;

        })
      }
      else {
        Utils.showError(Constants.INTERNET_ERROR)
      }
    } catch (error) {
      Utils.showError(error)
    }
  }

  async getShareBranchURL(dataParam) {
    const isConnected = NetworkUtils.instance.isAvailable()
    const comicPathURL = dataParam.action ? dataParam.action : dataParam.pageURL ? dataParam.pageURL : dataParam.pathURL
    const pathURL = Utils.resolvePath(settings.apiBaseURL, comicPathURL)

    try {
      if (isConnected) {
        const currentUser = FirebaseManager.instance.currentUser()

        try {
          let data = {}
          if (dataParam && dataParam.actionType == 'website') {
            const description = dataParam.description
            const comments = dataParam.comment
            const ogImagePath = dataParam.image
            const title = dataParam.title
            const storyID = dataParam.storyID
            const actionType = Constants.UPPERCASE_STORY
            const storyType = dataParam.actionType

            data = { title, description, ogImagePath, pathURL, currentUser, comments, actionType, storyID, storyType };
          } else {
            let res = await Ajax.readComic(pathURL)
            const description = res.comics.description
            const comments = res.comics.comments
            const ogImagePath = res.comics.ogImage
            const title = res.comics.title
            const isSharedByPremiumUser = dataParam.isSharedByPremiumUser
            const storyID = dataParam.storyID

            data = { title, description, ogImagePath, pathURL, currentUser, comments, storyID, isSharedByPremiumUser }
          }

          if (data.description) {
            data.description = Utils.excludeHTML(data.description);
          }

          if (data.comments) {
            data.comments = Utils.excludeHTML(data.comments);
          }

          if (dataParam.isSharedByPremiumUser) {
            const currUserName = UserSession.instance.getCurrentUserInfo() && UserSession.instance.getCurrentUserInfo().displayName ? UserSession.instance.getCurrentUserInfo().displayName : Constants.A_USER
            data.senderName = currUserName
          }

          const currentUserUID = Utils.getCurrentUserId()
          const utm_campaign = UserSession.instance.isUserAComicCreator() ? Constants.CREATOR : SessionManager.instance.hasAnySubscriptionPurchase() ? Constants.SUBSCRIBER : Constants.USER
          let linkProperties = {
            campaign: utm_campaign, //utm_campaign
            feature: Constants.REFERRAL, //utm_medium
            channel: currentUserUID //utm_source
          }

          const { url, error } = await this.createLink(data, linkProperties)
          Utils.log('branch url has created for sharing => ' + data.actionType + ' ' + url);

          if (error && !url) {
            throw error;
          }
          return url;

        } catch (comicError) {
          throw comicError;
        }
      }
      else {
        Utils.showError(Constants.INTERNET_ERROR)
      }
    } catch (error) {
      Utils.log('Error in getting branch URL for sharing: ' + error);
      FirebaseManager.instance.recordError(111, error, 'Error in getting branch URL for sharing')
      const webURL = Utils.getWebURL(pathURL);
      Utils.showWebShareLink(webURL)
    }
  }

}
