import { Dimensions } from 'react-native'

const TINYVIEW = 'tinyview';
const LETTER_CASE_TINYVIEW = 'Tinyview'

const WINDOW_WIDTH = Dimensions.get('window').width;

const TIME_DATE_FORMAT = 'ddd, ll・LT'
const LIST_DATE_FORMAT = 'll'

const DRAWER_NAVIGATOR = "DrawerNavigator"
const DRAWER_LOGIN_NAVIGATOR = "DrawerLogin"

const NON_SIGNED_IN_USER = "Guest User"
const ANONYMOUS_USER = "Anonymous User"
const ANONYMOUS_USERS = "Anonymous Users"
const OTP_SCREEN = 'OtpScreen'
const EDIT_PROFILE_SCREEN = 'EditProfileScreen'
const DOB_SCREEN = "DOBScreen"
const LOGIN_SCREEN = 'LoginScreen'
const USER_PROFILE_SCREEN = 'UserProfileScreen'
const WHY_SIGNUP_SCREEN = 'WhySignUpScreen'
const WHY_SIGNUP = 'Why Sign Up?'
const WHY_SIGNIN = 'Why Sign In?'

const WRITE_PUBLIC_COMMENT = 'Write a public comment…'
const WRITE_GROUP_COMMENT = 'Write a comment for friends…'
const WRITE_PERSONAL_COMMENT = 'Write a private comment…'
const WRITE_COMMENT_FOR_ALL_FRIENDS = 'Write a comment for all friends…'
const DISABLED_COMMENT = 'You are not allowed to post comments.'
const WRITE_COMMENT_FOR_NON_SUBSCRIBERS = 'Please upgrade to comment.'

const FEED_SCREEN = 'FeedScreen'
const USER_LIST_SCREEN = 'UserListScreen'
const STORY_REACTIONS_LIST_SCREEN = "StoryReactionsListScreen"
const POST_COMMENTS_SCREEN = "PostCommentsScreen"
const REPOST_SCREEN = 'RepostScreen'
const SEND_POST_SCREEN = 'SendPostScreen'
const SEND_POST = 'Send Post'
const ALL_FRIENDS = 'All Friends'
const WHATSAPP = 'WhatsApp'
const SELECT_FRIENDS = 'Select Friends'
const SEND_TO_GROUP = "Send to group"
const SEND_TO_EACH = "Send to each"
const COPY_LINK = "Copy Link"
const SHARE_VIA = "Share via…"
const INDIVIDUAL = "Individual"
const GROUP = "Group"
const SEND = "Send"
const SHARE_WITH = "Share with"
const ACTION = "Action"

const LIKED = 'Liked'
const COMMENT = 'Comment'
const COMMENTS = 'Comments'
const REPOST = 'Repost'
const REPOSTED = 'Reposted'
const WITH_WHOM_STORY_SHARED = "withWhomStoryShared"
const SHARE = 'Share'
const LIKE = 'Like'
const UNLIKE = 'Unlike'
const BE_FIRST_TO_LIKE = 'Be first to like'
const LIKES = 'Likes'
const REACTIONS = "Reactions"
const COMMENT_LIKE = 'Comment_Like'
const DISLIKE = 'Dislike'
const EDIT = 'Edit'
const REPLY = 'Reply'
const REPORT = 'Report'
const MORE = "More"
const FEED_PAGE_COMMENT_PAGINATION_COUNT = 5
const STORY_PAGE_COMMENT_PAGINATION_COUNT = 100
const FEED_PAGINATION_COUNT = 20
const SERIES_EPISODES_PAGINATION_COUNT = 30
const NOTIFICATIONS_PAGINATION_COUNT = 10
const FEED_USER_LIST_PAGINATION_COUNT = 100
const FRIENDS_LIST_PAGINATION_COUNT = 10
const USER_PROFILE_PAGE_PAGINATION_COUNT = 5
const REQUIRED_POINTS_FOR_BONUS_PANEL = 1
const REQUIRED_POINTS_FOR_PREMIUM_COMIC = 5
const CONFIRM = 'Confirm'
const DELETE = 'Delete'
const COPY = 'Copy'
const REJECT = "Reject"
const UNFRIEND = 'Unfriend'
const RESEND = 'Resend'
const SHOW_REPLIED_COMMENT = 'Show_Replied_Comment'
const FEED_LIKE = 'Feed_Like'

const STORY_PAGE_TITLE = "Chat"

const TEXT_INPUT_REF = 'textInputREF'

const REPORT_COMMENT = "Report comment"
const BLOCK_USER = "Block user"

const REQUEST_SENT = "Request Sent"
const CANCEL_REQUEST = "Cancel Request"
const REQUEST_RECEIVED = "Request Received"
const ACCEPTED = 'accepted'
const ACCEPT = 'Accept'
const ACCEPT_REQUEST = 'Accept Request'
const PENDING = 'pending'
const AVAILABLE = 'available'
const EDIT_SOCIAL_LINKS = "EditSocialLinks"
const INSTAGRAM_WEB = "instagram.com"
const FACEBOOK_WEB = "facebook.com"
const TWITTER_WEB = "twitter.com"
const inviteFriendTemplateStart = "Hey there! Your friend"
const inviteFriendTemplateEnd = "thinks you'd love Tinyview Comics. Let's connect and dive into the fun together! " //"Can I add you as a friend on Tinyview Comics?" //"Hey! \n\nI'd like you as a friend on Tinyview and share comic with you. You may also get one month free when you sign up using this invite!"
const TINYVIEW_USER = 'Tinyview User'
const ADD_FRIEND = "ADD_FRIEND"
const ADD_FRIEND_TITLE_CASE = "Add Friend"
const INVITE_FRIEND_ACTION_TYPE = "INVITE_FRIEND"
const USER_PROFILE_ACTION_TYPE = "USER_PROFILE"
const FRIEND_REQUEST = "FRIEND_REQUEST"
const INVITE_FRIENDS = "Invite Friends"
const INVITE_FRIEND = "Invite Friend"
const INTERNET_ERROR = "Oops! Something is not right. Please check your internet connection."
const PROFILE_INFO_TEXT = 'Following info help us understand our users so we can create more suitable content'
const numberConfirmationText = 'Your phone number is confirmed!'
const autoVerifyConfirmationText = 'Your phone number is confirmed! Thank you.'
const accessAccountText = 'You can now use this account on multiple devices and the web'
const otpText = 'Please enter the code sent to'
const enterPhoneNoText = 'Enter your phone number'
const PHONE_NUMBER = "Phone Number"
const VERIFICATION_CODE = "Verification Code"
const CONFIRMATION = "Confirmation"
const CONFIRMATION_SCREEN = 'ConfirmationScreen'
const WEBSITE_LINK = "websiteLink"
const FACEBOOK_LINK = "facebook"
const INSTAGRAM_LINK = "instagram"
const TWITTER_LINK = "twitter"
const X_LINK = "x"
const WHATSAPP_LINK = "whatsapp"
const MESSAGES_LINK = "messages"
const AUTHOR = 'Author'
const CANCEL = 'Cancel'
const GOT_IT = 'Got it'
const UPPERCASE_STORY = 'STORY'
const LOWERCASE_STORY = 'story'
const UPPERCASE_REPOST = 'REPOST'
const SENT_POST = 'SENT'
const GROUP_SENT_POST = 'GROUP_SENT'
const SEE_PREVIOUS_COMMENT = 'See previous comments…'
const ASC = 'asc'
const DESC = 'desc'
const COMMENT_LIMIT = 1000
const TITLE_TEXT_LIMIT = 100
const FREE = 'FREE'
const HEART = "HEART"
const ART_SUPPLIES = 'ART_SUPPLIES'
const BAGEL = 'BAGEL'
const PIZZA = 'PIZZA'
const COFFEE = 'COFFEE'
const COOKIE = 'COOKIE'
const ALL_GIFTS = "ALL_GIFTS"
const Test_PRODUCT_ID = 'Test'
const ALL = 'All'
const COPYLINK = 'copy link'
const LATEST_COMIC = 'Latest Comic'
const FIRST_COMIC = 'First Comic'
const DELETE_ACCOUNT_ALERT = 'Are you sure you want to delete this account? This action cannot be undone.'

const FIRST_EPISODE = 'First Episode'
const LATEST_EPISODE = 'Latest Episode'
const NEXT_EPISODE = 'Next Episode'
const PREVIOUS_EPISODE = 'Previous Episode'
const ALL_EPISODES = 'All Episodes'

const PREVIOUS = 'Previous'

const PUBLISH = 'Publish'
const PUBLIC = "Public"

const INSTAGRAM_DOMAIN_URL = "https://www.instagram.com/"
const FACEBOOK_DOMAIN_URL = "https://www.facebook.com/"
const TWITTER_DOMAIN_URL = "https://www.twitter.com/"
const WWW_PREFIX = "www."
const HTTPS_PREFIX = "https://"
const HTTP_PREFIX = "http://"

const VERIFY_PHONE_MSG = `So we can send you a verification code and verify it's you.`
const YOU_CAN_SIGN_IN = "So you can Sign In to the same account on multiple devices or web."
const WHY_DO_WE_ASK = 'Why do we ask?'
const WHY_DO_WE_ASK_MSG = 'You must be at least 13 years to proceed.'
const USER_ALREADY_EXIT_WITH_THIS_PHONE = `Uh Oh! A user already exists with this phone number.`
const IF_THIS_IS_YOU = 'If it is you, tap the back button and Sign In instead of Sign Up.'
const USER_NOT_EXIT = `Uh Oh! You haven’t signed up using this number before. Please go back and use Sign Up instead of Sign In first.`

const MUST_BE_ABOVE_13_ERROR = `Uh ho! You must be 13 years to Sign Up.`
const MUST_BE_ABOVE_13 = `You must be at least 13 years to proceed.`
const TAP_BACK_TO_SKIP_SIGNUP = 'Tap the back button to skip Signing Up.'
const TAP_BACK_TO_SKIP_SIGNIN = 'Tap the back button to skip Signing In.'

const PHONE_CONFIRMATION_MSG = "Your phone number is Confirmed!"
const USE_THIS_ACCOUNT_ON_MULTIPLE = 'You can now use this account on multiple devices and the web.'
const CLICK_NEXT_TO_ADD_FRIEND = "Click Next to Add Friends. Soon you will be able to privately share comics, likes, and Comments with your friends."

const ACCESS_FROM_ANYWHERE = 'Access from Anywhere'
const ACCESS_FROM_ANYWHERE_MSG = "Once you sign up, you can access your account from anywhere - mobile app, mobile browser, or desktop browser."
const SEE_COMMENTS_FROM_FRIENDS = 'See Comments from Friends'
const SEE_COMMENTS_FROM_FRIENDS_MSG = 'Add friends. Coming soon - share comics and comments privately with your friends.'

const USER_PROFILE_BLANK_MESSAGE = "Coming soon - share comics and comments privately with your friends. Start adding friends."

const ACCOUNT_DELETED_MESSAGE = "We are sorry to see you go. If you change your mind, please visit"

const UNLOCK_BONUS_PANEL_CONFIRMATION_MESSAGE = "Do you want to use your influence points to unlock this bonus panel?"
const UNLOCK_COMIC_CONFIRMATION_MESSAGE = "Do you want to use your influence points to unlock this comic?"

const MANDATORY_UPDATE_ALERT_TITLE = "You must update the app to continue"
const OPTIONAL_UPDATE_ALERT_TITLE = "Fresh new version available"

//Error Codes
const E_NO_LIBRARY_PERMISSION = "E_NO_LIBRARY_PERMISSION"
const E_NO_CAMERA_PERMISSION = "E_NO_CAMERA_PERMISSION"
const E_PICKER_CANCELLED = "E_PICKER_CANCELLED"

const INVITE_FRIENDS_SHARE_SHEET_COMMENT_FOR_SUBSCRIBERS = 'See likes and comments of your friends'

const COMIC_SHARE_SHEET_MESSAGE_FOR_ONLY_SUBSCRIBERS = 'As a premium user, you can gift comics to anyone. They can read the full comic for FREE including any bonus panels.'
const REFERRAL_SHARE_SHEET_MESSAGE = `Share this comic with friends. When they upgrade to a premium plan, you'll earn a referral bonus.`

// Drawer Menu Constant
const HOME = "Home"
const MESSAGE = "Message"
const MESSAGES = "Messages"
const DIRECTORY_OF_COMIC_SERIES = "Directory of Comic Series"
const INFLUENCE = "Influence"
const CURRENT_BALANCE = "Current Balance"
const SUBSCRIBE = "SUBSCRIBE"
const MANAGE_YOUR_PLAN = "Manage Your Plan"
const UPGRADE_TO_A_PREMIUM_PLAN = "Upgrade to a Premium Plan"
const APPLE_SUBSCRIPTIONS = 'Apple Subscriptions'
const GOOGLE_SUBSCRIPTIONS = 'Google Subscriptions'
const STRIPE_SUBSCRIPTIONS = 'Stripe Subscriptions'
const STRIPE = 'Stripe'
const APPLE = 'Apple'
const MY_INFLUENCE_COMPONENT = "MyInfluenceComponent"
const CONTACT_US = "Contact Us"
const RESTORE_PURCHASE = "Restore Purchases"
const I_AM_A_SUBSCRIBER = "I am a Subscriber"
const I_AM_A_PREMIUM_USER = "I am a Premium User"
const PREMIUM_PLAN = "Premium Plan"
const SETTINGS = "Settings"
const TERMSOFUSE = "Terms Of Use"
const PRIVACYPOLICY = "Privacy Policy"
const SIGN_IN = "Sign In"
const SIGN_UP = "Sign Up"
const USER_CONTACT = "Contact List"
const SIGN_OUT = "Sign Out"
const ACCOUNT_DELETE = 'Delete Account'
const ACCOUNT = "Account"
const LEGAL = "Legal"
const PROFILE = "Profile"
const EDIT_PROFILE = "Edit Profile"
const MY_PROFILE = "My Profile"
const DateOfBirth = 'Date Of Birth'
const CLEAR_CACHED_IMAGES = "Clear Cached Images"
const CONNECTION = "Connection"
const ADD_FRIENDS = "Add Friends"
const FRIENDS = "Friends"
const FRIEND = 'Friend'
const REQUESTS = "Friend Requests"
const FRIENDS_COMPONENT = "FriendsComponent"
const USER_PROFILE_SCREEN_DM = 'User Profile Screen'
const SEE_FRIENDS = 'See Friends'
const DRAWER_HOME = 'DrawerHome'
const EARN_INFLUENCE_POINTS = 'Earn Influence Points'
const USE_INFLUENCE_POINTS = 'Use Influence Points'
const INFLUENCE_POINTS = 'Influence Points'
const FOLLOW = 'Follow'
const FOLLOWING = 'Following'
const NOTIFICATIONS_SCREEN = 'NotificationsScreen'
const NOTIFICATION = 'Notification'
const TODAY = 'Today'
const YESTERDAY = 'Yesterday'
const EARLIER = 'Earlier'
const DIRECTORY = 'Directory'
const NOW = 'Now'
const BONUS = 'bonus'
const INVITES_RECEIVED = 'Invites Received'
const INVITES_SENT = 'Invites Sent'
const EARN_POINTS = 'Earn Points'
const USE_POINTS = 'Use Points'
const SIGN_IN_ACTION_SHEET_MESSAGE = 'You need to Sign In first'
const INVITE_FRIENDS_ACTION_SHEET_MESSAGE = 'You must be signed in to invite friends'
const UPGRADE_PLAN_SIGN_IN_ACTION_SHEET_MSG = 'You must Sign In before upgrading so that you can access your premium plan on any device.'
const LIKE_COMMENT_SHARE = 'Like, Comment, and Share'
const TOAST_DURATION = 4000
const VERIFY = 'Verify'
const SEND_VERIFICATION_CODE = 'Send Verification Code'
const SEND_VERIFICATION_LINK = 'Send Verification Link'
const ENTER_PHONE_NUMBER = 'Enter Phone Number'
const ENTER_VERIFICATION_CODE = 'Enter Verification Code'
const CREATE_PROFILE = 'Create Profile'
const SAVE = 'Save'
const SIGN_UP_SUCCESSFUL = 'Sign Up Successful'
const VISIT_HOME_PAGE = 'Visit Home Page'
const SIGN_IN_SUCCESSFUL = 'Sign In Successful'
const DO_IT_LATER = 'Do It Later'
const AGE_VALIDATION_ERROR = 'User is not older than 13 years'
const ERROR = 'Error'
const COMPLETE_PROFILE = 'Complete profile'
const SERIES_HOME = 'Series Home'
const ALL_SERIES = 'All Series'
const TEMPLATE_TYPE_LIST = 'list'
const TEMPLATE_TYPE_TOC = 'toc'
const TEMPLATE_TYPE_STORIES = 'stories'
const LONG_COMMENT_LIMIT = 90
const VIEW_MORE = 'view more'
const YOUTUBE_DOMAIN = 'youtube'
const MANAGE_ALERTS_SCREEN = 'ManageAlertsScreen'
const ALERTS = 'Alerts'
const MANAGE_ALERTS = 'Manage Alerts'
const EMAIL_ADDRESS = 'Email Address'
const EMAIL_VERIFY_SCREEN = 'EmailVerifyScreen'
const CHECK_YOUR_EMAIL = 'Check Your Email'
const MOBILE = 'mobile'
const IN_APP_PURCHASE = 'in-app-purchase'
const DONE = 'Done'
const NOT_NOW = 'Not Now'
const GET_UNLIMITED_ACCESS = 'Get Unlimited Access'
const MANAGE_ALERTS_ACTIONSHEET_TITLE = 'We’re unable to reach you'
const UNLOCK_PREMIUM_COMIC = "Unlock Comic"
const UNLOCK_BONUS_PANEL = "Unlock Bonus Panel"
const PREMIUM = "premium"
const SUBSCRIBERS_ONLY = "subscribers-only"
const EVERYONE = "everyone"
const ALL_AGES = "all-ages"
const SUBSCRIBERS_COMIC_TITLE = 'Premium Users Only Comic'
const NO_THANKS = 'No Thanks'
const UPDATE_THE_APP = 'Update the App'
const EPISODES = 'Episodes'
const CONTINUE_READING = 'continueReading'
const OTHER_SERIES = 'otherSeries'
const POST_A_COMMENT = 'Post a comment'
const EMPTY_COMMENTS_TEXT = 'Be the first one to comment.'
const OPEN_LINK = 'Open Link'
const VIEW_COMIC = 'View Comic'
const UNREAD = 'Unread'
const SHOW_UNREAD_COMICS = 'Unread Comics'
const SHOW_ALL_COMICS = 'All Comics'
const FREE_PLAN = 'Free Plan'
const UPGRADE = 'Upgrade'
const UPGRADE_SUBSCRIPTION_TITLE_MESSAGE = 'Support the artists.'
const A_USER = 'A user'
const GIFTS_SENT = 'Gifts Sent'
const RECENT_COMMENTS = 'Recent Comments'
const REJECTED = 'rejected'
const FRIEND_REQUESTS = "Friend Requests"
const DOWN = "Down"
const UP = "Up"
const SEND_FRIEND_REQUEST = "sendFriendRequest"
const SCROLL_THRESHOLD = 30
const INTERNAL_PAYMENT_PLATFORM = "tv"

//Share icons
const FACEBOOK_APP_LINK = "fb://share?link='"
const INSTAGRAM_APP_LINK = "instagram://sharesheet?text="
const X_APP_LINK = "https://twitter.com/intent/tweet?url="
const WHATSAPP_APP_LINK = "whatsapp://send?text={}&phone="

const ANDROID_FACEBOOK_APP_LINK = "com.facebook.katana"
const ANDROID_INSTAGRAM_APP_LINK = "com.instagram.android"
const ANDROID_X_APP_LINK = "com.twitter.android"
const ANDROID_WHATSAPP_APP_LINK = "com.whatsapp"

//Configuration For Action Sheet
const ACTION_SHEET_FOR_UPGRADE_PLAN = "UpgradePlan"

const LOADING_GIF_DIMEN = {
  width: 800,
  height: 1200
}

const Constants = {
  TINYVIEW,
  WINDOW_WIDTH,
  HOME,
  DIRECTORY_OF_COMIC_SERIES,
  INFLUENCE,
  INFLUENCE_POINTS,
  SUBSCRIBE,
  UPPERCASE_STORY,
  UPPERCASE_REPOST,
  SENT_POST,
  GROUP_SENT_POST,
  CANCEL,
  GOT_IT,
  FREE,
  FREE_PLAN,
  WHATSAPP,
  MESSAGE,
  MESSAGES,
  EARN_INFLUENCE_POINTS,
  USE_INFLUENCE_POINTS,
  MY_INFLUENCE_COMPONENT,
  CONTACT_US,
  I_AM_A_SUBSCRIBER,
  I_AM_A_PREMIUM_USER,
  RESTORE_PURCHASE,
  SETTINGS,
  TERMSOFUSE,
  PRIVACYPOLICY,
  SIGN_IN,
  SIGN_UP,
  USER_CONTACT,
  SIGN_OUT,
  ACCOUNT_DELETE,
  DELETE_ACCOUNT_ALERT,
  ACCOUNT,
  LEGAL,
  PROFILE,
  EDIT_PROFILE,
  MY_PROFILE,
  DateOfBirth,
  CLEAR_CACHED_IMAGES,
  CONNECTION,
  ADD_FRIENDS,
  NEXT: "Next",
  SKIP: "Skip",
  PREV: "Prev",
  FRIENDS,
  FRIEND,
  UNFRIEND,
  CONFIRM,
  DELETE,
  COPY,
  REPORT_COMMENT,
  BLOCK_USER,
  REJECT,
  RESEND,
  SEE_FRIENDS,
  REQUESTS,
  FRIENDS_COMPONENT,
  USER_PROFILE_SCREEN_DM,
  ADD_FRIEND,
  INVITE_FRIEND_ACTION_TYPE,
  REQUEST_SENT,
  CANCEL_REQUEST,
  REQUEST_RECEIVED,
  FRIEND_REQUEST,
  INTERNET_ERROR,
  INSTAGRAM_DOMAIN_URL,
  TWITTER_DOMAIN_URL,
  FACEBOOK_DOMAIN_URL,
  WWW_PREFIX,
  HTTPS_PREFIX,
  HTTP_PREFIX,
  FACEBOOK_LINK,
  TWITTER_LINK,
  X_LINK,
  INSTAGRAM_LINK,
  OTP_SCREEN,
  WEBSITE_LINK,
  otpText,
  enterPhoneNoText,
  inviteFriendTemplateStart,
  inviteFriendTemplateEnd,
  TINYVIEW_USER,
  SHARED_PREF_PROFILE_DETAILS: 'sharedPrefProfileDetails',
  SEE_PREVIOUS_COMMENT,
  RECEIVED: 'Received',
  SENT: 'Sent',
  GIFT: 'Gift',
  PROFILE_INFO_TEXT,
  EDIT_PROFILE_SCREEN,
  DOB_SCREEN,
  WHY_SIGNUP_SCREEN,
  LOGIN_SCREEN,
  USER_PROFILE_SCREEN,
  DRAWER_LOGIN_NAVIGATOR,
  DRAWER_NAVIGATOR,
  EDIT_SOCIAL_LINKS,
  INSTAGRAM_WEB,
  FACEBOOK_WEB,
  TWITTER_WEB,
  VERIFY_PHONE_MSG,
  WHY_DO_WE_ASK,
  WHY_DO_WE_ASK_MSG,
  WHY_SIGNUP,
  WHY_SIGNIN,
  YOU_CAN_SIGN_IN,
  USER_ALREADY_EXIT_WITH_THIS_PHONE,
  IF_THIS_IS_YOU,
  MUST_BE_ABOVE_13_ERROR,
  MUST_BE_ABOVE_13,
  TAP_BACK_TO_SKIP_SIGNUP,
  TAP_BACK_TO_SKIP_SIGNIN,
  PHONE_CONFIRMATION_MSG,
  USE_THIS_ACCOUNT_ON_MULTIPLE,
  CLICK_NEXT_TO_ADD_FRIEND,
  ACCESS_FROM_ANYWHERE,
  ACCESS_FROM_ANYWHERE_MSG,
  SEE_COMMENTS_FROM_FRIENDS,
  SEE_COMMENTS_FROM_FRIENDS_MSG,
  PHONE_NUMBER,
  VERIFICATION_CODE,
  numberConfirmationText,
  autoVerifyConfirmationText,
  accessAccountText,
  CONFIRMATION,
  CONFIRMATION_SCREEN,
  USER_NOT_EXIT,
  USER_PROFILE_BLANK_MESSAGE,
  E_NO_LIBRARY_PERMISSION,
  E_NO_CAMERA_PERMISSION,
  E_PICKER_CANCELLED,
  DISLIKE,
  EDIT,
  LIKE,
  UNLIKE,
  LIKES,
  REACTIONS,
  BE_FIRST_TO_LIKE,
  COMMENT_LIKE,
  LIKED,
  COMMENT,
  REPOST,
  REPOSTED,
  WITH_WHOM_STORY_SHARED,
  SHARE,
  REPLY,
  REPORT,
  MORE,
  FEED_SCREEN,
  USER_LIST_SCREEN,
  STORY_REACTIONS_LIST_SCREEN,
  REPOST_SCREEN,
  POST_COMMENTS_SCREEN,
  ALL,
  TEXT_INPUT_REF,
  FEED_PAGE_COMMENT_PAGINATION_COUNT,
  FEED_PAGINATION_COUNT,
  FEED_USER_LIST_PAGINATION_COUNT,
  NON_SIGNED_IN_USER,
  ANONYMOUS_USER,
  ANONYMOUS_USERS,
  AUTHOR,
  STORY_PAGE_TITLE,
  SHOW_REPLIED_COMMENT,
  FEED_LIKE,
  TIME_DATE_FORMAT,
  LIST_DATE_FORMAT,
  DRAWER_HOME,
  ALL_FRIENDS,
  SELECT_FRIENDS,
  COPY_LINK,
  SHARE_VIA,
  ACCEPTED,
  PENDING,
  SEND_TO_EACH,
  SEND_TO_GROUP,
  INDIVIDUAL,
  GROUP,
  AVAILABLE,
  SEND,
  SEND_POST_SCREEN,
  SEND_POST,
  PUBLISH,
  PUBLIC,
  ASC,
  DESC,
  COPYLINK,
  COMMENT_LIMIT,
  WRITE_PUBLIC_COMMENT,
  WRITE_GROUP_COMMENT,
  WRITE_PERSONAL_COMMENT,
  WRITE_COMMENT_FOR_ALL_FRIENDS,
  WRITE_COMMENT_FOR_NON_SUBSCRIBERS,
  DISABLED_COMMENT,
  HEART,
  ALL_GIFTS,
  FIRST_EPISODE,
  LATEST_EPISODE,
  NEXT_EPISODE,
  ALL_EPISODES,
  PREVIOUS_EPISODE,
  Test_PRODUCT_ID,
  SHARE_WITH,
  ACTION,
  ART_SUPPLIES,
  LATEST_COMIC,
  FIRST_COMIC,
  COOKIE_PRODUCT_ID: 'com.tinyview.subscription',
  COFFEE_PRODUCT_ID: 'com.tinyview.subscription.coffee',
  ART_SUPPLIES_PRODUCT_ID: 'com.tinyview.subscription.artsupplies',
  BAGEL_PRODUCT_ID: 'com.tinyview.subscription.bagel',
  PIZZA_PRODUCT_ID: 'com.tinyview.subscription.pizza',
  MIN_VERSION: "minVersion",
  MAX_VERSION: "maxVersion",
  COMMENT_PERMISSION_KEY: "comment",
  LIKE_PERMISSION_KEY: "like",
  REPOST_PERMISSION_KEY: "repost",
  SENT_PERMISSION_KEY: "sent",
  PURCHASE_PERMISSION_KEY: "purchase",
  FRIEND_REQUEST_PERMISSION_KEY: "friendRequest",
  ACCOUNT_DELETED_MESSAGE,
  SUBSCRIBERS_TEMPLATE: "subscribers",
  PAYMENT_MODE_STRIPE: "stripe",
  UNLOCK_BONUS_PANEL_CONFIRMATION_MESSAGE,
  UNLOCK_COMIC_CONFIRMATION_MESSAGE,
  FACEBOOK_APP_LINK,
  INSTAGRAM_APP_LINK,
  X_APP_LINK,
  WHATSAPP_APP_LINK,
  WHATSAPP_LINK,
  MESSAGES_LINK,
  ANDROID_WHATSAPP_APP_LINK,
  ANDROID_X_APP_LINK,
  ANDROID_INSTAGRAM_APP_LINK,
  ANDROID_FACEBOOK_APP_LINK,
  PIZZA,
  COFFEE,
  BAGEL,
  COOKIE,
  FOLLOW,
  FOLLOWING,
  LETTER_CASE_TINYVIEW,
  NOTIFICATIONS_SCREEN,
  NOTIFICATION,
  NOTIFICATIONS: "Notifications",
  TODAY,
  YESTERDAY,
  EARLIER,
  DIRECTORY,
  NOW,
  NOTIFICATIONS_PAGINATION_COUNT,
  BONUS,
  LOWERCASE_STORY,
  LOADING_GIF_DIMEN,
  CURRENT_BALANCE,
  INVITE_FRIENDS,
  INVITES_RECEIVED,
  INVITES_SENT,
  INVITE_FRIEND,
  INVITE_FRIENDS_SHARE_SHEET_COMMENT_FOR_SUBSCRIBERS,
  COMIC_SHARE_SHEET_MESSAGE_FOR_ONLY_SUBSCRIBERS,
  EARN_POINTS,
  USE_POINTS,
  INVITE_FRIENDS_ACTION_SHEET_MESSAGE,
  ACCEPT,
  LIKE_COMMENT_SHARE,
  WHY_INVITE_FRIEND_WITH_BUTTON_IMAGE: "why-invite-friends-with-button.jpg",
  WHY_INVITE_FRIEND_IMAGE: "why-invite-friends.jpg",
  TOAST_DURATION,
  VERIFY,
  SEND_VERIFICATION_CODE,
  SEND_VERIFICATION_LINK,
  ENTER_PHONE_NUMBER,
  ENTER_VERIFICATION_CODE,
  CREATE_PROFILE,
  SAVE,
  SIGN_UP_SUCCESSFUL,
  VISIT_HOME_PAGE,
  SIGN_IN_SUCCESSFUL,
  DO_IT_LATER,
  AGE_VALIDATION_ERROR,
  ERROR,
  COMPLETE_PROFILE,
  SERIES_HOME,
  ALL_SERIES,
  STORY_PAGE_COMMENT_PAGINATION_COUNT,
  SERIES_EPISODES_PAGINATION_COUNT,
  TEMPLATE_TYPE_LIST,
  TEMPLATE_TYPE_TOC,
  TEMPLATE_TYPE_STORIES,
  LONG_COMMENT_LIMIT,
  VIEW_MORE,
  FRIENDS_LIST_PAGINATION_COUNT,
  USER_PROFILE_PAGE_PAGINATION_COUNT,
  YOUTUBE_DOMAIN,
  IN_APP_PURCHASE,
  CREATOR: "creator",
  SUBSCRIBER: "subscriber",
  USER: "user",
  REFERRAL: "referral",
  MANAGE_ALERTS_SCREEN,
  ALERTS,
  MANAGE_ALERTS,
  EMAIL_ADDRESS,
  CHECK_YOUR_EMAIL,
  EMAIL_VERIFY_SCREEN,
  MOBILE,
  DONE,
  MANAGE_ALERTS_ACTION_TYPE: "manage_alerts",
  SEE_FRIENDS_ACTION_TYPE: "see_friends",
  NOT_NOW,
  GET_UNLIMITED_ACCESS,
  MANAGE_ALERTS_ACTIONSHEET_TITLE,
  UNLOCK_PREMIUM_COMIC,
  UNLOCK_BONUS_PANEL,
  PREMIUM,
  REQUIRED_POINTS_FOR_BONUS_PANEL,
  REQUIRED_POINTS_FOR_PREMIUM_COMIC,
  SUBSCRIBERS_ONLY,
  EVERYONE,
  ALL_AGES,
  SUBSCRIBERS_COMIC_TITLE,
  NO_THANKS,
  UPDATE_THE_APP,
  MANDATORY_UPDATE_ALERT_TITLE,
  OPTIONAL_UPDATE_ALERT_TITLE,
  EPISODES,
  CONTINUE_READING,
  OTHER_SERIES,
  POST_A_COMMENT,
  EMPTY_COMMENTS_TEXT,
  OPEN_LINK,
  VIEW_COMIC,
  UNREAD,
  SHOW_UNREAD_COMICS,
  SHOW_ALL_COMICS,
  TITLE_TEXT_LIMIT,
  LOAD_MORE: "Load more",
  SHOP: "Shop",
  BACK: "Back",
  PREVIOUS,
  APPLE_SUBSCRIPTIONS,
  GOOGLE_SUBSCRIPTIONS,
  STRIPE_SUBSCRIPTIONS,
  STRIPE,
  APPLE,
  UPGRADE_PLAN_SIGN_IN_ACTION_SHEET_MSG,
  UPGRADE,
  ACTION_SHEET_FOR_UPGRADE_PLAN,
  PREMIUM_PLAN,
  UPGRADE_TO_A_PREMIUM_PLAN,
  MANAGE_YOUR_PLAN,
  UPGRADE_SUBSCRIPTION_TITLE_MESSAGE,
  A_USER,
  GIFTS_SENT,
  COMMENTS,
  USER_PROFILE_ACTION_TYPE,
  RECENT_COMMENTS,
  REJECTED,
  FRIEND_REQUESTS,
  ACCEPT_REQUEST,
  ADD_FRIEND_TITLE_CASE,
  DOWN,
  UP,
  SEND_FRIEND_REQUEST,
  SCROLL_THRESHOLD,
  REFERRAL_SHARE_SHEET_MESSAGE,
  INTERNAL_PAYMENT_PLATFORM,
  SIGN_IN_ACTION_SHEET_MESSAGE
};


export { Constants }