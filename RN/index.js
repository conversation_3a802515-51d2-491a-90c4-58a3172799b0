/**
 * @format
 */

import 'react-native-gesture-handler'
import { AppRegistry } from 'react-native';
import App from './app/App';
import { name as appName } from './app.json';
import { getApp } from '@react-native-firebase/app'
import '@react-native-firebase/messaging';
import DeepLinkManager from './app/config/DeepLinkManager';
import { Utils } from './app/config/Utils';

getApp().messaging().setBackgroundMessageHandler(async remoteMessage => {
    if (__DEV__) {
        Utils.log('Message handled in the background!', remoteMessage);
    }
});


AppRegistry.registerComponent(appName, () => App);

DeepLinkManager.instance.subscribeBranch()
DeepLinkManager.instance.handleDynamicLink()
