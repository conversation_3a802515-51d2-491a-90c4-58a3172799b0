<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <application tools:replace="android:label"  android:label="TinyviewDev"

        android:name=".MainApplication">
        <activity
            android:name=".MainActivity">

            <intent-filter>
                <data android:scheme="tinyviewdev" />
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>

            <!-- Branch App Links (optional) -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="https" android:host="tinyview.test-app.link" />
            </intent-filter>            

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="https" android:host="tinyview-dev.firebaseapp.com" />
            </intent-filter>
            

        </activity>
        <meta-data tools:replace="android:value" android:name="io.branch.sdk.TestMode" android:value="true" />
        
        <meta-data tools:replace="android:value" android:name="com.facebook.sdk.ApplicationId" android:value="@string/facebook_app_id_dev"/>
        <meta-data tools:replace="android:value" android:name="com.facebook.sdk.ClientToken" android:value="@string/facebook_client_token_dev"/>

    </application>

    <queries>
        <package android:name="com.whatsapp" />
        <package android:name="com.facebook.katana" />
        <package android:name="com.instagram.android" />
        <package android:name="com.twitter.android" />
    </queries>

</manifest>
