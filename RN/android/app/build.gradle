apply plugin: "com.android.application"
apply plugin: "org.jetbrains.kotlin.android"
apply plugin: "com.facebook.react"
apply plugin: 'com.google.gms.google-services'

import com.google.gms.googleservices.GoogleServicesPlugin

/**
 * This is the configuration block to customize your React Native Android app.
 * By default you don't need to apply any configuration, just uncomment the lines you need.
 */

 react {
    /* Folders */
    //   The root of your project, i.e. where "package.json" lives. Default is '../..'
    // root = file("../../")
    //   The folder where the react-native NPM package is. Default is ../../node_modules/react-native
    // reactNativeDir = file("../../node_modules/react-native")
    //   The folder where the react-native Codegen package is. Default is ../../node_modules/@react-native/codegen
    // codegenDir = file("../../node_modules/@react-native/codegen")
    //   The cli.js file which is the React Native CLI entrypoint. Default is ../../node_modules/react-native/cli.js
    // cliFile = file("../../node_modules/react-native/cli.js")
    /* Variants */
    //   The list of variants to that are debuggable. For those we're going to
    //   skip the bundling of the JS bundle and the assets. By default is just 'debug'.
    //   If you add flavors like lite, prod, etc. you'll have to list your debuggableVariants.
    // debuggableVariants = ["liteDebug", "prodDebug"]
    /* Bundling */
    //   A list containing the node command and its flags. Default is just 'node'.
    // nodeExecutableAndArgs = ["node"]
    //
    //   The command to run when bundling. By default is 'bundle'
    // bundleCommand = "ram-bundle"
    //
    //   The path to the CLI configuration file. Default is empty.
    // bundleConfig = file(../rn-cli.config.js)
    //
    //   The name of the generated asset file containing your JS bundle
    // bundleAssetName = "MyApplication.android.bundle"
    //
    //   The entry file for bundle generation. Default is 'index.android.js' or 'index.js'
    // entryFile = file("../js/MyApplication.android.js")
    //
    //   A list of extra flags to pass to the 'bundle' commands.
    //   See https://github.com/react-native-community/cli/blob/main/docs/commands.md#bundle
    // extraPackagerArgs = []
    /* Hermes Commands */
    //   The hermes compiler command to run. By default it is 'hermesc'
    // hermesCommand = "$rootDir/my-custom-hermesc/bin/hermesc"
    //
    //   The list of flags to pass to the Hermes compiler. By default is "-O", "-output-source-map"
    // hermesFlags = ["-O", "-output-source-map"]
    /* Autolinking */
    autolinkLibrariesWithApp()
}

/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = false


/**
 * The preferred build flavor of JavaScriptCore (JSC)
 *
 * For example, to use the international variant, you can use:
 * `def jscFlavor = 'org.webkit:android-jsc-intl:+'`
 *
 * The international variant includes ICU i18n library and necessary data
 * allowing to use e.g. `Date.toLocaleString` and `String.localeCompare` that
 * give correct results when using with locales other than en-US.  Note that
 * this variant is about 6MiB larger per architecture than default.
 */
def jscFlavor = 'org.webkit:android-jsc:+'

def appPropertiesFile = rootProject.file("app.properties")
def appProperties = new Properties()
appProperties.load(new FileInputStream(appPropertiesFile))

android {
    ndkVersion rootProject.ext.ndkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdk rootProject.ext.compileSdkVersion
    namespace "com.newput.tinyview"
    defaultConfig {
        applicationId "com.newput.tinyview"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        compileSdk rootProject.ext.compileSdkVersion
        versionCode 525
        versionName "9.7.$versionCode"
        multiDexEnabled true
        missingDimensionStrategy 'store', 'play'
        vectorDrawables.useSupportLibrary = true        
    }


    signingConfigs {
        release {
            storeFile file(appProperties["UploadKeyFile"])
            storePassword appProperties["UploadKeyFilePassword"]
            keyAlias appProperties["UploadKeyAlias"]
            keyPassword appProperties["UploadKeyPassword"]
        }
    }

    buildTypes {
        release {
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
            signingConfig signingConfigs.release
        }

        debug {
            signingConfig signingConfigs.release
            debuggable true
        }
    }

    flavorDimensions "version"
    productFlavors {
        prod {
            applicationId "com.newput.tinyview"
            dimension "version"
            buildConfigField "boolean", "DEVELOPMENT", "false"
        }
        dev {
            applicationId "com.newput.tinyviewdev"
            dimension "version"
            versionNameSuffix "-dev"
            buildConfigField "boolean", "DEVELOPMENT", "true"
        }
        qa {
            applicationId "com.newput.tinyviewqa"
            dimension "version"
            versionNameSuffix "-qa"
            buildConfigField "boolean", "DEVELOPMENT", "true"
        }
    }
}

dependencies {
    implementation "io.branch.sdk.android:library:5.0.3"
    implementation 'com.android.support:multidex:1.0.3'
    implementation 'com.google.android.recaptcha:recaptcha:18.4.0'
    implementation "com.android.support:appcompat-v7:${rootProject.ext.supportLibVersion}"
    implementation "com.facebook.react:react-android"
    
    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }
    implementation 'com.facebook.fresco:animated-gif:2.0.0'
    implementation platform('com.google.firebase:firebase-bom:33.6.0')
}

apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'
apply plugin: 'com.google.firebase.firebase-perf'
googleServices.disableVersionCheck = true
